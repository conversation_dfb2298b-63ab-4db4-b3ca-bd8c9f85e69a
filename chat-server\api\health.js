export default function handler(req, res) {
  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.status(200).end(); 
  }

  const allowedOrigins = process.env.ALLOWED_ORIGINS 
    ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim())
    : [
        'http://localhost:5173',
        'http://localhost:3000',
        'https://www.gamedaynexus.com.br'
      ];

  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin,
    allowedOrigins,
    env: {
      hasSupabaseUrl: !!process.env.SUPABASE_URL,
      hasSupabaseKey: !!process.env.SUPABASE_SERVICE_KEY,
      hasAllowedOrigins: !!process.env.ALLOWED_ORIGINS,
      allowedOriginsValue: process.env.ALLOWED_ORIGINS,
      nodeEnv: process.env.NODE_ENV
    }
  });
}