// Sistema avançado de tracking para blog e SEO

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

export interface BlogAnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

export class BlogAnalytics {
  private static instance: BlogAnalytics;
  private isInitialized = false;

  static getInstance(): BlogAnalytics {
    if (!BlogAnalytics.instance) {
      BlogAnalytics.instance = new BlogAnalytics();
    }
    return BlogAnalytics.instance;
  }

  // Inicializar tracking
  init(measurementId: string) {
    if (this.isInitialized) return;

    // Carregar Google Analytics 4
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };

    window.gtag('js', new Date());
    window.gtag('config', measurementId, {
      // Enhanced ecommerce para tracking de conversões
      send_page_view: true,
      // Custom dimensions
      custom_map: {
        'dimension1': 'user_type',
        'dimension2': 'content_category',
        'dimension3': 'lead_magnet_type',
        'dimension4': 'blog_post_title'
      }
    });

    this.isInitialized = true;
  }

  // Track page view
  trackPageView(url: string, title: string, category?: string) {
    if (!this.isInitialized) return;

    window.gtag('config', 'G-C6XNFXQXCC', {
      page_title: title,
      page_location: `https://gamedaynexus.com${url}`,
      content_group1: category || 'blog'
    });

    // Custom event para blog posts
    this.trackEvent({
      action: 'page_view',
      category: 'blog',
      label: title,
      custom_parameters: {
        content_category: category,
        blog_post_title: title
      }
    });
  }

  // Track scroll depth
  trackScrollDepth(depth: number, postTitle: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'scroll', {
      event_category: 'engagement',
      event_label: postTitle,
      value: depth,
      custom_parameters: {
        scroll_depth: depth,
        blog_post_title: postTitle
      }
    });
  }

  // Track reading time
  trackReadingTime(timeSpent: number, postTitle: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'timing_complete', {
      name: 'blog_reading_time',
      value: timeSpent,
      event_category: 'engagement',
      event_label: postTitle,
      custom_parameters: {
        reading_time_seconds: timeSpent,
        blog_post_title: postTitle
      }
    });
  }

  // Track lead magnet downloads
  trackLeadMagnetDownload(fileName: string, postTitle: string, email?: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'download', {
      event_category: 'lead_magnet',
      event_label: fileName,
      value: 1,
      custom_parameters: {
        lead_magnet_type: this.getLeadMagnetType(fileName),
        source_post: postTitle,
        user_email: email ? 'provided' : 'not_provided'
      }
    });

    // Enhanced ecommerce event
    window.gtag('event', 'purchase', {
      transaction_id: `lead_${Date.now()}`,
      value: 0,
      currency: 'BRL',
      items: [{
        item_id: fileName,
        item_name: fileName,
        category: 'lead_magnet',
        quantity: 1,
        price: 0
      }]
    });
  }

  // Track CTA clicks
  trackCTAClick(ctaType: string, location: string, postTitle?: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'cta_click', {
      event_category: 'conversion',
      event_label: `${ctaType}_${location}`,
      value: 1,
      custom_parameters: {
        cta_type: ctaType,
        cta_location: location,
        source_post: postTitle
      }
    });
  }

  // Track video engagement
  trackVideoProgress(videoId: string, progress: number, postTitle?: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'video_progress', {
      event_category: 'video',
      event_label: videoId,
      value: progress,
      custom_parameters: {
        video_progress: progress,
        source_post: postTitle
      }
    });
  }

  // Track search queries
  trackSearch(query: string, results: number) {
    if (!this.isInitialized) return;

    window.gtag('event', 'search', {
      search_term: query,
      event_category: 'site_search',
      value: results
    });
  }

  // Track FAQ interactions
  trackFAQClick(question: string, postTitle: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'faq_click', {
      event_category: 'engagement',
      event_label: question,
      custom_parameters: {
        faq_question: question,
        source_post: postTitle
      }
    });
  }

  // Track social shares
  trackSocialShare(platform: string, postTitle: string, url: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'share', {
      method: platform,
      content_type: 'blog_post',
      item_id: url,
      event_category: 'social',
      event_label: postTitle
    });
  }

  // Track email signups
  trackEmailSignup(source: string, leadMagnet?: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'sign_up', {
      method: 'email',
      event_category: 'conversion',
      event_label: source,
      custom_parameters: {
        signup_source: source,
        lead_magnet: leadMagnet
      }
    });
  }

  // Track trial starts
  trackTrialStart(source: string, postTitle?: string) {
    if (!this.isInitialized) return;

    window.gtag('event', 'begin_checkout', {
      currency: 'BRL',
      value: 0,
      event_category: 'conversion',
      event_label: 'trial_start',
      custom_parameters: {
        trial_source: source,
        source_post: postTitle
      }
    });
  }

  // Generic event tracking
  trackEvent(event: BlogAnalyticsEvent) {
    if (!this.isInitialized) return;

    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    });
  }

  // Helper methods
  private getLeadMagnetType(fileName: string): string {
    if (fileName.includes('planilha')) return 'planilha';
    if (fileName.includes('template')) return 'template';
    if (fileName.includes('checklist')) return 'checklist';
    if (fileName.includes('calculadora')) return 'calculadora';
    if (fileName.includes('.pdf')) return 'pdf';
    return 'outros';
  }
}

// Hook para usar analytics em componentes React
export function useBlogAnalytics() {
  const analytics = BlogAnalytics.getInstance();

  return {
    trackPageView: analytics.trackPageView.bind(analytics),
    trackScrollDepth: analytics.trackScrollDepth.bind(analytics),
    trackReadingTime: analytics.trackReadingTime.bind(analytics),
    trackLeadMagnetDownload: analytics.trackLeadMagnetDownload.bind(analytics),
    trackCTAClick: analytics.trackCTAClick.bind(analytics),
    trackVideoProgress: analytics.trackVideoProgress.bind(analytics),
    trackSearch: analytics.trackSearch.bind(analytics),
    trackFAQClick: analytics.trackFAQClick.bind(analytics),
    trackSocialShare: analytics.trackSocialShare.bind(analytics),
    trackEmailSignup: analytics.trackEmailSignup.bind(analytics),
    trackTrialStart: analytics.trackTrialStart.bind(analytics),
    trackEvent: analytics.trackEvent.bind(analytics)
  };
}

// Hook para tracking automático de scroll e reading time
export function useReadingAnalytics(postTitle: string) {
  const { trackScrollDepth, trackReadingTime } = useBlogAnalytics();
  
  React.useEffect(() => {
    const startTime = Date.now();
    let maxScroll = 0;
    let scrollTracked = new Set<number>();

    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      );
      
      maxScroll = Math.max(maxScroll, scrollPercent);

      // Track scroll milestones
      const milestones = [25, 50, 75, 90, 100];
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !scrollTracked.has(milestone)) {
          trackScrollDepth(milestone, postTitle);
          scrollTracked.add(milestone);
        }
      });
    };

    const handleBeforeUnload = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      if (timeSpent > 10) { // Only track if spent more than 10 seconds
        trackReadingTime(timeSpent, postTitle);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [postTitle, trackScrollDepth, trackReadingTime]);
}

// Inicializar analytics globalmente
export function initBlogAnalytics() {
  const analytics = BlogAnalytics.getInstance();
  analytics.init('G-C6XNFXQXCC');
}

// Export da instância para uso direto
export const blogAnalytics = BlogAnalytics.getInstance();