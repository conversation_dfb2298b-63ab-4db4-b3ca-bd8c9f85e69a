import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useFinancialCategoriesStore } from "@/store/useFinancialCategoriesStore";
import { useToast } from "@/components/ui/use-toast";

interface ManageFinancialCategoriesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function ManageFinancialCategoriesDialog({ open, onOpenChange, clubId }: ManageFinancialCategoriesDialogProps) {
  const { categories, fetchCategories, addCategory, updateCategory, deleteCategory, loading } = useFinancialCategoriesStore();
  const { toast } = useToast();
  const [newName, setNewName] = useState("");
  const [editing, setEditing] = useState<Record<number, string>>({});

  useEffect(() => {
    if (open) {
      fetchCategories(clubId);
    }
  }, [open, clubId, fetchCategories]);

  const handleAdd = async () => {
    if (!newName.trim()) return;
    await addCategory(clubId, newName.trim());
    setNewName("");
    toast({ title: "Sucesso", description: "Categoria adicionada." });
  };

  const handleUpdate = async (id: number) => {
    const name = editing[id];
    if (!name || !name.trim()) return;
    await updateCategory(clubId, id, name.trim());
    toast({ title: "Sucesso", description: "Categoria atualizada." });
  };

  const handleDelete = async (id: number) => {
    await deleteCategory(clubId, id);
    toast({ title: "Sucesso", description: "Categoria removida." });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Gerenciar Categorias</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-2">
          <div className="flex items-end gap-2">
            <div className="flex-grow space-y-2">
              <Label htmlFor="newCat">Nova Categoria</Label>
              <Input id="newCat" value={newName} onChange={(e) => setNewName(e.target.value)} />
            </div>
            <Button onClick={handleAdd} disabled={loading || !newName.trim()}>Adicionar</Button>
          </div>

          <div className="space-y-2">
            {categories.length === 0 ? (
              <p className="text-sm text-muted-foreground">Nenhuma categoria cadastrada.</p>
            ) : (
              categories.map((cat) => (
                <div key={cat.id} className="flex items-center gap-2">
                  <Input
                    value={editing[cat.id] ?? cat.name}
                    onChange={(e) => setEditing({ ...editing, [cat.id]: e.target.value })}
                  />
                  <Button size="sm" variant="outline" onClick={() => handleUpdate(cat.id)} disabled={loading}>Salvar</Button>
                  <Button size="sm" variant="destructive" onClick={() => handleDelete(cat.id)} disabled={loading}>
                    Excluir
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}