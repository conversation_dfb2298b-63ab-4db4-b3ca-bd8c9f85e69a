import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import html2canvas from "html2canvas";
import { getCategories } from './categories';
import { getClubTitles, ClubTitle } from './clubTitles';
import { createMedicalNotification } from './notifications';
import { getCollaboratorById } from './collaborators';

/**
 * Creates a notification for a task assignment
 * @param clubId Club ID
 * @param task The created task
 * @param createdByUserId User ID who created the task
 */
async function createTaskNotification(
  clubId: number,
  task: AdministrativeTask,
  createdByUserId: string
): Promise<void> {
  try {
    // Get collaborator info to find their user_id
    const collaborator = await getCollaboratorById(clubId, task.collaborator_id!);

    if (!collaborator || !collaborator.user_id) {
      console.warn(`Collaborator ${task.collaborator_id} not found or has no user_id`);
      return;
    }

    // Get creator name for the notification
    const { data: creatorData, error: creatorError } = await supabase
      .from('users')
      .select('name')
      .eq('id', createdByUserId)
      .single();

    const creatorName = creatorData?.name || 'Administrador';

    // Format due date for notification
    const dueDateText = task.due_date
      ? ` com prazo para ${new Date(task.due_date).toLocaleDateString('pt-BR')}`
      : '';

    // Create notification for the assigned collaborator
    await createMedicalNotification({
      club_id: clubId,
      user_id: collaborator.user_id,
      title: "Nova tarefa atribuída",
      message: `${creatorName} criou uma nova tarefa para você: "${task.title}"${dueDateText}.`,
      type: "task_assignment",
      reference_id: task.id.toString(),
      reference_type: "administrative_task"
    });

  } catch (error) {
    console.error("Error creating task notification:", error);
    throw error;
  }
}

// Types
export type AdministrativeDocument = {
  id: number;
  club_id: number;
  document_number: number;
  title: string;
  content: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  digital_signature: boolean;
  signed_by?: string;
  signed_at?: string;
  signature_url?: string;
  document_type: string;
  creator_name?: string; // For joins
  signer_name?: string; // For joins
};

export type AdministrativeTask = {
  id: number;
  club_id: number;
  title: string;
  description?: string;
  responsible?: string;
  collaborator_id?: number;
  due_date?: string;
  status: 'a_fazer' | 'em_andamento' | 'concluido';
  created_by: string;
  created_at: string;
  updated_at: string;
  responsible_name?: string; // For joins
  creator_name?: string; // For joins
  collaborator_name?: string; // For joins
};

export type AdministrativeReminder = {
  id: number;
  club_id: number;
  title: string;
  description?: string;
  reminder_date: string;
  completed: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  reminder_type: 'activity' | 'document' | 'email' | 'meeting';
  creator_name?: string; // For joins
};

// Document Functions
export async function getAdministrativeDocuments(
  clubId: number,
  limit: number = 10,
  offset: number = 0
): Promise<{ data: AdministrativeDocument[]; total: number }> {
  const { data, error, count } = await supabase
    .from("administrative_documents_view")
    .select("*", { count: "exact" })
    .eq("club_id", clubId)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Error fetching administrative documents:", error);
    throw new Error(`Error fetching administrative documents: ${error.message}`);
  }

  return { data: data || [], total: count || 0 };
}

export async function getAdministrativeDocumentById(clubId: number, id: number): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents_view")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Error fetching administrative document:", error);
    throw new Error(`Error fetching administrative document: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeDocument(
  clubId: number,
  document: Omit<AdministrativeDocument, "id" | "created_at" | "updated_at" | "document_number">
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .insert({
      club_id: clubId,
      title: document.title,
      content: document.content,
      created_by: document.created_by,
      digital_signature: document.digital_signature || false,
      document_type: document.document_type
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative document:", error);
    throw new Error(`Error creating administrative document: ${error.message}`);
  }

  return data;
};

export async function updateAdministrativeDocument(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeDocument>
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative document:", error);
    throw new Error(`Error updating administrative document: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeDocument(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_documents")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative document:", error);
    throw new Error(`Error deleting administrative document: ${error.message}`);
  }

  return true;
};

export async function signAdministrativeDocument(
  clubId: number,
  id: number,
  userId: string,
  signatureUrl?: string
): Promise<AdministrativeDocument> {
  const { data, error } = await supabase
    .from("administrative_documents")
    .update({
      digital_signature: true,
      signed_by: userId,
      signed_at: new Date().toISOString(),
      signature_url: signatureUrl,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error signing administrative document:", error);
    throw new Error(`Error signing administrative document: ${error.message}`);
  }

  // Fetch the document from the view to include joined fields like signer_name
  const { data: viewData, error: viewError } = await supabase
    .from("administrative_documents_view")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", data?.id)
    .single();

  if (viewError) {
    console.error("Error fetching administrative document after sign:", viewError);
    throw new Error(
      `Error fetching administrative document after sign: ${viewError.message}`
    );
  }

  return viewData as AdministrativeDocument;
};

// Task Functions
export async function getAdministrativeTasks(
  clubId: number,
  collaboratorId?: number
): Promise<AdministrativeTask[]> {
  let query = supabase
    .from("administrative_tasks_view")
    .select("*")
    .eq("club_id", clubId);

  if (collaboratorId) {
    query = query.eq("collaborator_id", collaboratorId);
  }

  const { data, error } = await query.order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching administrative tasks:", error);
    throw new Error(`Error fetching administrative tasks: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeTask(
  clubId: number,
  task: Omit<AdministrativeTask, "id" | "created_at" | "updated_at">
): Promise<AdministrativeTask> {
  const { data, error } = await supabase
    .from("administrative_tasks")
    .insert({
      club_id: clubId,
      title: task.title,
      description: task.description,
      responsible: task.responsible,
      collaborator_id: task.collaborator_id,
      due_date: task.due_date,
      status: task.status || 'a_fazer',
      created_by: task.created_by
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative task:", error);
    throw new Error(`Error creating administrative task: ${error.message}`);
  }

  // Create notification for the assigned collaborator
  if (task.collaborator_id) {
    try {
      await createTaskNotification(clubId, data, task.created_by);
    } catch (notificationError) {
      console.error("Error creating task notification:", notificationError);
      // Don't fail task creation if notification fails
    }
  }

  return data;
};

export async function updateAdministrativeTask(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeTask>
): Promise<AdministrativeTask> {
  const { data, error } = await supabase
    .from("administrative_tasks")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative task:", error);
    throw new Error(`Error updating administrative task: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeTask(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_tasks")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative task:", error);
    throw new Error(`Error deleting administrative task: ${error.message}`);
  }

  return true;
};

// Reminder Functions
export async function getAdministrativeReminders(clubId: number): Promise<AdministrativeReminder[]> {
  const { data, error } = await supabase
    .from("administrative_reminders_view")
    .select("*")
    .eq("club_id", clubId)
    .order("reminder_date", { ascending: true });

  if (error) {
    console.error("Error fetching administrative reminders:", error);
    throw new Error(`Error fetching administrative reminders: ${error.message}`);
  }

  return data;
}

export async function createAdministrativeReminder(
  clubId: number,
  reminder: Omit<AdministrativeReminder, "id" | "created_at" | "updated_at">
): Promise<AdministrativeReminder> {
  const { data, error } = await supabase
    .from("administrative_reminders")
    .insert({
      club_id: clubId,
      title: reminder.title,
      description: reminder.description,
      reminder_date: reminder.reminder_date,
      completed: reminder.completed || false,
      created_by: reminder.created_by,
      reminder_type: reminder.reminder_type
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating administrative reminder:", error);
    throw new Error(`Error creating administrative reminder: ${error.message}`);
  }

  return data;
};

export async function updateAdministrativeReminder(
  clubId: number,
  id: number,
  updates: Partial<AdministrativeReminder>
): Promise<AdministrativeReminder> {
  const { data, error } = await supabase
    .from("administrative_reminders")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating administrative reminder:", error);
    throw new Error(`Error updating administrative reminder: ${error.message}`);
  }

  return data;
};

export async function deleteAdministrativeReminder(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("administrative_reminders")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Error deleting administrative reminder:", error);
    throw new Error(`Error deleting administrative reminder: ${error.message}`);
  }

  return true;
};

// PDF Generation
export async function generateDocumentPDF(docData: AdministrativeDocument, clubInfo: any): Promise<Blob> {
  return new Promise(async (resolve, reject) => {
    const doc = new jsPDF();
    const leftBarWidth = 40;
    const contentX = leftBarWidth + 15;
    const contentWidth = doc.internal.pageSize.width - leftBarWidth - 25;
    let initialY = 10;

    const categories = await getCategories(docData.club_id);
    const titles: ClubTitle[] = await getClubTitles(docData.club_id);

    // Desenha barra lateral com informações do clube
    doc.setFillColor(clubInfo.primary_color || '#1f2937');
    doc.rect(0, 0, leftBarWidth, doc.internal.pageSize.height, 'F');

    // A cor do texto na barra lateral deve sempre ser branca para garantir contraste
    doc.setTextColor('#ffffff'); doc.setFontSize(10);
    let sidebarY = 20;
    const sidebarLineHeight = 7; // Espaçamento maior para os itens da barra lateral

    const addSidebarEntry = (label: string, value: string, indent = 5) => {
      const maxWidth = leftBarWidth - indent - 3;
      const lines = doc.splitTextToSize(`${label}${value}`, maxWidth);
      doc.text(lines, indent, sidebarY, { maxWidth });
      sidebarY += lines.length * 5;
    };

    if (clubInfo.website) {
      addSidebarEntry('Site: ', clubInfo.website);
    }
    if (clubInfo.email) {
      addSidebarEntry('Email: ', clubInfo.email);
    }
    if (clubInfo.address) {
      addSidebarEntry('Endereço: ', clubInfo.address);
    }
    if (categories.length) {
      doc.text('Categorias:', 5, sidebarY);
      sidebarY += sidebarLineHeight;
      categories.forEach((cat: any) => {
        const lines = doc.splitTextToSize(`- ${cat.name}`, leftBarWidth - 12);
        doc.text(lines, 7, sidebarY, { maxWidth: leftBarWidth - 12 });
        sidebarY += lines.length * sidebarLineHeight;
      });
    }
    if (titles.length) {
      doc.text('Títulos:', 5, sidebarY);
      sidebarY += sidebarLineHeight;
      titles.forEach((t) => {
        const text = `- ${t.title}${t.year ? ' (' + t.year + ')' : ''}`;
        const lines = doc.splitTextToSize(text, leftBarWidth - 12);
        doc.text(lines, 7, sidebarY, { maxWidth: leftBarWidth - 12 });
        sidebarY += lines.length * sidebarLineHeight;
      });
    }
    doc.setTextColor(0, 0, 0);

    // Função auxiliar para adicionar cabeçalho de texto e conteúdo principal
    const addHeaderTextAndMainContent = async () => {
      const centerX = leftBarWidth + (doc.internal.pageSize.width - leftBarWidth) / 2;
      doc.setFontSize(18);
      doc.text(clubInfo.name || 'Clube', centerX, initialY > 30 ? initialY - 10 : 20, { align: 'center' });
      initialY = initialY > 30 ? initialY : 30;

      doc.setFontSize(14);
      doc.text(`${docData.document_type} Nº ${docData.document_number}`, centerX, initialY, { align: 'center' });
      initialY += 10;

      doc.setFontSize(12);
      const createdDate = new Date(docData.created_at).toLocaleDateString('pt-BR');
      doc.text(`Data: ${createdDate}`, contentX, initialY);
      initialY += 10;

      doc.setFontSize(14);
      doc.text(docData.title, contentX, initialY, { maxWidth: contentWidth });
      const titleLines = doc.splitTextToSize(docData.title, contentWidth);
      initialY += (titleLines.length * 7) + 10;

      if (initialY > doc.internal.pageSize.height - 40) {
        doc.addPage();
        initialY = 20;
      }

      const tempDiv = window.document.createElement('div');
      tempDiv.style.fontFamily = 'Arial, sans-serif';
      tempDiv.style.fontSize = '10pt';
      tempDiv.style.lineHeight = '1.4';
      tempDiv.style.color = '#333';
      tempDiv.style.width = `${contentWidth}mm`;
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.paddingBottom = '20px';
      tempDiv.innerHTML = docData.content;
      window.document.body.appendChild(tempDiv);

      const canvas = await html2canvas(tempDiv, { scale: 2, useCORS: true });
      window.document.body.removeChild(tempDiv);

      const imgWidthPx = canvas.width;
      const imgHeightPx = canvas.height;
      const widthRatio = contentWidth / imgWidthPx;
      const finalWidth = imgWidthPx * widthRatio;
      const finalHeight = imgHeightPx * widthRatio;

      const pageHeight = doc.internal.pageSize.height;
      const bottomMargin = 20;
      const availableHeight = pageHeight - bottomMargin;

      const imgData = canvas.toDataURL('image/png');
      let remainingHeight = finalHeight;
      let yStart = 0;
      let currentY = initialY;

      while (remainingHeight > 0) {
        const spaceLeft = availableHeight - currentY;
        const drawHeight = Math.min(spaceLeft, remainingHeight);
        const cropHeight = drawHeight / widthRatio;

        doc.addImage(
          imgData,
          'PNG',
          contentX,
          currentY,
          finalWidth,
          drawHeight,
          '',
          'FAST',
          0,
          yStart,
          imgWidthPx,
          cropHeight
        );

        remainingHeight -= drawHeight;
        yStart += cropHeight;
        currentY += drawHeight;

        if (remainingHeight > 0) {
          doc.addPage();
          // Redesenhar barra lateral na nova página
          doc.setFillColor(clubInfo.primary_color || '#1f2937');
          doc.rect(0, 0, leftBarWidth, doc.internal.pageSize.height, 'F');
          doc.setTextColor(0, 0, 0);
          currentY = 20;
        }
      }

      if (docData.digital_signature && docData.signed_by) {
        let signatureY = currentY + 20;
        if (doc.internal.pageSize.height - signatureY < 70) {
          doc.addPage();
          doc.setFillColor(clubInfo.primary_color || '#1f2937');
          doc.rect(0, 0, leftBarWidth, doc.internal.pageSize.height, 'F');
          doc.setTextColor(0, 0, 0);
          signatureY = 30;
        }

        // Buscar a role do usuário que assinou
        let signerRole = '';
        try {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('role')
            .eq('id', docData.signed_by)
            .single();

          if (userError) {
            console.warn("Could not fetch signer's role:", userError.message);
          } else {
            signerRole = userData?.role || '';
          }
        } catch (fetchError) {
          console.warn("Error fetching signer's role:", fetchError);
        }

        if (docData.signature_url) {
          try {
            const res = await fetch(docData.signature_url);
            const blob = await res.blob();
            const reader = new FileReader();

            const dataUrl: string = await new Promise((res, rej) => {
              reader.onerror = () => rej(reader.error);
              reader.onload = () => res(reader.result as string);
              reader.readAsDataURL(blob);
            });

            doc.addImage(dataUrl, 'PNG', (doc.internal.pageSize.width / 2) - 30, signatureY, 60, 30);
            signatureY += 35;
          } catch (error) {
            console.error('Error adding signature image to PDF:', error);
          }
        }

        doc.setFontSize(10);
        doc.text('Documento assinado digitalmente por:', doc.internal.pageSize.width / 2, signatureY, { align: 'center' });
        signatureY += 5;
        doc.text(docData.signer_name || 'Usuário', doc.internal.pageSize.width / 2, signatureY, { align: 'center' });
        signatureY += 5;
        // Adicionar a role, se disponível
        if (signerRole) {
          doc.text(signerRole, doc.internal.pageSize.width / 2, signatureY, { align: 'center' });
          signatureY += 5;
        }
        doc.text(`Data: ${new Date(docData.signed_at || '').toLocaleDateString('pt-BR')}`, doc.internal.pageSize.width / 2, signatureY, { align: 'center' });
      }

      resolve(doc.output('blob'));
    };

    // Lógica para adicionar o logo (se existir)
    if (clubInfo.logo_url) {
      try {
        // Tenta adicionar a imagem do logo diretamente pela URL
        // NOTA: Isso pode não funcionar no servidor para URLs http/https.
        // Se não funcionar, a URL precisará ser buscada e convertida para base64 ANTES de chamar addImage.
        // Para addImage funcionar com URL no servidor, jsPDF precisaria de um ambiente que suporte fetch ou XHR e Canvas, o que não é o caso aqui.
        // Por agora, apenas tentamos. Se falhar, o console mostrará um erro, mas o PDF tentará continuar.
        doc.addImage(clubInfo.logo_url, 'JPEG', contentX, initialY, 30, 0); // Altura 0 para auto-ajuste pela largura 40
        // A Posição Y (initialY) será ajustada *após* a imagem ser teoricamente adicionada.
        // Como não temos como saber a altura real da imagem de forma síncrona aqui sem 'new Image()',
        // vamos adicionar um espaço fixo. Isso pode precisar de ajuste se a imagem for muito alta ou muito baixa.
        initialY += 40 + 10; // Assumindo altura do logo ~40 + espaço
        await addHeaderTextAndMainContent();
      } catch (error) {
        console.error("Error adding logo to PDF (direct URL attempt):", error);
        // Prosseguir sem o logo se houver erro na tentativa direta
        await addHeaderTextAndMainContent();
      }
    } else {
      // Prosseguir sem o logo
      await addHeaderTextAndMainContent();
    }
  });
}