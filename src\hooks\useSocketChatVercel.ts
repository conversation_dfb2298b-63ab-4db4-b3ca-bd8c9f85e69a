import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/context/UserContext';

let socket: Socket | null = null;

export function useSocketChatVercel() {
  const { user } = useUser();
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);

  useEffect(() => {
    if (!user?.id) return;

    const connectSocket = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        const token = session?.access_token;
        const storedClubId = localStorage.getItem('clubId');
        const clubId = user?.club_id ?? (storedClubId ? Number(storedClubId) : undefined);

        if (!token || !clubId) {
          setMessages(prev => [...prev, 'Erro: Token ou clubId não encontrado']);
          return;
        }

        const socketUrl = 'https://chat-server-iota-inky.vercel.app';
        const socketPath = '/api/socket-vercel';

        console.log('🔌 Conectando Socket.IO Vercel...', { socketUrl, socketPath, clubId });

        socket = io(socketUrl, {
          path: socketPath,
          auth: { token, clubId },
          transports: ['polling'],
          timeout: 10000,
          forceNew: true
        });

        socket.on('connect', () => {
          console.log('✅ Conectado ao Vercel!');
          setIsConnected(true);
          setMessages(prev => [...prev, '✅ Conectado com sucesso!']);
        });

        socket.on('disconnect', (reason) => {
          console.log('❌ Desconectado:', reason);
          setIsConnected(false);
          setMessages(prev => [...prev, `❌ Desconectado: ${reason}`]);
        });

        socket.on('connect_error', (error) => {
          console.error('❌ Erro de conexão:', error.message);
          setMessages(prev => [...prev, `❌ Erro: ${error.message}`]);
        });

        socket.on('connected', (data) => {
          console.log('🎉 Confirmação do servidor:', data);
          setMessages(prev => [...prev, `🎉 Servidor: ${data.message}`]);
        });

        socket.on('pong', (data) => {
          console.log('🏓 Pong recebido:', data);
          setMessages(prev => [...prev, `🏓 Pong: ${JSON.stringify(data)}`]);
        });

        socket.on('test-response', (data) => {
          console.log('📨 Resposta teste:', data);
          setMessages(prev => [...prev, `📨 Resposta: ${JSON.stringify(data)}`]);
        });

      } catch (error) {
        console.error('Erro ao conectar:', error);
        setMessages(prev => [...prev, `Erro: ${error}`]);
      }
    };

    connectSocket();

    return () => {
      if (socket) {
        socket.disconnect();
        socket = null;
        setIsConnected(false);
      }
    };
  }, [user?.id, user?.club_id]);

  const sendPing = () => {
    if (socket?.connected) {
      const pingData = { message: 'ping', timestamp: new Date().toISOString() };
      console.log('🏓 Enviando ping:', pingData);
      socket.emit('ping', pingData);
      setMessages(prev => [...prev, `🏓 Ping enviado: ${JSON.stringify(pingData)}`]);
    } else {
      setMessages(prev => [...prev, '❌ Não conectado']);
    }
  };

  const sendTestMessage = (message: string) => {
    if (socket?.connected) {
      console.log('💬 Enviando mensagem teste:', message);
      socket.emit('test-message', { text: message, timestamp: new Date().toISOString() });
      setMessages(prev => [...prev, `💬 Enviado: ${message}`]);
    } else {
      setMessages(prev => [...prev, '❌ Não conectado']);
    }
  };

  return {
    isConnected,
    messages,
    sendPing,
    sendTestMessage
  };
}