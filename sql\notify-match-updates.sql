
-- Arquivo: sql/notify-match-updates.sql
-- Descrição: Cria funções e gatilhos no PostgreSQL para notificar administradores e presidentes sobre alterações em partidas e escalações.

-- 1. Função de Notificação para Partidas (Tabela: matches)
CREATE OR REPLACE FUNCTION public.notify_match_changes()
RETURNS TRIGGER AS $
DECLARE
    user_name TEXT;
    operation_type TEXT;
    notification_title TEXT;
    notification_message TEXT;
    target_club_id INT;
    record_id TEXT;
    opponent_name TEXT;
    match_date DATE;
BEGIN
    -- Define o club_id e o ID do registro com base na operação
    IF (TG_OP = 'DELETE') THEN
        target_club_id := OLD.club_id;
        record_id := OLD.id::TEXT;
        opponent_name := OLD.opponent;
        match_date := OLD.date;
    ELSE
        target_club_id := NEW.club_id;
        record_id := NEW.id::TEXT;
        opponent_name := NEW.opponent;
        match_date := NEW.date;
    END IF;

    -- Busca o nome do usuário que realizou a ação
    SELECT u.name INTO user_name
    FROM public.users u
    JOIN public.collaborators c ON u.id = c.user_id
    WHERE c.user_id = auth.uid() AND c.club_id = target_club_id;

    IF user_name IS NULL THEN
        SELECT email INTO user_name FROM auth.users WHERE id = auth.uid();
    END IF;

    -- Define o tipo de operação
    IF (TG_OP = 'INSERT') THEN
        operation_type := 'criou a partida contra';
    ELSIF (TG_OP = 'UPDATE') THEN
        operation_type := 'atualizou a partida contra';
    ELSIF (TG_OP = 'DELETE') THEN
        operation_type := 'excluiu a partida contra';
    END IF;

    -- Constrói a notificação
    notification_title := 'Atualização no Módulo de Partidas';
    notification_message := 'O usuário ' || COALESCE(user_name, 'desconhecido') || ' ' || operation_type || ' ' || opponent_name || ' no dia ' || TO_CHAR(match_date, 'DD/MM/YYYY') || '.';

    -- Insere as notificações para admins e presidentes
    INSERT INTO public.notifications (club_id, user_id, title, message, type, reference_id, reference_type)
    SELECT
        target_club_id,
        cm.user_id,
        notification_title,
        notification_message,
        'system',
        record_id,
        'match'
    FROM public.club_members cm
    WHERE cm.club_id = target_club_id
      AND cm.role IN ('admin', 'president');

    RETURN NULL;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Gatilho para a Tabela de Partidas (matches)
DROP TRIGGER IF EXISTS trg_notify_match_changes ON public.matches;
CREATE TRIGGER trg_notify_match_changes
AFTER INSERT OR UPDATE OR DELETE ON public.matches
FOR EACH ROW EXECUTE FUNCTION public.notify_match_changes();

-- 3. Função de Notificação para Escalações (Tabela: match_squad)
CREATE OR REPLACE FUNCTION public.notify_match_squad_changes()
RETURNS TRIGGER AS $
DECLARE
    user_name TEXT;
    notification_title TEXT;
    notification_message TEXT;
    target_club_id INT;
    record_id TEXT;
    opponent_name TEXT;
    match_date DATE;
BEGIN
    -- Define o club_id e o ID do registro com base na operação
    IF (TG_OP = 'DELETE') THEN
        target_club_id := OLD.club_id;
        record_id := OLD.match_id::TEXT;
    ELSE
        target_club_id := NEW.club_id;
        record_id := NEW.match_id::TEXT;
    END IF;

    -- Busca o nome do oponente e a data da partida
    SELECT opponent, date INTO opponent_name, match_date
    FROM public.matches
    WHERE id = record_id::UUID;

    -- Busca o nome do usuário que realizou a ação
    SELECT u.name INTO user_name
    FROM public.users u
    JOIN public.collaborators c ON u.id = c.user_id
    WHERE c.user_id = auth.uid() AND c.club_id = target_club_id;

    IF user_name IS NULL THEN
        SELECT email INTO user_name FROM auth.users WHERE id = auth.uid();
    END IF;

    -- Constrói a notificação
    notification_title := 'Atualização na Escalação da Partida';
    notification_message := 'O usuário ' || COALESCE(user_name, 'desconhecido') || ' atualizou a escalação para a partida contra ' || opponent_name || ' no dia ' || TO_CHAR(match_date, 'DD/MM/YYYY') || '.';

    -- Insere as notificações para admins e presidentes
    INSERT INTO public.notifications (club_id, user_id, title, message, type, reference_id, reference_type)
    SELECT
        target_club_id,
        cm.user_id,
        notification_title,
        notification_message,
        'system',
        record_id,
        'match_squad'
    FROM public.club_members cm
    WHERE cm.club_id = target_club_id
      AND cm.role IN ('admin', 'president');

    RETURN NULL;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Gatilho para a Tabela de Escalação (match_squad)
-- Usamos um gatilho de TRUNCATE também, caso a escalação seja limpa
DROP TRIGGER IF EXISTS trg_notify_match_squad_changes ON public.match_squad;
CREATE TRIGGER trg_notify_match_squad_changes
AFTER INSERT OR UPDATE OR DELETE ON public.match_squad
FOR EACH ROW EXECUTE FUNCTION public.notify_match_squad_changes();

-- 5. Comentários para Documentação
COMMENT ON FUNCTION public.notify_match_changes() IS 'Envia uma notificação para admins e presidentes quando uma partida é criada, atualizada ou excluída.';
COMMENT ON TRIGGER trg_notify_match_changes ON public.matches IS 'Executa a função notify_match_changes() após uma alteração em um registro de partida.';
COMMENT ON FUNCTION public.notify_match_squad_changes() IS 'Envia uma notificação para admins e presidentes quando a escalação de uma partida é alterada.';
COMMENT ON TRIGGER trg_notify_match_squad_changes ON public.match_squad IS 'Executa a função notify_match_squad_changes() após uma alteração na escalação de uma partida.';
