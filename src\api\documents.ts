import { supabase } from "@/integrations/supabase/client";
import { uploadPlayerDocument, registerPlayerDocument } from "./storage";
import { withPermission, withAuditLog } from "./middleware";
import { PLAYER_PERMISSIONS } from "@/constants/permissions";
import { isPlayerOwnedByUser } from "./players";

export type PlayerDocument = {
  id: number;
  club_id: number;
  player_id: string;
  document_type: string;
  file_url: string;
  status: "pending" | "verified" | "rejected";
  uploaded_at: string;
  verified_at: string | null;
  verified_by: string | null;
  rejection_reason?: string | null;
  verifier_name?: string; // Campo adicional para junção
};

export const DOCUMENT_TYPES = {
  ID_CARD: "id_card",
  CPF: "cpf",
  BIRTH_CERTIFICATE: "birth_certificate",
  VACCINATION_CARD: "vaccination_card",
  GUARDIAN_DOCUMENT: "guardian_document",
  SCHOOL_DECLARATION: "school_declaration",
  MEDICAL_CERTIFICATE: "medical_certificate",
  HOUSING_AUTHORIZATION: "housing_authorization",
  CONTRACT: "contract",
  OTHER: "other",
};

export const DOCUMENT_LABELS: Record<string, string> = {
  [DOCUMENT_TYPES.ID_CARD]: "RG",
  [DOCUMENT_TYPES.CPF]: "CPF",
  [DOCUMENT_TYPES.BIRTH_CERTIFICATE]: "Certidão de Nascimento",
  [DOCUMENT_TYPES.VACCINATION_CARD]: "Carteira de Vacinação",
  [DOCUMENT_TYPES.GUARDIAN_DOCUMENT]: "Documento do Responsável",
  [DOCUMENT_TYPES.SCHOOL_DECLARATION]: "Declaração Escolar",
  [DOCUMENT_TYPES.MEDICAL_CERTIFICATE]: "Atestado Médico",
  [DOCUMENT_TYPES.HOUSING_AUTHORIZATION]: "Autorização de Moradia",
  [DOCUMENT_TYPES.CONTRACT]: "Contrato",
  [DOCUMENT_TYPES.OTHER]: "Outros",
};

/**
 * Faz upload de um documento de jogador (versão sem verificação de permissões)
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param documentType Tipo do documento
 * @param file Arquivo do documento
 * @returns Documento registrado
 */
export async function uploadDocument(
  clubId: number,
  playerId: string,
  documentType: string,
  file: File
): Promise<PlayerDocument>;

/**
 * Faz upload de um documento de jogador (versão com verificação de permissões)
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @param documentType Tipo do documento
 * @param file Arquivo do documento
 * @param userId ID do usuário (para verificação de permissões)
 * @returns Documento registrado
 */
export async function uploadDocument(
  clubId: number,
  playerId: string,
  documentType: string,
  file: File,
  userId: string
): Promise<PlayerDocument>;

/**
 * Implementação da função uploadDocument
 */
export async function uploadDocument(
  clubId: number,
  playerId: string,
  documentType: string,
  file: File,
  userId?: string
): Promise<PlayerDocument> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    try {
      // Fazer upload do arquivo
      const fileUrl = await uploadPlayerDocument(clubId, playerId, documentType, file);

      // Registrar documento no banco de dados
      const document = await registerPlayerDocument(clubId, playerId, documentType, fileUrl);

      return document;
    } catch (error: any) {
      console.error("Erro ao fazer upload de documento:", error);
      throw new Error(error.message || "Erro ao fazer upload de documento");
    }
  }

  try {
    // Verificar se o jogador pertence ao usuário (para jogadores editando seu próprio perfil)
    const isOwnProfile = await isPlayerOwnedByUser(clubId, playerId, userId);

    // Se for o próprio jogador, usar permissão EDIT_OWN, caso contrário, usar EDIT
    const permissionRequired = isOwnProfile ? PLAYER_PERMISSIONS.EDIT_OWN : PLAYER_PERMISSIONS.EDIT;

    // Com userId, verificamos permissões e registramos no log de auditoria
    return withPermission(
      clubId,
      userId,
      permissionRequired,
      async () => {
        return withAuditLog(
          clubId,
          userId,
          "player.upload_document",
          { player_id: playerId, document_type: documentType, file_name: file.name, file_size: file.size, is_own_profile: isOwnProfile },
          async () => {
            try {
              // Fazer upload do arquivo
              const fileUrl = await uploadPlayerDocument(clubId, playerId, documentType, file);

              // Registrar documento no banco de dados
              const document = await registerPlayerDocument(clubId, playerId, documentType, fileUrl);

              return document;
            } catch (error: any) {
              console.error("Erro ao fazer upload de documento:", error);
              throw new Error(error.message || "Erro ao fazer upload de documento");
            }
          }
        );
      }
    );
  } catch (error: any) {
    console.error("Erro ao verificar permissões para upload de documento:", error);
    throw new Error(`Erro ao fazer upload do documento: ${error.message}`);
  }
}

/**
 * Obtém os documentos de um jogador
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @returns Lista de documentos
 */
export async function getPlayerDocuments(
  clubId: number,
  playerId: string
): Promise<PlayerDocument[]> {
  try {
    const { data, error } = await supabase
      .from("player_documents")
      .select(`
        *,
        verifier:verified_by (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .eq("player_id", playerId)
      .order("document_type");

    if (error) {
      throw new Error(`Erro ao obter documentos: ${error.message}`);
    }

    // Formatar os dados para incluir o nome do verificador
    return (data || []).map((item) => ({
      ...item,
      verifier_name: item.verifier?.name || null,
    }));
  } catch (error: any) {
    console.error("Erro ao obter documentos:", error);
    throw new Error(error.message || "Erro ao obter documentos");
  }
}

/**
 * Verifica um documento de jogador
 * @param documentId ID do documento
 * @param userId ID do usuário que está verificando
 * @param status Status da verificação ('verified' ou 'rejected')
 * @param rejectionReason Motivo da rejeição (apenas quando status='rejected')
 * @returns Documento atualizado
 */
export async function verifyDocument(
  documentId: number,
  userId: string,
  status: "verified" | "rejected",
  rejectionReason?: string
): Promise<PlayerDocument> {
  try {
    // Preparar os dados para atualização
    const updateData: any = {
      status,
      verified_at: new Date().toISOString(),
      verified_by: userId,
    };

    // Adicionar motivo da rejeição se for rejeitado
    if (status === "rejected" && rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    } else if (status === "verified") {
      // Limpar o motivo da rejeição se for aprovado
      updateData.rejection_reason = null;
    }

    const { data, error } = await supabase
      .from("player_documents")
      .update(updateData)
      .eq("id", documentId)
      .select(`
        *,
        verifier:verified_by (
          id,
          name
        )
      `)
      .single();

    if (error) {
      throw new Error(`Erro ao verificar documento: ${error.message}`);
    }

    // Formatar os dados para incluir o nome do verificador
    return {
      ...data,
      verifier_name: data.verifier?.name || null,
    };
  } catch (error: any) {
    console.error("Erro ao verificar documento:", error);
    throw new Error(error.message || "Erro ao verificar documento");
  }
}

/**
 * Exclui um documento de jogador
 * @param documentId ID do documento
 * @returns true se o documento foi excluído com sucesso
 */
export async function deleteDocument(documentId: number): Promise<boolean> {
  try {
    // Primeiro, obter o documento para saber o caminho do arquivo
    const { data: document, error: getError } = await supabase
      .from("player_documents")
      .select("*")
      .eq("id", documentId)
      .single();

    if (getError || !document) {
      throw new Error(`Erro ao obter documento: ${getError?.message || "Documento não encontrado"}`);
    }

    // Extrair o caminho do arquivo da URL
    const fileUrl = document.file_url;

    // Verificar se a URL é válida
    if (!fileUrl || !fileUrl.includes("/storage/v1/object/public/")) {
      console.error("URL inválida:", fileUrl);
      throw new Error("URL do documento inválida");
    }

    // Extrair o caminho do arquivo
    const path = fileUrl.split("/storage/v1/object/public/")[1];
    if (!path) {
      console.error("Caminho inválido:", fileUrl);
      throw new Error("Caminho do documento inválido");
    }

    // Extrair o bucket e o caminho do arquivo
    const [bucket, ...rest] = path.split("/");
    const filePath = rest.join("/");

    console.log("Excluindo arquivo:", {
      bucket,
      filePath,
      fullUrl: fileUrl
    });

    // Excluir o arquivo do storage
    const { error: storageError } = await supabase.storage
      .from("playerdocuments") // Nome do bucket no Supabase (sem underscore)
      .remove([filePath]);

    if (storageError) {
      console.error("Erro ao excluir arquivo do storage:", storageError);
      // Continuar mesmo se houver erro ao excluir o arquivo
    }

    // Excluir o registro do banco de dados
    const { error: dbError } = await supabase
      .from("player_documents")
      .delete()
      .eq("id", documentId);

    if (dbError) {
      throw new Error(`Erro ao excluir documento: ${dbError.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao excluir documento:", error);
    throw new Error(error.message || "Erro ao excluir documento");
  }
}

/**
 * Obtém os documentos pendentes de verificação
 * @param clubId ID do clube
 * @returns Lista de documentos pendentes
 */
export async function getPendingDocuments(clubId: number): Promise<PlayerDocument[]> {
  try {
    const { data, error } = await supabase
      .from("player_documents")
      .select(`
        *,
        players:player_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .eq("status", "pending")
      .order("uploaded_at");

    if (error) {
      throw new Error(`Erro ao obter documentos pendentes: ${error.message}`);
    }

    // Formatar os dados para incluir o nome do jogador
    return (data || []).map((item) => ({
      ...item,
      player_name: item.players?.name || "Jogador desconhecido",
    }));
  } catch (error: any) {
    console.error("Erro ao obter documentos pendentes:", error);
    throw new Error(error.message || "Erro ao obter documentos pendentes");
  }
}

/**
 * Verifica se um jogador tem todos os documentos obrigatórios
 * @param clubId ID do clube
 * @param playerId ID do jogador
 * @returns Objeto com status de cada documento obrigatório
 */
export async function checkRequiredDocuments(
  clubId: number,
  playerId: string
): Promise<Record<string, { status: "missing" | "pending" | "verified" | "rejected" }>> {
  try {
    // Obter todos os documentos do jogador
    const documents = await getPlayerDocuments(clubId, playerId);

    // Lista de documentos obrigatórios
    const requiredDocuments = [
      DOCUMENT_TYPES.ID_CARD,
      DOCUMENT_TYPES.CPF,
      DOCUMENT_TYPES.BIRTH_CERTIFICATE,
      DOCUMENT_TYPES.VACCINATION_CARD,
      DOCUMENT_TYPES.GUARDIAN_DOCUMENT,
      DOCUMENT_TYPES.SCHOOL_DECLARATION,
      DOCUMENT_TYPES.MEDICAL_CERTIFICATE,
      DOCUMENT_TYPES.HOUSING_AUTHORIZATION,
      DOCUMENT_TYPES.CONTRACT,
    ];

    // Verificar status de cada documento obrigatório
    const result: Record<string, { status: "missing" | "pending" | "verified" | "rejected" }> = {};

    for (const docType of requiredDocuments) {
      const doc = documents.find((d) => d.document_type === docType);

      if (!doc) {
        result[docType] = { status: "missing" };
      } else {
        result[docType] = { status: doc.status as "pending" | "verified" | "rejected" };
      }
    }

    return result;
  } catch (error: any) {
    console.error("Erro ao verificar documentos obrigatórios:", error);
    throw new Error(error.message || "Erro ao verificar documentos obrigatórios");
  }
}
