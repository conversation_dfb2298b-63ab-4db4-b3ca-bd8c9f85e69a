import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { createMasterPlan, AVAILABLE_MODULES, AVAILABLE_FEATURES, type CreatePlanData } from '@/api/masterPlans';
import { toast } from 'sonner';

const createPlanSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  price: z.number().min(0, 'Preço deve ser maior ou igual a 0'),
  billing_cycle: z.enum(['monthly', 'yearly']),
  max_users: z.number().nullable(),
  max_players: z.number().nullable(),
});

interface CreatePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CreatePlanModal: React.FC<CreatePlanModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedModules, setSelectedModules] = useState<Record<string, boolean>>({});
  const [selectedFeatures, setSelectedFeatures] = useState<Record<string, any>>({});

  const form = useForm<z.infer<typeof createPlanSchema>>({
    resolver: zodResolver(createPlanSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      billing_cycle: 'monthly',
      max_users: null,
      max_players: null,
    },
  });

  const handleModuleToggle = (moduleKey: string, enabled: boolean) => {
    setSelectedModules(prev => ({
      ...prev,
      [moduleKey]: enabled
    }));
  };

  const handleFeatureChange = (featureKey: string, value: any) => {
    setSelectedFeatures(prev => ({
      ...prev,
      [featureKey]: value
    }));
  };

  const onSubmit = async (values: z.infer<typeof createPlanSchema>) => {
    try {
      setLoading(true);

      const planData: CreatePlanData = {
        ...values,
        modules: selectedModules,
        features: selectedFeatures,
      };

      await createMasterPlan(planData);
      
      toast.success('Plano criado com sucesso!');
      onSuccess();
      onClose();
      
      // Reset form
      form.reset();
      setSelectedModules({});
      setSelectedFeatures({});
    } catch (error: any) {
      console.error('Erro ao criar plano:', error);
      toast.error(error.message || 'Erro ao criar plano');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Criar Novo Plano</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informações Básicas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome do Plano</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Plano Básico" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billing_cycle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ciclo de Cobrança</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o ciclo" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="monthly">Mensal</SelectItem>
                            <SelectItem value="yearly">Anual</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Descreva as características do plano..."
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preço (R$)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="max_users"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Máx. Usuários</FormLabel>
                        <FormControl>
                          <Input 
                            type="number"
                            placeholder="Ilimitado"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="max_players"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Máx. Atletas</FormLabel>
                        <FormControl>
                          <Input 
                            type="number"
                            placeholder="Ilimitado"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Módulos */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Módulos Inclusos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(AVAILABLE_MODULES).map(([key, label]) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Switch
                        id={`module-${key}`}
                        checked={selectedModules[key] || false}
                        onCheckedChange={(checked) => handleModuleToggle(key, checked)}
                      />
                      <label htmlFor={`module-${key}`} className="text-sm font-medium">
                        {label}
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Features Adicionais */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recursos Adicionais</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Limite de Armazenamento (GB)</label>
                    <Input
                      type="number"
                      placeholder="Ex: 10"
                      onChange={(e) => handleFeatureChange('storage_limit', parseInt(e.target.value) || null)}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Limite de Chamadas API/mês</label>
                    <Input
                      type="number"
                      placeholder="Ex: 10000"
                      onChange={(e) => handleFeatureChange('api_calls_limit', parseInt(e.target.value) || null)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="custom_branding"
                      onCheckedChange={(checked) => handleFeatureChange('custom_branding', checked)}
                    />
                    <label htmlFor="custom_branding" className="text-sm font-medium">
                      Marca Personalizada
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="priority_support"
                      onCheckedChange={(checked) => handleFeatureChange('priority_support', checked)}
                    />
                    <label htmlFor="priority_support" className="text-sm font-medium">
                      Suporte Prioritário
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="advanced_reports"
                      onCheckedChange={(checked) => handleFeatureChange('advanced_reports', checked)}
                    />
                    <label htmlFor="advanced_reports" className="text-sm font-medium">
                      Relatórios Avançados
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Criando...' : 'Criar Plano'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};