import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, Users, Briefcase, Shield, Edit2 } from "lucide-react";
import { getCollaborators, type Collaborator } from "@/api/api";
import { getOperationRoles, type OperationRole } from "@/api/gameOperations";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { MatchSquadMember } from "@/api/matchLineups";

interface FlexibleCollaboratorSelectorProps {
  clubId: number;
  onCollaboratorSelect: (collaborator: Collaborator, customRole: string, squadRole: 'technical_staff' | 'staff' | 'executive') => void;
  existingMembers: MatchSquadMember[];
}

// Funções pré-definidas organizadas por categoria
const PREDEFINED_ROLES = {
  technical_staff: [
    "Técnico Principal",
    "Auxiliar Técnico", 
    "Preparador Físico",
    "Preparador de Goleiros",
    "Analista de Desempenho",
    "Médico",
    "Fisioterapeuta",
    "Nutricionista",
    "Psicólogo Esportivo"
  ],
  staff: [
    "Roupeiro",
    "Massagista", 
    "Segurança",
    "Motorista",
    "Cozinheiro",
    "Auxiliar Geral",
    "Operador de Câmera",
    "Assessor de Imprensa"
  ],
  executive: [
    "Presidente",
    "Vice-Presidente",
    "Diretor de Futebol",
    "Diretor Executivo",
    "Diretor Financeiro",
    "Gerente de Futebol",
    "Coordenador",
    "Conselheiro"
  ]
};

export function FlexibleCollaboratorSelector({
  clubId,
  onCollaboratorSelect,
  existingMembers
}: FlexibleCollaboratorSelectorProps) {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [operationRoles, setOperationRoles] = useState<OperationRole[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("");
  
  // Dialog state
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [selectedCollaborator, setSelectedCollaborator] = useState<Collaborator | null>(null);
  const [selectedSquadRole, setSelectedSquadRole] = useState<'technical_staff' | 'staff' | 'executive'>('technical_staff');
  const [customRole, setCustomRole] = useState("");

  useEffect(() => {
    loadData();
  }, [clubId]);

  const loadData = async () => {
    if (!clubId) return;
    
    setLoading(true);
    try {
      const [collaboratorsData, rolesData] = await Promise.all([
        getCollaborators(clubId),
        getOperationRoles(clubId).catch(() => []) // Não falhar se não existir
      ]);
      
      // Filtrar apenas colaboradores ativos
      const activeCollaborators = collaboratorsData.filter(c => 
        c.status !== 'inativo' && c.status !== 'demitido'
      );
      setCollaborators(activeCollaborators);
      setOperationRoles(rolesData);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
    } finally {
      setLoading(false);
    }
  };

  // Filtrar colaboradores baseado na busca e filtros
  const filteredCollaborators = collaborators.filter(collaborator => {
    const matchesSearch = collaborator.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         collaborator.role.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = !roleFilter || collaborator.role_type === roleFilter;
    
    // Verificar se já está no squad
    const alreadyInSquad = existingMembers.some(member => {
      if (member.collaborator_id && collaborator.id) {
        return member.collaborator_id === collaborator.id;
      }
      if (member.user_id && collaborator.user_id) {
        return member.user_id === collaborator.user_id;
      }
      return false;
    });
    
    return matchesSearch && matchesRole && !alreadyInSquad;
  });

  // Obter tipos de função únicos
  const uniqueRoleTypes = Array.from(new Set(collaborators.map(c => c.role_type).filter(Boolean))).sort();

  const handleCollaboratorClick = (collaborator: Collaborator) => {
    setSelectedCollaborator(collaborator);
    setCustomRole(collaborator.role); // Usar a função atual como padrão
    
    // Sugerir categoria baseada na função atual
    const role = collaborator.role?.toLowerCase() || '';
    const roleType = collaborator.role_type?.toLowerCase() || '';
    
    if (roleType.includes('técnic') || roleType.includes('tecnic') || roleType.includes('esport') ||
        role.includes('técnic') || role.includes('tecnic') || role.includes('treinad') ||
        role.includes('prepar') || role.includes('fisio') || role.includes('médic') ||
        role.includes('medic') || role.includes('nutri') || role.includes('massag')) {
      setSelectedSquadRole('technical_staff');
    } else if (roleType.includes('diret') || roleType.includes('execut') || roleType.includes('presid') ||
               role.includes('diret') || role.includes('presid') || role.includes('vice') ||
               role.includes('execut') || role.includes('conselho') || role.includes('administr')) {
      setSelectedSquadRole('executive');
    } else {
      setSelectedSquadRole('staff');
    }
    
    setShowRoleDialog(true);
  };

  const handleConfirmSelection = () => {
    if (selectedCollaborator && customRole.trim()) {
      onCollaboratorSelect(selectedCollaborator, customRole.trim(), selectedSquadRole);
      setShowRoleDialog(false);
      setSelectedCollaborator(null);
      setCustomRole("");
    }
  };

  const getRoleColor = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return 'bg-blue-100 text-blue-800';
      case 'executive':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  const getRoleIcon = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return <Briefcase className="h-4 w-4" />;
      case 'executive':
        return <Shield className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getRoleLabel = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return 'Comissão Técnica';
      case 'executive':
        return 'Diretoria';
      default:
        return 'Staff Operacional';
    }
  };

  return (
    <>
      <div className="space-y-4">
        {/* Filtros */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nome ou função..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {uniqueRoleTypes.length > 1 && (
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="border rounded-md px-3 py-2 min-w-[150px]"
            >
              <option value="">Todos os tipos</option>
              {uniqueRoleTypes.map(roleType => (
                <option key={roleType} value={roleType}>
                  {roleType}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Lista de colaboradores */}
        {loading ? (
          <div className="text-center py-8">Carregando colaboradores...</div>
        ) : (
          <div className="grid grid-cols-1 gap-3 max-h-[400px] overflow-y-auto">
            {filteredCollaborators.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchTerm || roleFilter ? 
                  "Nenhum colaborador encontrado com os filtros aplicados" : 
                  existingMembers.length > 0 ?
                    "Todos os colaboradores já foram adicionados" :
                    "Nenhum colaborador disponível"
                }
              </div>
            ) : (
              filteredCollaborators.map(collaborator => (
                <div
                  key={collaborator.id}
                  className="border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md hover:bg-blue-50 hover:border-blue-300"
                  onClick={() => handleCollaboratorClick(collaborator)}
                >
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={collaborator.image || ""} />
                      <AvatarFallback className="bg-blue-600 text-white font-bold">
                        {collaborator.full_name.split(' ').map(n => n.charAt(0)).join('').substring(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{collaborator.full_name}</div>
                      <div className="text-sm text-gray-500 truncate">{collaborator.role}</div>
                      
                      {collaborator.role_type && (
                        <Badge variant="outline" className="text-xs mt-1">
                          {collaborator.role_type}
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-1">
                      <Edit2 className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Estatísticas */}
        <div className="text-sm text-gray-500 border-t pt-3">
          Mostrando {filteredCollaborators.length} de {collaborators.length} colaboradores disponíveis
        </div>
      </div>

      {/* Dialog para definir função */}
      <Dialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Definir Função na Partida</DialogTitle>
          </DialogHeader>
          
          {selectedCollaborator && (
            <div className="space-y-4">
              {/* Informações do colaborador */}
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Avatar>
                  <AvatarImage src={selectedCollaborator.image || ""} />
                  <AvatarFallback>
                    {selectedCollaborator.full_name.split(' ').map(n => n.charAt(0)).join('').substring(0, 2)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{selectedCollaborator.full_name}</div>
                  <div className="text-sm text-gray-500">{selectedCollaborator.role}</div>
                </div>
              </div>

              {/* Categoria do squad */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Categoria no Squad:
                </label>
                <div className="grid grid-cols-1 gap-2">
                  {(['technical_staff', 'staff', 'executive'] as const).map((role) => (
                    <label
                      key={role}
                      className={`flex items-center gap-3 p-3 border rounded-md cursor-pointer transition-colors ${
                        selectedSquadRole === role
                          ? getRoleColor(role) + ' border-current'
                          : 'bg-white hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="radio"
                        name="squad-role"
                        value={role}
                        checked={selectedSquadRole === role}
                        onChange={(e) => setSelectedSquadRole(e.target.value as any)}
                        className="h-4 w-4"
                      />
                      {getRoleIcon(role)}
                      <span className="font-medium">{getRoleLabel(role)}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Função específica */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Função Específica na Partida:
                </label>
                <div className="space-y-2">
                  <Input
                    value={customRole}
                    onChange={(e) => setCustomRole(e.target.value)}
                    placeholder="Digite a função específica..."
                  />
                  
                  {/* Sugestões baseadas na categoria */}
                  <div className="flex flex-wrap gap-1">
                    {PREDEFINED_ROLES[selectedSquadRole].map((role) => (
                      <Button
                        key={role}
                        variant="outline"
                        size="sm"
                        className="text-xs h-7"
                        onClick={() => setCustomRole(role)}
                      >
                        {role}
                      </Button>
                    ))}
                  </div>

                  {/* Funções do sistema de operação de jogos */}
                  {operationRoles.length > 0 && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">Funções cadastradas:</div>
                      <div className="flex flex-wrap gap-1">
                        {operationRoles.map((role) => (
                          <Button
                            key={role.id}
                            variant="ghost"
                            size="sm"
                            className="text-xs h-7"
                            onClick={() => setCustomRole(role.name)}
                          >
                            {role.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRoleDialog(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleConfirmSelection}
              disabled={!customRole.trim()}
            >
              Adicionar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}