import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from "../integrations/supabase/client";
import { ensureAuthenticated } from "../integrations/supabase/ensureAuth";
import { differenceInDays, parseISO } from 'date-fns';

export interface ClubAccessInfo {
  hasAccess: boolean;
  subscriptionStatus: 'active' | 'suspended' | 'cancelled' | 'trial';
  paymentStatus: 'current' | 'overdue' | 'cancelled';
  planName?: string;
  planModules?: Record<string, boolean>;
  planFeatures?: Record<string, any>;
  daysUntilSuspension?: number;
  restrictedModules?: string[];
  warningMessage?: string;
  errorMessage?: string;
  isTrialExpired?: boolean;
  trialDaysRemaining?: number;
}

export interface ClubLimits {
  maxUsers: number | null;
  maxPlayers: number | null;
  currentUsers: number;
  currentPlayers: number;
  storageLimit: number | null; // GB
  currentStorage: number; // GB
  apiCallsLimit: number | null;
  currentApiCalls: number;
}

// Hook principal para verificar acesso do clube
export const useClubAccess = (clubId: number) => {
  const [accessInfo, setAccessInfo] = useState<ClubAccessInfo>({
    hasAccess: true,
    subscriptionStatus: 'active',
    paymentStatus: 'current'
  });
  const [loading, setLoading] = useState(true);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkClubAccess = useCallback(async () => {
    if (!clubId) {
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: 'cancelled',
        paymentStatus: 'cancelled',
        errorMessage: 'ID do clube não fornecido'
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      await ensureAuthenticated();
      // Buscar informações do clube e plano
      const { data: club, error: clubError } = await supabase
        .from('club_info')
        .select(`
          *,
          master_plans:master_plan_id (
            name,
            modules,
            features,
            max_users,
            max_players
          )
        `)
        .eq('id', clubId)
        .single();

      if (clubError || !club) {
        throw new Error('Clube não encontrado');
      }

      // Verificar status da assinatura
      if (club.subscription_status === 'cancelled') {
        setAccessInfo({
          hasAccess: false,
          subscriptionStatus: 'cancelled',
          paymentStatus: 'cancelled',
          errorMessage: 'Assinatura cancelada. Entre em contato com o suporte.'
        });
        return;
      }

      // Verificar se é trial e se expirou
      if (club.is_trial && club.trial_end_date) {
        const trialEndDate = parseISO(club.trial_end_date);
        const now = new Date();
        const daysRemaining = differenceInDays(trialEndDate, now);

        if (daysRemaining < 0) {
          setAccessInfo({
            hasAccess: false,
            subscriptionStatus: 'trial',
            paymentStatus: 'overdue',
            isTrialExpired: true,
            errorMessage: 'Período de teste expirado. Assine um plano para continuar.'
          });
          return;
        }

        if (daysRemaining <= 3) {
          setAccessInfo(prev => ({
            ...prev,
            trialDaysRemaining: daysRemaining,
            warningMessage: `Seu período de teste expira em ${daysRemaining} dia(s). Assine um plano para continuar.`
          }));
        }
      }

      // Verificar status de pagamento
      if (club.payment_status === 'overdue') {
        // Buscar último pagamento em atraso
        const { data: overduePayment } = await supabase
          .from('master_payments')
          .select('due_date, amount')
          .eq('club_id', clubId)
          .eq('status', 'overdue')
          .order('due_date', { ascending: false })
          .limit(1)
          .single();

        if (overduePayment) {
          const daysSinceOverdue = differenceInDays(new Date(), parseISO(overduePayment.due_date));
          const gracePeriod = 7; // 7 dias de tolerância

          if (daysSinceOverdue > gracePeriod) {
            setAccessInfo({
              hasAccess: false,
              subscriptionStatus: 'suspended',
              paymentStatus: 'overdue',
              errorMessage: `Acesso suspenso por falta de pagamento. Regularize sua situação para reativar o acesso.`
            });
            return;
          } else {
            const daysUntilSuspension = gracePeriod - daysSinceOverdue;
            setAccessInfo(prev => ({
              ...prev,
              daysUntilSuspension,
              warningMessage: `Pagamento em atraso. Acesso será suspenso em ${daysUntilSuspension} dia(s).`
            }));
          }
        }
      }

      // Verificar se foi suspenso manualmente
      if (club.subscription_status === 'suspended') {
        setAccessInfo({
          hasAccess: false,
          subscriptionStatus: 'suspended',
          paymentStatus: club.payment_status,
          errorMessage: 'Acesso suspenso. Entre em contato com o suporte.'
        });
        return;
      }

      // Se chegou até aqui, o clube tem acesso
      setAccessInfo({
        hasAccess: true,
        subscriptionStatus: club.subscription_status,
        paymentStatus: club.payment_status,
        planName: club.master_plans?.name,
        planModules: club.master_plans?.modules || {},
        planFeatures: club.master_plans?.features || {},
        ...(accessInfo.warningMessage && { warningMessage: accessInfo.warningMessage }),
        ...(accessInfo.trialDaysRemaining && { trialDaysRemaining: accessInfo.trialDaysRemaining })
      });

      setLastCheck(new Date());
    } catch (error: any) {
      console.error('Erro ao verificar acesso do clube:', error);
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: 'cancelled',
        paymentStatus: 'cancelled',
        errorMessage: 'Erro ao verificar acesso. Tente novamente.'
      });
    } finally {
      setLoading(false);
    }
  }, [clubId, accessInfo.warningMessage, accessInfo.trialDaysRemaining]);

  useEffect(() => {
    checkClubAccess();
  }, [checkClubAccess]);

  // Recheck a cada 5 minutos
  useEffect(() => {
    const interval = setInterval(() => {
      if (lastCheck && differenceInDays(new Date(), lastCheck) < 1) {
        checkClubAccess();
      }
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, [checkClubAccess, lastCheck]);

  return { accessInfo, loading, checkClubAccess };
};

// Hook para verificar limites de uso
export const useClubLimits = (clubId: number) => {
  const [limits, setLimits] = useState<ClubLimits>({
    maxUsers: null,
    maxPlayers: null,
    currentUsers: 0,
    currentPlayers: 0,
    storageLimit: null,
    currentStorage: 0,
    apiCallsLimit: null,
    currentApiCalls: 0
  });
  const [loading, setLoading] = useState(true);

  const checkLimits = useCallback(async () => {
    if (!clubId) return;

    try {
      setLoading(true);

      await ensureAuthenticated();
      // Buscar limites do plano
      const { data: club, error: clubError } = await supabase
        .from('club_info')
        .select(`
          master_plans:master_plan_id (
            max_users,
            max_players,
            features
          )
        `)
        .eq('id', clubId)
        .single();

      if (clubError || !club) {
        throw new Error('Clube não encontrado');
      }

      // Contar usuários atuais
      const { count: currentUsers } = await supabase
        .from('club_members')
        .select('*', { count: 'exact', head: true })
        .eq('club_id', clubId)
        .eq('status', 'ativo');

      // Contar jogadores atuais
      const { count: currentPlayers } = await supabase
        .from('players')
        .select('*', { count: 'exact', head: true })
        .eq('club_id', clubId)
        .neq('status', 'inativo');

      // Buscar uso de storage (simulado - em produção seria calculado)
      const currentStorage = 0; // TODO: Implementar cálculo real

      // Buscar uso de API (simulado - em produção seria calculado)
      const currentApiCalls = 0; // TODO: Implementar cálculo real

      const planFeatures = club.master_plans?.features || {};

      setLimits({
        maxUsers: club.master_plans?.max_users || null,
        maxPlayers: club.master_plans?.max_players || null,
        currentUsers: currentUsers || 0,
        currentPlayers: currentPlayers || 0,
        storageLimit: planFeatures.storage_limit || null,
        currentStorage,
        apiCallsLimit: planFeatures.api_calls_limit || null,
        currentApiCalls
      });
    } catch (error) {
      console.error('Erro ao verificar limites:', error);
    } finally {
      setLoading(false);
    }
  }, [clubId]);

  useEffect(() => {
    checkLimits();
  }, [checkLimits]);

  return { limits, loading, checkLimits };
};

// Hook para verificar acesso a módulos específicos
export const useModuleAccess = (clubId: number, moduleKey: string) => {
  const { accessInfo } = useClubAccess(clubId);
  
  const hasModuleAccess = () => {
    if (!accessInfo.hasAccess) return false;
    if (!accessInfo.planModules) return true; // Se não há restrições, permitir
    
    return accessInfo.planModules[moduleKey] === true;
  };

  return {
    hasAccess: hasModuleAccess(),
    planName: accessInfo.planName,
    subscriptionStatus: accessInfo.subscriptionStatus
  };
};

// Função para verificar se um limite foi atingido
export const isLimitReached = (current: number, max: number | null): boolean => {
  if (max === null || max === -1) return false; // Ilimitado
  return current >= max;
};

// Função para calcular porcentagem de uso
export const getUsagePercentage = (current: number, max: number | null): number => {
  if (max === null || max === -1) return 0; // Ilimitado
  if (max === 0) return 100;
  return Math.min((current / max) * 100, 100);
};

// Componente de proteção para módulos
export const ModuleGuard: React.FC<{
  clubId: number;
  moduleKey: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ clubId, moduleKey, children, fallback }) => {
  const { hasAccess } = useModuleAccess(clubId, moduleKey);

  if (!hasAccess) {
    return (
      <div className="p-8 text-center">
        {fallback || (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Módulo não disponível
            </h3>
            <p className="text-gray-600">
              Este módulo não está incluído no seu plano atual.
            </p>
          </div>
        )}
      </div>
    );
  }

  return <>{children}</>;
};

// Componente de proteção para limites
export const LimitGuard: React.FC<{
  clubId: number;
  limitType: 'users' | 'players';
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ clubId, limitType, children, fallback }) => {
  const { limits } = useClubLimits(clubId);
  
  const isReached = limitType === 'users' 
    ? isLimitReached(limits.currentUsers, limits.maxUsers)
    : isLimitReached(limits.currentPlayers, limits.maxPlayers);

  if (isReached) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        {fallback || (
          <div>
            <h4 className="font-semibold text-yellow-800">
              Limite atingido
            </h4>
            <p className="text-yellow-700 text-sm">
              Você atingiu o limite de {limitType === 'users' ? 'usuários' : 'jogadores'} do seu plano.
            </p>
          </div>
        )}
      </div>
    );
  }

  return <>{children}</>;
};

// Componente de aviso de acesso
export const AccessWarning: React.FC<{ clubId: number }> = ({ clubId }) => {
  const { accessInfo } = useClubAccess(clubId);

  if (!accessInfo.warningMessage) return null;

  return (
    <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            {accessInfo.warningMessage}
          </p>
        </div>
      </div>
    </div>
  );
};

// Componente de erro de acesso
export const AccessError: React.FC<{ clubId: number }> = ({ clubId }) => {
  const { accessInfo } = useClubAccess(clubId);

  if (accessInfo.hasAccess || !accessInfo.errorMessage) return null;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div className="mt-4 text-center">
          <h3 className="text-lg font-medium text-gray-900">
            Acesso Restrito
          </h3>
          <p className="mt-2 text-sm text-gray-600">
            {accessInfo.errorMessage}
          </p>
          <div className="mt-6">
            <button
              onClick={() => window.location.href = '/contato'}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Entrar em Contato
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};