# Sistema de Suspensão por Competição/Categoria

## Resumo das Mudanças Implementadas

### 1. Estrutura do Banco de Dados

**Nova Tabela: `player_suspensions`**
- `id`: UUID único da suspensão
- `club_id`: ID do clube
- `player_id`: ID do jogador
- `competition_id`: ID da competição (NULL = suspensão geral)
- `category_id`: ID da categoria (NULL = suspensão geral)
- `reason`: Motivo da suspensão
- `matches_remaining`: Jogos restantes de suspensão
- `original_matches`: Número original de jogos
- `suspended_at`: Data da suspensão
- `created_at` / `updated_at`: Timestamps

**Características:**
- Suporte a suspensões específicas por competição
- Suporte a suspensões específicas por categoria
- Suspensões gerais (sem competição/categoria) se aplicam a tudo
- Índices otimizados para consultas rápidas
- Constraint de unicidade por contexto

### 2. API Atualizada (`cardSuspensions.ts`)

**Funções Principais:**

#### `getPlayerCardHistory(clubId, playerId, competitionId?, categoryId?, limit?)`
- Busca histórico de cartões filtrado por competição/categoria
- Usado para verificar acúmulo de cartões amarelos

#### `checkPlayerSuspension(clubId, playerId, cardType, matchId, competitionId?, categoryId?)`
- Verifica se um cartão deve gerar suspensão
- Considera apenas cartões da mesma competição/categoria

#### `applySuspension(clubId, playerId, reason, matches, competitionId?, categoryId?)`
- Aplica suspensão específica para um contexto
- Soma jogos se já existe suspensão ativa no mesmo contexto

#### `processCardSuspension(clubId, playerId, cardType, matchId, competitionId?, categoryId?)`
- Processa suspensão automática após cartão
- Integrada com o sistema de eventos de partida

#### `processSuspensionAfterMatch(clubId, competitionId?, categoryId?)`
- Reduz contadores de suspensão após partida
- Remove suspensões quando contador chega a zero

#### `isPlayerSuspendedForMatch(clubId, playerId, competitionId?, categoryId?)`
- Verifica se jogador está suspenso para contexto específico
- Considera suspensões gerais e específicas

#### `getSuspendedPlayers(clubId, competitionId?, categoryId?)`
- Lista jogadores suspensos para um contexto
- Filtra por competição/categoria se especificado

### 3. Componentes Atualizados

#### `PlayerSelector`
- Recebe `competitionId` e `categoryId` como props
- Verifica suspensões específicas do contexto
- Mostra indicadores visuais apropriados

#### `PlayerSelectWithSuspension`
- Suporte a competição/categoria
- Desabilita jogadores suspensos no contexto específico

#### `SuspensionIndicator`
- Mantém funcionalidade visual existente
- Funciona com o novo sistema de dados

### 4. Integração com Partidas

#### `matchEvents.ts`
- `createCardEvent()` agora busca competição/categoria da partida
- Passa contexto para `processCardSuspension()`

#### `FinalizarPartidaDialog.tsx`
- Chama `processSuspensionAfterMatch()` ao finalizar partida
- Processa suspensões específicas do contexto da partida

### 5. Regras de Negócio

#### **Hierarquia de Suspensões:**
1. **Suspensão Geral**: Sem competição/categoria → Aplica-se a tudo
2. **Suspensão por Competição**: Específica da competição
3. **Suspensão por Categoria**: Específica da categoria

#### **Verificação de Suspensão:**
- Jogador suspenso se tem suspensão ativa no contexto da partida
- Suspensões gerais sempre se aplicam
- Suspensões específicas só se aplicam ao contexto correspondente

#### **Processamento de Cartões:**
- Cartões são analisados apenas dentro do mesmo contexto
- 3 amarelos em jogos diferentes da mesma competição = suspensão
- Cartão vermelho = suspensão imediata no contexto

### 6. Migração de Dados

**Campos Removidos da Tabela `players`:**
- `suspension_reason`
- `suspension_matches_remaining` 
- `suspended_at`

**Nova Estrutura:**
- Todas as suspensões agora na tabela `player_suspensions`
- Suporte completo a múltiplas suspensões simultâneas
- Controle granular por contexto

### 7. Como Testar

#### **Teste 1: Suspensão por Competição**
1. Crie partidas em competições diferentes
2. Aplique cartões em uma competição
3. Verifique que suspensão só afeta essa competição

#### **Teste 2: Suspensão por Categoria**
1. Crie partidas em categorias diferentes
2. Aplique cartões em uma categoria
3. Verifique que suspensão só afeta essa categoria

#### **Teste 3: Suspensão Geral**
1. Aplique suspensão sem especificar competição/categoria
2. Verifique que afeta todas as partidas

#### **Teste 4: Múltiplas Suspensões**
1. Suspenda jogador em competições diferentes
2. Verifique que ambas as suspensões são respeitadas
3. Teste redução independente de contadores

### 8. Benefícios da Implementação

- ✅ **Isolamento**: Suspensões específicas por contexto
- ✅ **Flexibilidade**: Suporte a suspensões gerais e específicas
- ✅ **Performance**: Índices otimizados para consultas
- ✅ **Escalabilidade**: Suporte a múltiplas suspensões simultâneas
- ✅ **Integridade**: Constraints e validações adequadas
- ✅ **Compatibilidade**: Interface mantém funcionalidade existente

### 9. Próximos Passos

1. **Executar SQL**: Aplicar `sql/add-suspension-fields-to-players.sql`
2. **Testar Sistema**: Validar todas as funcionalidades
3. **Migrar Dados**: Se houver suspensões existentes
4. **Documentar**: Atualizar documentação do usuário
5. **Treinar**: Orientar usuários sobre novo comportamento

## Conclusão

O sistema agora suporta completamente suspensões por competição/categoria, mantendo compatibilidade com suspensões gerais e oferecendo controle granular sobre o contexto das suspensões.