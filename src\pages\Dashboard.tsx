import { Calendar, Trophy, Users, Activity, Sparkles, Star, ArrowUp, Flame, Gift } from "lucide-react";
import { StatCard } from "@/components/dashboard/StatCard";
import { TeamPerformanceChart } from "@/components/dashboard/TeamPerformanceChart";
import { UpcomingEvents } from "@/components/dashboard/UpcomingEvents";
import { SquadStatus } from "@/components/dashboard/SquadStatus";
import { PastGamesWidget } from "@/components/dashboard/PastGamesWidget";
import { ExpiringContractsWidget } from "@/components/dashboard/ExpiringContractsWidget";
import { EvaluationStatusWidget } from "@/components/dashboard/EvaluationStatusWidget";
import { ExpiredContractsWidget } from "@/components/dashboard/ExpiredContractsWidget";
import { PaymentStatusCard } from "@/components/dashboard/PaymentStatusCard";
import { FitnessModal } from "@/components/modals/FitnessModal";
import { BirthdaysModal } from "@/components/modals/BirthdaysModal";
import { useEffect, useState, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useSeasonStore } from "@/store/useSeasonStore";
import { getUpcomingMatches, getMatchHistory } from "@/api";
import type { UpcomingMatch, MatchHistory } from "@/api";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useNotificationsStore } from "@/store/useNotificationsStore";
import { getAgendaEvents } from "@/api/api";
import { getPlayerUpcomingTrainings, getUpcomingTrainingsBySeasonAndCategory } from "@/api/trainings";
import type { Training } from "@/api/trainings";
import { supabase } from "@/integrations/supabase/client";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { getPlayerCategories } from "@/api";
import { usePermission } from "@/hooks/usePermission";
import { useDashboardStats } from "@/hooks/useDashboardStats";
import { useMonthlyBirthdays } from "@/hooks/useMonthlyBirthdays";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { formatDate } from "@/lib/utils";


export default function Dashboard() {
  const [upcomingMatches, setUpcomingMatches] = useState<UpcomingMatch[]>([]);
  const [matchHistory, setMatchHistory] = useState<MatchHistory[]>([]);
  const [upcomingTrainings, setUpcomingTrainings] = useState<Training[]>([]);
  const { players, loading: loadingPlayers, error, fetchPlayers } = usePlayersStore();
  const [loading, setLoading] = useState(true);
  const [dashboardError, setDashboardError] = useState<string | null>(null);
  const [isPlayerAccount, setIsPlayerAccount] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined);
  const [selectedCategoryName, setSelectedCategoryName] = useState<string | null>(null);
  const [fitnessModalOpen, setFitnessModalOpen] = useState(false);
  const [birthdaysModalOpen, setBirthdaysModalOpen] = useState(false);

  const clubId = useCurrentClubId();
  const { seasons, activeSeason, fetchSeasons, setActiveSeason } = useSeasonStore();
  const { user } = useUser();
  const { createNotification } = useNotificationsStore();
  const { categories, fetchCategories } = useCategoriesStore();
  const { role, isLoaded } = usePermission();
  const { clubInfo } = useClubInfoStore();
  const contractWarningDays = clubInfo?.contract_warning_days ?? 60;

  // Use dashboard stats hook
  const { stats: dashboardStats, loading: loadingStats } = useDashboardStats(clubId, selectedCategoryId);
  const { birthdays } = useMonthlyBirthdays(clubId, selectedCategoryId);

  // Verificar se o usuário é um jogador
  useEffect(() => {
    if (!user?.id || !clubId) return;

    async function checkIfPlayerAccount() {
      try {
        // Buscar jogador pelo user_id
        const { data: playerData } = await supabase
          .from("players")
          .select("id")
          .eq("user_id", user.id)
          .eq("club_id", clubId)
          .single();

        if (playerData) {
          setIsPlayerAccount(true);
          setPlayerId(playerData.id);
        } else {
          setIsPlayerAccount(false);
          setPlayerId(null);
        }
      } catch (error) {
        console.error("Erro ao verificar conta de jogador:", error);
        setIsPlayerAccount(false);
      }
    }

    checkIfPlayerAccount();
  }, [user?.id, clubId]);

  // Carregar temporadas e categorias para contas de jogador
  useEffect(() => {
    if (!clubId || !isLoaded || role !== "player") return;

    if (seasons.length === 0) {
      fetchSeasons(clubId);
    }

    if (categories.length === 0) {
      fetchCategories(clubId);
    }
  }, [clubId, isLoaded, role, seasons.length, categories.length, fetchSeasons, fetchCategories]);

  // Definir temporada ativa padrão para jogadores
  useEffect(() => {
    if (role === "player" && seasons.length > 0 && !activeSeason) {
      const sorted = [...seasons].sort((a, b) => {
        const aDate = new Date(a.start_date || '').getTime();
        const bDate = new Date(b.start_date || '').getTime();
        return bDate - aDate;
      });
      setActiveSeason(sorted[0]);
    }
  }, [role, seasons, activeSeason, setActiveSeason]);

  // Definir categoria padrão do jogador
  useEffect(() => {
    if (!clubId || role !== "player" || !playerId) return;

    async function loadPlayerCategory() {
      try {
        const playerCategories = await getPlayerCategories(clubId, playerId);
        if (playerCategories && playerCategories.length > 0) {
          const cat = playerCategories[0];
          setSelectedCategoryId(cat.id);
          setSelectedCategoryName(cat.name);
          localStorage.setItem("selectedCategoryId", cat.id.toString());
        }
      } catch (error) {
        console.error("Erro ao buscar categoria do jogador:", error);
      }
    }

    loadPlayerCategory();
  }, [clubId, role, playerId]);

  // Verificar categoria selecionada e escutar mudanças
  useEffect(() => {
    const updateSelectedCategory = () => {
      const storedCategoryId = localStorage.getItem('selectedCategoryId');
      if (storedCategoryId) {
        const categoryId = parseInt(storedCategoryId);
        setSelectedCategoryId(categoryId);

        // Buscar nome da categoria
        const category = categories.find(c => c.id === categoryId);
        if (category) {
          setSelectedCategoryName(category.name);
        } else {
          setSelectedCategoryName(null);
        }
      } else {
        setSelectedCategoryId(undefined);
        setSelectedCategoryName(null);
      }
    };

    // Atualizar categoria inicialmente
    updateSelectedCategory();

    // Escutar mudanças no localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedCategoryId") {
        updateSelectedCategory();
      }
    };

    // Escutar o evento personalizado de mudança de categoria
    const handleCategoryChanged = (e: CustomEvent) => {
      updateSelectedCategory();
    };

    // Criar um observador para monitorar mudanças no localStorage em tempo real
    const checkForChanges = setInterval(() => {
      const currentCategoryId = localStorage.getItem("selectedCategoryId");
      const currentSelectedId = selectedCategoryId ? selectedCategoryId.toString() : null;

      if (currentCategoryId !== currentSelectedId) {
        updateSelectedCategory();
      }
    }, 500);

    // Adicionar event listeners
    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("categoryChanged", handleCategoryChanged as EventListener);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("categoryChanged", handleCategoryChanged as EventListener);
      clearInterval(checkForChanges);
    };
  }, [categories, selectedCategoryId]);

  // Buscar treinos (para jogadores ou colaboradores)
  useEffect(() => {
    if (!clubId) return;

    async function fetchTrainings() {
      try {
        if (isPlayerAccount && playerId) {
          // Para jogadores: buscar apenas treinos da categoria do jogador
          const trainings = await getPlayerUpcomingTrainings(clubId, playerId);
          setUpcomingTrainings(trainings);
        } else if (activeSeason) {
          // Para colaboradores: buscar treinos da temporada e categoria selecionada
          const trainings = await getUpcomingTrainingsBySeasonAndCategory(
            clubId,
            activeSeason.id,
            selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined
          );
          setUpcomingTrainings(trainings);
        }
      } catch (error) {
        console.error("Erro ao buscar treinos:", error);
      }
    }

    fetchTrainings();
  }, [isPlayerAccount, playerId, clubId, activeSeason, selectedCategoryId]);

  useEffect(() => {
    let cancelled = false;
    async function fetchDashboardData() {
      if (!clubId || !activeSeason) {
        setDashboardError("Nenhum clube ou temporada selecionado.");
        setLoading(false);
        return;
      }
      setLoading(true);
      setDashboardError(null);
      try {
        const [upcomingData, historyData] = await Promise.all([
          getUpcomingMatches(clubId, activeSeason.id, selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined),
          getMatchHistory(clubId, activeSeason.id, selectedCategoryId && selectedCategoryId > 0 ? selectedCategoryId : undefined)
        ]);
        if (!cancelled) {
          setUpcomingMatches(upcomingData);
          setMatchHistory(historyData);
        }
        await fetchPlayers(clubId);

        // --- NOTIFICAÇÕES DE EVENTOS DO DIA (JOGO/TREINO) ---
        if (user?.id) {
          // Obter data de hoje no formato YYYY-MM-DD
          const today = new Date().toISOString().slice(0, 10);
          // Buscar notificações já criadas para o usuário hoje
          const userNotifications = await import("@/api/api").then(api => api.getUserNotifications(user.id));
          const todayNotifications = userNotifications.filter(n => n.created_at.slice(0, 10) === today);

          // Buscar jogos futuros e treinos
          const [upcomingMatchesToday, trainingsToday] = [
            upcomingData.filter(match => match.date.slice(0, 10) === today),
            (await import("@/api/api")).getTrainings ? (await (await import("@/api/api")).getTrainings(clubId)).filter(training => training.date.slice(0, 10) === today && training.status !== "concluído") : []
          ];

          // Notificação de jogo do dia
          if (upcomingMatchesToday.length > 0) {
            const alreadyNotified = todayNotifications.some(n => n.title === "Jogo hoje" && n.created_at.slice(0, 10) === today);
            if (!alreadyNotified) {
              await createNotification({
                user_id: user.id,
                title: "Jogo hoje",
                description: `Você tem um jogo agendado para hoje pelo clube!`,
              });
            }
          }

          // Notificação de treino do dia
          if (trainingsToday.length > 0) {
            const alreadyNotified = todayNotifications.some(n => n.title === "Treino hoje" && n.created_at.slice(0, 10) === today);
            if (!alreadyNotified) {
              await createNotification({
                user_id: user.id,
                title: "Treino hoje",
                description: `Você tem um treino agendado para hoje pelo clube!`,
              });
            }
          }
        }
        // --- FIM NOTIFICAÇÕES DE EVENTOS DO DIA ---

      } catch (err: any) {
        if (!cancelled) setDashboardError(err?.message || "Erro ao carregar dados do dashboard.");
      } finally {
        if (!cancelled) setLoading(false);
      }
    }
    fetchDashboardData();
    return () => { cancelled = true; };
  }, [fetchPlayers, clubId, activeSeason, user?.id, createNotification, selectedCategoryId]);



  // DEBUG: Exibir players.length e erro
  const debugPlayers = players ? players.length : "null";
  const debugError = error ? error : "nenhum";

  // Média de minutos jogados dos jogadores
  const minutesValues = players
    .map((p) => (p.stats?.minutes ? p.stats.minutes : null))
    .filter((v): v is number => v !== null);
  const avgMinutes =
    minutesValues.length > 0
      ? Math.round(minutesValues.reduce((a, b) => a + b, 0) / minutesValues.length)
      : 0;

  if (dashboardError) {
    return <div className="text-center text-destructive font-medium mt-8">{dashboardError}</div>;
  }



  // Renderização condicional baseada no tipo de usuário
  const renderDashboard = () => {
    // Dashboard para jogadores
    if (isLoaded && role === "player") {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center animate-float">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Dashboard do Jogador
              </h1>
              {selectedCategoryName && (
                <Badge variant="outline" className="w-fit bg-gradient-to-r from-blue-100 to-purple-100 border-blue-300">
                  <Star className="w-3 h-3 mr-1" />
                  Categoria: {selectedCategoryName}
                </Badge>
              )}
            </div>
          </div>

          {/* Stats section para jogadores */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {/* Card de Jogadores */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 mb-1">
                      {selectedCategoryName ? `Jogadores - ${selectedCategoryName}` : "Total de Jogadores"}
                    </p>
                    <p className="text-3xl font-bold text-blue-900">
                      {dashboardStats?.totalActivePlayers?.toString() || players.length.toString()}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {selectedCategoryName ? `Ativos na categoria ${selectedCategoryName}` : "Jogadores ativos no elenco"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center animate-float">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Vitórias */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 mb-1">
                      {selectedCategoryName ? `Vitórias - ${selectedCategoryName}` : "Vitórias na Temporada"}
                    </p>
                    <div className="flex items-center space-x-2">
                      <p className="text-3xl font-bold text-green-900">
                        {matchHistory.length > 0 ? matchHistory.filter((match) => match.result === "win").length.toString() : "0"}
                      </p>
                      <ArrowUp className="w-5 h-5 text-green-500" />
                    </div>
                    <p className="text-xs text-green-600 mt-1">
                      {selectedCategoryName ? `Vitórias da categoria ${selectedCategoryName}` : "Vitórias na temporada atual"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center animate-float">
                    <Trophy className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Próxima Partida */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 mb-1">Próxima Partida</p>
                    <p className="text-2xl font-bold text-purple-900">
                      {upcomingMatches.length > 0 ? formatDate(upcomingMatches[0].date) : "Sem partidas"}
                    </p>
                    <p className="text-xs text-purple-600 mt-1">
                      {upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center animate-float">
                    <Calendar className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Status dos Atletas */}
            <Card
              className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              onClick={() => setFitnessModalOpen(true)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 mb-1">Status dos Atletas</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-3xl font-bold text-orange-900">
                        {dashboardStats?.playersInRehabilitation?.toString() || "0"}
                      </p>
                      <Flame className="w-5 h-5 text-orange-500" />
                    </div>
                    <p className="text-xs text-orange-600 mt-1">
                      {`${dashboardStats?.playersInRehabilitation || 0} atletas em reabilitação`}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center animate-float">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Aniversariantes */}
            <Card
              className="relative overflow-hidden bg-gradient-to-br from-pink-50 to-pink-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              onClick={() => setBirthdaysModalOpen(true)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-pink-600 mb-1">Aniversariantes</p>
                    <p className="text-3xl font-bold text-pink-900">
                      {birthdays.length.toString()}
                    </p>
                    <p className="text-xs text-pink-600 mt-1">Este mês</p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-pink-600 flex items-center justify-center animate-float">
                    <Gift className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-pink-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>
          </div>

          {/* Charts section */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            <div className="lg:col-span-1 xl:col-span-1">
              <TeamPerformanceChart players={players} matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-1 xl:col-span-1">
              <PastGamesWidget matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-2 xl:col-span-1 space-y-3 sm:space-y-4">
              <SquadStatus />
            </div>         
          </div>

          {/* Upcoming events para jogadores */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }

    // Dashboard para médicos
    else if (isLoaded && role === "medical") {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Visão geral do departamento médico
            </p>
          </div>

          {/* Stats section para médicos */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <StatCard
              title="Total de Jogadores"
              value={dashboardStats?.totalActivePlayers?.toString() || players.length.toString()}
              description="Jogadores ativos no elenco"
              icon={Users}
            />
            <StatCard
              title="Próxima Partida"
              value={upcomingMatches.length > 0 ? formatDate(upcomingMatches[0].date) : "Sem partidas"}
              description={upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
              icon={Calendar}
            />
            <StatCard
              title="Status dos Atletas"
              value={dashboardStats?.playersInRehabilitation?.toString() || "0"}
              description={`${dashboardStats?.playersInRehabilitation || 0} atletas em reabilitação`}
              icon={Activity}
              clickable={true}
              onClick={() => setFitnessModalOpen(true)}
            />
          </div>

          {/* Squad status para médicos */}
          <div className="grid gap-4">
            <SquadStatus />
          </div>

          {/* Upcoming events para médicos */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }

    // Dashboard padrão para outros usuários
    else {
      return (
        <div className="space-y-4 sm:space-y-6">
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center animate-float">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Dashboard Executivo
                </h1>
                {selectedCategoryName ? (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                    <p className="text-sm sm:text-base text-muted-foreground">
                      Visão geral do seu time e atividades recentes
                    </p>
                    <Badge variant="outline" className="w-fit bg-gradient-to-r from-blue-100 to-purple-100 border-blue-300">
                      <Star className="w-3 h-3 mr-1" />
                      Categoria: {selectedCategoryName}
                    </Badge>
                  </div>
                ) : (
                  <p className="text-sm sm:text-base text-muted-foreground">
                    Visão geral do seu time e atividades recentes
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Stats section modernos */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {/* Card de Jogadores */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 mb-1">
                      {selectedCategoryName ? `Jogadores - ${selectedCategoryName}` : "Total de Jogadores"}
                    </p>
                    <p className="text-3xl font-bold text-blue-900">
                      {dashboardStats?.totalActivePlayers?.toString() || players.length.toString()}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {selectedCategoryName ? `Ativos na categoria ${selectedCategoryName}` : "Jogadores ativos no elenco"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center animate-float">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Vitórias */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 mb-1">
                      {selectedCategoryName ? `Vitórias - ${selectedCategoryName}` : "Vitórias na Temporada"}
                    </p>
                    <div className="flex items-center space-x-2">
                      <p className="text-3xl font-bold text-green-900">
                        {matchHistory.length > 0 ? matchHistory.filter((match) => match.result === "win").length.toString() : "0"}
                      </p>
                      <ArrowUp className="w-5 h-5 text-green-500" />
                    </div>
                    <p className="text-xs text-green-600 mt-1">
                      {selectedCategoryName ? `Vitórias da categoria ${selectedCategoryName}` : "Vitórias na temporada atual"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center animate-float">
                    <Trophy className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Próxima Partida */}
            <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 mb-1">Próxima Partida</p>
                    <p className="text-2xl font-bold text-purple-900">
                      {upcomingMatches.length > 0 ? formatDate(upcomingMatches[0].date) : "Sem partidas"}
                    </p>
                    <p className="text-xs text-purple-600 mt-1">
                      {upcomingMatches.length > 0 ? `vs. ${upcomingMatches[0].opponent}` : "Nenhuma partida agendada"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center animate-float">
                    <Calendar className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>

            {/* Card de Status dos Atletas */}
            <Card
              className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              onClick={() => setFitnessModalOpen(true)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 mb-1">Status dos Atletas</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-3xl font-bold text-orange-900">
                        {dashboardStats?.playersInRehabilitation?.toString() || "0"}
                      </p>
                      <Flame className="w-5 h-5 text-orange-500" />
                    </div>
                    <p className="text-xs text-orange-600 mt-1">
                      {`${dashboardStats?.playersInRehabilitation || 0} atletas em reabilitação`}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center animate-float">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>
            
            {/* Card de Aniversariantes */}
            <Card
              className="relative overflow-hidden bg-gradient-to-br from-pink-50 to-pink-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              onClick={() => setBirthdaysModalOpen(true)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-pink-600 mb-1">Aniversariantes</p>
                    <p className="text-3xl font-bold text-pink-900">
                      {birthdays.length.toString()}
                    </p>
                    <p className="text-xs text-pink-600 mt-1">Este mês</p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-pink-600 flex items-center justify-center animate-float">
                    <Gift className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-pink-500/10 rounded-full -mr-10 -mt-10"></div>
              </CardContent>
            </Card>
          </div>

          {/* Charts section */}
          <div className="grid gap-3 sm:gap-4 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
            <div className="lg:col-span-1 xl:col-span-1">
              <TeamPerformanceChart players={players} matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-1 xl:col-span-1">
              <PastGamesWidget matchHistory={matchHistory} />
            </div>
            <div className="lg:col-span-2 xl:col-span-1 space-y-3 sm:space-y-4">
              <PaymentStatusCard />
              <ExpiringContractsWidget
                players={players}
                daysThreshold={contractWarningDays}
              />
              <ExpiredContractsWidget players={players} />
              <EvaluationStatusWidget />
              <SquadStatus />
            </div>
          </div>

          {/* Upcoming events */}
          <div className="w-full overflow-hidden">
            <UpcomingEvents
              upcomingMatches={upcomingMatches}
              upcomingTrainings={upcomingTrainings}
            />
          </div>
        </div>
      );
    }
  };

  return (
    <>
      {renderDashboard()}

      {/* Fitness Modal */}
      <FitnessModal
        open={fitnessModalOpen}
        onOpenChange={setFitnessModalOpen}
        selectedCategoryId={selectedCategoryId}
      />
      
      {/* Birthdays Modal */}
      <BirthdaysModal
        open={birthdaysModalOpen}
        onOpenChange={setBirthdaysModalOpen}
        selectedCategoryId={selectedCategoryId}
      />
    </>
  );
}
