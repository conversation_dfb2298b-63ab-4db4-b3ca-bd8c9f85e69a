/**
 * Utilitários para categorização de funções em convocações
 */

// Definir funções de Staff (apoio operacional)
export const STAFF_ROLES = [
  "Roupeiro", 
  "Supervisor", 
  "Fisioterapeuta", 
  "Nutricionista", 
  "Fisiologista", 
  "Segurança", 
  "Motorista", 
  "Analista de desempenho",
  "Analista de Desempenho",
  "Assessor de Imprensa",
  "Assessor Empresa",
  "Massagista",
  "Comunicação",
  "Fotógrafo"
];

// Definir funções de Comissão Técnica (técnico-esportiva)
export const TECHNICAL_ROLES = [
  "Técnico", 
  "Auxiliar Técnico", 
  "Preparador Físico", 
  "Médico",
  "Preparador de goleiro",  
  "Psicólogo"
];

// Definir funções de Diretoria Executiva
export const EXECUTIVE_ROLES = [
  "<PERSON><PERSON><PERSON>", 
  "Presidente"
];

/**
 * Categoriza uma função em seu tipo correspondente
 * @param role - A função a ser categorizada
 * @returns O tipo da função: 'staff', 'technical', 'executive', 'athlete' ou 'other'
 */
export function categorizeRole(role: string, roleType?: string): 'staff' | 'technical' | 'executive' | 'athlete' | 'other' {
  const r = (role || '').toLowerCase();

  if (['technical', 'assistant_technical'].includes((roleType || '').toLowerCase())) {
    return 'technical';
  }

  if (["atleta", "titular", "reserva"].includes(r)) {
    return 'athlete';
  }

  const staff = STAFF_ROLES.map(s => s.toLowerCase());
  if (staff.includes(r)) {
    return 'staff';
  }

  const technical = TECHNICAL_ROLES.map(t => t.toLowerCase());
  if (technical.includes(r)) {
    return 'technical';
  }

  const executive = EXECUTIVE_ROLES.map(e => e.toLowerCase());
  if (executive.includes(r) || /presidente|diretor|ceo|gerente|vice|coordenador|conselheiro/.test(r)) {
    return 'executive';
  }

  // Verificar padrões para comissão técnica
  if (/técnic|tecnic|treinad|prepar|fisio|médic|medic|nutri|psicólog|psicologo|analista.*desempenho/.test(r)) {
    return 'technical';
  }

  // Verificar padrões para staff operacional
  if (/roupeiro|massag|seguranç|motorista|cozinheiro|auxiliar|operador|assessor|comunicação|fotógrafo/.test(r)) {
    return 'staff';
  }

  // Funções não reconhecidas entram como staff para aparecer no relatório
  return 'staff';
}

/**
 * Filtra uma lista de jogadores por categoria de função
 * @param players - Lista de jogadores
 * @param category - Categoria desejada
 * @returns Lista filtrada de jogadores
 */
export function filterPlayersByRoleCategory<T extends { role: string }>(
  players: T[], 
  category: 'staff' | 'technical' | 'executive' | 'athlete' | 'other'
): T[] {
  return players.filter(player => categorizeRole(player.role) === category);
}

/**
 * Verifica se uma função é de Staff
 * @param role - A função a verificar
 * @returns true se for função de Staff
 */
export function isStaffRole(role: string): boolean {
  return STAFF_ROLES.includes(role);
}

/**
 * Verifica se uma função é de Comissão Técnica
 * @param role - A função a verificar
 * @returns true se for função de Comissão Técnica
 */
export function isTechnicalRole(role: string): boolean {
  return TECHNICAL_ROLES.includes(role);
}

/**
 * Verifica se uma função é de Diretoria Executiva
 * @param role - A função a verificar
 * @returns true se for função de Diretoria Executiva
 */
export function isExecutiveRole(role: string): boolean {
  return EXECUTIVE_ROLES.includes(role);
}

/**
 * Obtém todas as funções disponíveis organizadas por categoria
 * @returns Objeto com todas as funções categorizadas
 */
export function getAllRolesByCategory() {
  return {
    staff: STAFF_ROLES,
    technical: TECHNICAL_ROLES,
    executive: EXECUTIVE_ROLES,
    athlete: ["Atleta"]
  };
}