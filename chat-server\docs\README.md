# Chat Server - Socket.IO

Servidor Socket.IO para o sistema de chat em tempo real do Game Day Nexus.

## Deploy na Vercel

1. **Instalar Vercel CLI:**
```bash
npm install -g vercel
```

2. **Fazer deploy:**
```bash
vercel --prod
```

3. **Configurar variáveis de ambiente na Vercel:**
- `SUPABASE_URL`: URL do seu projeto Supabase
- `SUPABASE_SERVICE_KEY`: Service key do Supabase (não a anon key!)

## Desenvolvimento Local

```bash
npm install
npm run dev
```

## Estrutura

- `index.js`: Servidor Socket.IO principal
- `api/socket.js`: Handler para Vercel
- `vercel.json`: Configuração do Vercel

## Eventos Socket.IO

### Cliente → Servidor
- `send-message`: Enviar mensagem
- `create-room`: <PERSON><PERSON><PERSON> sala
- `join-room`: Entrar em sala
- `create-direct-chat`: Criar chat 1x1
- `mark-as-read`: Marcar como lido

### Servidor → Cliente
- `new-message`: Nova mensagem recebida
- `user-presence-update`: Atualização de presença
- `online-users-list`: Lista de usuários online
- `room-created`: Nova sala criada
- `direct-chat-created`: Chat direto criado
- `error`: Erro ocorrido