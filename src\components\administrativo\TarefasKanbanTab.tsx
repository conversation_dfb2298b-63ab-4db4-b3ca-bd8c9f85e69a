import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  Pencil,
  Trash2,
  Calendar,
  User,
  Clock,
  Settings,
  Users,
  Filter
} from "lucide-react";
import { AdministrativeTask, Collaborator, getCollaborators } from "@/api/api";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { NovaTarefaDialog } from "@/components/administrativo/NovaTarefaDialog";
import { EditarTarefaDialog } from "@/components/administrativo/EditarTarefaDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";
import { GerenciarTiposTarefaDialog } from "@/components/administrativo/GerenciarTiposTarefaDialog";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
import { usePermission } from "@/hooks/usePermission";
import { ADMINISTRATIVE_PERMISSIONS } from "@/constants/permissions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type TaskType = Database['public']['Tables']['task_types']['Row'];

interface TarefasKanbanTabProps {
  tasks: AdministrativeTask[];
  loading: boolean;
  error: string | null;
  clubId: number;
  currentCollaboratorId?: number | null;
}

export function TarefasKanbanTab({ tasks, loading, error, clubId, currentCollaboratorId }: TarefasKanbanTabProps) {
  const [novaTarefaDialogOpen, setNovaTarefaDialogOpen] = useState(false);
  const [editarTarefaDialogOpen, setEditarTarefaDialogOpen] = useState(false);
  const [excluirTarefaDialogOpen, setExcluirTarefaDialogOpen] = useState(false);
  const [gerenciarTiposDialogOpen, setGerenciarTiposDialogOpen] = useState(false);
  const [tarefaParaExcluir, setTarefaParaExcluir] = useState<AdministrativeTask | null>(null);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [loadingTaskTypes, setLoadingTaskTypes] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [selectedCollaboratorId, setSelectedCollaboratorId] = useState<string | null>(null);
  
  // Date filter states
  const [dateFilters, setDateFilters] = useState({
    dueDateFrom: '',
    dueDateTo: '',
    createdFrom: '',
    createdTo: '',
    updatedFrom: '',
    updatedTo: ''
  });

  // Per-column date filters
  const [columnDateFilters, setColumnDateFilters] = useState<Record<string, {
    dueDateFrom: string;
    dueDateTo: string;
    createdFrom: string;
    createdTo: string;
    updatedFrom: string;
    updatedTo: string;
  }>>({});

  // We don't need the user object anymore since we're using the permission hook

  const {
    updateTaskStatus,
    removeTask,
    getTasksByStatus
  } = useAdministrativeTasksStore();

    // Permission checks
    const { can } = usePermission();
    const canCreate = can(ADMINISTRATIVE_PERMISSIONS.TASKS.CREATE);
    const canEdit = can(ADMINISTRATIVE_PERMISSIONS.TASKS.EDIT);
    const canDelete = can(ADMINISTRATIVE_PERMISSIONS.TASKS.DELETE);
    const canViewAll = can(ADMINISTRATIVE_PERMISSIONS.TASKS.VIEW);  

  const [selectedTask, setSelectedTask] = useState<AdministrativeTask | null>(null);

  useEffect(() => {
    fetchTaskTypes();
    fetchCollaborators();
  }, [clubId, currentCollaboratorId, canViewAll]);

  const fetchTaskTypes = async () => {
    setLoadingTaskTypes(true);
    try {
      const { data, error } = await supabase
        .from("task_types")
        .select("*")
        .eq("club_id", clubId)
        .order("position", { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar tipos de tarefas: ${error.message}`);
      }
      setTaskTypes(data || []);
    } catch (err: any) {
      console.error("Erro ao buscar tipos de tarefas:", err);
      toast({
        title: "Erro",
        description: "Erro ao carregar tipos de tarefas",
        variant: "destructive",
      });
    } finally {
      setLoadingTaskTypes(false);
    }
  };

  const fetchCollaborators = async () => {
    setLoadingCollaborators(true);
    try {
      const clubCollaborators = await getCollaborators(clubId);
      // Sort collaborators alphabetically by name
      clubCollaborators.sort((a, b) => a.full_name.localeCompare(b.full_name));
      // Filter out inactive collaborators
      let activeCollaborators = clubCollaborators.filter(
        collaborator => collaborator.status !== 'inactive'
      );
      if (!canViewAll && currentCollaboratorId) {
        activeCollaborators = activeCollaborators.filter(c => c.id === currentCollaboratorId);
        setSelectedCollaboratorId(currentCollaboratorId.toString());
      } else if (canViewAll) {
        setSelectedCollaboratorId(null);
      }
      setCollaborators(activeCollaborators);
    } catch (error) {
      console.error("Erro ao buscar colaboradores:", error);
      toast({
        title: "Erro",
        description: "Erro ao carregar colaboradores",
        variant: "destructive",
      });
    } finally {
      setLoadingCollaborators(false);
    }
  };

  // Filter tasks by status, selected collaborator, and date filters
  const getTasksByCustomStatus = (status: string) => {
    return tasks.filter(task => {
      // First filter by status
      const statusMatch = task.status === status;

      // Filter by collaborator
      let collaboratorMatch = true;
      if (selectedCollaboratorId) {
        collaboratorMatch = task.collaborator_id === parseInt(selectedCollaboratorId);
      }

      // Get column-specific filters or fall back to global filters
      const columnFilters = columnDateFilters[status] || dateFilters;

      // Filter by date ranges
      let dateMatch = true;
      
      // Due date filter
      if (columnFilters.dueDateFrom && task.due_date) {
        const taskDueDate = new Date(task.due_date);
        const filterFromDate = new Date(columnFilters.dueDateFrom);
        if (taskDueDate < filterFromDate) dateMatch = false;
      }
      
      if (columnFilters.dueDateTo && task.due_date) {
        const taskDueDate = new Date(task.due_date);
        const filterToDate = new Date(columnFilters.dueDateTo);
        filterToDate.setHours(23, 59, 59, 999); // End of day
        if (taskDueDate > filterToDate) dateMatch = false;
      }
      
      // Created date filter
      if (columnFilters.createdFrom) {
        const taskCreatedDate = new Date(task.created_at);
        const filterFromDate = new Date(columnFilters.createdFrom);
        if (taskCreatedDate < filterFromDate) dateMatch = false;
      }
      
      if (columnFilters.createdTo) {
        const taskCreatedDate = new Date(task.created_at);
        const filterToDate = new Date(columnFilters.createdTo);
        filterToDate.setHours(23, 59, 59, 999); // End of day
        if (taskCreatedDate > filterToDate) dateMatch = false;
      }
      
      // Updated date filter
      if (columnFilters.updatedFrom) {
        const taskUpdatedDate = new Date(task.updated_at);
        const filterFromDate = new Date(columnFilters.updatedFrom);
        if (taskUpdatedDate < filterFromDate) dateMatch = false;
      }
      
      if (columnFilters.updatedTo) {
        const taskUpdatedDate = new Date(task.updated_at);
        const filterToDate = new Date(columnFilters.updatedTo);
        filterToDate.setHours(23, 59, 59, 999); // End of day
        if (taskUpdatedDate > filterToDate) dateMatch = false;
      }

      return statusMatch && collaboratorMatch && dateMatch;
    });
  };

  // Helper function to check if a column has active filters
  const hasColumnFilters = (status: string) => {
    const filters = columnDateFilters[status];
    if (!filters) return false;
    return filters.dueDateFrom || filters.dueDateTo || filters.createdFrom || 
           filters.createdTo || filters.updatedFrom || filters.updatedTo;
  };

  // Helper function to clear column filters
  const clearColumnFilters = (status: string) => {
    setColumnDateFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[status];
      return newFilters;
    });
  };

  // Helper function to update column filters
  const updateColumnFilters = (status: string, filters: typeof dateFilters) => {
    setColumnDateFilters(prev => ({
      ...prev,
      [status]: filters
    }));
  };

  const handleEditTask = (task: AdministrativeTask) => {
    setSelectedTask(task);
    setEditarTarefaDialogOpen(true);
  };

  const handleDeleteTask = (task: AdministrativeTask) => {
    setTarefaParaExcluir(task);
    setExcluirTarefaDialogOpen(true);
  };

  const confirmDeleteTask = async () => {
    if (tarefaParaExcluir) {
      try {
        await removeTask(clubId, tarefaParaExcluir.id);
        toast({
          title: "Tarefa excluída",
          description: "A tarefa foi excluída com sucesso.",
        });
      } catch (error) {
        toast({
          title: "Erro ao excluir tarefa",
          description: "Ocorreu um erro ao excluir a tarefa.",
          variant: "destructive",
        });
      }
    }
    setExcluirTarefaDialogOpen(false);
  };

  const handleDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId } = result;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) return;

    const taskId = parseInt(draggableId.replace('task-', ''));

    const newStatus = destination.droppableId;

    try {
      await updateTaskStatus(clubId, taskId, newStatus);
    } catch (error) {
      toast({
        title: "Erro ao mover tarefa",
        description: "Ocorreu um erro ao atualizar o status da tarefa.",
        variant: "destructive",
      });
    }
  };

  // Check if user has permission to delete tasks
  const canDeleteTasks = () => {
    return canDelete;
  };

  const renderTaskCard = (task: AdministrativeTask) => (
    <Draggable key={task.id} draggableId={`task-${task.id}`} index={task.id}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className="bg-white p-3 rounded-md shadow-sm mb-2 border border-gray-200"
        >
          <div className="flex justify-between items-start">
            <h3 className="font-medium text-sm">{task.title}</h3>
            <div className="flex gap-1">
              {canEdit && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => handleEditTask(task)}
                >
                  <Pencil className="h-3 w-3" />
                </Button>
              )}
              {canDeleteTasks() && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => handleDeleteTask(task)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {task.description && (
            <p className="text-xs text-gray-500 mt-1">{task.description}</p>
          )}

          <div className="flex flex-wrap gap-2 mt-2">
            {task.collaborator_name && (
              <div className="flex items-center text-xs text-gray-500">
                <User className="h-3 w-3 mr-1" />
                {task.collaborator_name}
              </div>
            )}

            {task.due_date && (
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {new Date(task.due_date).toLocaleDateString('pt-BR')}
              </div>
            )}
          </div>
        </div>
      )}
    </Draggable>
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Tarefas Diárias (Kanban)</CardTitle>
          <CardDescription>
            Organize e acompanhe as tarefas do clube
          </CardDescription>
        </div>
        <div className="flex gap-2">
          {canEdit && (
            <Button
              variant="outline"
              onClick={() => setGerenciarTiposDialogOpen(true)}
              title="Gerenciar Tipos de Tarefa"
            >
              <Settings className="h-4 w-4 mr-2" />
              Tipos de Tarefa
            </Button>
          )}
          {canCreate && (
            <Button
              onClick={() => {
                setSelectedTask(null);
                setNovaTarefaDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Tarefa
            </Button>
          )}
        </div>
      </CardHeader>
      <div className="px-6 pb-4 space-y-3">
        {/* Collaborator Filter */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Colaborador:</span>
          </div>
          <div className="w-64">
            <Select
              value={selectedCollaboratorId || "all"}
              onValueChange={(value) => setSelectedCollaboratorId(value === "all" ? null : value)}
              disabled={!canViewAll}
            >
              <SelectTrigger>
                <SelectValue placeholder={canViewAll ? "Todos os colaboradores" : "Seu cadastro"} />
              </SelectTrigger>
              <SelectContent>
                {canViewAll && <SelectItem value="all">Todos os colaboradores</SelectItem>}
                {loadingCollaborators ? (
                  <SelectItem value="loading" disabled>
                    Carregando colaboradores...
                  </SelectItem>
                ) : (
                  collaborators.map((collaborator) => (
                    <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                      {collaborator.full_name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Date Filters */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filtros de Data:</span>
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Filtrar por Data
                {(dateFilters.dueDateFrom || dateFilters.dueDateTo || dateFilters.createdFrom || 
                  dateFilters.createdTo || dateFilters.updatedFrom || dateFilters.updatedTo) && (
                  <span className="ml-2 h-2 w-2 bg-blue-500 rounded-full"></span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96" align="start">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filtros de Data</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setDateFilters({
                      dueDateFrom: '',
                      dueDateTo: '',
                      createdFrom: '',
                      createdTo: '',
                      updatedFrom: '',
                      updatedTo: ''
                    })}
                  >
                    Limpar
                  </Button>
                </div>

                {/* Due Date Filter */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Data de Vencimento</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">De:</Label>
                      <Input
                        type="date"
                        value={dateFilters.dueDateFrom}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, dueDateFrom: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Até:</Label>
                      <Input
                        type="date"
                        value={dateFilters.dueDateTo}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, dueDateTo: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Created Date Filter */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Data de Criação</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">De:</Label>
                      <Input
                        type="date"
                        value={dateFilters.createdFrom}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, createdFrom: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Até:</Label>
                      <Input
                        type="date"
                        value={dateFilters.createdTo}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, createdTo: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Updated Date Filter */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Data de Atualização</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">De:</Label>
                      <Input
                        type="date"
                        value={dateFilters.updatedFrom}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, updatedFrom: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Até:</Label>
                      <Input
                        type="date"
                        value={dateFilters.updatedTo}
                        onChange={(e) => setDateFilters(prev => ({ ...prev, updatedTo: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Carregando tarefas...</div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className={`grid grid-cols-1 ${taskTypes.length > 2 ? 'md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'md:grid-cols-' + (taskTypes.length === 0 ? 1 : taskTypes.length) } gap-4`}>
              {taskTypes.map(type => {
                const tasksInColumn = getTasksByCustomStatus(type.name);
                let displayName = type.name.charAt(0).toUpperCase() + type.name.slice(1).replace(/_/g, ' ');
                if (type.name === 'a_fazer') displayName = 'A Fazer';
                if (type.name === 'em_andamento') displayName = 'Em Andamento';
                if (type.name === 'concluido') displayName = 'Concluído';

                return (
                  <div
                    key={type.id}
                    className="bg-gray-50 p-4 rounded-md"
                    style={{
                      borderTop: `4px solid ${type.color || '#6b7280'}`, // Default to gray if no color
                    }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium flex items-center" style={{ color: type.color || '#6b7280' }}>
                        {displayName}
                        <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                          {tasksInColumn.length}
                        </span>
                      </h3>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 hover:bg-gray-200"
                            title={`Filtrar ${displayName}`}
                          >
                            <Filter className="h-3 w-3" />
                            {hasColumnFilters(type.name) && (
                              <span className="absolute -top-1 -right-1 h-2 w-2 bg-blue-500 rounded-full"></span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80" align="start">
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">Filtros para {displayName}</h4>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => clearColumnFilters(type.name)}
                              >
                                Limpar
                              </Button>
                            </div>

                            {/* Due Date Filter */}
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Data de Vencimento</Label>
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label className="text-xs text-muted-foreground">De:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.dueDateFrom || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      dueDateFrom: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs text-muted-foreground">Até:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.dueDateTo || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      dueDateTo: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Created Date Filter */}
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Data de Criação</Label>
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label className="text-xs text-muted-foreground">De:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.createdFrom || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      createdFrom: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs text-muted-foreground">Até:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.createdTo || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      createdTo: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Updated Date Filter */}
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Data de Atualização</Label>
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label className="text-xs text-muted-foreground">De:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.updatedFrom || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      updatedFrom: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs text-muted-foreground">Até:</Label>
                                  <Input
                                    type="date"
                                    value={columnDateFilters[type.name]?.updatedTo || ''}
                                    onChange={(e) => updateColumnFilters(type.name, {
                                      ...columnDateFilters[type.name] || { dueDateFrom: '', dueDateTo: '', createdFrom: '', createdTo: '', updatedFrom: '', updatedTo: '' },
                                      updatedTo: e.target.value
                                    })}
                                    className="text-sm"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                    <Droppable droppableId={type.name}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="min-h-[200px]"
                        >
                          {tasksInColumn.map(renderTaskCard)}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                );
              })}
            </div>
          </DragDropContext>
        )}
      </CardContent>

      {canCreate && (
        <NovaTarefaDialog
          open={novaTarefaDialogOpen}
          onOpenChange={setNovaTarefaDialogOpen}
          clubId={clubId}
          initialCollaboratorId={
            selectedCollaboratorId !== "all" && selectedCollaboratorId
              ? parseInt(selectedCollaboratorId)
              : undefined
          }
        />
      )}

      {canEdit && (
        <EditarTarefaDialog
          open={editarTarefaDialogOpen}
          onOpenChange={setEditarTarefaDialogOpen}
          clubId={clubId}
          task={selectedTask}
        />
      )}

      {canDelete && (
        <ConfirmDialog
          open={excluirTarefaDialogOpen}
          onOpenChange={setExcluirTarefaDialogOpen}
          title="Excluir tarefa"
          description="Tem certeza que deseja excluir esta tarefa? Esta ação não pode ser desfeita."
          onConfirm={confirmDeleteTask}
        />
      )}

      {canEdit && (
        <GerenciarTiposTarefaDialog
          open={gerenciarTiposDialogOpen}
          onOpenChange={setGerenciarTiposDialogOpen}
          clubId={clubId}
          onSuccess={fetchTaskTypes}
        />
      )}
    </Card>
  );
}
