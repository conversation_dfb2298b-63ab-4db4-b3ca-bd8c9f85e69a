import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { User, Settings, Lock } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/api/supabaseClient";
import { useUser } from "@/context/UserContext";
import { AvatarUpload } from "@/components/users/AvatarUpload";
import { updateUserProfile } from "@/api/users";
import { updateUserPassword } from "@/api/auth";

interface ProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProfileDialog({ open, onOpenChange }: ProfileDialogProps) {
  const [activeTab, setActiveTab] = useState("profile");
  const { user, loading, refreshUser } = useUser();
  const [editName, setEditName] = useState(user?.name || "");
  const [editEmail, setEditEmail] = useState(user?.email || "");
  const [profileImage, setProfileImage] = useState(user?.profile_image || "");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [saving, setSaving] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [error, setError] = useState("");
  const [passwordError, setPasswordError] = useState("");

  useEffect(() => {
    if (open && user) {
      setEditName(user.name || "");
      setEditEmail(user.email || "");
      setProfileImage(user.profile_image || "");
      setActiveTab("profile"); // Garantir que sempre abra na aba de perfil
      setError(""); // Limpar erros anteriores
      setPasswordError(""); // Limpar erros de senha
    }
  }, [open, user]);

  const handleSave = async () => {
    setSaving(true);
    setError("");

    if (!editName.trim() || !editEmail.trim()) {
      setError("Nome e email são obrigatórios");
      setSaving(false);
      return;
    }

    const updateData = {
      name: editName,
      email: editEmail,
      profile_image: profileImage
    };

    try {
      // Usar a função updateUserProfile em vez de chamar diretamente o Supabase
      if (user?.id) {
        await updateUserProfile(user.id, updateData);

        // Salvar a imagem no localStorage para uso imediato
        if (profileImage) {
          localStorage.setItem("userProfileImage", profileImage);
        }

        await refreshUser(true);

        toast({
          title: "Perfil atualizado",
          description: "Suas alterações foram salvas com sucesso!",
          variant: "default",
        });

        onOpenChange(false);
      } else {
        throw new Error("ID do usuário não encontrado");
      }
    } catch (err: any) {
      console.error("Erro ao atualizar perfil:", err);
      setError("Erro ao salvar perfil: " + (err.message || "Erro desconhecido"));
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = async () => {
    setPasswordError("");
    setChangingPassword(true);

    // Validar senha
    if (!newPassword) {
      setPasswordError("A nova senha é obrigatória");
      setChangingPassword(false);
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError("A senha deve ter pelo menos 6 caracteres");
      setChangingPassword(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("As senhas não coincidem");
      setChangingPassword(false);
      return;
    }

    try {
      await updateUserPassword(newPassword);

      // Limpar campos de senha
      setNewPassword("");
      setConfirmPassword("");

      toast({
        title: "Senha atualizada",
        description: "Sua senha foi atualizada com sucesso!",
        variant: "default",
      });
    } catch (err: any) {
      console.error("Erro ao atualizar senha:", err);
      setPasswordError("Erro ao atualizar senha: " + (err.message || "Erro desconhecido"));
    } finally {
      setChangingPassword(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onOpenChange={(newOpen) => {
        console.log("ProfileDialog onOpenChange:", newOpen);
        onOpenChange(newOpen);
      }}
    >
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle>Perfil</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>Perfil</span>
            </TabsTrigger>
            <TabsTrigger value="password" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              <span>Senha</span>
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span>Preferências</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4 py-2">
            <div className="flex items-center gap-4">
              <AvatarUpload
                userId={user?.id || ""}
                name={user?.name || ""}
                imageUrl={profileImage}
                onImageChange={setProfileImage}
                size="lg"
              />
              <div className="flex-1">
                <h3 className="text-lg font-semibold">{user?.name || "Usuário"}</h3>
                <p className="text-muted-foreground text-xs">{editEmail}</p>
              </div>
            </div>

            <Separator />

            <div className="grid gap-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label htmlFor="name">Nome</Label>
                  <Input id="name" value={editName} onChange={e => setEditName(e.target.value)} />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" value={editEmail} onChange={e => setEditEmail(e.target.value)} />
                </div>
              </div>
            </div>
            {error && <div className="text-red-500 text-xs mt-1">{error}</div>}
          </TabsContent>

          <TabsContent value="password" className="space-y-4 py-2">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-primary/10 rounded-full">
                <Lock className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold">Alterar Senha</h3>
                <p className="text-muted-foreground text-xs">Defina uma nova senha para sua conta</p>
              </div>
            </div>

            <Separator />

            <div className="grid gap-3">
              <div className="space-y-1">
                <Label htmlFor="new-password">Nova Senha</Label>
                <Input
                  id="new-password"
                  type="password"
                  value={newPassword}
                  onChange={e => setNewPassword(e.target.value)}
                  placeholder="Digite sua nova senha"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="confirm-password">Confirmar Senha</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}
                  placeholder="Confirme sua nova senha"
                />
              </div>
              {passwordError && <div className="text-red-500 text-xs mt-1">{passwordError}</div>}
              <Button
                onClick={handlePasswordChange}
                isLoading={changingPassword}
                disabled={changingPassword || !newPassword || !confirmPassword}
                className="mt-2"
              >
                {changingPassword ? "Atualizando..." : "Atualizar Senha"}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="preferences" className="py-2">
            <div className="space-y-3">
              <h3 className="text-sm font-medium">Notificações</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="events" className="text-sm">Eventos e compromissos</Label>
                    <p className="text-muted-foreground text-xs">Receber notificações sobre eventos</p>
                  </div>
                  <Switch id="events" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="games" className="text-sm">Partidas</Label>
                    <p className="text-muted-foreground text-xs">Alertas sobre jogos próximos</p>
                  </div>
                  <Switch id="games" defaultChecked />
                </div>
              </div>

              <Separator className="my-2" />

              <h3 className="text-sm font-medium">Aparência</h3>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="compact" className="text-sm">Modo compacto</Label>
                    <p className="text-muted-foreground text-xs">Reduz o espaçamento entre elementos</p>
                  </div>
                  <Switch id="compact" />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)} variant="outline">Cancelar</Button>
          <Button onClick={handleSave} isLoading={saving} disabled={saving}>
            {saving ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
