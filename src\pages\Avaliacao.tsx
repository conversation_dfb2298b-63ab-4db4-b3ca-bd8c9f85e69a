import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreateEvaluationInvitationForm } from "@/components/evaluation/CreateEvaluationInvitationForm";
import { EvaluationInvitationsTable } from "@/components/evaluation/EvaluationInvitationsTable";
import { PlayersInEvaluationTable } from "@/components/evaluation/PlayersInEvaluationTable";
import { EvaluationStatusCounts } from "@/components/evaluation/EvaluationStatusCounts";
import { usePermission } from "@/hooks/usePermission";
import { EVALUATION_PERMISSIONS } from "@/constants/permissions";
import { ModuleGuard } from "@/components/guards/ModuleGuard";

export default function Avaliacao() {
  return (
    <ModuleGuard module="evaluations">
      <AvaliacaoContent />
    </ModuleGuard>
  );
}

function AvaliacaoContent() {
  const { can, isLoaded } = usePermission();
  const getDefaultTab = () => {
    if (can(EVALUATION_PERMISSIONS.TABS.PLAYERS)) return "players";
    if (can(EVALUATION_PERMISSIONS.TABS.INVITATIONS)) return "invitations";
    if (can(EVALUATION_PERMISSIONS.TABS.NEW_INVITATION)) return "create";
    if (can(EVALUATION_PERMISSIONS.TABS.DASHBOARD)) return "dashboard";
    return "";
  };
  const [activeTab, setActiveTab] = useState<string>("players");

  // Atualiza a aba ativa após carregar permissões
  useEffect(() => {
    if (isLoaded) {
      setActiveTab(getDefaultTab());
    }
  }, [isLoaded]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const handleInvitationCreated = () => {
    // Switch to invitations tab after creating a new invitation
    setActiveTab("invitations");
  };

  return (
    <div className="container py-4 sm:py-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Pré Cadastro de Atletas</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Gerencie o processo de pré cadastro de novos atletas
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 overflow-x-auto">
          {can(EVALUATION_PERMISSIONS.TABS.PLAYERS) && (
            <TabsTrigger value="players" className="text-xs sm:text-sm">
              <span className="hidden sm:inline">Atletas em Pré Cadastro</span>
              <span className="sm:hidden">Atletas</span>
            </TabsTrigger>
          )}
          {can(EVALUATION_PERMISSIONS.TABS.INVITATIONS) && (
            <TabsTrigger value="invitations" className="text-xs sm:text-sm">Convites</TabsTrigger>
          )}
          {can(EVALUATION_PERMISSIONS.TABS.NEW_INVITATION) && (
            <TabsTrigger value="create" className="text-xs sm:text-sm">
              <span className="hidden sm:inline">Novo Convite</span>
              <span className="sm:hidden">Novo</span>
            </TabsTrigger>
          )}
          {can(EVALUATION_PERMISSIONS.TABS.DASHBOARD) && (
            <TabsTrigger value="dashboard" className="text-xs sm:text-sm">Dashboard</TabsTrigger>
          )}
        </TabsList>

        {can(EVALUATION_PERMISSIONS.TABS.PLAYERS) && (
          <TabsContent value="players" className="space-y-6">
            <PlayersInEvaluationTable />
          </TabsContent>
        )}

        {can(EVALUATION_PERMISSIONS.TABS.INVITATIONS) && (
          <TabsContent value="invitations" className="space-y-6">
            <EvaluationInvitationsTable />
          </TabsContent>
        )}

        {can(EVALUATION_PERMISSIONS.TABS.NEW_INVITATION) && (
          <TabsContent value="create" className="space-y-6">
            {can(EVALUATION_PERMISSIONS.INVITATIONS.CREATE) ? (
              <CreateEvaluationInvitationForm onSuccess={handleInvitationCreated} />
            ) : (
              <div className="p-6 text-center">
                <p className="text-muted-foreground">
                  Você não tem permissão para criar convites de pré cadastro.
                </p>
              </div>
            )}
          </TabsContent>
        )}

        {can(EVALUATION_PERMISSIONS.TABS.DASHBOARD) && (
        <TabsContent value="dashboard" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Atletas por Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <EvaluationStatusCounts />

                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Próximos Pré Cadastros</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center p-8 text-muted-foreground">
                    Funcionalidade em desenvolvimento
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
