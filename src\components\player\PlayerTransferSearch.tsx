import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Search, User, FileText, Building, Calendar, Phone, Mail, MapPin, AlertTriangle } from 'lucide-react';
import { 
  searchPlayerByCPF, 
  getGlobalPlayerDocuments, 
  initiatePlayerTransfer,
  validateCPF,
  formatCPF,
  type PlayerSearchResult,
  type GlobalPlayerDocument 
} from '@/api/playerTransfers';
import { useUser } from '@/context/UserContext';
import { toast } from 'sonner';

interface PlayerTransferSearchProps {
  onPlayerTransferred?: (playerId: string) => void;
}

export function PlayerTransferSearch({ onPlayerTransferred }: PlayerTransferSearchProps) {
  const { user } = useUser();
  const [cpf, setCpf] = useState('');
  const [searchResult, setSearchResult] = useState<PlayerSearchResult | null>(null);
  const [documents, setDocuments] = useState<GlobalPlayerDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [transferring, setTransferring] = useState(false);
  const [showTransferForm, setShowTransferForm] = useState(false);
  const [transferData, setTransferData] = useState({
    position: '',
    number: '',
    category: '',
    entry_date: new Date().toISOString().split('T')[0],
    contract_end_date: '',
    observation: ''
  });

  const handleCPFChange = (value: string) => {
    // Aplicar máscara de CPF
    const cleanValue = value.replace(/\D/g, '');
    if (cleanValue.length <= 11) {
      const formatted = cleanValue.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4');
      setCpf(formatted);
    }
  };

  const handleSearch = async () => {
    if (!cpf.trim()) {
      toast.error('Digite um CPF para buscar');
      return;
    }

    if (!validateCPF(cpf)) {
      toast.error('CPF inválido');
      return;
    }

    setLoading(true);
    try {
      const result = await searchPlayerByCPF(cpf);
      setSearchResult(result);

      if (result.found && result.global_player_id) {
        // Buscar documentos do jogador
        const playerDocuments = await getGlobalPlayerDocuments(result.global_player_id);
        setDocuments(playerDocuments);
        
        if (result.documents_count > 0) {
          toast.success(`Jogador encontrado! ${result.documents_count} documento(s) disponível(is)`);
        } else {
          toast.success('Jogador encontrado!');
        }
      } else {
        toast.info('Jogador não encontrado no sistema. Será criado um novo registro.');
        setDocuments([]);
      }
    } catch (error) {
      console.error('Erro na busca:', error);
      toast.error('Erro ao buscar jogador');
    } finally {
      setLoading(false);
    }
  };

  const handleTransfer = async () => {
    if (!user?.club_id) {
      toast.error('Clube não identificado');
      return;
    }

    if (!transferData.position) {
      toast.error('Posição é obrigatória');
      return;
    }

    setTransferring(true);
    try {
      const playerData = {
        name: searchResult?.name || '',
        position: transferData.position,
        ...(transferData.number && { number: parseInt(transferData.number) }),
        age: searchResult?.birthdate ? 
          new Date().getFullYear() - new Date(searchResult.birthdate).getFullYear() : 0,
        birthdate: searchResult?.birthdate,
        birthplace: searchResult?.birthplace,
        nationality: searchResult?.nationality || 'Brasil',
        rg_number: searchResult?.rg_number,
        cpf_number: cpf.replace(/\D/g, ''),
        father_name: searchResult?.father_name,
        mother_name: searchResult?.mother_name,
        phone: searchResult?.phone,
        email: searchResult?.email,
        height: searchResult?.height,
        weight: searchResult?.weight,
        entry_date: transferData.entry_date,
        contract_end_date: transferData.contract_end_date || null,
        observation: transferData.observation,
        status: 'ativo'
      };

      const result = await initiatePlayerTransfer(
        cpf,
        user.club_id,
        playerData,
        user.id
      );

      if (result.success) {
        toast.success(result.message);
        
        // Se há documentos, copiá-los
        if (documents.length > 0 && result.player_id) {
          toast.info('Copiando documentos...');
          // A cópia de documentos será feita automaticamente pela função
        }

        // Resetar formulário
        setCpf('');
        setSearchResult(null);
        setDocuments([]);
        setShowTransferForm(false);
        setTransferData({
          position: '',
          number: '',
          category: '',
          entry_date: new Date().toISOString().split('T')[0],
          contract_end_date: '',
          observation: ''
        });

        // Callback para atualizar lista de jogadores
        if (onPlayerTransferred && result.player_id) {
          onPlayerTransferred(result.player_id);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro na transferência:', error);
      toast.error('Erro ao transferir jogador');
    } finally {
      setTransferring(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Busca por CPF */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Buscar Jogador por CPF
          </CardTitle>
          <CardDescription>
            Digite o CPF do jogador para verificar se ele já está cadastrado no sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="000.000.000-00"
              value={cpf}
              onChange={(e) => handleCPFChange(e.target.value)}
              className="flex-1"
              maxLength={14}
            />
            <Button 
              onClick={handleSearch} 
              disabled={loading}
              className="min-w-[100px]"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Buscando...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Buscar
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Resultado da busca */}
      {searchResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {searchResult.found ? 'Jogador Encontrado' : 'Novo Jogador'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {searchResult.found ? (
              <div className="space-y-4">
                {/* Informações básicas */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-lg">{searchResult.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      CPF: {formatCPF(cpf)}
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {searchResult.last_club_name && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        Último clube: {searchResult.last_club_name}
                      </Badge>
                    )}
                    {searchResult.documents_count > 0 && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {searchResult.documents_count} documento(s)
                      </Badge>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Status atual do jogador */}
                {searchResult.is_active_elsewhere && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                    {searchResult.active_club_name === user?.club_info?.name ? (
                        <strong>Jogador já está cadastrado em seu clube.</strong>
                      ) : (
                        <>
                          <strong>Jogador já está ativo no clube: {searchResult.active_club_name}</strong>
                          <br />
                          O clube atual deve marcar o jogador como "inativo" antes que ele possa ser transferido para outro clube.
                        </>
                      )}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Detalhes pessoais */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  {searchResult.birthdate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Nascimento: {new Date(searchResult.birthdate).toLocaleDateString('pt-BR')}</span>
                    </div>
                  )}
                  {searchResult.birthplace && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>Natural de: {searchResult.birthplace}</span>
                    </div>
                  )}
                  {searchResult.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{searchResult.phone}</span>
                    </div>
                  )}
                  {searchResult.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{searchResult.email}</span>
                    </div>
                  )}
                  {searchResult.current_status && (
                    <div className="flex items-center gap-2">
                      <Badge variant={searchResult.is_active_elsewhere ? "destructive" : "secondary"}>
                        Status: {searchResult.current_status}
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Documentos disponíveis */}
                {documents.length > 0 && (
                  <div>
                    <h5 className="font-medium mb-2">Documentos Disponíveis:</h5>
                    <div className="flex flex-wrap gap-2">
                      {documents.map((doc) => (
                        <Badge key={doc.id} variant="outline" className="text-xs">
                          {doc.document_type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Alert>
                  <AlertDescription>
                    Os dados pessoais e documentos serão automaticamente copiados para o novo cadastro.
                    Você só precisa informar os dados específicos do clube (posição, número, etc.).
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <Alert>
                <AlertDescription>
                  Jogador não encontrado no sistema. Um novo registro será criado com os dados informados.
                </AlertDescription>
              </Alert>
            )}

            <div className="mt-4">
              <Button 
                onClick={() => setShowTransferForm(true)}
                className="w-full"
                disabled={searchResult.found && searchResult.is_active_elsewhere}
              >
                {searchResult.found ? 
                  (searchResult.is_active_elsewhere ? 'Jogador Ativo em Outro Clube' : 'Transferir Jogador') 
                  : 'Cadastrar Novo Jogador'
                }
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Formulário de transferência */}
      {showTransferForm && (
        <Card>
          <CardHeader>
            <CardTitle>Dados do Clube</CardTitle>
            <CardDescription>
              Informe os dados específicos do jogador para este clube
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Posição *</label>
                <Select
                  value={transferData.position}
                  onValueChange={(value) => setTransferData(prev => ({ ...prev, position: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a posição" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Goleiro">Goleiro</SelectItem>
                    <SelectItem value="Zagueiro">Zagueiro</SelectItem>
                    <SelectItem value="Lateral">Lateral</SelectItem>
                    <SelectItem value="Volante">Volante</SelectItem>
                    <SelectItem value="Meias">Meias</SelectItem>
                    <SelectItem value="Extremo">Extremo</SelectItem>
                    <SelectItem value="Centroavante">Centroavante</SelectItem>
                    <SelectItem value="Atacante">Atacante</SelectItem>
                    <SelectItem value="Outro">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Número da Camisa</label>
                <Input
                  type="number"
                  value={transferData.number}
                  onChange={(e) => setTransferData(prev => ({ ...prev, number: e.target.value }))}
                  placeholder="Ex: 10"
                  min="1"
                  max="99"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Data de Entrada</label>
                <Input
                  type="date"
                  value={transferData.entry_date}
                  onChange={(e) => setTransferData(prev => ({ ...prev, entry_date: e.target.value }))}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Fim do Contrato</label>
                <Input
                  type="date"
                  value={transferData.contract_end_date}
                  onChange={(e) => setTransferData(prev => ({ ...prev, contract_end_date: e.target.value }))}
                />
              </div>
              <div className="md:col-span-2">
                <label className="text-sm font-medium">Observações</label>
                <Input
                  value={transferData.observation}
                  onChange={(e) => setTransferData(prev => ({ ...prev, observation: e.target.value }))}
                  placeholder="Observações adicionais..."
                />
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button 
                onClick={handleTransfer} 
                disabled={transferring}
                className="flex-1"
              >
                {transferring ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Processando...
                  </>
                ) : (
                  searchResult?.found ? 'Confirmar Transferência' : 'Cadastrar Jogador'
                )}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowTransferForm(false)}
                disabled={transferring}
              >
                Cancelar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}