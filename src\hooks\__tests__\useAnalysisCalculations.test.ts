import { describe, it, expect, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import {
  useAnalysisCalculations,
  calculateSpatialCoverage,
  calculatePlayerInteraction,
  analyzeTacticalFocus,
  analyzePhysicalDemand,
  analyzeSkillDevelopment
} from '../useAnalysisCalculations';
import { TrainingDrill, TrainingElement } from '@/components/training/InteractiveTrainingBuilder';

// Mock data for testing
const mockDrill: TrainingDrill = {
  id: 'test-drill',
  name: 'Test Drill',
  category: 'technical',
  difficulty: 'medium',
  totalDuration: 600, // 10 minutes
  playersRequired: 8,
  objectives: ['improve passing', 'enhance ball control'],
  steps: [
    { id: '1', description: 'Step 1', duration: 300 },
    { id: '2', description: 'Step 2', duration: 300 }
  ]
};

const mockElements: TrainingElement[] = [
  {
    id: 'player1',
    type: 'player',
    position: { x: 10, y: 10 },
    label: 'Player 1'
  },
  {
    id: 'player2',
    type: 'player',
    position: { x: 50, y: 50 },
    label: 'Player 2'
  },
  {
    id: 'cone1',
    type: 'cone',
    position: { x: 30, y: 30 },
    label: 'Cone 1'
  }
];

describe('useAnalysisCalculations', () => {
  describe('calculateSpatialCoverage', () => {
    it('should return 0 for less than 2 positions', () => {
      expect(calculateSpatialCoverage([])).toBe(0);
      expect(calculateSpatialCoverage([{ x: 10, y: 10 }])).toBe(0);
    });

    it('should calculate coverage for multiple positions', () => {
      const positions = [
        { x: 0, y: 0 },
        { x: 100, y: 100 }
      ];
      const coverage = calculateSpatialCoverage(positions);
      expect(coverage).toBeGreaterThan(0);
      expect(coverage).toBeLessThanOrEqual(100);
    });

    it('should return maximum 100% coverage', () => {
      const positions = [
        { x: 0, y: 0 },
        { x: 1000, y: 1000 }
      ];
      const coverage = calculateSpatialCoverage(positions);
      expect(coverage).toBe(100);
    });
  });

  describe('calculatePlayerInteraction', () => {
    it('should return 0 for less than 2 players', () => {
      const singlePlayer = [mockElements[0]];
      expect(calculatePlayerInteraction(singlePlayer)).toBe(0);
      
      const noPlayers = [mockElements[2]]; // cone only
      expect(calculatePlayerInteraction(noPlayers)).toBe(0);
    });

    it('should calculate interaction for multiple players', () => {
      const players = mockElements.filter(e => e.type === 'player');
      const interaction = calculatePlayerInteraction(players);
      expect(interaction).toBeGreaterThanOrEqual(0);
      expect(interaction).toBeLessThanOrEqual(100);
    });

    it('should return higher interaction for closer players', () => {
      const closePlayers: TrainingElement[] = [
        { id: 'p1', type: 'player', position: { x: 10, y: 10 }, label: 'P1' },
        { id: 'p2', type: 'player', position: { x: 15, y: 15 }, label: 'P2' }
      ];
      
      const farPlayers: TrainingElement[] = [
        { id: 'p1', type: 'player', position: { x: 10, y: 10 }, label: 'P1' },
        { id: 'p2', type: 'player', position: { x: 100, y: 100 }, label: 'P2' }
      ];

      const closeInteraction = calculatePlayerInteraction(closePlayers);
      const farInteraction = calculatePlayerInteraction(farPlayers);
      
      expect(closeInteraction).toBeGreaterThan(farInteraction);
    });
  });

  describe('analyzeTacticalFocus', () => {
    it('should analyze technical category correctly', () => {
      const result = analyzeTacticalFocus(mockDrill);
      expect(result).toHaveProperty('attacking');
      expect(result).toHaveProperty('defending');
      expect(result).toHaveProperty('transition');
      expect(result).toHaveProperty('setpieces');
    });

    it('should prioritize attacking for finishing category', () => {
      const finishingDrill = { ...mockDrill, category: 'finishing' as const };
      const result = analyzeTacticalFocus(finishingDrill);
      expect(result.attacking).toBe(80);
    });

    it('should prioritize defending for tactical category with defense objectives', () => {
      const tacticalDrill = { 
        ...mockDrill, 
        category: 'tactical' as const,
        objectives: ['defesa', 'marcação']
      };
      const result = analyzeTacticalFocus(tacticalDrill);
      expect(result.defending).toBe(70);
    });

    it('should prioritize transition for transition objectives', () => {
      const transitionDrill = { 
        ...mockDrill, 
        objectives: ['transição', 'contra-ataque']
      };
      const result = analyzeTacticalFocus(transitionDrill);
      expect(result.transition).toBe(90);
    });
  });

  describe('analyzePhysicalDemand', () => {
    it('should analyze physical category correctly', () => {
      const physicalDrill = { ...mockDrill, category: 'physical' as const };
      const result = analyzePhysicalDemand(physicalDrill);
      
      expect(result.cardio).toBeGreaterThan(0);
      expect(result.strength).toBe(70);
      expect(result.agility).toBe(60);
    });

    it('should analyze technical category correctly', () => {
      const result = analyzePhysicalDemand(mockDrill); // technical category
      
      expect(result.coordination).toBe(80);
      expect(result.agility).toBe(60);
      expect(result.cardio).toBe(40);
    });

    it('should analyze tactical category correctly', () => {
      const tacticalDrill = { ...mockDrill, category: 'tactical' as const };
      const result = analyzePhysicalDemand(tacticalDrill);
      
      expect(result.cardio).toBe(50);
      expect(result.coordination).toBe(70);
      expect(result.agility).toBe(50);
    });
  });

  describe('analyzeSkillDevelopment', () => {
    it('should return correct skill development for technical category', () => {
      const result = analyzeSkillDevelopment(mockDrill);
      
      expect(result.technical).toBe(90);
      expect(result.tactical).toBe(30);
      expect(result.physical).toBe(40);
      expect(result.mental).toBe(50);
    });

    it('should return correct skill development for tactical category', () => {
      const tacticalDrill = { ...mockDrill, category: 'tactical' as const };
      const result = analyzeSkillDevelopment(tacticalDrill);
      
      expect(result.technical).toBe(40);
      expect(result.tactical).toBe(90);
      expect(result.physical).toBe(50);
      expect(result.mental).toBe(80);
    });

    it('should return default values for unknown category', () => {
      const unknownDrill = { ...mockDrill, category: 'unknown' as any };
      const result = analyzeSkillDevelopment(unknownDrill);
      
      expect(result.technical).toBe(50);
      expect(result.tactical).toBe(50);
      expect(result.physical).toBe(50);
      expect(result.mental).toBe(50);
    });
  });

  describe('useAnalysisCalculations hook', () => {
    it('should return null metrics for null drill', () => {
      const { result } = renderHook(() => useAnalysisCalculations(null, mockElements));
      expect(result.current.metrics).toBeNull();
    });

    it('should return null metrics for empty elements', () => {
      const { result } = renderHook(() => useAnalysisCalculations(mockDrill, []));
      expect(result.current.metrics).toBeNull();
    });

    it('should calculate metrics for valid drill and elements', () => {
      const { result } = renderHook(() => useAnalysisCalculations(mockDrill, mockElements));
      
      expect(result.current.metrics).not.toBeNull();
      expect(result.current.metrics).toHaveProperty('intensity');
      expect(result.current.metrics).toHaveProperty('complexity');
      expect(result.current.metrics).toHaveProperty('spatialCoverage');
      expect(result.current.metrics).toHaveProperty('playerInteraction');
      expect(result.current.metrics).toHaveProperty('tacticalFocus');
      expect(result.current.metrics).toHaveProperty('physicalDemand');
      expect(result.current.metrics).toHaveProperty('skillDevelopment');
    });

    it('should handle errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Create a drill that might cause errors
      const problematicDrill = {
        ...mockDrill,
        totalDuration: 0, // This could cause division by zero
        steps: [] // Empty steps
      };
      
      const { result } = renderHook(() => useAnalysisCalculations(problematicDrill, mockElements));
      
      // Should still return some result, not crash
      expect(result.current.metrics).toBeDefined();
      
      consoleSpy.mockRestore();
    });

    it('should memoize results correctly', () => {
      const { result, rerender } = renderHook(
        ({ drill, elements }) => useAnalysisCalculations(drill, elements),
        { initialProps: { drill: mockDrill, elements: mockElements } }
      );
      
      const firstResult = result.current.metrics;
      
      // Rerender with same props
      rerender({ drill: mockDrill, elements: mockElements });
      
      // Should return the same object reference (memoized)
      expect(result.current.metrics).toBe(firstResult);
    });
  });
});