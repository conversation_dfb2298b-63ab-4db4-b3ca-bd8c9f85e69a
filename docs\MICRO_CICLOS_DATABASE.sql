-- =====================================================
-- SISTEMA DE MICRO CICLOS - ESTRUTURA DO BANCO DE DADOS
-- =====================================================

-- Extensão necessária para geração de UUIDs
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Tabela para armazenar drills de treinamento
CREATE TABLE IF NOT EXISTS training_drills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('tactical', 'technical', 'physical', 'transition', 'finishing')),
    difficulty VARCHAR(20) NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    total_duration INTEGER NOT NULL DEFAULT 0, -- em segundos
    players_required INTEGER NOT NULL DEFAULT 11,
    equipment_needed JSONB DEFAULT '[]'::jsonb,
    objectives JSONB DEFAULT '[]'::jsonb,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar passos/etapas dos drills
CREATE TABLE IF NOT EXISTS drill_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER NOT NULL DEFAULT 0, -- em segundos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(drill_id, step_order)
);

-- Tabela para armazenar elementos de treinamento (cones, bolas, jogadores, etc.)
CREATE TABLE IF NOT EXISTS training_elements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_id UUID NOT NULL REFERENCES drill_steps(id) ON DELETE CASCADE,
    element_type VARCHAR(50) NOT NULL CHECK (element_type IN ('cone', 'ball', 'goal', 'player', 'marker', 'annotation')),
    position_x DECIMAL(5,2) NOT NULL, -- posição em porcentagem (0-100)
    position_y DECIMAL(5,2) NOT NULL, -- posição em porcentagem (0-100)
    properties JSONB DEFAULT '{}'::jsonb, -- propriedades específicas do elemento
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar desenhos/anotações gráficas
CREATE TABLE IF NOT EXISTS training_drawings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_id UUID NOT NULL REFERENCES drill_steps(id) ON DELETE CASCADE,
    drawing_type VARCHAR(50) NOT NULL CHECK (drawing_type IN ('line', 'arrow', 'circle', 'rectangle', 'polygon', 'text', 'freehand')),
    points JSONB NOT NULL, -- array de pontos [{x: number, y: number}]
    properties JSONB DEFAULT '{}'::jsonb, -- cor, espessura, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar trajetórias de movimento
CREATE TABLE IF NOT EXISTS training_trajectories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_id UUID NOT NULL REFERENCES drill_steps(id) ON DELETE CASCADE,
    element_id UUID REFERENCES training_elements(id) ON DELETE CASCADE,
    trajectory_type VARCHAR(50) NOT NULL CHECK (trajectory_type IN ('sprint', 'jog', 'walk', 'pass', 'shot')),
    points JSONB NOT NULL, -- array de pontos com timestamp
    duration INTEGER NOT NULL DEFAULT 0, -- duração em milissegundos
    properties JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar configurações do sistema por usuário
CREATE TABLE IF NOT EXISTS training_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    club_id INTEGER REFERENCES club_info(id) ON DELETE CASCADE,
    settings_category VARCHAR(50) NOT NULL CHECK (settings_category IN ('field', 'ui', 'performance', 'controls', 'export')),
    settings_data JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, club_id, settings_category)
);

-- Tabela para armazenar favoritos de templates
CREATE TABLE IF NOT EXISTS training_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, drill_id)
);

-- Tabela para armazenar histórico de uso de drills
CREATE TABLE IF NOT EXISTS training_usage_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    duration_used INTEGER, -- tempo real usado em segundos
    notes TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar análises táticas automáticas
CREATE TABLE IF NOT EXISTS training_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL CHECK (analysis_type IN ('intensity', 'complexity', 'spatial_coverage', 'player_interaction')),
    score DECIMAL(5,2) NOT NULL, -- pontuação de 0-100
    metrics JSONB DEFAULT '{}'::jsonb, -- métricas detalhadas
    recommendations JSONB DEFAULT '[]'::jsonb, -- recomendações de melhoria
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para consultas frequentes
CREATE INDEX idx_training_drills_club_id ON training_drills(club_id);
CREATE INDEX idx_training_drills_category ON training_drills(category);
CREATE INDEX idx_training_drills_difficulty ON training_drills(difficulty);
CREATE INDEX idx_training_drills_is_template ON training_drills(is_template);
CREATE INDEX idx_training_drills_created_by ON training_drills(created_by);

CREATE INDEX idx_drill_steps_drill_id ON drill_steps(drill_id);
CREATE INDEX idx_drill_steps_order ON drill_steps(drill_id, step_order);

CREATE INDEX idx_training_elements_step_id ON training_elements(step_id);
CREATE INDEX idx_training_elements_type ON training_elements(element_type);

CREATE INDEX idx_training_drawings_step_id ON training_drawings(step_id);
CREATE INDEX idx_training_drawings_type ON training_drawings(drawing_type);

CREATE INDEX idx_training_trajectories_step_id ON training_trajectories(step_id);
CREATE INDEX idx_training_trajectories_element_id ON training_trajectories(element_id);

CREATE INDEX idx_training_settings_user_club ON training_settings(user_id, club_id);
CREATE INDEX idx_training_settings_category ON training_settings(settings_category);

CREATE INDEX idx_training_favorites_user_id ON training_favorites(user_id);
CREATE INDEX idx_training_favorites_drill_id ON training_favorites(drill_id);

CREATE INDEX idx_training_usage_history_drill_id ON training_usage_history(drill_id);
CREATE INDEX idx_training_usage_history_user_id ON training_usage_history(user_id);
CREATE INDEX idx_training_usage_history_club_id ON training_usage_history(club_id);
CREATE INDEX idx_training_usage_history_date ON training_usage_history(session_date);

CREATE INDEX idx_training_analysis_drill_id ON training_analysis(drill_id);
CREATE INDEX idx_training_analysis_type ON training_analysis(analysis_type);

-- =====================================================
-- TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar trigger nas tabelas relevantes
CREATE TRIGGER update_training_drills_updated_at BEFORE UPDATE ON training_drills FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_drill_steps_updated_at BEFORE UPDATE ON drill_steps FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_elements_updated_at BEFORE UPDATE ON training_elements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_drawings_updated_at BEFORE UPDATE ON training_drawings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_trajectories_updated_at BEFORE UPDATE ON training_trajectories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_settings_updated_at BEFORE UPDATE ON training_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_training_analysis_updated_at BEFORE UPDATE ON training_analysis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas tabelas
ALTER TABLE training_drills ENABLE ROW LEVEL SECURITY;
ALTER TABLE drill_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_drawings ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_trajectories ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_usage_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_analysis ENABLE ROW LEVEL SECURITY;

-- Políticas para training_drills
CREATE POLICY "Users can view drills from their club" ON training_drills
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        ) OR is_public = true
    );

CREATE POLICY "Users can create drills for their club" ON training_drills
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own drills" ON training_drills
    FOR UPDATE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM club_members
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'president')
        )
    );

CREATE POLICY "Users can delete their own drills" ON training_drills
    FOR DELETE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM club_members
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'president')
        )
    );

-- Políticas similares para outras tabelas (drill_steps, training_elements, etc.)
-- serão herdadas através das foreign keys

-- =====================================================
-- FUNÇÕES AUXILIARES
-- =====================================================

-- Função para calcular métricas de análise tática
CREATE OR REPLACE FUNCTION calculate_drill_metrics(drill_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    element_count INTEGER;
    spatial_coverage DECIMAL;
    complexity_score DECIMAL;
    result JSONB;
BEGIN
    -- Contar elementos
    SELECT COUNT(*) INTO element_count
    FROM training_elements te
    JOIN drill_steps ds ON te.step_id = ds.id
    WHERE ds.drill_id = drill_uuid;
    
    -- Calcular cobertura espacial (simplificado)
    SELECT COALESCE(
        (MAX(position_x) - MIN(position_x)) * (MAX(position_y) - MIN(position_y)) / 100.0,
        0
    ) INTO spatial_coverage
    FROM training_elements te
    JOIN drill_steps ds ON te.step_id = ds.id
    WHERE ds.drill_id = drill_uuid;
    
    -- Calcular complexidade baseada no número de elementos e passos
    SELECT COALESCE(
        (COUNT(DISTINCT ds.id) * 10 + element_count * 2),
        0
    ) INTO complexity_score
    FROM drill_steps ds
    WHERE ds.drill_id = drill_uuid;
    
    result := jsonb_build_object(
        'element_count', element_count,
        'spatial_coverage', spatial_coverage,
        'complexity_score', LEAST(complexity_score, 100),
        'calculated_at', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar drills com filtros
CREATE OR REPLACE FUNCTION search_training_drills(
    p_club_id INTEGER,
    p_category VARCHAR DEFAULT NULL,
    p_difficulty VARCHAR DEFAULT NULL,
    p_search_term VARCHAR DEFAULT NULL,
    p_is_template BOOLEAN DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    description TEXT,
    category VARCHAR,
    difficulty VARCHAR,
    total_duration INTEGER,
    players_required INTEGER,
    is_template BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    creator_name VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        td.id,
        td.name,
        td.description,
        td.category,
        td.difficulty,
        td.total_duration,
        td.players_required,
        td.is_template,
        td.created_at,
        COALESCE(u.name, 'Sistema') as creator_name
    FROM training_drills td
    LEFT JOIN users u ON td.created_by = u.id
    WHERE 
        (td.club_id = p_club_id OR td.is_public = true)
        AND (p_category IS NULL OR td.category = p_category)
        AND (p_difficulty IS NULL OR td.difficulty = p_difficulty)
        AND (p_is_template IS NULL OR td.is_template = p_is_template)
        AND (
            p_search_term IS NULL OR 
            td.name ILIKE '%' || p_search_term || '%' OR
            td.description ILIKE '%' || p_search_term || '%'
        )
    ORDER BY td.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;
