import { TrendingUp, Users, BookOpen, Download } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface BlogStatsProps {
  className?: string;
}

export function BlogStats({ className = '' }: BlogStatsProps) {
  const stats = [
    {
      icon: BookOpen,
      value: '50+',
      label: '<PERSON><PERSON><PERSON>r<PERSON>',
      color: 'text-blue-600'
    },
    {
      icon: Download,
      value: '10K+',
      label: 'Downloads',
      color: 'text-green-600'
    },
    {
      icon: Users,
      value: '500+',
      label: 'Clubes Atendidos',
      color: 'text-purple-600'
    },
    {
      icon: TrendingUp,
      value: '95%',
      label: 'Taxa de Sucesso',
      color: 'text-orange-600'
    }
  ];

  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}>
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="text-center hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3`}>
                <Icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}