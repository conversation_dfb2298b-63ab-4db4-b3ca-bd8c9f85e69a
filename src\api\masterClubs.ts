import { supabase } from "@/integrations/supabase/client";
import {
  logError,
  handleDatabaseError,
  validateRequired,
  validateEmail,
  validatePositiveNumber
} from "./masterErrorHandler";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";

export interface MasterClub {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  document?: string;
  address?: Record<string, any>;
  logo_url?: string;
  master_plan_id?: number;
  subscription_status: 'active' | 'suspended' | 'cancelled' | 'trial';
  subscription_start_date?: string;
  subscription_end_date?: string;
  payment_status: 'current' | 'overdue' | 'cancelled';
  last_payment_date?: string;
  next_payment_date?: string;
  custom_modules?: Record<string, boolean>;
  usage_limits?: Record<string, any>;
  usage_stats?: Record<string, any>;
  is_trial: boolean;
  trial_start_date?: string;
  trial_end_date?: string;
  grace_period_days: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  suspended_at?: string;
  suspended_by?: string;
  suspension_reason?: string;
  // Relacionamentos
  master_plans?: MasterPlan;
  _count?: {
    users: number;
    players: number;
    matches: number;
  };
}

export interface MasterPlan {
  id: number;
  name: string;
  description?: string;
  price: number;
  billing_cycle: 'monthly' | 'yearly';
  max_users: number;
  max_players: number;
  max_storage_gb: number;
  modules: Record<string, boolean>;
  features: Record<string, any>;
  is_active: boolean;
  is_trial: boolean;
  trial_days: number;
  sort_order: number;
}

export interface CreateClubData {
  name: string;
  email?: string;
  /** Senha definida manualmente para o presidente */
  president_password?: string;
  phone?: string;
  document?: string;
  address?: Record<string, any>;
  master_plan_id: number;
  is_trial?: boolean;
  trial_days?: number;
  notes?: string;
}

export interface UpdateClubData {
  name?: string;
  email?: string;
  phone?: string;
  document?: string;
  address?: Record<string, any>;
  master_plan_id?: number;
  subscription_status?: 'active' | 'suspended' | 'cancelled' | 'trial';
  payment_status?: 'current' | 'overdue' | 'cancelled';
  custom_modules?: Record<string, boolean>;
  usage_limits?: Record<string, any>;
  grace_period_days?: number;
  notes?: string;
}

export interface ClubFilters {
  search?: string;
  subscription_status?: string;
  payment_status?: string;
  master_plan_id?: number;
  is_trial?: boolean;
  created_after?: string;
  created_before?: string;
}

/**
 * Busca clubes com filtros
 */
export const getMasterClubs = async (filters?: ClubFilters): Promise<MasterClub[]> => {
  try {
    await ensureAuthenticated();
    let query = supabase
      .from('club_info')
      .select(`
        *,
        master_plans:master_plan_id (
          id,
          name,
          description,
          price,
          billing_cycle,
          max_users,
          max_players,
          max_storage_gb,
          modules,
          features,
          is_active,
          is_trial,
          trial_days,
          sort_order
        )
      `);

    // Aplicar filtros
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,document.ilike.%${filters.search}%`);
    }

    if (filters?.subscription_status) {
      query = query.eq('subscription_status', filters.subscription_status);
    }

    if (filters?.payment_status) {
      query = query.eq('payment_status', filters.payment_status);
    }

    if (filters?.master_plan_id) {
      query = query.eq('master_plan_id', filters.master_plan_id);
    }

    if (filters?.is_trial !== undefined) {
      query = query.eq('is_trial', filters.is_trial);
    }

    // O campo created_at não existe mais em club_info, então removemos filtros
    // e ordenação por essa coluna

    const { data, error } = await query.order('id', { ascending: false });

    if (error) {
      console.error('Database error in getMasterClubs:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao buscar clubes: ${error.message}`);
    }

    // Add fallback handling for clubs without plans or missing plan data
    const clubsWithFallbacks = (data || []).map(club => ({
      ...club,
      master_plans: club.master_plans || {
        id: 0,
        name: 'Plano não definido',
        description: null,
        price: 0,
        billing_cycle: 'monthly' as const,
        max_users: 0,
        max_players: 0,
        max_storage_gb: 1,
        modules: {},
        features: {},
        is_active: false,
        is_trial: false,
        trial_days: 0,
        sort_order: 999
      }
    }));

    return clubsWithFallbacks;
  } catch (error: any) {
    console.error('Erro ao buscar clubes:', error);
    if (error.message.includes('Campos não encontrados')) {
      throw error; // Re-throw specific field errors
    }
    throw new Error(error.message || 'Erro ao buscar clubes');
  }
};

/**
 * Busca um clube específico por ID
 */
export const getMasterClubById = async (clubId: number): Promise<MasterClub> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('club_info')
      .select(`
        *,
        master_plans:master_plan_id (
          id,
          name,
          description,
          price,
          billing_cycle,
          max_users,
          max_players,
          max_storage_gb,
          modules,
          features,
          is_active,
          is_trial,
          trial_days,
          sort_order
        )
      `)
      .eq('id', clubId)
      .single();

    if (error) {
      console.error('Database error in getMasterClubById:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao buscar clube: ${error.message}`);
    }

    if (!data) {
      throw new Error('Clube não encontrado');
    }

    // Add fallback handling for missing plan data
    const clubWithFallback = {
      ...data,
      master_plans: data.master_plans || {
        id: 0,
        name: 'Plano não definido',
        description: null,
        price: 0,
        billing_cycle: 'monthly' as const,
        max_users: 0,
        max_players: 0,
        max_storage_gb: 1,
        modules: {},
        features: {},
        is_active: false,
        is_trial: false,
        trial_days: 0,
        sort_order: 999
      }
    };

    // Buscar estatísticas de uso
    const [usersCount, playersCount, matchesCount] = await Promise.all([
      getClubUsersCount(clubId),
      getClubPlayersCount(clubId),
      getClubMatchesCount(clubId)
    ]);

    return {
      ...clubWithFallback,
      _count: {
        users: usersCount,
        players: playersCount,
        matches: matchesCount
      }
    };
  } catch (error: any) {
    console.error('Erro ao buscar clube:', error);
    if (error.message.includes('Campos não encontrados')) {
      throw error; // Re-throw specific field errors
    }
    throw new Error(error.message || 'Erro ao buscar clube');
  }
};

/**
 * Gera uma senha temporária segura
 */
const generateTemporaryPassword = (): string => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

/**
 * Cria usuário presidente para o clube
 */
const createPresidentUser = async (
  clubId: number,
  email: string,
  clubName: string,
  password?: string
): Promise<{ email: string; password: string }> => {
  try {
    // Chamar Edge Function para criar usuário presidente
    const { data, error } = await supabase.functions.invoke('create-president-user', {
      body: {
        clubId: clubId,
        email: email,
        clubName: clubName,
        password
      }
    });

    if (error) {
      throw new Error(`Erro na Edge Function: ${error.message}`);
    }

    if (!data.success) {
      throw new Error(`Erro ao criar usuário: ${data.error}`);
    }

    return {
      email: data.email,
      password: data.password
    };

  } catch (error: any) {
    console.error('Erro ao criar usuário presidente:', error);
    throw error;
  }
};

/**
 * Envia email de boas-vindas com credenciais
 */
const sendWelcomeEmail = async (clubName: string, email: string, password: string, planName: string) => {
  try {
    // Usar a função correta do serviço Brevo
    const { sendWelcomeEmail: brevoSendWelcomeEmail } = await import('@/services/brevoEmailService');
    
    await brevoSendWelcomeEmail(email, 'Presidente', password, clubName);
    
    console.log('Email de boas-vindas enviado com sucesso');
  } catch (error) {
    console.error('Erro ao enviar email de boas-vindas:', error);
    // Não falhar a criação do clube se o email falhar
    throw error;
  }
};

/**
 * Reenvia email de boas-vindas para um clube
 */
export const resendWelcomeEmail = async (
  clubId: number,
  newPassword?: string
): Promise<void> => {
  try {
    await ensureAuthenticated();
    // Buscar dados do clube
    const { data: club, error: clubError } = await supabase
      .from('club_info')
      .select(`
        id,
        name,
        email,
        master_plans:master_plan_id (name)
      `)
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error('Clube não encontrado');
    }

    // Buscar membro presidente
    const { data: presidentMember, error: memberError } = await supabase
      .from('club_members')
      .select('user_id')
      .eq('club_id', clubId)
      .eq('role', 'president')
      .single();

    if (memberError || !presidentMember) {
      console.error('Erro ao buscar membro presidente:', memberError);
      throw new Error('Presidente não encontrado no clube');
    }

    // Buscar dados do usuário
    const { data: presidentUser, error: userError } = await supabase
      .from('users')
      .select('email, name')
      .eq('id', presidentMember.user_id)
      .single();

    if (userError || !presidentUser) {
      console.error('Erro ao buscar dados do usuário:', userError);
      throw new Error('Dados do presidente não encontrados');
    }

    /// Gerar nova senha temporária, caso não seja fornecida
    const passwordToUse = newPassword || generateTemporaryPassword();

    // Tentar atualizar senha no Supabase Auth via Edge Function
    try {
      const { error: updateError } = await supabase.functions.invoke('dynamic-function', {
        body: {
          userId: presidentMember.user_id,
          password: passwordToUse
        }
      });

      if (updateError) {
        console.warn('Não foi possível atualizar a senha via Edge Function:', updateError);
        throw new Error(`Erro ao atualizar senha: ${updateError.message}`);
      }
    } catch (updateError) {
      console.error('Erro ao chamar Edge Function de atualização de senha:', updateError);
      throw updateError;
    }

    // Enviar email
    await sendWelcomeEmail(
      club.name,
      presidentUser.email,
      passwordToUse,
      club.master_plans?.name || 'Plano não definido'
    );

   // Log da ação (requer autenticação)
   const {
    data: { user }
  } = await supabase.auth.getUser();
  if (!user) throw new Error('Usuário não autenticado');

  await supabase
    .from('master_audit_logs')
    .insert({
      user_id: user.id,
        action: 'resend_welcome_email',
        entity_type: 'club',
        entity_id: clubId,
        details: {
          message: 'Email de boas-vindas reenviado',
          recipient: presidentUser.email
        }
      });

  } catch (error: any) {
    console.error('Erro ao reenviar email:', error);
    throw new Error(error.message || 'Erro ao reenviar email');
  }
};

/**
 * Redefine a senha do presidente de um clube
 */
export const resetPresidentPassword = async (
  clubId: number,
  newPassword: string,
  sendEmail: boolean = true
): Promise<void> => {
  try {
    await ensureAuthenticated();
    
    // Buscar dados do clube
    const { data: club, error: clubError } = await supabase
      .from('club_info')
      .select(`
        id,
        name,
        email,
        master_plans:master_plan_id (name)
      `)
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error('Clube não encontrado');
    }

    // Buscar membro presidente
    const { data: presidentMember, error: memberError } = await supabase
      .from('club_members')
      .select('user_id')
      .eq('club_id', clubId)
      .eq('role', 'president')
      .single();

    if (memberError || !presidentMember) {
      console.error('Erro ao buscar membro presidente:', memberError);
      throw new Error('Presidente não encontrado no clube');
    }

    // Buscar dados do usuário
    const { data: presidentUser, error: userError } = await supabase
      .from('users')
      .select('email, name')
      .eq('id', presidentMember.user_id)
      .single();

    if (userError || !presidentUser) {
      console.error('Erro ao buscar dados do usuário:', userError);
      throw new Error('Dados do presidente não encontrados');
    }

    // Atualizar senha no Supabase Auth via Edge Function
    const { error: updateError } = await supabase.functions.invoke('dynamic-function', {
      body: {
        userId: presidentMember.user_id,
        password: newPassword
      }
    });

    if (updateError) {
      console.error('Erro ao atualizar senha via Edge Function:', updateError);
      throw new Error(`Erro ao atualizar senha: ${updateError.message}`);
    }

    // Enviar email se solicitado
    if (sendEmail) {
      await sendWelcomeEmail(
        club.name,
        presidentUser.email,
        newPassword,
        club.master_plans?.name || 'Plano não definido'
      );
    }

    // Log da ação
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Usuário não autenticado');

    await supabase
      .from('master_audit_logs')
      .insert({
        user_id: user.id,
        action: 'reset_president_password',
        entity_type: 'club',
        entity_id: clubId,
        details: {
          message: 'Senha do presidente redefinida',
          recipient: presidentUser.email,
          email_sent: sendEmail
        }
      });

  } catch (error: any) {
    console.error('Erro ao redefinir senha:', error);
    throw new Error(error.message || 'Erro ao redefinir senha');
  }
};

/**
 * Cria um novo clube com usuário presidente
 */
export const createMasterClub = async (clubData: CreateClubData): Promise<MasterClub> => {
  try {
    await ensureAuthenticated();
    // Data validation
    validateRequired(clubData, ['name', 'master_plan_id']);
    validatePositiveNumber(clubData.master_plan_id, 'ID do plano');

    if (clubData.email) {
      validateEmail(clubData.email);
    }

    // Buscar dados do plano
    const { data: plan, error: planError } = await supabase
      .from('master_plans')
      .select(`
        id,
        name,
        description,
        price,
        billing_cycle,
        max_users,
        max_players,
        max_storage_gb,
        modules,
        features,
        is_active,
        is_trial,
        trial_days,
        sort_order
      `)
      .eq('id', clubData.master_plan_id)
      .single();

    if (planError) {
      console.error('Error fetching plan in createMasterClub:', planError);
      if (planError.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao buscar plano: ${planError.message}`);
    }

    if (!plan) {
      throw new Error('Plano não encontrado');
    }

    // Calcular datas
    const now = new Date();
    const subscriptionStartDate = now.toISOString().split('T')[0];
    let subscriptionStatus: 'active' | 'trial' = 'active';
    let trialEndDate: string | undefined;
    let nextPaymentDate: string | undefined;

    if (clubData.is_trial || plan.is_trial) {
      subscriptionStatus = 'trial';
      const trialDays = clubData.trial_days || plan.trial_days || 30;
      const trialEnd = new Date(now);
      trialEnd.setDate(trialEnd.getDate() + trialDays);
      trialEndDate = trialEnd.toISOString().split('T')[0];
      nextPaymentDate = trialEndDate;
    } else {
      // Calcular próxima data de pagamento
      const nextPayment = new Date(now);
      if (plan.billing_cycle === 'yearly') {
        nextPayment.setFullYear(nextPayment.getFullYear() + 1);
      } else {
        nextPayment.setMonth(nextPayment.getMonth() + 1);
      }
      nextPaymentDate = nextPayment.toISOString().split('T')[0];
    }

    const { data, error } = await supabase
      .from('club_info')
      .insert({
        name: clubData.name,
        email: clubData.email,
        phone: clubData.phone,
        document: clubData.document,
        address: clubData.address || {},
        master_plan_id: clubData.master_plan_id,
        subscription_status: subscriptionStatus,
        subscription_start_date: subscriptionStartDate,
        payment_status: 'current',
        next_payment_date: nextPaymentDate,
        is_trial: clubData.is_trial || plan.is_trial,
        trial_start_date: subscriptionStatus === 'trial' ? subscriptionStartDate : undefined,
        trial_end_date: trialEndDate,
        grace_period_days: 7,
        notes: clubData.notes,
        created_by: (await supabase.auth.getUser()).data.user?.id
      })
      .select(`
        *,
        master_plans:master_plan_id (
          id,
          name,
          description,
          price,
          billing_cycle,
          max_users,
          max_players,
          max_storage_gb,
          modules,
          features,
          is_active,
          is_trial,
          trial_days,
          sort_order
        )
      `)
      .single();

    if (error) {
      throw new Error(`Erro ao criar clube: ${error.message}`);
    }

    // 4. Criar usuário presidente se email foi fornecido
    if (clubData.email) {
      try {
        const credentials = await createPresidentUser(
          data.id,
          clubData.email,
          data.name,
          clubData.president_password
        );
        // 5. Enviar email de boas-vindas
        await sendWelcomeEmail(
          data.name,
          credentials.email,
          credentials.password,
          plan.name
        );

        // 6. Log da criação
        const {
          data: { user: currentUser }
        } = await supabase.auth.getUser();
        if (!currentUser) throw new Error('Usuário não autenticado');

        await supabase
          .from('master_audit_logs')
          .insert({
            user_id: currentUser.id,          
            action: 'create_club_with_president',
            entity_type: 'club',
            entity_id: data.id,
            new_values: {
              club_name: data.name,
              president_email: credentials.email,
              plan_name: plan.name
            },
            details: {
              message: 'Clube criado com usuário presidente e email enviado'
            }
          });

        console.log(`Clube ${data.name} criado com sucesso. Credenciais enviadas para ${credentials.email}`);
      } catch (userError: any) {
        console.error('Erro ao criar usuário presidente:', userError);
        // Clube foi criado, mas usuário falhou - adicionar nota
        await supabase
          .from('club_info')
          .update({
            notes: `${clubData.notes || ''}\n\n⚠️ ATENÇÃO: Erro ao criar usuário presidente automaticamente. Criar manualmente.\nErro: ${userError.message}`
          })
          .eq('id', data.id);
      }
    }

    return data;
  } catch (error: any) {
    console.error('Erro ao criar clube:', error);
    throw new Error(error.message || 'Erro ao criar clube');
  }
};

/**
 * Atualiza um clube
 */
export const updateMasterClub = async (clubId: number, updates: UpdateClubData): Promise<MasterClub> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('club_info')
      .update(updates)
      .eq('id', clubId)
      .select(`
        *,
        master_plans:master_plan_id (
          id,
          name,
          description,
          price,
          billing_cycle,
          max_users,
          max_players,
          max_storage_gb,
          modules,
          features,
          is_active,
          is_trial,
          trial_days,
          sort_order
        )
      `)
      .single();

    if (error) {
      console.error('Database error in updateMasterClub:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao atualizar clube: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error('Erro ao atualizar clube:', error);
    throw new Error(error.message || 'Erro ao atualizar clube');
  }
};

/**
 * Suspende um clube
 */
export const suspendMasterClub = async (clubId: number, reason?: string): Promise<void> => {
  try {
    const { error } = await supabase.rpc('suspend_club_for_non_payment', {
      p_club_id: clubId
    });

    if (error) {
      throw new Error(`Erro ao suspender clube: ${error.message}`);
    }

    // Se uma razão específica foi fornecida, atualizar
    if (reason) {
      await supabase
        .from('club_info')
        .update({ suspension_reason: reason })
        .eq('id', clubId);
    }
  } catch (error: any) {
    console.error('Erro ao suspender clube:', error);
    throw new Error(error.message || 'Erro ao suspender clube');
  }
};

/**
 * Reativa um clube
 */
export const reactivateMasterClub = async (clubId: number): Promise<void> => {
  try {
    await ensureAuthenticated();
    const { error } = await supabase.rpc('reactivate_club', {
      p_club_id: clubId
    });

    if (error) {
      throw new Error(`Erro ao reativar clube: ${error.message}`);
    }
  } catch (error: any) {
    console.error('Erro ao reativar clube:', error);
    throw new Error(error.message || 'Erro ao reativar clube');
  }
};

/**
 * Altera o plano de um clube
 */
export const changeClubPlan = async (
  clubId: number,
  newPlanId: number,
  reason?: string
): Promise<void> => {
  try {
    await ensureAuthenticated();
    const { error } = await supabase.rpc('change_club_plan', {
      p_club_id: clubId,
      p_new_plan_id: newPlanId,
      p_change_reason: reason || 'manual_change'
    });

    if (error) {
      throw new Error(`Erro ao alterar plano: ${error.message}`);
    }
  } catch (error: any) {
    console.error('Erro ao alterar plano:', error);
    throw new Error(error.message || 'Erro ao alterar plano');
  }
};

/**
 * Exclui um clube (soft delete)
 */
export const deleteMasterClub = async (clubId: number): Promise<void> => {
  try {
    await ensureAuthenticated();
    // Primeiro, cancelar assinatura
    await supabase
      .from('club_info')
      .update({
        subscription_status: 'cancelled',
        payment_status: 'cancelled'
      })
      .eq('id', clubId);

    // Nota: Implementar soft delete ou mover para tabela de arquivos
    // Por enquanto, apenas cancelamos a assinatura
  } catch (error: any) {
    console.error('Erro ao excluir clube:', error);
    throw new Error(error.message || 'Erro ao excluir clube');
  }
};

/**
 * Busca planos disponíveis
 */
export const getAvailablePlans = async (): Promise<MasterPlan[]> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('master_plans')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      throw new Error(`Erro ao buscar planos: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error('Erro ao buscar planos:', error);
    throw new Error(error.message || 'Erro ao buscar planos');
  }
};

// Funções auxiliares para estatísticas
const getClubUsersCount = async (clubId: number): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('club_members')
      .select('*', { count: 'exact', head: true })
      .eq('club_id', clubId)
      .eq('status', 'ativo');

    if (error) {
      console.error('Erro ao contar usuários:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Erro ao contar usuários:', error);
    return 0;
  }
};

const getClubPlayersCount = async (clubId: number): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('players')
      .select('*', { count: 'exact', head: true })
      .eq('club_id', clubId);

    if (error) {
      console.error('Erro ao contar jogadores:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Erro ao contar jogadores:', error);
    return 0;
  }
};

const getClubMatchesCount = async (clubId: number): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('matches')
      .select('*', { count: 'exact', head: true })
      .eq('club_id', clubId);

    if (error) {
      console.error('Erro ao contar partidas:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Erro ao contar partidas:', error);
    return 0;
  }
};

/**
 * Busca estatísticas de uso de um clube
 */
export const getClubUsageStats = async (clubId: number) => {
  try {
    await ensureAuthenticated();
    const [usersCount, playersCount, matchesCount] = await Promise.all([
      getClubUsersCount(clubId),
      getClubPlayersCount(clubId),
      getClubMatchesCount(clubId)
    ]);

    // Buscar limites do plano
    const { data: club } = await supabase
      .from('club_info')
      .select(`
        master_plans:master_plan_id (
          max_users,
          max_players,
          max_storage_gb,
          features
        ),
        usage_limits
      `)
      .eq('id', clubId)
      .single();

    const planLimits = club?.master_plans?.features || {};
    const customLimits = club?.usage_limits || {};

    // Combinar limites (custom sobrescreve plano)
    const limits = { ...planLimits, ...customLimits };

    return {
      usage: {
        users: usersCount,
        players: playersCount,
        matches: matchesCount
      },
      limits: {
        max_users: limits.max_users || club?.master_plans?.max_users || -1,
        max_players: limits.max_players || club?.master_plans?.max_players || -1,
        max_storage_gb: limits.max_storage_gb || club?.master_plans?.max_storage_gb || -1,
        max_matches_per_month: limits.max_matches_per_month || -1
      },
      percentages: {
        users: limits.max_users > 0 ? (usersCount / limits.max_users) * 100 : 0,
        players: limits.max_players > 0 ? (playersCount / limits.max_players) * 100 : 0
      }
    };
  } catch (error: any) {
    console.error('Erro ao buscar estatísticas de uso:', error);
    return {
      usage: { users: 0, players: 0, matches: 0 },
      limits: { max_users: -1, max_players: -1, max_storage_gb: -1, max_matches_per_month: -1 },
      percentages: { users: 0, players: 0 }
    };
  }
};