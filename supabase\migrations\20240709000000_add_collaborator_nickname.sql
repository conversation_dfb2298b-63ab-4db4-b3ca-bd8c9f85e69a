-- Add nickname column to collaborators table
ALTER TABLE collaborators
ADD COLUMN IF NOT EXISTS nickname TEXT;

-- Update collaborators_view to include nickname
DROP VIEW IF EXISTS collaborators_view;
CREATE VIEW collaborators_view AS
SELECT
  c.*, 
  u.name as user_name,
  u.email as user_email
FROM collaborators c
LEFT JOIN users u ON c.user_id = u.id;

COMMENT ON COLUMN collaborators.nickname IS 'Apelido do colaborador';