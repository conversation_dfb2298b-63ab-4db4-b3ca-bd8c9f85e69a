import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Users, DollarSign, <PERSON>ting<PERSON>, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { CreatePlanModal } from '@/components/master/CreatePlanModal';
import { EditPlanModal } from '@/components/master/EditPlanModal';
import { 
  getMasterPlans, 
  deleteMasterPlan, 
  togglePlanStatus, 
  getPlanStats,
  type MasterPlan,
  AVAILABLE_MODULES 
} from '@/api/masterPlans';
import { toast } from 'sonner';

export const Plans: React.FC = () => {
  const [plans, setPlans] = useState<MasterPlan[]>([]);
  const [planStats, setPlanStats] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<MasterPlan | null>(null);
  const [planToDelete, setPlanToDelete] = useState<MasterPlan | null>(null);

  useEffect(() => {
    loadPlans();
    loadPlanStats();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const data = await getMasterPlans();
      setPlans(data);
    } catch (error: any) {
      console.error('Erro ao carregar planos:', error);
      toast.error('Erro ao carregar planos');
    } finally {
      setLoading(false);
    }
  };

  const loadPlanStats = async () => {
    try {
      const stats = await getPlanStats();
      setPlanStats(stats);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  const handleCreateSuccess = () => {
    loadPlans();
    loadPlanStats();
  };

  const handleEditSuccess = () => {
    loadPlans();
    loadPlanStats();
  };

  const handleEdit = (plan: MasterPlan) => {
    setSelectedPlan(plan);
    setShowEditModal(true);
  };

  const handleDelete = async () => {
    if (!planToDelete) return;

    try {
      await deleteMasterPlan(planToDelete.id);
      toast.success('Plano excluído com sucesso!');
      loadPlans();
      loadPlanStats();
    } catch (error: any) {
      console.error('Erro ao excluir plano:', error);
      toast.error(error.message || 'Erro ao excluir plano');
    } finally {
      setPlanToDelete(null);
    }
  };

  const handleToggleStatus = async (plan: MasterPlan) => {
    try {
      await togglePlanStatus(plan.id, !plan.is_active);
      toast.success(`Plano ${!plan.is_active ? 'ativado' : 'desativado'} com sucesso!`);
      loadPlans();
    } catch (error: any) {
      console.error('Erro ao alterar status do plano:', error);
      toast.error(error.message || 'Erro ao alterar status do plano');
    }
  };

  const formatPrice = (price: number, cycle: string) => {
    const formatted = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
    
    return `${formatted}/${cycle === 'monthly' ? 'mês' : 'ano'}`;
  };

  const getActiveModulesCount = (modules: Record<string, boolean>) => {
    return Object.values(modules).filter(Boolean).length;
  };

  const getClubCount = (planName: string) => {
    return planStats[planName] || 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando planos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestão de Planos</h1>
          <p className="text-gray-600 mt-1">
            Gerencie os planos disponíveis para os clubes
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Novo Plano
        </Button>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Planos</p>
                <p className="text-2xl font-bold">{plans.length}</p>
              </div>
              <Settings className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Planos Ativos</p>
                <p className="text-2xl font-bold">{plans.filter(p => p.is_active).length}</p>
              </div>
              <Eye className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Clubes Ativos</p>
                <p className="text-2xl font-bold">{Object.values(planStats).reduce((a, b) => a + b, 0)}</p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Receita Potencial</p>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(
                    plans.reduce((total, plan) => {
                      const clubCount = getClubCount(plan.name);
                      return total + (plan.price * clubCount);
                    }, 0)
                  )}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Planos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card key={plan.id} className={`relative ${!plan.is_active ? 'opacity-60' : ''}`}>
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={plan.is_active ? 'default' : 'secondary'}>
                      {plan.is_active ? 'Ativo' : 'Inativo'}
                    </Badge>
                    <Badge variant="outline">
                      {getClubCount(plan.name)} clubes
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPrice(plan.price, plan.billing_cycle)}
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-gray-600 text-sm">{plan.description}</p>

              {/* Limites */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Usuários:</span>
                  <span className="font-medium">
                    {plan.max_users ? plan.max_users : 'Ilimitado'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Atletas:</span>
                  <span className="font-medium">
                    {plan.max_players ? plan.max_players : 'Ilimitado'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Módulos:</span>
                  <span className="font-medium">
                    {getActiveModulesCount(plan.modules)} de {Object.keys(AVAILABLE_MODULES).length}
                  </span>
                </div>
              </div>

              {/* Recursos Especiais */}
              {plan.features && Object.keys(plan.features).length > 0 && (
                <div className="pt-2 border-t">
                  <p className="text-xs text-gray-500 mb-2">Recursos inclusos:</p>
                  <div className="flex flex-wrap gap-1">
                    {plan.features.custom_branding && (
                      <Badge variant="outline" className="text-xs">Marca Própria</Badge>
                    )}
                    {plan.features.priority_support && (
                      <Badge variant="outline" className="text-xs">Suporte VIP</Badge>
                    )}
                    {plan.features.advanced_reports && (
                      <Badge variant="outline" className="text-xs">Relatórios+</Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Ações */}
              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(plan)}
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Editar
                </Button>
                <Button
                  variant={plan.is_active ? "secondary" : "default"}
                  size="sm"
                  onClick={() => handleToggleStatus(plan)}
                >
                  {plan.is_active ? 'Desativar' : 'Ativar'}
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => setPlanToDelete(plan)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modals */}
      <CreatePlanModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />

      <EditPlanModal
        isOpen={showEditModal}
        plan={selectedPlan}
        onClose={() => {
          setShowEditModal(false);
          setSelectedPlan(null);
        }}
        onSuccess={handleEditSuccess}
      />

      {/* Dialog de Confirmação de Exclusão */}
      <AlertDialog open={!!planToDelete} onOpenChange={() => setPlanToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o plano "{planToDelete?.name}"? 
              Esta ação não pode ser desfeita.
              {getClubCount(planToDelete?.name || '') > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                  <strong>Atenção:</strong> Este plano está sendo usado por {getClubCount(planToDelete?.name || '')} clube(s).
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};