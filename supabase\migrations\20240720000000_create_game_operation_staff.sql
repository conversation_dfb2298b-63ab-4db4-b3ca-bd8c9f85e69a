-- Create table for game operation staff
CREATE TABLE IF NOT EXISTS public.game_operation_staff (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  birth_date DATE,
  cpf TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_game_operation_staff_club ON public.game_operation_staff(club_id);

ALTER TABLE public.game_operation_staff ENABLE ROW LEVEL SECURITY;

CREATE POLICY game_operation_staff_select ON public.game_operation_staff
  FOR SELECT
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_staff_insert ON public.game_operation_staff
  FOR INSERT
  WITH CHECK (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_staff_update ON public.game_operation_staff
  FOR UPDATE
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_staff_delete ON public.game_operation_staff
  FOR DELETE
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

-- Table linking staff to callups
CREATE TABLE IF NOT EXISTS public.callup_operation_staff (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
  callup_id INTEGER NOT NULL REFERENCES public.callups(id) ON DELETE CASCADE,
  staff_id INTEGER REFERENCES public.game_operation_staff(id) ON DELETE SET NULL,
  name TEXT,
  role TEXT,
  birth_date DATE,
  cpf TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_callup_operation_staff_callup ON public.callup_operation_staff(callup_id);
CREATE INDEX IF NOT EXISTS idx_callup_operation_staff_club ON public.callup_operation_staff(club_id);

ALTER TABLE public.callup_operation_staff ENABLE ROW LEVEL SECURITY;

CREATE POLICY callup_operation_staff_select ON public.callup_operation_staff
  FOR SELECT
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY callup_operation_staff_insert ON public.callup_operation_staff
  FOR INSERT
  WITH CHECK (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY callup_operation_staff_delete ON public.callup_operation_staff
  FOR DELETE
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));
