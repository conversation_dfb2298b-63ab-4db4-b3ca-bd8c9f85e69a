# Guia de Implementação - Sistema de Transferência de Atletas

## Visão Geral

Este guia detalha como implementar o sistema de transferência de atletas no Game Day Nexus Platform. O sistema permite que jogadores sejam transferidos entre clubes mantendo seus dados pessoais e documentos, evitando duplicação e facilitando o processo.

## Pré-requisitos

- Sistema Game Day Nexus funcionando
- Acesso ao banco de dados PostgreSQL
- Permissões de administrador
- Backup do banco de dados (recomendado)

## Etapa 1: Criação das Tabelas e Funções

### 1.1 Executar Script Principal
```sql
-- Executar o arquivo: sql/create-player-transfer-system.sql
\i sql/create-player-transfer-system.sql
```

Este script criará:
- Tabela `global_players` (dados globais dos jogadores)
- Tabela `global_player_documents` (documentos compartilhados)
- Tabela `player_transfers` (histórico de transferências)
- Funções RPC para busca e transferência
- Políticas RLS para segurança
- Índices para performance

### 1.2 Verificar Criação
```sql
-- Verificar se as tabelas foram criadas
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('global_players', 'global_player_documents', 'player_transfers');

-- Verificar se as funções foram criadas
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%player%transfer%';
```

## Etapa 2: Migração de Dados Existentes

### 2.1 Executar Script de Migração
```sql
-- Executar o arquivo: sql/migrate-existing-players.sql
\i sql/migrate-existing-players.sql
```

### 2.2 Verificar Status da Migração
```sql
-- Verificar status atual
SELECT * FROM check_migration_status();

-- Encontrar CPFs duplicados
SELECT * FROM find_duplicate_cpf_players();
```

### 2.3 Resolver Duplicatas (se necessário)
```sql
-- Para cada CPF duplicado, escolher qual jogador manter
-- Exemplo: manter jogador com ID 'uuid-do-jogador-principal'
SELECT * FROM resolve_duplicate_cpf('12345678901', 'uuid-do-jogador-principal');
```

### 2.4 Executar Migração Completa
```sql
-- Migrar todos os jogadores
SELECT * FROM migrate_all_players_to_global();
```

## Etapa 3: Implementação da API

### 3.1 Adicionar Arquivo da API
Copiar o arquivo `src/api/playerTransfers.ts` para o projeto.

### 3.2 Atualizar Importações
```typescript
// Em src/api/api.ts, adicionar:
export * from './playerTransfers';
```

### 3.3 Criar Hook Personalizado
Copiar o arquivo `src/hooks/usePlayerTransfers.ts` para o projeto.

## Etapa 4: Implementação da Interface

### 4.1 Componente de Busca por CPF
Copiar o arquivo `src/components/player/PlayerTransferSearch.tsx` para o projeto.

### 4.2 Integrar no Cadastro de Jogadores
```typescript
// Em src/pages/PlayersPage.tsx ou similar
import { PlayerTransferSearch } from '@/components/player/PlayerTransferSearch';

// Adicionar o componente na página
<PlayerTransferSearch onPlayerTransferred={handlePlayerAdded} />
```

### 4.3 Gerenciador de Migração (Opcional)
Para administradores, copiar `src/components/admin/PlayerMigrationManager.tsx`.

## Etapa 5: Configuração de Permissões

### 5.1 Adicionar Permissões
```sql
-- Adicionar permissões para transferência de jogadores
INSERT INTO permissions (name, description, category) VALUES
('player_transfer.search', 'Buscar jogadores por CPF', 'players'),
('player_transfer.initiate', 'Iniciar transferência de jogador', 'players'),
('player_transfer.view_history', 'Ver histórico de transferências', 'players'),
('player_transfer.manage_migration', 'Gerenciar migração do sistema', 'admin');
```

### 5.2 Atualizar Constantes de Permissões
```typescript
// Em src/constants/permissions.ts
export const PLAYER_TRANSFER_PERMISSIONS = {
  SEARCH: 'player_transfer.search',
  INITIATE: 'player_transfer.initiate',
  VIEW_HISTORY: 'player_transfer.view_history',
  MANAGE_MIGRATION: 'player_transfer.manage_migration'
} as const;
```

## Etapa 6: Testes

### 6.1 Teste de Busca por CPF
```typescript
// Teste básico
import { searchPlayerByCPF } from '@/api/playerTransfers';

const testSearch = async () => {
  const result = await searchPlayerByCPF('12345678901');
  console.log('Resultado da busca:', result);
};
```

### 6.2 Teste de Transferência
```typescript
// Teste de transferência
import { initiatePlayerTransfer } from '@/api/playerTransfers';

const testTransfer = async () => {
  const result = await initiatePlayerTransfer(
    '12345678901',
    2, // ID do clube de destino
    {
      name: 'João Silva',
      position: 'Atacante',
      number: 10,
      // ... outros dados
    },
    'user-id'
  );
  console.log('Resultado da transferência:', result);
};
```

### 6.3 Verificar Cópia de Documentos
```sql
-- Verificar se documentos foram copiados
SELECT 
  gp.name,
  gp.cpf_number,
  COUNT(gpd.id) as document_count,
  COUNT(pd.id) as club_document_count
FROM global_players gp
LEFT JOIN global_player_documents gpd ON gpd.global_player_id = gp.id
LEFT JOIN players p ON p.global_player_id = gp.id
LEFT JOIN player_documents pd ON pd.player_id = p.id
GROUP BY gp.id, gp.name, gp.cpf_number;
```

## Etapa 7: Monitoramento e Manutenção

### 7.1 Queries de Monitoramento
```sql
-- Status geral do sistema
SELECT * FROM check_migration_status();

-- Transferências recentes
SELECT 
  pt.*,
  gp.name as player_name,
  ci_from.name as from_club,
  ci_to.name as to_club
FROM player_transfers pt
JOIN global_players gp ON gp.id = pt.global_player_id
LEFT JOIN club_info ci_from ON ci_from.id = pt.from_club_id
JOIN club_info ci_to ON ci_to.id = pt.to_club_id
ORDER BY pt.created_at DESC
LIMIT 10;

-- Jogadores com mais transferências
SELECT 
  gp.name,
  gp.cpf_number,
  COUNT(pt.id) as transfer_count
FROM global_players gp
JOIN player_transfers pt ON pt.global_player_id = gp.id
GROUP BY gp.id, gp.name, gp.cpf_number
ORDER BY transfer_count DESC
LIMIT 10;
```

### 7.2 Limpeza Periódica
```sql
-- Limpar transferências antigas (mais de 2 anos)
DELETE FROM player_transfers 
WHERE created_at < NOW() - INTERVAL '2 years' 
AND status = 'completed';

-- Limpar documentos órfãos
DELETE FROM global_player_documents 
WHERE global_player_id NOT IN (SELECT id FROM global_players);
```

## Etapa 8: Backup e Rollback

### 8.1 Backup Antes da Implementação
```bash
# Backup completo
pg_dump -h localhost -U postgres -d game_day_nexus > backup_before_transfer_system.sql

# Backup apenas das tabelas de jogadores
pg_dump -h localhost -U postgres -d game_day_nexus -t players -t player_documents > backup_players.sql
```

### 8.2 Rollback (se necessário)
```sql
-- ATENÇÃO: Use apenas em caso de emergência
SELECT cleanup_transfer_system();

-- Remover colunas adicionadas
ALTER TABLE players 
DROP COLUMN IF EXISTS global_player_id,
DROP COLUMN IF EXISTS is_transfer,
DROP COLUMN IF EXISTS transfer_id;

-- Remover tabelas
DROP TABLE IF EXISTS player_transfers CASCADE;
DROP TABLE IF EXISTS global_player_documents CASCADE;
DROP TABLE IF EXISTS global_players CASCADE;
```

## Troubleshooting

### Problema: Erro de permissão RLS
**Solução**: Verificar se as políticas RLS estão corretas:
```sql
SELECT * FROM pg_policies WHERE tablename IN ('global_players', 'global_player_documents', 'player_transfers');
```

### Problema: CPF duplicado na migração
**Solução**: Usar a função de resolução de duplicatas:
```sql
SELECT * FROM find_duplicate_cpf_players();
-- Para cada duplicata, executar:
SELECT * FROM resolve_duplicate_cpf('CPF', 'ID_DO_JOGADOR_A_MANTER');
```

### Problema: Documentos não copiados
**Solução**: Verificar permissões do storage e executar cópia manual:
```typescript
import { copyPlayerDocuments } from '@/api/playerTransfers';
await copyPlayerDocuments(globalPlayerId, newPlayerId, clubId, documents);
```

### Problema: Performance lenta
**Solução**: Verificar índices:
```sql
-- Recriar índices se necessário
REINDEX TABLE global_players;
REINDEX TABLE global_player_documents;
REINDEX TABLE player_transfers;
```

## Considerações de Segurança

1. **RLS Ativo**: Sempre manter Row Level Security ativo
2. **Validação de CPF**: Sempre validar CPF antes de operações
3. **Auditoria**: Manter logs de todas as transferências
4. **Backup Regular**: Fazer backup antes de operações em massa
5. **Permissões**: Restringir acesso às funções administrativas

## Próximos Passos

1. Implementar notificações de transferência
2. Criar relatórios de transferências
3. Implementar aprovação de transferências
4. Adicionar histórico detalhado
5. Criar API para clubes externos

## Suporte

Para dúvidas ou problemas:
1. Verificar logs do sistema
2. Consultar documentação do Supabase
3. Revisar políticas RLS
4. Verificar permissões do usuário
5. Contatar suporte técnico se necessário