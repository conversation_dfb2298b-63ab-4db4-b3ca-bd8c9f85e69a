import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useCurrentClubId } from '@/context/ClubContext';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Settings, 
  DollarSign, 
  Calendar,
  Mail,
  Percent,
  CreditCard
} from 'lucide-react';
import {
  getMonthlyFeeSettings,
  createMonthlyFeeSetting,
  updateMonthlyFeeSetting,
  deleteMonthlyFeeSetting,
  MonthlyFeeSetting,
  CreateMonthlyFeeSettingData
} from '@/api/monthlyFees';
import { getCategories } from '@/api/api';

interface Category {
  id: number;
  name: string;
}

export function MonthlyFeeSettingsManager() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<MonthlyFeeSetting[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSetting, setEditingSetting] = useState<MonthlyFeeSetting | null>(null);
  
  const [formData, setFormData] = useState<CreateMonthlyFeeSettingData>({
    name: '',
    amount: 0,
    due_day: 5,
    reminder_days_before: 3,
    late_fee_percentage: 0,
    discount_percentage: 0,
    discount_days_before: 0,
    pix_key: '',
    description: ''
  });

  useEffect(() => {
    loadData();
  }, [clubId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [settingsData, categoriesData] = await Promise.all([
        getMonthlyFeeSettings(clubId),
        getCategories(clubId)
      ]);
      
      setSettings(settingsData);
      setCategories(categoriesData);
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as configurações',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      if (!formData.name.trim()) {
        toast({
          title: 'Erro',
          description: 'Nome é obrigatório',
          variant: 'destructive'
        });
        return;
      }

      if (formData.amount <= 0) {
        toast({
          title: 'Erro',
          description: 'Valor deve ser maior que zero',
          variant: 'destructive'
        });
        return;
      }

      if (formData.due_day < 1 || formData.due_day > 31) {
        toast({
          title: 'Erro',
          description: 'Dia de vencimento deve estar entre 1 e 31',
          variant: 'destructive'
        });
        return;
      }

      if (editingSetting) {
        await updateMonthlyFeeSetting(clubId, editingSetting.id, formData);
        toast({
          title: 'Sucesso',
          description: 'Configuração atualizada com sucesso'
        });
      } else {
        await createMonthlyFeeSetting(clubId, formData);
        toast({
          title: 'Sucesso',
          description: 'Configuração criada com sucesso'
        });
      }

      setIsDialogOpen(false);
      resetForm();
      loadData();
    } catch (error: any) {
      console.error('Erro ao salvar configuração:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao salvar configuração',
        variant: 'destructive'
      });
    }
  };

  const handleEdit = (setting: MonthlyFeeSetting) => {
    setEditingSetting(setting);
    setFormData({
      category_id: setting.category_id || undefined,
      name: setting.name,
      amount: setting.amount,
      due_day: setting.due_day,
      reminder_days_before: setting.reminder_days_before,
      late_fee_percentage: setting.late_fee_percentage,
      discount_percentage: setting.discount_percentage,
      discount_days_before: setting.discount_days_before,
      pix_key: setting.pix_key || '',
      description: setting.description || ''
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (setting: MonthlyFeeSetting) => {
    if (!confirm(`Tem certeza que deseja excluir a configuração "${setting.name}"?`)) {
      return;
    }

    try {
      await deleteMonthlyFeeSetting(clubId, setting.id);
      toast({
        title: 'Sucesso',
        description: 'Configuração excluída com sucesso'
      });
      loadData();
    } catch (error: any) {
      console.error('Erro ao excluir configuração:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao excluir configuração',
        variant: 'destructive'
      });
    }
  };

  const handleToggleActive = async (setting: MonthlyFeeSetting) => {
    try {
      await updateMonthlyFeeSetting(clubId, setting.id, {
        is_active: !setting.is_active
      });
      toast({
        title: 'Sucesso',
        description: `Configuração ${!setting.is_active ? 'ativada' : 'desativada'} com sucesso`
      });
      loadData();
    } catch (error: any) {
      console.error('Erro ao alterar status:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao alterar status',
        variant: 'destructive'
      });
    }
  };

  const resetForm = () => {
    setEditingSetting(null);
    setFormData({
      name: '',
      amount: 0,
      due_day: 5,
      reminder_days_before: 3,
      late_fee_percentage: 0,
      discount_percentage: 0,
      discount_days_before: 0,
      pix_key: '',
      description: ''
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Configurações de Mensalidades
            </div>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Nova Configuração
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {settings.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma configuração de mensalidade encontrada</p>
              <p className="text-sm">Crie uma configuração para começar a gerenciar mensalidades</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Categoria</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Vencimento</TableHead>
                    <TableHead>Lembrete</TableHead>
                    <TableHead>Multa</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[100px]">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {settings.map((setting) => (
                    <TableRow key={setting.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{setting.name}</div>
                          {setting.description && (
                            <div className="text-sm text-muted-foreground">
                              {setting.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {setting.category_name ? (
                          <Badge variant="outline">{setting.category_name}</Badge>
                        ) : (
                          <span className="text-muted-foreground">Todas</span>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(setting.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          Dia {setting.due_day}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Mail className="w-4 h-4" />
                          {setting.reminder_days_before} dia(s)
                        </div>
                      </TableCell>
                      <TableCell>
                        {setting.late_fee_percentage > 0 ? (
                          <div className="flex items-center gap-1">
                            <Percent className="w-4 h-4" />
                            {setting.late_fee_percentage}%
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Sem multa</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={setting.is_active}
                            onCheckedChange={() => handleToggleActive(setting)}
                          />
                          <Badge variant={setting.is_active ? 'default' : 'secondary'}>
                            {setting.is_active ? 'Ativa' : 'Inativa'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(setting)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(setting)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog para criar/editar configuração */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingSetting ? 'Editar Configuração' : 'Nova Configuração de Mensalidade'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome da Configuração *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Ex: Mensalidade Sub-15"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Categoria</Label>
                <Select
                  value={formData.category_id?.toString() || ''}
                  onValueChange={(value) => setFormData({ 
                    ...formData, 
                    category_id: value && value !== 'all' ? parseInt(value) : undefined 
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma categoria (opcional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as categorias</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Valor (R$) *</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                  placeholder="0,00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="due_day">Dia do Vencimento *</Label>
                <Input
                  id="due_day"
                  type="number"
                  min="1"
                  max="31"
                  value={formData.due_day}
                  onChange={(e) => setFormData({ ...formData, due_day: parseInt(e.target.value) || 1 })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reminder_days">Lembrete (dias antes)</Label>
                <Input
                  id="reminder_days"
                  type="number"
                  min="0"
                  value={formData.reminder_days_before}
                  onChange={(e) => setFormData({ ...formData, reminder_days_before: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="late_fee">Multa por Atraso (%)</Label>
                <Input
                  id="late_fee"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.late_fee_percentage}
                  onChange={(e) => setFormData({ ...formData, late_fee_percentage: parseFloat(e.target.value) || 0 })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="discount">Desconto Antecipado (%)</Label>
                <Input
                  id="discount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData({ ...formData, discount_percentage: parseFloat(e.target.value) || 0 })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="discount_days">Dias para Desconto</Label>
                <Input
                  id="discount_days"
                  type="number"
                  min="0"
                  value={formData.discount_days_before}
                  onChange={(e) => setFormData({ ...formData, discount_days_before: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pix_key">Chave PIX (opcional)</Label>
              <Input
                id="pix_key"
                value={formData.pix_key}
                onChange={(e) => setFormData({ ...formData, pix_key: e.target.value })}
                placeholder="Se não informada, será usada a chave PIX do clube"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descrição adicional sobre esta mensalidade"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSubmit}>
              {editingSetting ? 'Atualizar' : 'Criar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}