import { useState, useEffect, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import {
  Copy,
  RefreshCw,
  Mail,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  Calendar,
  Search
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { EVALUATION_PERMISSIONS } from "@/constants/permissions";
import {
  getPlayerEvaluationInvitations,
  sendEvaluationInvitationEmail,
  deletePlayerEvaluationInvitation,
  type PlayerEvaluationInvitation
} from "@/api";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { DataPagination } from "@/components/ui/data-pagination";

export function EvaluationInvitationsTable() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const { user } = useUser();
  const { can } = usePermission();

  const [invitations, setInvitations] = useState<PlayerEvaluationInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resendingId, setResendingId] = useState<number | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<PlayerEvaluationInvitation | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const itemsPerPage = 10;

  // Date filters - default to current month (1st day to last day)
  const getCurrentMonthDates = () => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    return { firstDay, lastDay };
  };

  const { firstDay, lastDay } = getCurrentMonthDates();
  const [startDate, setStartDate] = useState<Date | undefined>(firstDay);
  const [endDate, setEndDate] = useState<Date | undefined>(lastDay);

  // Fetch invitations with date filters
  const fetchInvitations = async () => {
    try {
      setLoading(true);
      setError(null);

      const startDateStr = startDate ? format(startDate, "yyyy-MM-dd") : undefined;
      const endDateStr = endDate ? format(endDate, "yyyy-MM-dd") : undefined;

      const data = await getPlayerEvaluationInvitations(clubId, startDateStr, endDateStr);
      setInvitations(data);
    } catch (err: any) {
      console.error("Erro ao buscar convites:", err);
      setError(err.message || "Erro ao buscar convites");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar convites",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load invitations on mount and when date filters change
  useEffect(() => {
    fetchInvitations();
  }, [clubId, startDate, endDate]);

  // Filter invitations by search term
  const filteredInvitations = useMemo(() => {
    const term = searchTerm.toLowerCase();
    return invitations.filter((invitation) => {
      return (
        invitation.email.toLowerCase().includes(term) ||
        (invitation.cpf && invitation.cpf.includes(term)) ||
        (invitation.created_by_user?.name &&
          invitation.created_by_user.name.toLowerCase().includes(term))
      );
    });
  }, [invitations, searchTerm]);

  // Pagination
  const totalPages = Math.ceil(filteredInvitations.length / itemsPerPage);
  const paginatedInvitations = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredInvitations.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredInvitations, currentPage, itemsPerPage]);

  // Reset page when data or search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [invitations]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Copy invitation link to clipboard
  const copyInvitationLink = (token: string) => {
    const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
    const registrationUrl = `${SITE_URL}/evaluation-registration?token=${token}`;

    navigator.clipboard.writeText(registrationUrl);

    toast({
      title: "Link copiado",
      description: "Link de convite copiado para a área de transferência",
    });
  };

  // Resend invitation email
  const resendInvitation = async (invitation: PlayerEvaluationInvitation) => {
    try {
      setResendingId(invitation.id);

      // Get club name
      const { data: clubData } = await supabase
        .from("club_info")
        .select("name")
        .eq("id", clubId)
        .single();

      const clubName = clubData?.name || "Game Day Nexus";

      // Send invitation email
      await sendEvaluationInvitationEmail(invitation.email, invitation.token, clubName);

      toast({
        title: "Sucesso",
        description: "Convite reenviado com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao reenviar convite:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao reenviar convite",
        variant: "destructive",
      });
    } finally {
      setResendingId(null);
    }
  };

  // Handle delete invitation
  const handleDeleteInvitation = async () => {
    if (!selectedInvitation || !user?.id) return;

    try {
      setDeletingId(selectedInvitation.id);

      // Delete invitation
      await deletePlayerEvaluationInvitation(clubId, selectedInvitation.id, user.id);

      toast({
        title: "Sucesso",
        description: "Convite excluído com sucesso",
      });

      // Refresh invitations
      fetchInvitations();
    } catch (err: any) {
      console.error("Erro ao excluir convite:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir convite",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
      setDeleteDialogOpen(false);
      setSelectedInvitation(null);
    }
  };

  // Open delete confirmation dialog
  const confirmDelete = (invitation: PlayerEvaluationInvitation) => {
    setSelectedInvitation(invitation);
    setDeleteDialogOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: string, expiresAt: string) => {
    const now = new Date();
    const expiration = new Date(expiresAt);

    if (status === "used") {
      return <Badge className="bg-green-500">Utilizado</Badge>;
    }

    if (status === "expired" || expiration < now) {
      return <Badge className="bg-red-500">Expirado</Badge>;
    }

    return <Badge className="bg-primary">Pendente</Badge>;
  };


  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Convites para pré cadastro</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchInvitations}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {/* Date Filters */}
      <div className="flex flex-col sm:flex-row gap-4 p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filtrar por período:</span>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">De:</span>
            <DatePicker
              date={startDate}
              setDate={setStartDate}
              placeholder="Data inicial"
              className="w-[140px]"
            />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Até:</span>
            <DatePicker
              date={endDate}
              setDate={setEndDate}
              placeholder="Data final"
              className="w-[140px]"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const { firstDay, lastDay } = getCurrentMonthDates();
              setStartDate(firstDay);
              setEndDate(lastDay);
            }}
            className="text-xs"
          >
            Mês atual
          </Button>
        </div>
      </div>

      <div className="relative w-full sm:w-72">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar por email, CPF ou indicador..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {error && (
        <div className="p-4 bg-red-50 text-red-800 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center p-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
        </div>
      ) : filteredInvitations.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          {searchTerm ? "Nenhum convite encontrado com esse termo." : "Nenhum convite encontrado."}
        </div>
      ) : (
        <>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>CPF</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Criado em</TableHead>
                  <TableHead>Expira em</TableHead>
                  <TableHead>Indicador por</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedInvitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>{invitation.cpf || "-"}</TableCell>
                    <TableCell>
                      {getStatusBadge(invitation.status, invitation.expires_at)}
                    </TableCell>
                    <TableCell>{formatDate(invitation.created_at)}</TableCell>
                    <TableCell>{formatDate(invitation.expires_at)}</TableCell>
                    <TableCell>{invitation.created_by_user?.name || "-"}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {can(EVALUATION_PERMISSIONS.INVITATIONS.COPY_LINK) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => copyInvitationLink(invitation.token)}
                            title="Copiar link"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        )}

                        {invitation.status === "pending" && can(EVALUATION_PERMISSIONS.INVITATIONS.RESEND) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => resendInvitation(invitation)}
                            disabled={resendingId === invitation.id}
                            title="Reenviar email"
                          >
                            {resendingId === invitation.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Mail className="h-4 w-4" />
                            )}
                          </Button>
                        )}

                        {invitation.status === "used" && invitation.player_id && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => window.location.href = `/jogador/${invitation.player_id}`}
                            title="Ver jogador"
                          >
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </Button>
                        )}

                        {/* Only show delete button for pending or expired invitations */}
                        {(invitation.status === "pending" || invitation.status === "expired") && can(EVALUATION_PERMISSIONS.INVITATIONS.DELETE) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => confirmDelete(invitation)}
                            disabled={deletingId === invitation.id}
                            title="Excluir convite"
                          >
                            {deletingId === invitation.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin text-red-500" />
                            ) : (
                              <Trash2 className="h-4 w-4 text-red-500" />
                            )}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="mt-4">
              <DataPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                showInfo={true}
                totalItems={filteredInvitations.length}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </>
      )}

      {/* Confirmation dialog for deleting invitations */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Excluir Convite"
        description={
          selectedInvitation
            ? `Tem certeza que deseja excluir o convite para ${selectedInvitation.email}? Esta ação não pode ser desfeita.`
            : "Tem certeza que deseja excluir este convite? Esta ação não pode ser desfeita."
        }
        onConfirm={handleDeleteInvitation}
      />
    </div>
  );
}
