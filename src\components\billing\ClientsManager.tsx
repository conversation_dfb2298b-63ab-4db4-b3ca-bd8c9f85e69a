import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { Plus, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import { Client, CreateClientData, createClient, updateClient, deleteClient } from '@/api/billing';

interface ClientsManagerProps {
    clients: Client[];
    onUpdate: () => void;
    clubId: number;
}

export function ClientsManager({ clients, onUpdate, clubId }: ClientsManagerProps) {
    const [modalOpen, setModalOpen] = useState(false);
    const [editingClient, setEditingClient] = useState<Client | null>(null);
    const [formData, setFormData] = useState<CreateClientData>({
        name: '',
        email: '',
        phone: '',
        pix_key: ''
    });
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();

    const handleOpenModal = (client?: Client) => {
        if (client) {
            setEditingClient(client);
            setFormData({
                name: client.name,
                email: client.email || '',
                phone: client.phone || '',
                pix_key: client.pix_key || ''
            });
        } else {
            setEditingClient(null);
            setFormData({
                name: '',
                email: '',
                phone: '',
                pix_key: ''
            });
        }
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setModalOpen(false);
        setEditingClient(null);
        setFormData({
            name: '',
            email: '',
            phone: '',
            pix_key: ''
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.name.trim()) {
            toast({
                title: 'Nome obrigatório',
                description: 'O nome do cliente é obrigatório',
                variant: 'destructive'
            });
            return;
        }

        setLoading(true);
        try {
            if (editingClient) {
                await updateClient(clubId, editingClient.id, formData);
                toast({
                    title: 'Cliente atualizado',
                    description: 'O cliente foi atualizado com sucesso'
                });
            } else {
                await createClient(clubId, formData);
                toast({
                    title: 'Cliente criado',
                    description: 'O cliente foi criado com sucesso'
                });
            }

            onUpdate();
            handleCloseModal();
        } catch (error) {
            toast({
                title: 'Erro',
                description: 'Não foi possível salvar o cliente',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (client: Client) => {
        if (!confirm(`Tem certeza que deseja excluir o cliente "${client.name}"?`)) return;

        try {
            await deleteClient(clubId, client.id);
            toast({
                title: 'Cliente excluído',
                description: 'O cliente foi excluído com sucesso'
            });
            onUpdate();
        } catch (error) {
            toast({
                title: 'Erro',
                description: 'Não foi possível excluir o cliente',
                variant: 'destructive'
            });
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    return (
        <>
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Clientes Cadastrados</h3>
                    <Button onClick={() => handleOpenModal()}>
                        <Plus className="w-4 h-4 mr-2" />
                        Novo Cliente
                    </Button>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Nome</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Telefone</TableHead>
                                <TableHead>Chave PIX</TableHead>
                                <TableHead>Cadastrado em</TableHead>
                                <TableHead className="w-[50px]"></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {clients.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                        Nenhum cliente cadastrado
                                    </TableCell>
                                </TableRow>
                            ) : (
                                clients.map((client) => (
                                    <TableRow key={client.id}>
                                        <TableCell className="font-medium">{client.name}</TableCell>
                                        <TableCell>{client.email || '-'}</TableCell>
                                        <TableCell>{client.phone || '-'}</TableCell>
                                        <TableCell>
                                            {client.pix_key ? (
                                                <span className="font-mono text-sm bg-muted px-2 py-1 rounded">
                                                    {client.pix_key.length > 20 ? `${client.pix_key.substring(0, 20)}...` : client.pix_key}
                                                </span>
                                            ) : '-'}
                                        </TableCell>
                                        <TableCell>{formatDate(client.created_at)}</TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={() => handleOpenModal(client)}>
                                                        <Edit className="mr-2 h-4 w-4" />
                                                        Editar
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => handleDelete(client)}
                                                        className="text-red-600"
                                                    >
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Excluir
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* Modal de criação/edição */}
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {editingClient ? 'Editar Cliente' : 'Novo Cliente'}
                        </DialogTitle>
                    </DialogHeader>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="name">Nome *</Label>
                            <Input
                                id="name"
                                value={formData.name}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                placeholder="Nome completo do cliente"
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="email">Email</Label>
                            <Input
                                id="email"
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                                placeholder="<EMAIL>"
                            />
                        </div>

                        <div>
                            <Label htmlFor="phone">Telefone</Label>
                            <Input
                                id="phone"
                                value={formData.phone}
                                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                                placeholder="(11) 99999-9999"
                            />
                        </div>

                        <div>
                            <Label htmlFor="pix_key">Chave PIX</Label>
                            <Input
                                id="pix_key"
                                value={formData.pix_key}
                                onChange={(e) => setFormData({ ...formData, pix_key: e.target.value })}
                                placeholder="CPF, CNPJ, email, telefone ou chave aleatória"
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                                Opcional - usado para recebimentos do cliente
                            </p>
                        </div>

                        <div className="flex gap-2 pt-4">
                            <Button type="submit" disabled={loading} className="flex-1">
                                {loading ? 'Salvando...' : (editingClient ? 'Atualizar' : 'Criar')}
                            </Button>
                            <Button type="button" variant="outline" onClick={handleCloseModal}>
                                Cancelar
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </>
    );
}