import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Plus, History, FileText, CheckCircle, XCircle, AlertTriangle, Download, Upload, Trash2, PenTool } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Dialog<PERSON>it<PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { formatDate } from "@/lib/utils";
import { Player, PlayerOccurrence, getPlayerOccurrences, createPlayerOccurrence, updatePlayerOccurrence, deletePlayerOccurrence, signPlayerOccurrence, getUserSignatureInfo } from "@/api/api";

import { PermissionControl } from "@/components/PermissionControl";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { OccurrenceForm } from "./OccurrenceForm";
import { generateOccurrencePDF } from '@/utils/occurrencePdfGenerator';
import { useClubInfoStore } from '@/store/useClubInfoStore';
import { UploadOccurrenceSignatureDialog } from './UploadOccurrenceSignatureDialog';

interface PlayerOccurrencesProps {
  playerId: string;
  clubId: number;
  playerName?: string;
}

export function PlayerOccurrences({ playerId, clubId, playerName }: PlayerOccurrencesProps) {
  const { user } = useUser();
  const { toast } = useToast();
  const { clubInfo } = useClubInfoStore();
  const [activeTab, setActiveTab] = useState("active");
  const [occurrences, setOccurrences] = useState<PlayerOccurrence[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedOccurrence, setSelectedOccurrence] = useState<PlayerOccurrence | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [occurrenceToUpload, setOccurrenceToUpload] = useState<PlayerOccurrence | null>(null);
  const [signDialogOpen, setSignDialogOpen] = useState(false);
  const [occurrenceToSign, setOccurrenceToSign] = useState<PlayerOccurrence | null>(null);
  const [userSignatureInfo, setUserSignatureInfo] = useState<{name: string, role: string} | null>(null);
  const { can } = usePermission();

  useEffect(() => {
    loadOccurrences();
  }, [playerId]);

  const loadOccurrences = async () => {
    try {
      const data = await getPlayerOccurrences(clubId, playerId);
      setOccurrences(data);
    } catch (error) {
      console.error("Erro ao carregar ocorrências:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as ocorrências do jogador.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOccurrence = () => {
    setSelectedOccurrence(null);
    setIsFormOpen(true);
  };

  const openUploadDialog = (occ: PlayerOccurrence) => {
    setOccurrenceToUpload(occ);
    setUploadDialogOpen(true);
  };

  const handleUploadSuccess = async () => {
    setUploadDialogOpen(false);
    setOccurrenceToUpload(null);
    await loadOccurrences();
  };

  const openSignDialog = async (occurrence: PlayerOccurrence) => {
    try {
      const signatureInfo = await getUserSignatureInfo(clubId);
      setUserSignatureInfo(signatureInfo);
      setOccurrenceToSign(occurrence);
      setSignDialogOpen(true);
    } catch (error) {
      console.error("Erro ao buscar informações do usuário:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as informações do usuário.",
        variant: "destructive",
      });
    }
  };

  const handleDigitalSignature = async () => {
    if (!occurrenceToSign) return;

    try {
      await signPlayerOccurrence(occurrenceToSign.id, clubId);

      toast({
        title: "Sucesso",
        description: "Ocorrência assinada digitalmente com sucesso.",
      });

      setSignDialogOpen(false);
      setOccurrenceToSign(null);
      setUserSignatureInfo(null);
      await loadOccurrences();
    } catch (error) {
      console.error("Erro ao assinar ocorrência:", error);
      toast({
        title: "Erro",
        description: "Não foi possível assinar a ocorrência digitalmente.",
        variant: "destructive",
      });
    }
  };
  // Esquema de validação do formulário
  const occurrenceSchema = z.object({
    type: z.enum(["occurrence", "divergence", "punishment", "training_release", "training_game_release"]),
    title: z.string().min(1, "Título é obrigatório"),
    description: z.string().min(1, "Descrição é obrigatória"),
    severity: z.enum(["low", "medium", "high"]),
    status: z.enum(["active", "resolved", "archived"]),
    resolution_notes: z.string().optional(),
  });

  // Inicializar o formulário
  const form = useForm<z.infer<typeof occurrenceSchema>>({
    resolver: zodResolver(occurrenceSchema),
    defaultValues: {
      type: "occurrence",
      title: "",
      description: "",
      severity: "medium",
      status: "active",
      resolution_notes: "",
    },
  });

  const handleEditOccurrence = (occurrence: PlayerOccurrence) => {
    // Criar um objeto com os campos necessários para o formulário
    const formData = {
      type: occurrence.type,
      title: occurrence.title,
      description: occurrence.description,
      severity: occurrence.severity,
      status: occurrence.status,
      resolution_notes: occurrence.resolution_notes || ''
    };
    
    setSelectedOccurrence(occurrence);
    // Resetar o formulário com os dados da ocorrência
    form.reset(formData);
    setIsFormOpen(true);
  };

  const handleDeleteOccurrence = async (occurrenceId: number) => {
    try {
      await deletePlayerOccurrence(occurrenceId);
      await loadOccurrences();
      toast({
        title: "Sucesso",
        description: "Ocorrência excluída com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao excluir ocorrência:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a ocorrência.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveSignature = async (occurrenceId: number) => {
    try {
      await updatePlayerOccurrence(occurrenceId, { signature_url: null });
      toast({ title: 'Sucesso', description: 'Imagem removida com sucesso.' });
      await loadOccurrences();
    } catch (error) {
      console.error('Erro ao remover imagem:', error);
      toast({ title: 'Erro', description: 'Não foi possível remover a imagem.', variant: 'destructive' });
    }
  };

  const handleDownloadPdf = async (occurrence: PlayerOccurrence) => {
    try {
      const blob = await generateOccurrencePDF(
        { 
          ...occurrence, 
          player_name: playerName,
          signed_at: occurrence.signed_at,
          signed_by_name: occurrence.signed_by_name,
          signed_by_role: occurrence.signed_by_role
        },
        clubInfo!
      );
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ocorrencia-${occurrence.id}.pdf`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o PDF da ocorrência.',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      if (selectedOccurrence) {
        await updatePlayerOccurrence(selectedOccurrence.id, {
          ...data,
          club_id: clubId,
          player_id: playerId,
        });
        toast({
          title: "Sucesso",
          description: "Ocorrência atualizada com sucesso.",
        });
      } else {
        await createPlayerOccurrence(clubId, playerId, {
          ...data,
          club_id: clubId,
          player_id: playerId,
        });
        toast({
          title: "Sucesso",
          description: "Ocorrência criada com sucesso.",
        });
      }
      setIsFormOpen(false);
      await loadOccurrences();
    } catch (error) {
      console.error("Erro ao salvar ocorrência:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a ocorrência.",
        variant: "destructive",
      });
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "low":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Baixa</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Média</Badge>;
      case "high":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Alta</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Ativa</Badge>;
      case "resolved":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Resolvida</Badge>;
      case "archived":
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Arquivada</Badge>;
      default:
        return null;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "occurrence":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Ocorrência</Badge>;
      case "divergence":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Divergência</Badge>;
      case "punishment":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Punição</Badge>;
      case "training_release":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Liberação p/ Treinar</Badge>;
      case "training_game_release":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Liberação p/ Treino e Jogo</Badge>;
      default:
        return null;
    }
  };

  const filteredOccurrences = occurrences.filter(occurrence => {
    if (activeTab === "active") return occurrence.status === "active";
    if (activeTab === "resolved") return occurrence.status === "resolved";
    if (activeTab === "archived") return occurrence.status === "archived";
    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Ocorrências</h2>
          <p className="text-muted-foreground">
            Registro de ocorrências, divergências e punições
          </p>
        </div>
        <PermissionControl permissions={["players.occurrences.create", "players.occurrences.edit"]}>
          <Button onClick={handleCreateOccurrence}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Ocorrência
          </Button>
        </PermissionControl>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="active" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            <span>Ativas</span>
          </TabsTrigger>
          <TabsTrigger value="resolved" className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4" />
            <span>Resolvidas</span>
          </TabsTrigger>
          <TabsTrigger value="archived" className="flex items-center gap-1">
            <History className="h-4 w-4" />
            <span>Arquivadas</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {loading ? (
            <div className="text-center py-8">Carregando ocorrências...</div>
          ) : filteredOccurrences.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Nenhuma ocorrência registrada</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredOccurrences.map((occurrence) => (
                <Card key={occurrence.id}>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{occurrence.title}</CardTitle>
                      <div className="flex gap-2 mt-2">
                        {getTypeBadge(occurrence.type)}
                        {getSeverityBadge(occurrence.severity)}
                        {getStatusBadge(occurrence.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {format(new Date(occurrence.created_at), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleDownloadPdf(occurrence)}>
                        <Download className="h-4 w-4" />
                        PDF
                      </Button>
                      {!occurrence.signed_at && user?.user_metadata?.role !== 'player' && (
                        <PermissionControl permissions={["players.occurrences.sign"]}>
                          <Button variant="outline" size="sm" onClick={() => openSignDialog(occurrence)} className="text-blue-600 border-blue-200 hover:bg-blue-50">
                            <PenTool className="h-4 w-4 mr-1" />
                            Assinar Digitalmente
                          </Button>
                        </PermissionControl>
                      )}
                      <PermissionControl permissions={["players.occurrences.edit", "players.occurrences.delete"]}>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditOccurrence(occurrence)}>
                            Editar
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              if (confirm("Tem certeza que deseja excluir esta ocorrência?")) {
                                handleDeleteOccurrence(Number(occurrence.id));
                              }
                            }}
                          >
                            Excluir
                          </Button>
                        </div>
                      </PermissionControl>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Descrição</h4>
                        <p className="text-muted-foreground">{occurrence.description}</p>
                      </div>
                      {occurrence.status === "resolved" && occurrence.resolution_notes && (
                        <div>
                          <h4 className="font-medium mb-2">Resolução</h4>
                          <p className="text-muted-foreground">{occurrence.resolution_notes}</p>
                        </div>
                      )}
                      {occurrence.signed_at && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <h4 className="font-medium text-green-800">Assinado Digitalmente</h4>
                          </div>
                          <div className="text-sm text-green-700 space-y-1">
                            <p><strong>Assinado por:</strong> {occurrence.signed_by_name}</p>
                            <p><strong>Função:</strong> {occurrence.signed_by_role}</p>
                            <p><strong>Data:</strong> {format(new Date(occurrence.signed_at), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}</p>
                          </div>
                        </div>
                      )}
                      {occurrence.signature_url ? (
                        <div className="space-y-2">
                          <h4 className="font-medium">Imagem Assinada</h4>
                          <img src={occurrence.signature_url} alt="Assinatura" className="max-h-40 border rounded-md" />
                          <PermissionControl permissions={["players.occurrences.edit"]}>
                            <Button variant="ghost" size="sm" className="gap-1 text-red-600" onClick={() => handleRemoveSignature(Number(occurrence.id))}>
                              <Trash2 className="h-4 w-4" /> Remover
                            </Button>
                          </PermissionControl>
                        </div>
                      ) : (
                        <PermissionControl permissions={["players.occurrences.edit"]}>
                          <Button variant="outline" size="sm" className="gap-1" onClick={() => openUploadDialog(occurrence)}>
                            <Upload className="h-4 w-4" /> Enviar Assinatura
                          </Button>
                        </PermissionControl>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <OccurrenceForm
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedOccurrence(null);
          form.reset();
        }}
        onSubmit={handleSubmit}
        form={form}
        playerName={playerName}
      />
      
      <UploadOccurrenceSignatureDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        occurrenceId={occurrenceToUpload?.id || null}
        occurrenceTitle={occurrenceToUpload?.title || ''}
        onSuccess={handleUploadSuccess}
      />

      <Dialog open={signDialogOpen} onOpenChange={setSignDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assinatura Digital</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Você está prestes a assinar digitalmente a ocorrência:</p>
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium">{occurrenceToSign?.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">{occurrenceToSign?.description}</p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Informações da Assinatura</span>
              </div>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>Nome:</strong> {userSignatureInfo?.name || 'Carregando...'}</p>
                <p><strong>Função:</strong> {userSignatureInfo?.role || 'Carregando...'}</p>
                <p><strong>Data/Hora:</strong> {format(new Date(), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}</p>
              </div>
            </div>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                A assinatura digital é permanente e não pode ser desfeita. Certifique-se de que todas as informações estão corretas.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setSignDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleDigitalSignature} className="bg-blue-600 hover:bg-blue-700">
              <PenTool className="h-4 w-4 mr-2" />
              Confirmar Assinatura
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 