import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Users, Share2, ArrowLeft, ArrowRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useSeasonStore } from "@/store/useSeasonStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { CategorySelector } from "@/components/CategorySelector";
import type { Player } from "@/api/api";
import { getCategoryLineup, saveCategoryLineup, getCategoryPlayers } from "@/api";
import { getMatchHistory, getTopScorers, getPlayerById } from "@/api";

// Formações disponíveis
const formations = [
  { id: "4-3-3", name: "4-3-3", description: "Padrão" },
  { id: "4-4-2", name: "4-4-2", description: "Tradicional" },
  { id: "3-5-2", name: "3-5-2", description: "Com 3 zagueiros" },
  { id: "4-2-3-1", name: "4-2-3-1", description: "Com volantes" },
  { id: "5-3-2", name: "5-3-2", description: "Defensivo" },
];

// Componente para posição de jogador no campo
const FieldPosition = ({ position, player = null, onSelect, onRemove }) => {
  return (
    <div
      className={`
        flex flex-col items-center justify-center p-2
        cursor-pointer border-dashed
        ${getPositionStyleByRole(position)}
      `}
      onClick={() => onSelect(position)}
    >
      {player ? (
        <div className="flex flex-col items-center">
          <Avatar className="h-12 w-12 border-2 border-white shadow-md">
            <AvatarImage src={player.image || ""} />
            <AvatarFallback className="bg-team-blue text-white text-xs font-bold">
              {player.number}
            </AvatarFallback>
          </Avatar>
          <span className="mt-1 text-xs font-medium whitespace-nowrap">{player.name.split(" ")[0]}</span>
          <span className="text-[10px] text-muted-foreground">{player.position}</span>
          <button
            className="text-xs text-red-500 mt-1 hover:underline"
            onClick={e => { e.stopPropagation(); onRemove(position); }}
          >Remover</button>
        </div>
      ) : (
        <div className="rounded-full h-12 w-12 bg-gray-200 border-2 border-dashed border-gray-300 flex items-center justify-center text-gray-500">
          +
        </div>
      )}
    </div>
  );
};

// Função para obter estilo da posição baseado na função
function getPositionStyleByRole(position) {
  return "rounded-md hover:bg-gray-100 transition-colors";
}

// Função utilitária para mapear label da posição para filtro de jogadores
function getPositionFilter(label: string) {
  switch (label) {
    case "GOL": return "Goleiro";
    case "ZAG": return "Zagueiro";
    case "LE": case "LD": case "LWB": case "RWB": return "Lateral";
    case "VOL": case "DM": return "Volante";
    case "MC": case "MEI": case "AM": return "Meio-campista";
    case "PE": case "PD": case "LW": case "RW": return "Extremo";
    case "ATA": case "ST": return "Atacante";
    default: return undefined;
  }
}

// --- PATCH: Migração inteligente do lineup ao trocar de formação ---
// Função utilitária para mapear posições equivalentes entre formações
function mapLineupToNewFormation(oldLineup: Record<string, Player | undefined>, oldFormation: string, newFormation: string) {
  // Função utilitária para obter posições baseada na formação
  const positionsByFormation = {
    '4-3-3': ["gk","lb","cb1","cb2","rb","dm","cm1","cm2","lw","rw","cf"],
    '4-4-2': ["gk","lb","cb1","cb2","rb","lm","cm1","cm2","rm","st1","st2"],
    '3-5-2': ["gk","cb1","cb2","cb3","lwb","rwb","cm1","cm2","cam","st1","st2"],
    '4-2-3-1': ["gk","lb","cb1","cb2","rb","dm1","dm2","cam","lw","rw","st"],
    '5-3-2': ["gk","lcb","cb1","cb2","rcb","lwb","rwb","cm1","cm2","st1","st2"]
  };
  const newPositions = positionsByFormation[newFormation] || [];
  const usedPlayers = new Set<string>();
  const newLineup: Record<string, Player | undefined> = {};
  newPositions.forEach(newPos => {
    let found: Player | undefined = undefined;
    if (oldLineup[newPos] && oldLineup[newPos]?.id && !usedPlayers.has(oldLineup[newPos]!.id)) {
      found = oldLineup[newPos];
      usedPlayers.add(found.id);
    }
    newLineup[newPos] = found;
  });
  return newLineup;
}

// --- Funções utilitárias para estatísticas reais do comparativo ---
/**
 * Calcula a formação mais usada a partir do histórico de partidas.
 * @param matchHistory Array de partidas
 * @returns string (ex: '4-3-3')
 */
function calcularFormacaoMaisUsada(matchHistory) {
  const count: Record<string, number> = {};
  matchHistory.forEach(match => {
    if (match.formation) {
      count[match.formation] = (count[match.formation] || 0) + 1;
    }
  });
  let max = 0;
  let formacao = '';
  Object.entries(count).forEach(([formation, qtd]) => {
    if (qtd > max) {
      max = qtd;
      formacao = formation;
    }
  });
  return formacao || '4-4-2';
}

/**
 * Calcula a média de finalizações por jogo.
 * @param matchHistory Array de partidas
 * @param clubId ID do clube (para distinguir mandante/visitante se necessário)
 * @returns número
 */
function calcularMediaFinalizacoes(matchHistory) {
  const jogos = matchHistory.length;
  if (!jogos) return 0;
  let total = 0;
  matchHistory.forEach(match => {
    total += match.estatisticas?.chutes || 0;
  });
  return (total / jogos).toFixed(1);
}

/**
 * Calcula a média de gols marcados por jogo.
 * @param matchHistory Array de partidas
 * @param clubId ID do clube (para distinguir mandante/visitante)
 * @returns número
 */
function calcularMediaGolsMarcados(matchHistory, clubId) {
  const jogos = matchHistory.length;
  if (!jogos) return 0;
  let total = 0;
  matchHistory.forEach(match => {
    // Se o clube é mandante, gols marcados = score_home; visitante = score_away
    if (match.location === 'casa') {
      total += match.score_home || 0;
    } else {
      total += match.score_away || 0;
    }
  });
  return (total / jogos).toFixed(1);
}

/**
 * Calcula a média de gols sofridos por jogo.
 * @param matchHistory Array de partidas
 * @param clubId ID do clube (para distinguir mandante/visitante)
 * @returns número
 */
function calcularMediaGolsSofridos(matchHistory, clubId) {
  const jogos = matchHistory.length;
  if (!jogos) return 0;
  let total = 0;
  matchHistory.forEach(match => {
    // Se o clube é mandante, gols sofridos = score_away; visitante = score_home
    if (match.location === 'casa') {
      total += match.score_away || 0;
    } else {
      total += match.score_home || 0;
    }
  });
  return (total / jogos).toFixed(1);
}

// --- Função utilitária para desempenho individual por formação ---
/**
 * Retorna para cada jogador um mapa de formação -> { jogos, gols }
 * @param matchHistory
 * @returns Record<playerId, Record<formacao, { jogos: number, gols: number }>>
 */
function calcularDesempenhoIndividualPorFormacao(matchHistory) {
  const desempenho: Record<string, Record<string, { jogos: number; gols: number }>> = {};
  matchHistory.forEach(match => {
    if (!match.formation || !match.escalacao) return;
    // Suporta tanto array quanto objeto de escalacao
    const escalacaoObj: Record<string, string> = {};
    if (Array.isArray(match.escalacao)) {
      (match.escalacao as (string | undefined)[]).forEach(pid => {
        if (typeof pid === 'string' && pid) escalacaoObj[pid] = pid;
      });
    } else {
      Object.values(match.escalacao).forEach(pid => {
        if (typeof pid === 'string' && pid) escalacaoObj[pid] = pid;
      });
    }
    // Para cada jogador escalado
    Object.values(escalacaoObj).forEach(pid => {
      if (typeof pid !== 'string') return;
      if (!desempenho[pid]) desempenho[pid] = {};
      if (!desempenho[pid][match.formation]) desempenho[pid][match.formation] = { jogos: 0, gols: 0 };
      desempenho[pid][match.formation].jogos += 1;
    });
    // Gols (se houver match.gols)
    if (Array.isArray(match.gols)) {
      match.gols.forEach(gol => {
        const pid = gol.player_id || gol.player;
        if (typeof pid === 'string' && desempenho[pid] && desempenho[pid][match.formation]) {
          desempenho[pid][match.formation].gols += 1;
        }
      });
    }
  });
  return desempenho;
}

export default function EscalacaoJogadores() {
  // --- HOOKS ---
  const [isRestoring, setIsRestoring] = useState(true);
  const [hasUserEdited, setHasUserEdited] = useState(false);
  const [lineup, setLineup] = useState<Record<string, Player | undefined>>({});
  const [selectingFor, setSelectingFor] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("campo");
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [filterPosition, setFilterPosition] = useState("");
  const [formation, setFormation] = useState<string>('4-4-2');
  const [matchHistory, setMatchHistory] = useState<{id: string; date: string; score_home: number; score_away: number; result: string}[]>([]);
  const [topScorers, setTopScorers] = useState<{ player: string; goals: number }[]>([]);
  const [scorersLoading, setScorersLoading] = useState(false);
  const [scorersError, setScorersError] = useState<string | null>(null);
  const [scorersPlayers, setScorersPlayers] = useState<Record<string, Player>>({});
  const [categoryPlayers, setCategoryPlayers] = useState<Player[]>([]);

  const clubId = useCurrentClubId();
  const { activeSeason } = useSeasonStore();
  // Zustand store
  const { players, fetchPlayers } = usePlayersStore();
  const { categories, fetchCategories } = useCategoriesStore();

  useEffect(() => {
    fetchPlayers(clubId);
  }, [fetchPlayers, clubId]);

  // Removed fetchMatches as we're not using matches anymore

  useEffect(() => {
    if (clubId) fetchCategories(clubId);
  }, [clubId, fetchCategories]);

  // Carregar jogadores da categoria selecionada
  useEffect(() => {
    async function fetchCategoryPlayers() {
      if (selectedCategoryId && clubId) {
        try {
          const players = await getCategoryPlayers(clubId, selectedCategoryId, {
            includeInactive: false,
            includeLoaned: false
          });
          setCategoryPlayers(players);
        } catch (error) {
          console.error("Erro ao buscar jogadores da categoria:", error);
          setCategoryPlayers([]);
        }
      } else {
        setCategoryPlayers([]);
      }
    }
    fetchCategoryPlayers();
  }, [selectedCategoryId, clubId]);

  // Carregar a escalação da categoria selecionada
  useEffect(() => {
    async function fetchCategoryLineup() {
      if (selectedCategoryId && clubId) {
        try {
          const categoryLineup = await getCategoryLineup(clubId, selectedCategoryId);

          if (categoryLineup && categoryLineup.lineup) {
            // Converter o lineup de IDs para objetos Player
            const escalacaoObj = categoryLineup.lineup as Record<string, string | null>;
            const newLineup = Object.fromEntries(
              Object.entries(escalacaoObj).map(([pos, pid]) => [
                pos,
                pid ? (categoryPlayers.length > 0
                  ? categoryPlayers.find(p => p.id === pid)
                  : players.find(p => p.id === pid)) || undefined
                : undefined
              ])
            );

            setLineup(newLineup);
            setFormation(categoryLineup.formation || '4-4-2');
          } else {
            // Se não tem escalação salva para esta categoria
            setLineup({});
            setFormation('4-4-2');
          }
        } catch (error) {
          console.error("Erro ao buscar escalação da categoria:", error);
          setLineup({});
          setFormation('4-4-2');
        }
      } else {
        setLineup({});
        setFormation('4-4-2');
      }
      setIsRestoring(false);
    }

    setIsRestoring(true);
    fetchCategoryLineup();
  }, [selectedCategoryId, clubId, categoryPlayers, players]);

  // Sempre que lineup mudar, marcar que o usuário editou
  useEffect(() => {
    if (!isRestoring) {
      setHasUserEdited(true);
    }
  }, [lineup, isRestoring]);

  // Salvar a escalação da categoria quando o usuário fizer alterações
  useEffect(() => {
    async function saveLineup() {
      if (
        !isRestoring &&
        hasUserEdited &&
        selectedCategoryId &&
        clubId &&
        Object.values(lineup).some(Boolean) // só salva se houver pelo menos um jogador escalado
      ) {
        try {
          // Converter o lineup de objetos Player para IDs
          const positions = Object.keys(getPositionsByFormation(formation));
          const escalacaoObj: Record<string, string | null> = {};
          positions.forEach(pos => {
            escalacaoObj[pos] = lineup[pos]?.id || null;
          });

          // Salvar no banco
          await saveCategoryLineup(clubId, selectedCategoryId, escalacaoObj, formation);
          setHasUserEdited(false); // reseta após salvar
          toast.success("Escalação da categoria salva com sucesso!");
        } catch (error) {
          console.error("Erro ao salvar escalação da categoria:", error);
          toast.error("Erro ao salvar escalação da categoria");
        }
      }
    }

    saveLineup();
  }, [lineup, selectedCategoryId, clubId, formation, isRestoring, hasUserEdited]);

  // Persistir e restaurar seleção de categoria no localStorage
  useEffect(() => {
    const lastCategoryId = localStorage.getItem('selectedCategoryId');
    if (lastCategoryId && categories.some(c => c.id.toString() === lastCategoryId)) {
      setSelectedCategoryId(parseInt(lastCategoryId));
    }
  }, [categories]);

  useEffect(() => {
    if (selectedCategoryId) {
      localStorage.setItem('selectedCategoryId', selectedCategoryId.toString());
    }
  }, [selectedCategoryId]);

  // Buscar histórico de partidas da temporada atual
  useEffect(() => {
    async function fetchHistory() {
      if (clubId && activeSeason) {
        try {
          const allMatches = await getMatchHistory(clubId, activeSeason.id);
          // Ordena do mais recente para o mais antigo
          allMatches.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
          setMatchHistory(allMatches.slice(0, 5)); // Apenas os 5 jogos mais recentes
        } catch (error) {
          console.error("Erro ao buscar histórico de partidas:", error);
          setMatchHistory([]);
        }
      } else {
        setMatchHistory([]);
      }
    }
    fetchHistory();
  }, [clubId, activeSeason]);

  useEffect(() => {
    async function fetchScorers() {
      if (!clubId || !activeSeason) {
        setTopScorers([]);
        return;
      }
      setScorersLoading(true);
      setScorersError(null);
      try {
        const scorers = await getTopScorers(clubId, activeSeason.id);
        setTopScorers(scorers);
        // Buscar info dos jogadores
        const uniqueIds = Array.from(new Set(scorers.map(s => s.player)));
        const playersObj: Record<string, Player> = {};
        for (const id of uniqueIds) {
          try {
            const p = await getPlayerById(clubId, id);
            playersObj[id] = p;
          } catch {
            // ignora erro individual
          }
        }
        setScorersPlayers(playersObj);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Erro ao buscar artilheiros";
        setScorersError(errorMessage);
      } finally {
        setScorersLoading(false);
      }
    }
    fetchScorers();
  }, [clubId, activeSeason]);

  // Handler de mudança de formação com migração inteligente
  const handleFormationChange = (newFormation: string) => {
    setLineup(prev => mapLineupToNewFormation(prev, formation, newFormation));
    setFormation(newFormation);
  };

  // Posições no campo baseadas na formação selecionada
  const getPositionsByFormation = (formation: string) => {
    switch (formation) {
      case "4-3-3":
        return {
          gk: { label: "GOL", x: "50%", y: "90%" },
          lb: { label: "LE", x: "20%", y: "75%" },
          cb1: { label: "ZAG", x: "40%", y: "75%" },
          cb2: { label: "ZAG", x: "60%", y: "75%" },
          rb: { label: "LD", x: "80%", y: "75%" },
          dm: { label: "VOL", x: "50%", y: "60%" },
          cm1: { label: "MC", x: "35%", y: "45%" },
          cm2: { label: "MC", x: "65%", y: "45%" },
          cm3: { label: "MC", x: "50%", y: "30%" },
          lw: { label: "PE", x: "20%", y: "25%" },
          rw: { label: "PD", x: "80%", y: "25%" },
          cf: { label: "ATA", x: "50%", y: "10%" },
        };
      case "4-4-2":
        return {
          gk: { label: "GOL", x: "50%", y: "90%" },
          lb: { label: "LE", x: "20%", y: "75%" },
          cb1: { label: "ZAG", x: "40%", y: "75%" },
          cb2: { label: "ZAG", x: "60%", y: "75%" },
          rb: { label: "LD", x: "80%", y: "75%" },
          lm: { label: "MEI", x: "20%", y: "50%" },
          cm1: { label: "MC", x: "40%", y: "50%" },
          cm2: { label: "MC", x: "60%", y: "50%" },
          rm: { label: "MEI", x: "80%", y: "50%" },
          st1: { label: "ATA", x: "40%", y: "20%" },
          st2: { label: "ATA", x: "60%", y: "20%" },
        };
      case "3-5-2":
        return {
          gk: { label: "GOL", x: "50%", y: "90%" },
          cb1: { label: "ZAG", x: "30%", y: "75%" },
          cb2: { label: "ZAG", x: "50%", y: "75%" },
          cb3: { label: "ZAG", x: "70%", y: "75%" },
          lwb: { label: "LE", x: "10%", y: "55%" },
          rwb: { label: "LD", x: "90%", y: "55%" },
          cm1: { label: "MC", x: "35%", y: "45%" },
          cm2: { label: "MC", x: "65%", y: "45%" },
          cam: { label: "MEI", x: "50%", y: "30%" },
          st1: { label: "ATA", x: "40%", y: "15%" },
          st2: { label: "ATA", x: "60%", y: "15%" },
        };
      case "4-2-3-1":
        return {
          gk: { label: "GOL", x: "50%", y: "90%" },
          lb: { label: "LE", x: "20%", y: "75%" },
          cb1: { label: "ZAG", x: "40%", y: "75%" },
          cb2: { label: "ZAG", x: "60%", y: "75%" },
          rb: { label: "LD", x: "80%", y: "75%" },
          dm1: { label: "VOL", x: "40%", y: "60%" },
          dm2: { label: "VOL", x: "60%", y: "60%" },
          cam: { label: "MEI", x: "50%", y: "45%" },
          lw: { label: "PE", x: "25%", y: "30%" },
          rw: { label: "PD", x: "75%", y: "30%" },
          st: { label: "ATA", x: "50%", y: "15%" },
        };
      case "5-3-2":
        return {
          gk: { label: "GOL", x: "50%", y: "90%" },
          lcb: { label: "ZAG", x: "20%", y: "75%" },
          cb1: { label: "ZAG", x: "40%", y: "75%" },
          cb2: { label: "ZAG", x: "60%", y: "75%" },
          rcb: { label: "ZAG", x: "80%", y: "75%" },
          lwb: { label: "LE", x: "10%", y: "60%" },
          rwb: { label: "LD", x: "90%", y: "60%" },
          cm1: { label: "MC", x: "35%", y: "45%" },
          cm2: { label: "MC", x: "65%", y: "45%" },
          st1: { label: "ATA", x: "40%", y: "20%" },
          st2: { label: "ATA", x: "60%", y: "20%" },
        };
      default:
        return {};
    }
  };

  const positions = getPositionsByFormation(formation);

  // Agrupamento de posições por setor para exibição em cards
  const groupedPositions = [
    {
      setor: "Goleiro",
      icon: <Users className="w-4 h-4 text-blue-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "GOL"),
    },
    {
      setor: "Zagueiros",
      icon: <Users className="w-4 h-4 text-green-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "ZAG"),
    },
    {
      setor: "Laterais",
      icon: <Users className="w-4 h-4 text-purple-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "LE" || v.label === "LD" || v.label === "LWB" || v.label === "RWB"),
    },
    {
      setor: "Volantes",
      icon: <Users className="w-4 h-4 text-orange-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "VOL" || v.label === "DM"),
    },
    {
      setor: "Meias",
      icon: <Users className="w-4 h-4 text-pink-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "MC" || v.label === "MEI" || v.label === "AM"),
    },
    {
      setor: "Extremos",
      icon: <Users className="w-4 h-4 text-cyan-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "PE" || v.label === "PD" || v.label === "LW" || v.label === "RW"),
    },
    {
      setor: "Atacantes",
      icon: <Users className="w-4 h-4 text-red-700" />,
      positions: Object.entries(positions).filter(([_, v]) => v.label === "ATA" || v.label === "ST"),
    },
  ];

  // Selecionar uma posição para atribuir jogador
  const selectPosition = (positionKey: string) => {
    setSelectingFor(positionKey);
    const label = positions[positionKey]?.label;
    setFilterPosition(getPositionFilter(label));
    setActiveTab("jogadores");
  };

  // Atribuir jogador à posição
  const assignPlayer = (player: Player) => {
    if (selectingFor) {
      setLineup({
        ...lineup,
        [selectingFor]: player
      });
      setSelectingFor(null);
      setActiveTab("campo");
    }
  };

  // Remover jogador de uma posição
  const removePlayerFromPosition = (positionKey: string) => {
    setLineup(prev => {
      const newLineup = { ...prev };
      delete newLineup[positionKey];
      return newLineup;
    });
  };

  // Limpar escalação
  const clearLineup = () => {
    setLineup({});
    toast.success("Escalação limpa com sucesso!");
  };

  // Status do elenco dinâmico
  const disponiveis = players.filter(p => p.status === "disponivel").length;
  const lesionados = players.filter(p => p.status === "lesionado").length;
  const suspensos = players.filter(p => p.status === "suspenso").length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Escalação de Jogadores</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Monte sua escalação tática para as partidas
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Seleção de categoria */}
        <Card className="md:col-span-3">
          <CardHeader className="pb-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center">
              <div>
                <CardTitle>Escalação por Categoria</CardTitle>
                <CardDescription>Defina sua escalação padrão para cada categoria</CardDescription>
              </div>
              <div className="flex items-center gap-2 mt-4 md:mt-0">
                <CategorySelector
                  onCategoryChange={(categoryId) => setSelectedCategoryId(categoryId)}
                  className="w-[220px]"
                />
                {selectedCategoryId && categories.find(c => c.id === selectedCategoryId) && (
                  <Badge className="ml-2">
                    {categories.find(c => c.id === selectedCategoryId)?.type === "championship" ? "Campeonato" : "Faixa Etária"}
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Área principal */}
        <div className="md:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Escalação {formation}</CardTitle>
                <div className="flex flex-col gap-2">
                  <label htmlFor="formation-select">Formação</label>
                  <select
                    id="formation-select"
                    value={formation}
                    onChange={e => handleFormationChange(e.target.value)}
                    className="border rounded px-2 py-1"
                    style={{ maxWidth: 120 }}
                  >
                    <option value="4-4-2">4-4-2</option>
                    <option value="4-3-3">4-3-3</option>
                    <option value="3-5-2">3-5-2</option>
                    <option value="4-2-3-1">4-2-3-1</option>
                    <option value="4-1-4-1">4-1-4-1</option>
                    <option value="3-4-3">3-4-3</option>
                    <option value="5-3-2">5-3-2</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="campo">Campo</TabsTrigger>
                  <TabsTrigger value="jogadores">Jogadores</TabsTrigger>
                  <TabsTrigger value="comparacao">Comparação</TabsTrigger>
                  <TabsTrigger value="lista">Lista por Posição</TabsTrigger>
                </TabsList>

                <TabsContent value="campo">
                  <div className="relative bg-green-100 rounded-md aspect-[3/4] w-full max-w-3xl mx-auto border border-green-200 overflow-hidden">
                    {/* Campo de futebol */}
                    <div className="absolute inset-0 w-full h-full">
                      {/* Linhas do campo */}
                      <div className="border-2 border-white absolute top-0 left-1/2 transform -translate-x-1/2 w-[80%] h-[40%]"></div>
                      <div className="border-2 border-white absolute top-[60%] left-1/2 transform -translate-x-1/2 w-[80%] h-[40%]"></div>
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[50%] h-[20%] border-2 border-white"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[50%] h-[20%] border-2 border-white"></div>
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 rounded-full border-2 border-white"></div>
                      <div className="absolute top-1/2 left-0 transform translate-x-0 -translate-y-1/2 w-full h-[2px] bg-white"></div>
                    </div>

                    {/* Posições dos jogadores no campo */}
                    <div className="relative w-full h-full">
                      {Object.entries(positions).map(([key, pos]) => (
                        <div
                          key={key}
                          className="absolute transform -translate-x-1/2 -translate-y-1/2"
                          style={{ left: pos.x, top: pos.y }}
                        >
                          <FieldPosition
                            position={key}
                            player={lineup[key]}
                            onSelect={selectPosition}
                            onRemove={removePlayerFromPosition}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-center mt-4 gap-2">
                    <Button variant="outline" onClick={clearLineup}>Limpar</Button>
                    <Button variant="outline">
                      <Share2 className="mr-2 h-4 w-4" />
                      Compartilhar
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="jogadores">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {/* Mostrar apenas jogadores da categoria selecionada */}
                    {(selectedCategoryId ? categoryPlayers : players)
                      .filter(p => !filterPosition || p.position === filterPosition)
                      .map(player => (
                        <div key={player.id} className="border rounded p-2 flex items-center gap-2 cursor-pointer hover:bg-accent" onClick={() => assignPlayer(player)}>
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={player.image || ""} />
                            <AvatarFallback>{player.number}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-sm">{player.name}</div>
                            <div className="text-xs text-muted-foreground">{player.position}</div>
                          </div>
                        </div>
                      ))
                    }
                    {(selectedCategoryId ? categoryPlayers : players).filter(p => !filterPosition || p.position === filterPosition).length === 0 && (
                      <div className="col-span-2 md:col-span-3 text-center text-muted-foreground">
                        {selectedCategoryId
                          ? "Nenhum jogador disponível nesta categoria para essa posição"
                          : "Nenhum jogador disponível para essa posição"}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="comparacao">
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-md">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="font-semibold">Comparação de Formações</h3>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <ArrowLeft className="h-4 w-4 mr-1" />
                            Anterior
                          </Button>
                          <Button variant="outline" size="sm">
                            Próxima
                            <ArrowRight className="h-4 w-4 ml-1" />
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">{formation}</h4>
                            <Badge>Ofensiva</Badge>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Finalizações</span>
                              <span className="text-sm font-medium">{calcularMediaFinalizacoes(matchHistory)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Gols Marcados</span>
                              <span className="text-sm font-medium">{calcularMediaGolsSofridos(matchHistory, clubId)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Gols Sofridos</span>
                              <span className="text-sm font-medium">{calcularMediaGolsMarcados(matchHistory, clubId)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">{calcularFormacaoMaisUsada(matchHistory)}</h4>
                            <Badge variant="outline">Balanceada</Badge>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Finalizações</span>
                              <span className="text-sm font-medium">{calcularMediaFinalizacoes(matchHistory)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Gols Marcados</span>
                              <span className="text-sm font-medium">{calcularMediaGolsMarcados(matchHistory, clubId)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm">Gols Sofridos</span>
                              <span className="text-sm font-medium">{calcularMediaGolsSofridos(matchHistory, clubId)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border rounded-md p-4">
                      <h3 className="font-semibold mb-3">Desempenho Individual por Formação</h3>
                      <Separator className="my-2" />

                      <div className="space-y-3 mt-4">
                        {players.map(player => {
                          const desempenho = calcularDesempenhoIndividualPorFormacao(matchHistory)[player.id] || {};
                          const formacoes = Object.keys(desempenho);
                          if (formacoes.length === 0) return null;
                          return (
                            <div key={player.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={player.image || ''} />
                                  <AvatarFallback className="bg-team-blue text-white text-xs">{player.number || player.name[0]}</AvatarFallback>
                                </Avatar>
                                <span>{player.name}</span>
                              </div>
                              <div className="flex items-center gap-4">
                                {formacoes.map(formacao => (
                                  <div key={formacao} className="text-center">
                                    <p className="text-xs text-muted-foreground">{formacao}</p>
                                    <p className="font-medium">{desempenho[formacao].jogos}j/{desempenho[formacao].gols}g</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="lista">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {["Goleiro", "Zagueiro", "Lateral", "Volante", "Meio-campista", "Extremo", "Atacante", "Outro"].map(pos => (
                      <div key={pos} className="border rounded-md p-3 bg-gray-50">
                        <div className="font-semibold mb-2 text-team-blue">{pos}</div>
                        {(selectedCategoryId ? categoryPlayers : players).filter(p => p.position === pos).length === 0 ? (
                          <div className="text-muted-foreground text-sm">
                            {selectedCategoryId
                              ? `Nenhum jogador desta categoria cadastrado para ${pos}`
                              : `Nenhum jogador cadastrado para ${pos}`}
                          </div>
                        ) : (
                          <ul className="space-y-1">
                            {(selectedCategoryId ? categoryPlayers : players).filter(p => p.position === pos).map(player => (
                              <li key={player.id} className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={player.image || ""} />
                                  <AvatarFallback>{player.number}</AvatarFallback>
                                </Avatar>
                                <span className="text-sm">{player.name}</span>
                                <span className="text-xs text-muted-foreground">{player.status}</span>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Painel lateral */}
        <div className="md:col-span-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Informações do Elenco
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-sm mb-2">Status do Elenco</h3>
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="border-green-200 bg-green-50 text-green-800">
                      {disponiveis} Disponíveis
                    </Badge>
                    <Badge variant="outline" className="border-rose-200 bg-rose-50 text-rose-800">
                      {lesionados} Lesionados
                    </Badge>
                    <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-800">
                      {suspensos} Suspensos
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold text-sm mb-2">Informações da Categoria</h3>
                  <div className="space-y-2">
                    {!selectedCategoryId ? (
                      <span className="text-xs text-muted-foreground">Selecione uma categoria para ver informações.</span>
                    ) : (
                      <>
                        <div className="flex justify-between items-center text-sm">
                          <span>Nome:</span>
                          <span className="font-semibold">{categories.find(c => c.id === selectedCategoryId)?.name}</span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span>Tipo:</span>
                          <span className="font-semibold">
                            {categories.find(c => c.id === selectedCategoryId)?.type === "championship"
                              ? "Campeonato"
                              : categories.find(c => c.id === selectedCategoryId)?.type === "age_group"
                                ? "Faixa Etária"
                                : "Personalizada"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span>Jogadores:</span>
                          <span className="font-semibold">{categoryPlayers.length}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold text-sm mb-2">Artilheiros da Temporada</h3>
                  <div className="space-y-2">
                    {scorersLoading ? (
                      <span className="text-xs text-muted-foreground">Carregando artilheiros...</span>
                    ) : scorersError ? (
                      <span className="text-xs text-red-500">{scorersError}</span>
                    ) : topScorers.length === 0 ? (
                      <span className="text-xs text-muted-foreground">Nenhum gol registrado nesta temporada.</span>
                    ) : (
                      topScorers.slice(0, 5).map((scorer, idx) => {
                        const player = scorersPlayers[scorer.player];
                        return (
                          <div className="flex items-center justify-between" key={scorer.player}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                {player?.image ? (
                                  <AvatarImage src={player.image} />
                                ) : (
                                  <AvatarFallback className="bg-team-blue text-white text-xs">{player?.number || idx + 1}</AvatarFallback>
                                )}
                              </Avatar>
                              <span>{player?.name || `#${scorer.player}`}</span>
                            </div>
                            <span className="font-medium">{scorer.goals} gol{scorer.goals !== 1 ? 's' : ''}</span>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold text-sm mb-3">Ações</h3>
                  {selectedCategoryId ? (
                    <>
                      <Button variant="outline" className="w-full mb-2" onClick={clearLineup}>Limpar Escalação</Button>
                      <Button variant="outline" className="w-full mb-2">Usar Formação 4-3-3</Button>
                      <Button variant="outline" className="w-full">Usar Formação 4-4-2</Button>
                    </>
                  ) : (
                    <div className="text-xs text-muted-foreground text-center">
                      Selecione uma categoria para gerenciar a escalação
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
}
