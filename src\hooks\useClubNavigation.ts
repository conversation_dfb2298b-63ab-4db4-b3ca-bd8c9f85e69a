import { useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

/**
 * Hook para navegação dentro do contexto do clube
 * Mantém o slug do clube nas navegações
 */
export function useClubNavigation() {
  const navigate = useNavigate();
  const { clubSlug } = useParams<{ clubSlug: string }>();

  const navigateToClubPage = useCallback((path: string) => {
    if (!clubSlug) {
      console.warn('Slug do clube não encontrado, redirecionando para login');
      navigate('/login');
      return;
    }

    // Garantir que o path comece com /
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    navigate(`/${clubSlug}${cleanPath}`);
  }, [navigate, clubSlug]);

  const navigateToClubPageReplace = useCallback((path: string) => {
    if (!clubSlug) {
      console.warn('Slug do clube não encontrado, redirecionando para login');
      navigate('/login', { replace: true });
      return;
    }

    // Garantir que o path comece com /
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    navigate(`/${clubSlug}${cleanPath}`, { replace: true });
  }, [navigate, clubSlug]);

  const getClubUrl = useCallback((path: string) => {
    if (!clubSlug) return path;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `/${clubSlug}${cleanPath}`;
  }, [clubSlug]);

  return {
    navigateToClubPage,
    navigateToClubPageReplace,
    getClubUrl,
    clubSlug
  };
}