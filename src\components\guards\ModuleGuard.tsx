import { ReactNode } from "react";
import { useUser } from "@/context/UserContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Lock, CreditCard, Mail } from "lucide-react";

interface ModuleGuardProps {
  module: string;
  children: ReactNode;
  fallback?: ReactNode;
}

// Mapeamento de módulos para nomes amigáveis
const MODULE_NAMES: Record<string, string> = {
  dashboard: 'Dashboard',
  players: 'Elenco',
  trainings: 'Treinamentos',
  matches: '<PERSON>ó<PERSON><PERSON> de Partidas',
  evaluations: '<PERSON><PERSON><PERSON><PERSON> de Avaliações',
  medical: '<PERSON><PERSON><PERSON><PERSON>',
  finances: '<PERSON>ódulo <PERSON>iro',
  administrative: 'Módulo Administrativo',
  inventory: 'Mó<PERSON><PERSON> de Estoque',
  reports: 'Módulo de Relatórios',
  accommodations: '<PERSON><PERSON><PERSON><PERSON> de Alojamentos',
  meals: '<PERSON><PERSON><PERSON><PERSON> de Alimentação',
  billing: '<PERSON><PERSON><PERSON><PERSON> Cobranças',
  monthly_fees: '<PERSON><PERSON><PERSON><PERSON> de Mensalidades',
  callups: '<PERSON>ó<PERSON>lo de Convocações'
};

// Componente de bloqueio de módulo
const ModuleBlockedScreen = ({ module, planName }: { module: string; planName?: string }) => {
  const moduleName = MODULE_NAMES[module] || module;

  return (
    <div className="min-h-[60vh] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Lock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <CardTitle className="text-2xl font-bold text-gray-900">
            Módulo Não Disponível
          </CardTitle>
          <CardDescription className="text-lg">
            O módulo <span className="font-semibold">{moduleName}</span> não está incluído no seu plano atual.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            Para acessar este módulo, você precisa fazer upgrade para um plano que o inclua.
          </p>
          
          <div className="space-y-2">
            <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>?subject=Upgrade de Plano')}>
              <CreditCard className="w-4 h-4 mr-2" />
              Fazer Upgrade
            </Button>
            <Button variant="outline" className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
              <Mail className="w-4 h-4 mr-2" />
              Falar com Suporte
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-gray-500">
              Plano atual: <span className="font-medium">{planName || 'Não definido'}</span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export function ModuleGuard({ module, children, fallback }: ModuleGuardProps) {
  const { user } = useUser();

  const hasModuleAccess = (): boolean => {
    if (!user?.club_info) {
      return false;
    }

    const clubInfo = user.club_info;
    
    // Se não tem plano definido, negar acesso
    if (!clubInfo.master_plans) {
      return false;
    }

    const plan = clubInfo.master_plans;
    
    // Verificar se o módulo está incluído no plano
    if (plan.modules && plan.modules[module] === true) {
      return true;
    }

    // Verificar se o módulo está nos módulos customizados
    if (clubInfo.custom_modules && clubInfo.custom_modules[module] === true) {
      return true;
    }

    return false;
  };

  if (!hasModuleAccess()) {
    return fallback || <ModuleBlockedScreen module={module} planName={user?.club_info?.master_plans?.name} />;
  }

  return <>{children}</>;
}

// Hook para verificar acesso a módulos
export function useModuleAccess(module: string): boolean {
  const { user } = useUser();

  if (!user?.club_info?.master_plans) {
    return false;
  }

  const plan = user.club_info.master_plans;
  const customModules = user.club_info.custom_modules;

  // Verificar no plano
  if (plan.modules && plan.modules[module] === true) {
    return true;
  }

  // Verificar nos módulos customizados
  if (customModules && customModules[module] === true) {
    return true;
  }

  return false;
}