import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useCurrentClubId } from '@/context/ClubContext';
import { 
  DollarSign, 
  Calendar, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Plus,
  RefreshCw,
  Mail,
  Eye,
  CreditCard,
  Download
} from 'lucide-react';
import {
  getPlayerMonthly<PERSON>ees,
  generateMonthlyFeesForMonth,
  updateOverdueMonthlyFees,
  markMonthlyFeeAsPaid,
  createMonthlyFeePixCharge,
  getMonthlyFeeStats,
  PlayerMonthlyFee
} from '@/api/monthlyFees';
import { sendPaymentReminder, sendOverdueNotification, sendPaymentLink, sendReceiptConfirmation } from '@/services/monthlyFeeEmailService';
import { getClubInfo } from '@/api/api';

const MONTHS = [
  'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
  'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
];

interface Stats {
  total: number;
  paid: number;
  pending: number;
  overdue: number;
  cancelled: number;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
}

export function MonthlyFeeDashboard() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  
  const [monthlyFees, setMonthlyFees] = useState<PlayerMonthlyFee[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [generateYear, setGenerateYear] = useState(new Date().getFullYear());
  const [generateMonth, setGenerateMonth] = useState(new Date().getMonth() + 1);
  const [clubInfo, setClubInfo] = useState<any>(null);

  useEffect(() => {
    loadData();
    loadClubInfo();
  }, [clubId, selectedYear, selectedMonth, statusFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [feesData, statsData] = await Promise.all([
        getPlayerMonthlyFees(clubId, {
          reference_year: selectedYear,
          reference_month: selectedMonth,
          status: statusFilter && statusFilter !== 'all' ? statusFilter : undefined
        }),
        getMonthlyFeeStats(clubId, selectedYear, selectedMonth)
      ]);
      
      setMonthlyFees(feesData);
      setStats(statsData);
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os dados das mensalidades',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadClubInfo = async () => {
    try {
      const info = await getClubInfo(clubId);
      setClubInfo(info);
    } catch (error) {
      console.error('Erro ao carregar informações do clube:', error);
    }
  };

  const handleGenerateMonthlyFees = async () => {
    try {
      const count = await generateMonthlyFeesForMonth(clubId, generateYear, generateMonth);
      toast({
        title: 'Sucesso',
        description: `${count} mensalidades foram geradas para ${MONTHS[generateMonth - 1]}/${generateYear}`
      });
      setIsGenerateDialogOpen(false);
      
      // Recarregar dados se estamos visualizando o mesmo mês/ano
      if (generateYear === selectedYear && generateMonth === selectedMonth) {
        loadData();
      }
    } catch (error: any) {
      console.error('Erro ao gerar mensalidades:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao gerar mensalidades',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateOverdue = async () => {
    try {
      const count = await updateOverdueMonthlyFees(clubId);
      toast({
        title: 'Sucesso',
        description: `${count} mensalidades foram marcadas como em atraso`
      });
      loadData();
    } catch (error: any) {
      console.error('Erro ao atualizar mensalidades em atraso:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao atualizar mensalidades em atraso',
        variant: 'destructive'
      });
    }
  };

  const handleMarkAsPaid = async (monthlyFee: PlayerMonthlyFee) => {
    if (!confirm(`Marcar mensalidade de ${monthlyFee.player_name} como paga?`)) {
      return;
    }

    try {
      // Marcar mensalidade como paga
      await markMonthlyFeeAsPaid(clubId, monthlyFee.id, 'manual');
      
      // Enviar email de confirmação se o jogador tiver email
      if (monthlyFee.player_email) {
        try {
          await sendReceiptConfirmation({
            clubId,
            playerId: monthlyFee.player_id,
            playerName: monthlyFee.player_name || 'Jogador',
            playerEmail: monthlyFee.player_email,
            clubName: clubInfo?.name || 'Clube',
            monthlyFeeId: monthlyFee.id,
            feeName: monthlyFee.fee_setting_name || 'Mensalidade',
            referenceMonth: monthlyFee.reference_month,
            referenceYear: monthlyFee.reference_year,
            receiptFileName: 'Pagamento Manual',
            status: 'approved',
            reviewNotes: 'Mensalidade marcada como paga manualmente pela administração'
          });
        } catch (emailError) {
          console.error('Erro ao enviar email de confirmação:', emailError);
          // Não falhar a operação se o email não for enviado
        }
      }

      toast({
        title: 'Sucesso',
        description: monthlyFee.player_email 
          ? 'Mensalidade marcada como paga e email de confirmação enviado'
          : 'Mensalidade marcada como paga'
      });
      loadData();
    } catch (error: any) {
      console.error('Erro ao marcar como paga:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao marcar mensalidade como paga',
        variant: 'destructive'
      });
    }
  };

  const handleSendPaymentLink = async (monthlyFee: PlayerMonthlyFee) => {
    if (!monthlyFee.player_email) {
      toast({
        title: 'Erro',
        description: 'Jogador não possui email cadastrado',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Criar cobrança PIX se não existir
      const pixData = await createMonthlyFeePixCharge(clubId, monthlyFee.id);
      
      // Enviar email
      const success = await sendPaymentLink({
        clubId,
        playerId: monthlyFee.player_id,
        playerName: monthlyFee.player_name || 'Jogador',
        playerEmail: monthlyFee.player_email,
        clubName: clubInfo?.name || 'Clube',
        monthlyFeeId: monthlyFee.id,
        feeName: monthlyFee.fee_setting_name || 'Mensalidade',
        amount: monthlyFee.final_amount || monthlyFee.amount,
        referenceMonth: monthlyFee.reference_month,
        referenceYear: monthlyFee.reference_year,
        pixCode: pixData.pix_code,
        paymentLink: `${import.meta.env.VITE_SITE_URL || "http://localhost:3000"}/player/monthly-fees/${monthlyFee.id}`
      });

      if (success) {
        toast({
          title: 'Sucesso',
          description: 'Link de pagamento enviado por email'
        });
      } else {
        toast({
          title: 'Erro',
          description: 'Falha ao enviar email',
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      console.error('Erro ao enviar link de pagamento:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao enviar link de pagamento',
        variant: 'destructive'
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Pago</Badge>;
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />Em Atraso</Badge>;
      case 'cancelled':
        return <Badge variant="outline"><XCircle className="w-3 h-3 mr-1" />Cancelado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="flex justify-center items-center h-20">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Mensalidades</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.total_amount)} no total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pagas</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.paid}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.paid_amount)} recebido
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <p className="text-xs text-muted-foreground">
                Aguardando pagamento
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Em Atraso</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
              <p className="text-xs text-muted-foreground">
                Requer atenção
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Controles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Mensalidades
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsGenerateDialogOpen(true)}
                className="text-xs sm:text-sm"
              >
                <Plus className="w-3 w-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Gerar Mensalidades</span>
                <span className="sm:hidden">Gerar</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleUpdateOverdue}
                className="text-xs sm:text-sm"
              >
                <RefreshCw className="w-3 w-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Atualizar Atrasos</span>
                <span className="sm:hidden">Atualizar</span>
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="space-y-2">
              <Label>Ano</Label>
              <Select
                value={selectedYear.toString()}
                onValueChange={(value) => setSelectedYear(parseInt(value))}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Mês</Label>
              <Select
                value={selectedMonth.toString()}
                onValueChange={(value) => setSelectedMonth(parseInt(value))}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MONTHS.map((month, index) => (
                    <SelectItem key={index} value={(index + 1).toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendente</SelectItem>
                  <SelectItem value="paid">Pago</SelectItem>
                  <SelectItem value="overdue">Em Atraso</SelectItem>
                  <SelectItem value="cancelled">Cancelado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {monthlyFees.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma mensalidade encontrada</p>
              <p className="text-sm">
                {selectedMonth && selectedYear 
                  ? `Não há mensalidades para ${MONTHS[selectedMonth - 1]}/${selectedYear}`
                  : 'Ajuste os filtros ou gere novas mensalidades'
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Jogador</TableHead>
                    <TableHead>Mensalidade</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Vencimento</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Pagamento</TableHead>
                    <TableHead className="w-[120px]">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {monthlyFees.map((fee) => (
                    <TableRow key={fee.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{fee.player_name}</div>
                          {fee.category_name && (
                            <div className="text-sm text-muted-foreground">
                              {fee.category_name}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{fee.fee_setting_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {MONTHS[fee.reference_month - 1]}/{fee.reference_year}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {formatCurrency(fee.final_amount || fee.amount)}
                          </div>
                          {fee.late_fee_applied > 0 && (
                            <div className="text-sm text-red-600">
                              +{formatCurrency(fee.late_fee_applied)} multa
                            </div>
                          )}
                          {fee.discount_applied > 0 && (
                            <div className="text-sm text-green-600">
                              -{formatCurrency(fee.discount_applied)} desconto
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(fee.due_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(fee.status)}
                      </TableCell>
                      <TableCell>
                        {fee.paid_at ? (
                          <div className="text-sm">
                            <div>{formatDate(fee.paid_at)}</div>
                            <div className="text-muted-foreground">
                              {fee.payment_method || 'N/A'}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {fee.status !== 'paid' && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSendPaymentLink(fee)}
                                title="Enviar link de pagamento"
                              >
                                <Mail className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMarkAsPaid(fee)}
                                title="Marcar como pago"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog para gerar mensalidades */}
      <Dialog open={isGenerateDialogOpen} onOpenChange={setIsGenerateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Gerar Mensalidades</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="generate_year">Ano</Label>
                <Select
                  value={generateYear.toString()}
                  onValueChange={(value) => setGenerateYear(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="generate_month">Mês</Label>
                <Select
                  value={generateMonth.toString()}
                  onValueChange={(value) => setGenerateMonth(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {MONTHS.map((month, index) => (
                      <SelectItem key={index} value={(index + 1).toString()}>
                        {month}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              <p>Serão geradas mensalidades para todos os jogadores ativos baseadas nas configurações ativas.</p>
              <p>Mensalidades já existentes para o período não serão duplicadas.</p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsGenerateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleGenerateMonthlyFees}>
              Gerar Mensalidades
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}