import { Alert<PERSON>riangle, Credit<PERSON>ard, Clock, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePaymentWarnings } from '@/hooks/usePaymentWarnings';
import { useAdminRole } from '@/hooks/useAdminRole';
import { useState } from 'react';

export function PaymentWarningBanner() {
  const { paymentWarning } = usePaymentWarnings();
  const { isAdmin, loading: adminLoading } = useAdminRole();
  const [dismissed, setDismissed] = useState(false);

  // Só mostrar para usuários com roles administrativos
  if (!paymentWarning?.shouldShow || dismissed || adminLoading || !isAdmin) {
    return null;
  }

  const getIcon = () => {
    switch (paymentWarning.type) {
      case 'payment_overdue':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'payment_due_soon':
        return paymentWarning.severity === 'error' 
          ? <AlertTriangle className="h-5 w-5 text-red-500" />
          : <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <CreditCard className="h-5 w-5 text-blue-500" />;
    }
  };

  const getBannerStyle = () => {
    switch (paymentWarning.severity) {
      case 'error':
        return 'bg-red-50 border-l-4 border-red-400';
      case 'warning':
        return 'bg-yellow-50 border-l-4 border-yellow-400';
      default:
        return 'bg-blue-50 border-l-4 border-blue-400';
    }
  };

  const getTextColor = () => {
    switch (paymentWarning.severity) {
      case 'error':
        return 'text-red-700';
      case 'warning':
        return 'text-yellow-700';
      default:
        return 'text-blue-700';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <div className={`${getBannerStyle()} p-4 mb-4 relative`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${getTextColor()}`}>
                {paymentWarning.message}
              </p>
              {paymentWarning.nextPaymentDate && (
                <p className={`text-xs mt-1 ${getTextColor()} opacity-75`}>
                  Vencimento: {formatDate(paymentWarning.nextPaymentDate)}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                className="text-xs"
                onClick={() => window.open('mailto:<EMAIL>?subject=Pagamento de Mensalidade')}
              >
                <CreditCard className="w-3 h-3 mr-1" />
                Pagar Agora
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-xs p-1"
                onClick={() => setDismissed(true)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}