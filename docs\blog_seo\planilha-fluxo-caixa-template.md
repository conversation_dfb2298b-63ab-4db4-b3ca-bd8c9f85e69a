# Template: Planilha de Fluxo de Caixa para Clubes

## Como Criar no Google Sheets

### 1. Acesse: [sheets.google.com](https://sheets.google.com)
### 2. Crie nova planilha: "Fluxo de Caixa - [Nome do Clube]"
### 3. <PERSON><PERSON> as abas conforme abaixo:

---

## ABA 1: "Dashboard"

### Linha 1-3: <PERSON><PERSON><PERSON><PERSON><PERSON>
```
A1: FLUXO DE CAIXA - [NOME DO CLUBE]
A2: Período: Janeiro a Dezembro 2025
A3: Atualizado em: [Data Atual]
```

### Linha 5-8: Indicadores Principais
```
A5: SALDO ATUAL          B5: =Projeção.D2
A6: RECEITA MENSAL       B6: =MÉDIA(Projeção.B:B)
A7: DESPESA MENSAL       B7: =MÉDIA(Projeção.C:C)
A8: MARGEM DE LUCRO      B8: =(B6-B7)/B6*100&"%"
```

### Linha 10-15: <PERSON><PERSON><PERSON>
```
A10: STATUS FINANCEIRO
B10: =SE(B5>10000;"🟢 SAUDÁVEL";SE(B5>0;"🟡 ATENÇÃO";"🔴 CRÍTICO"))

A12: PRÓXIMOS VENCIMENTOS
A13: [Lista dos próximos 5 vencimentos importantes]
```

---

## ABA 2: "Receitas"

### Cabeçalho (Linha 1):
| A | B | C | D | E | F |
|---|---|---|---|---|---|
| Data | Categoria | Descrição | Valor | Status | Observações |

### Dados de Exemplo (Linha 2+):
```
01/01/2025 | Mensalidades | Categoria Sub-15 | 5000 | Recebido | 50 atletas x R$100
05/01/2025 | Mensalidades | Categoria Sub-17 | 7500 | Recebido | 50 atletas x R$150
10/01/2025 | Patrocínio | Empresa ABC | 10000 | Pendente | Contrato anual
15/01/2025 | Eventos | Torneio Interno | 2000 | Recebido | Taxa de inscrição
```

### Categorias Pré-definidas (Coluna H):
```
H1: CATEGORIAS DE RECEITA
H2: Mensalidades
H3: Patrocínios
H4: Eventos
H5: Vendas (uniformes/produtos)
H6: Doações
H7: Premiações
H8: Aluguel de espaços
H9: Outros
```

### Totalizadores (Linha 50+):
```
A50: TOTAL RECEITAS:     D50: =SOMA(D:D)
A51: RECEBIDO:           D51: =SOMASE(E:E,"Recebido",D:D)
A52: PENDENTE:           D52: =SOMASE(E:E,"Pendente",D:D)
```

---

## ABA 3: "Despesas"

### Cabeçalho (Linha 1):
| A | B | C | D | E | F |
|---|---|---|---|---|---|
| Data | Categoria | Descrição | Valor | Status | Fornecedor |

### Dados de Exemplo (Linha 2+):
```
01/01/2025 | Salários | Técnico Principal | 3000 | Pago | João Silva
01/01/2025 | Salários | Preparador Físico | 2000 | Pago | Maria Santos
05/01/2025 | Aluguel | Campo de Treino | 1500 | Pago | Complexo Esportivo XYZ
10/01/2025 | Materiais | Bolas de Futebol | 500 | Pago | Loja Esportiva ABC
15/01/2025 | Transporte | Ônibus para Jogo | 800 | Pendente | Viação Rápida
```

### Categorias Pré-definidas (Coluna H):
```
H1: CATEGORIAS DE DESPESA
H2: Salários
H3: Encargos Sociais
H4: Aluguel (campo/sede)
H5: Materiais Esportivos
H6: Uniformes
H7: Transporte
H8: Alimentação
H9: Médico/Fisioterapia
H10: Arbitragem
H11: Inscrições Competições
H12: Marketing
H13: Administrativo
H14: Manutenção
H15: Outros
```

### Totalizadores (Linha 50+):
```
A50: TOTAL DESPESAS:     D50: =SOMA(D:D)
A51: PAGO:               D51: =SOMASE(E:E,"Pago",D:D)
A52: PENDENTE:           D52: =SOMASE(E:E,"Pendente",D:D)
```

---

## ABA 4: "Projeção 12 Meses"

### Cabeçalho (Linha 1):
| A | B | C | D | E |
|---|---|---|---|---|
| Mês | Receitas | Despesas | Saldo Mensal | Saldo Acumulado |

### Fórmulas (Linha 2+):
```
A2: Janeiro
B2: =SOMASES(Receitas.D:D,Receitas.A:A,">="&DATA(2025,1,1),Receitas.A:A,"<"&DATA(2025,2,1))
C2: =SOMASES(Despesas.D:D,Despesas.A:A,">="&DATA(2025,1,1),Despesas.A:A,"<"&DATA(2025,2,1))
D2: =B2-C2
E2: =D2

A3: Fevereiro
B3: =SOMASES(Receitas.D:D,Receitas.A:A,">="&DATA(2025,2,1),Receitas.A:A,"<"&DATA(2025,3,1))
C3: =SOMASES(Despesas.D:D,Despesas.A:A,">="&DATA(2025,2,1),Despesas.A:A,"<"&DATA(2025,3,1))
D3: =B3-C3
E3: =E2+D3
```

### Gráfico (Inserir após linha 15):
- **Tipo**: Gráfico de linhas
- **Dados**: Colunas A, D, E (Mês, Saldo Mensal, Saldo Acumulado)
- **Título**: "Evolução do Fluxo de Caixa"

---

## ABA 5: "Análises"

### Indicadores Automáticos:
```
A1: ANÁLISE FINANCEIRA

A3: Receita Média Mensal:    B3: =MÉDIA(Projeção.B:B)
A4: Despesa Média Mensal:    B4: =MÉDIA(Projeção.C:C)
A5: Margem de Lucro:         B5: =(B3-B4)/B3*100&"%"

A7: Maior Receita:           B7: =MÁXIMO(Receitas.D:D)
A8: Maior Despesa:           B8: =MÁXIMO(Despesas.D:D)

A10: Meses com Saldo Positivo: B10: =CONT.SE(Projeção.D:D,">0")
A11: Meses com Saldo Negativo: B11: =CONT.SE(Projeção.D:D,"<0")

A13: ALERTAS:
A14: =SE(B5<10%,"⚠️ Margem de lucro baixa","✅ Margem saudável")
A15: =SE(MÍNIMO(Projeção.E:E)<0,"⚠️ Saldo negativo previsto","✅ Fluxo positivo")
```

### Top 5 Receitas por Categoria:
```
A17: TOP 5 RECEITAS POR CATEGORIA
A18: [Usar tabela dinâmica ou SOMASE para cada categoria]
```

### Top 5 Despesas por Categoria:
```
A25: TOP 5 DESPESAS POR CATEGORIA
A26: [Usar tabela dinâmica ou SOMASE para cada categoria]
```

---

## ABA 6: "Configurações"

### Metas Mensais:
```
A1: METAS E CONFIGURAÇÕES

A3: Meta Receita Mensal:     B3: 25000
A4: Meta Despesa Mensal:     B4: 20000
A5: Meta Margem Lucro:       B5: 20%

A7: Alerta Saldo Baixo:      B7: 5000
A8: Alerta Despesa Alta:     B8: 25000
```

### Categorias Personalizadas:
```
A10: SUAS CATEGORIAS PERSONALIZADAS
A11: (Adicione categorias específicas do seu clube)
```

### Contatos Importantes:
```
A15: CONTATOS FINANCEIROS
A16: Contador: [Nome e telefone]
A17: Banco: [Agência e gerente]
A18: Fornecedores principais: [Lista]
```

---

## Formatação e Validação

### Formatação de Células:
- **Valores monetários**: Formato moeda (R$)
- **Datas**: Formato dd/mm/aaaa
- **Percentuais**: Formato percentual com 1 casa decimal
- **Status**: Lista suspensa (Pago/Pendente/Recebido)

### Validação de Dados:
- **Coluna Status**: Lista = "Pago,Pendente,Recebido"
- **Coluna Categoria**: Lista das categorias pré-definidas
- **Valores**: Apenas números positivos

### Formatação Condicional:
- **Saldo positivo**: Verde
- **Saldo negativo**: Vermelho
- **Alertas**: Amarelo
- **Vencimentos próximos**: Laranja

---

## Instruções de Uso (Incluir como comentários)

### Na célula A1 da aba Dashboard:
```
COMO USAR ESTA PLANILHA:

1. PREENCHA AS RECEITAS:
   - Vá para aba "Receitas"
   - Adicione todas as entradas de dinheiro
   - Use as categorias pré-definidas

2. PREENCHA AS DESPESAS:
   - Vá para aba "Despesas"  
   - Adicione todas as saídas de dinheiro
   - Mantenha comprovantes organizados

3. ACOMPANHE A PROJEÇÃO:
   - Aba "Projeção 12 Meses" mostra o futuro
   - Gráfico ajuda a visualizar tendências
   - Saldo acumulado mostra situação geral

4. ANALISE OS RESULTADOS:
   - Aba "Análises" mostra indicadores
   - Identifique pontos de melhoria
   - Use alertas para tomar decisões

5. ATUALIZE SEMANALMENTE:
   - Adicione novas receitas e despesas
   - Revise projeções mensalmente
   - Ajuste metas conforme necessário

DICAS:
- Seja realista nas projeções
- Mantenha reserva de emergência
- Revise categorias periodicamente
- Faça backup mensal da planilha
```

---

## Próximos Passos

### Para Implementar:
1. **Criar no Google Sheets** usando este template
2. **Personalizar** com dados do seu clube
3. **Testar fórmulas** com dados reais
4. **Exportar para Excel** (.xlsx)
5. **Criar PDF** com instruções
6. **Disponibilizar** para download

### Tempo Estimado: 4 horas
### Resultado: Lead magnet profissional e funcional

Quer que eu continue com os outros lead magnets ou prefere focar em outra tarefa?