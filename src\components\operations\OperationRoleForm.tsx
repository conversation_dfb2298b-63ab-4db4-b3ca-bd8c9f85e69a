import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>ooter, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCurrentClubId } from '@/context/ClubContext';
import { useUser } from '@/context/UserContext';
import { createOperationRole } from '@/api/gameOperations';
import { toast } from '@/components/ui/use-toast';

interface OperationRoleFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreated?: (name: string) => void;
}

export function OperationRoleForm({ open, onOpenChange, onCreated }: OperationRoleFormProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) return;
    try {
      setLoading(true);
      const role = await createOperationRole(clubId, user?.id || '', name.trim());
      toast({ title: 'Sucesso', description: 'Função criada com sucesso' });
      onOpenChange(false);
      onCreated?.(role.name);
      setName('');
    } catch (err: any) {
      console.error(err);
      toast({ title: 'Erro', description: 'Não foi possível criar a função', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Nova Função</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-2">
          <Input placeholder="Nome da função" value={name} onChange={e => setName(e.target.value)} />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={!name.trim() || loading}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
