import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { teamThemes, TeamTheme } from "@/data/themes";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { hexToHsl, generateSecondaryColor } from "@/utils/themeUtils";

interface ThemeContextProps {
  theme: TeamTheme;
  setThemeId: (id: string) => void;
  logo: string | null;
  setLogo: (logo: string | null) => void;
  clubName: string;
  setClubName: (name: string) => void;
}

const ThemeContext = createContext<ThemeContextProps | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [themeId, setThemeId] = useState<string>(() => {
    const stored = localStorage.getItem("themeId");
    return stored && teamThemes[stored] ? stored : "blue";
  });
  const [logo, setLogo] = useState<string | null>(() => localStorage.getItem("teamLogo") || null);
  const [clubName, setClubName] = useState<string>(() => localStorage.getItem("clubName") || "Clube Exemplo");

  // Obter o clubInfo do store
  const clubInfo = useClubInfoStore(state => state.clubInfo);

  // Sincronizar com o clubInfo quando ele mudar
  useEffect(() => {
    if (clubInfo) {
      if (clubInfo.name) {
        setClubName(clubInfo.name);
        localStorage.setItem("clubName", clubInfo.name);
      }

      if (clubInfo.logo_url) {
        setLogo(clubInfo.logo_url);
        localStorage.setItem("teamLogo", clubInfo.logo_url);
      }

      // Definir o tema apenas se o ID existir na lista de temas
      if (clubInfo.primary_color && teamThemes[clubInfo.primary_color]) {
        setThemeId(clubInfo.primary_color);
      }
    }
  }, [clubInfo]);

  useEffect(() => {
    localStorage.setItem("themeId", themeId);
  }, [themeId]);

  useEffect(() => {
    if (logo) localStorage.setItem("teamLogo", logo);
    else localStorage.removeItem("teamLogo");
  }, [logo]);

  useEffect(() => {
    localStorage.setItem("clubName", clubName);
  }, [clubName]);

  const baseTheme = teamThemes[themeId] || teamThemes["blue"];

  // Determinar as cores primária e secundária considerando personalizações
  let primaryColor = baseTheme.colors.primary;
  let secondaryColor = baseTheme.colors.secondary;

  if (clubInfo?.primary_color) {
    if (teamThemes[clubInfo.primary_color]) {
      const clubTheme = teamThemes[clubInfo.primary_color];
      primaryColor = clubTheme.colors.primary;
      secondaryColor = clubInfo.secondary_color || clubTheme.colors.secondary;
    } else {
      primaryColor = clubInfo.primary_color;
      secondaryColor = clubInfo.secondary_color || generateSecondaryColor(primaryColor);
    }
  }

  const theme: TeamTheme = {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: primaryColor,
      secondary: secondaryColor,
    },
  };

  // Set CSS variables for theme colors
  useEffect(() => {
    const root = document.documentElement;

    // Atualizar variáveis CSS legadas (manter compatibilidade)
    root.style.setProperty("--color-primary", primaryColor);
    root.style.setProperty("--color-secondary", secondaryColor);
    root.style.setProperty("--color-background", theme.colors.background);
    root.style.setProperty("--color-text", theme.colors.text);

    // Atualizar as variáveis CSS do Tailwind (sistema principal)
    root.style.setProperty("--primary", hexToHsl(primaryColor));
    root.style.setProperty("--secondary", hexToHsl(secondaryColor));

    // Atualizar variáveis relacionadas ao tema primário
    root.style.setProperty("--ring", hexToHsl(primaryColor));
    root.style.setProperty("--accent", hexToHsl(secondaryColor));

    // Atualizar cores do sidebar para seguir o tema
    root.style.setProperty("--sidebar-primary", hexToHsl(primaryColor));
    root.style.setProperty("--sidebar-accent", hexToHsl(secondaryColor));
  }, [primaryColor, secondaryColor, theme.colors.background, theme.colors.text]);

  return (
    <ThemeContext.Provider value={{ theme, setThemeId, logo, setLogo, clubName, setClubName }}>
      {children}
    </ThemeContext.Provider>
  );
};

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) throw new Error("useTheme must be used within a ThemeProvider");
  return context;
}
