import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useFastClubNavigation } from "@/hooks/useFastClubNavigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  Loader2,
  Edit,
  ArrowLeft,
  Eye,
  Download,
  Home,
  Target
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import {
  getCallupById,
  getCallupPlayers,
  addPlayerToCallup,
  removePlayerFromCallup,
  updateCallup,
  generateCallupPDF,
  generateCallupAccommodationPDF,
  generateCallupDelegationPDF,
  Callup,
  CallupPlayer
} from "@/api/callups";
import { CallupOperationStaff, getCallupOperationStaff, addOperationStaffToCallup, removeOperationStaffFromCallup } from "@/api/gameOperations";
import { getCategories, getCategoryPlayers, Category } from "@/api/api";
import { getClubInfo, ClubInfo } from "@/api/api";
import { getClubUsers, ClubUser } from "@/api/users";
import { getCollaborators, Collaborator } from "@/api/collaborators";
import { CallupDesignEditor } from "@/components/callups/CallupDesignEditor";
import { CallupPreview } from "@/components/callups/CallupPreview";
import { CallupAccommodationControl } from "@/components/callups/CallupAccommodationControl";
import { isCallupEditable } from "@/utils/formatters";

// Componente principal da página de detalhes da convocação
export default function ConvocacaoDetalhesPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { navigateToClubPage } = useFastClubNavigation();
  const clubId = useCurrentClubId();
  const { user } = useUser();

  const [callup, setCallup] = useState<Callup | null>(null);
  const [callupPlayers, setCallupPlayers] = useState<CallupPlayer[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryPlayers, setCategoryPlayers] = useState<any[]>([]);
  const [clubUsers, setClubUsers] = useState<ClubUser[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [operationStaff, setOperationStaff] = useState<CallupOperationStaff[]>([]);
  const [clubInfo, setClubInfo] = useState<ClubInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [selectedTab, setSelectedTab] = useState("design");
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [generatingAccommodationPDF, setGeneratingAccommodationPDF] = useState(false);
  const [generatingGuestPDF, setGeneratingGuestPDF] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    players: true,
    schedule: true,
    hotel: true,
    notices: true,
    staff: true,
    technical: true,
    executive: true
  });

  // Carregar dados da convocação
  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Carregar detalhes da convocação
        const callupData = await getCallupById(clubId, parseInt(id));
        setCallup(callupData);

        // Se a convocação não for mais editável e a aba atual for "design", mudar para "preview"
        if (!isCallupEditable(callupData.match_date) && selectedTab === "design") {
          setSelectedTab("preview");
        }

        // Carregar jogadores da convocação
        const playersData = await getCallupPlayers(clubId, parseInt(id));
        setCallupPlayers(playersData);

        // Carregar categorias
        const categoriesData = await getCategories(clubId);
        setCategories(categoriesData);

        // Carregar jogadores da categoria
        if (callupData.category_id) {
          try {
            const categoryPlayersData = await getCategoryPlayers(clubId, callupData.category_id);
            setCategoryPlayers(categoryPlayersData);
          } catch (error) {
            console.error("Erro ao buscar jogadores da categoria:", error);
            setCategoryPlayers([]);
          }
        }

        // Carregar usuários do clube (para comissão técnica)
        const usersData = await getClubUsers(clubId);
        setClubUsers(usersData);

        // Carregar colaboradores do clube (para comissão técnica)
        try {
          const collaboratorsData = await getCollaborators(clubId);
          setCollaborators(collaboratorsData);
        } catch (error) {
          console.error("Erro ao buscar colaboradores:", error);
          setCollaborators([]);
        }

        try {
          const opData = await getCallupOperationStaff(clubId, parseInt(id));
          setOperationStaff(opData);
        } catch (error) {
          console.error("Erro ao buscar operação de jogo:", error);
          setOperationStaff([]);
        }

        // Carregar informações do clube
        const clubInfoData = await getClubInfo(clubId);
        setClubInfo(clubInfoData);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os detalhes da convocação.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, clubId]);

  // Efeito separado para verificar se deve mudar a aba quando a convocação não for mais editável
  useEffect(() => {
    if (callup && !isCallupEditable(callup.match_date) && selectedTab === "design") {
      setSelectedTab("preview");
    }
  }, [callup, selectedTab]);

  // Salvar alterações na convocação
  const handleSaveCallup = async () => {
    if (!callup || !id) {
      toast({
        title: "Erro",
        description: "Dados da convocação não encontrados.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);

      // Atualizar a convocação
      const updatedCallup = await updateCallup(clubId, parseInt(id), callup, user?.id);
      setCallup(updatedCallup);

      // Obter a lista atual de jogadores do banco de dados
      const currentPlayers = await getCallupPlayers(clubId, parseInt(id));

      // Encontrar jogadores que precisam ser removidos (estão no banco mas não na lista atual)
      const playersToRemove = currentPlayers.filter(
        dbPlayer => !callupPlayers.some(p => p.id === dbPlayer.id)
      );

      // Remover jogadores que não estão mais na lista
      for (const player of playersToRemove) {
        if (player.id) {
          try {
            await removePlayerFromCallup(clubId, parseInt(id), player.id, user?.id);
          } catch (error) {
            console.error(`Erro ao remover jogador ${player.id}:`, error);
          }
        }
      }

      // Atualizar/Adicionar apenas jogadores que não existem ainda
      const updatedPlayers: CallupPlayer[] = [];

      // Primeiro, mapear os jogadores atuais para verificação
      const existingPlayerMap = new Map();
      currentPlayers.forEach(player => {
        // Criar chaves únicas para diferentes tipos de identificação
        if (player.player_id) {
          existingPlayerMap.set(`player_${player.player_id}`, true);
        }
        if (player.user_id) {
          existingPlayerMap.set(`user_${player.user_id}`, true);
        }
        if (player.player_name && !player.player_id && !player.user_id) {
          existingPlayerMap.set(`name_${player.player_name.toLowerCase().trim()}`, true);
        }
      });

      for (const player of callupPlayers) {
        // Verificar se o jogador já existe antes de tentar adicionar
        let playerExists = false;

        if (player.player_id) {
          playerExists = existingPlayerMap.has(`player_${player.player_id}`);
        } else if (player.user_id) {
          playerExists = existingPlayerMap.has(`user_${player.user_id}`);
        } else if (player.player_name) {
          playerExists = existingPlayerMap.has(`name_${player.player_name.toLowerCase().trim()}`);
        }

        // Só adicionar se não existir
        if (!playerExists) {
          try {
            if (player.player_id) {
              // Jogador do elenco
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                player.player_id,
                player.role,
                user?.id,
                undefined,
                undefined,
                player.player_name
              );
              updatedPlayers.push(newPlayer);
            } else if (player.user_id) {
              // Membro da comissão (usuário ou colaborador)
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                undefined,
                player.role,
                user?.id,
                player.user_id,
                player.user_name
              );
              updatedPlayers.push(newPlayer);
            } else if (player.player_name) {
              // Jogador adicionado manualmente
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                undefined,
                player.role,
                user?.id,
                undefined,
                undefined,
                player.player_name
              );
              updatedPlayers.push(newPlayer);
            }
          } catch (playerError) {
            console.error(`Erro ao adicionar jogador ${player.player_name || player.user_name}:`, playerError);
            // Continuar com os próximos jogadores mesmo se um falhar
          }
        } else {
          console.log(`Jogador ${player.player_name || player.user_name} já existe na convocação, pulando...`);
        }
      }

      // Lista atual de membros de operação no banco
      const currentOps = await getCallupOperationStaff(clubId, parseInt(id));

      // Remover membros que não estão mais na lista
      const opsToRemove = currentOps.filter(
        (dbOp) => !operationStaff.some((op) => op.id === dbOp.id)
      );

      for (const op of opsToRemove) {
        try {
          await removeOperationStaffFromCallup(clubId, parseInt(id), op.id);
        } catch (err) {
          console.error(`Erro ao remover operação ${op.id}:`, err);
        }
      }

      // Adicionar novos membros de operação
      for (const op of operationStaff) {
        if (op.id === 0) {
          try {
            await addOperationStaffToCallup(clubId, parseInt(id), {
              name: op.name,
              role: op.role,
              birth_date: op.birth_date || undefined,
              cpf: op.cpf || undefined,
              pix_key: op.pix_key || null,
              value: op.value ?? null,
            });
          } catch (opErr) {
            console.error('Erro ao adicionar operação:', opErr);
          }
        }
      }

     // Recarrega a lista para garantir que todos os membros apareçam corretamente
     const refreshedPlayers = await getCallupPlayers(clubId, parseInt(id));
     setCallupPlayers(refreshedPlayers);
     const refreshedOps = await getCallupOperationStaff(clubId, parseInt(id));
     setOperationStaff(refreshedOps);

      toast({
        title: "Sucesso",
        description: "Convocação atualizada com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao salvar convocação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a convocação.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Gerar PDF da convocação
  const handleGeneratePDF = async () => {
    if (!callup || !clubInfo) {
      console.error("Não é possível gerar PDF: callup ou clubInfo não definidos");
      toast({
        title: "Erro",
        description: "Dados incompletos para gerar o PDF.",
        variant: "destructive",
      });
      return;
    }

    try {
      setGeneratingPDF(true);

      // Gerar o PDF para download (não para impressão)
      await generateCallupPDF(clubId, callup.id, false);

      toast({
        title: "Sucesso",
        description: "PDF gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o PDF.",
        variant: "destructive",
      });
    } finally {
      setGeneratingPDF(false);
    }
  };

  const handleGenerateAccommodationPDF = async () => {
    if (!callup || !clubInfo) {
      toast({ title: 'Erro', description: 'Dados incompletos para gerar o PDF.', variant: 'destructive' });
      return;
    }

    try {
      setGeneratingAccommodationPDF(true);
      await generateCallupAccommodationPDF(clubId, callup.id);
      toast({ title: 'Sucesso', description: 'PDF gerado com sucesso.' });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({ title: 'Erro', description: 'Não foi possível gerar o PDF.', variant: 'destructive' });
    } finally {
      setGeneratingAccommodationPDF(false);
    }
  };

  const handleGenerateGuestPDF = async () => {
    if (!callup || !clubInfo) {
      toast({ title: 'Erro', description: 'Dados incompletos para gerar o PDF.', variant: 'destructive' });
      return;
    }

    try {
      setGeneratingGuestPDF(true);
      await generateCallupDelegationPDF(clubId, callup.id);
      toast({ title: 'Sucesso', description: 'PDF gerado com sucesso.' });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({ title: 'Erro', description: 'Não foi possível gerar o PDF.', variant: 'destructive' });
    } finally {
      setGeneratingGuestPDF(false);
    }
  };

  // Função para navegar para a partida relacionada
  const handleGoToMatch = async () => {
    if (!callup) return;

    try {
      if (callup.match_id) {
        navigateToClubPage(`/partidas?match=${callup.match_id}`);
        return;
      }

      // Buscar partida relacionada à convocação
      const { data: matches, error } = await supabase
        .from("matches")
        .select("id")
        .eq("club_id", clubId)
        .eq("callup_id", callup.id)
        .single();

      if (error || !matches) {
        // Se não encontrar pelo callup_id, tentar buscar por data e categoria
        const callupDate = new Date(callup.match_date);
        const startOfDay = new Date(callupDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(callupDate);
        endOfDay.setHours(23, 59, 59, 999);

        const { data: nearbyMatches, error: nearbyError } = await supabase
          .from("matches")
          .select("id")
          .eq("club_id", clubId)
          .gte("date", startOfDay.toISOString().split('T')[0])
          .lte("date", endOfDay.toISOString().split('T')[0])
          .limit(1);

        if (nearbyError || !nearbyMatches || nearbyMatches.length === 0) {
          toast({
            title: "Partida não encontrada",
            description: "Nenhuma partida encontrada para esta convocação",
            variant: "destructive",
          });
          return;
        }

        navigateToClubPage(`/partidas?match=${nearbyMatches[0].id}`);
      } else {
        navigateToClubPage(`/partidas?match=${matches.id}`);
      }
    } catch (error) {
      console.error("Erro ao buscar partida:", error);
      toast({
        title: "Erro",
        description: "Erro ao buscar partida relacionada",
        variant: "destructive",
      });
    }
  };

  // Renderizar a página de detalhes
  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="flex items-center mb-4 sm:mb-6">
        <div className="flex gap-2 mr-4">
          <Button variant="outline" onClick={() => navigateToClubPage("/convocacao")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <Button 
            variant="outline" 
            onClick={handleGoToMatch}
            className="text-green-600 hover:text-green-700 hover:bg-green-50"
          >
            <Target className="h-4 w-4 mr-2" />
            Ver Partida
          </Button>
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Detalhes da Convocação</h1>
          {callup && (
            <p className="text-muted-foreground">
              {callup.tournament_type} - {format(new Date(callup.match_date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
            </p>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : !callup ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-muted-foreground mb-4">
              Convocação não encontrada
            </p>
            <Button onClick={() => navigateToClubPage("/convocacao")}>
              Voltar para Convocações
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6">
            <div>
              <h2 className="text-2xl font-bold">
                {callup?.tournament_type} - {callup?.match_date ? format(new Date(callup.match_date), "dd/MM/yyyy", { locale: ptBR }) : ""}
              </h2>
              <p className="text-muted-foreground">
                {callup?.match_location} - {categories.find(c => c.id === callup?.category_id)?.name || "Categoria não definida"}
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button 
                onClick={handleSaveCallup} 
                disabled={saving || !isCallupEditable(callup?.match_date)}
                title={!isCallupEditable(callup?.match_date) ? "Não é possível salvar alterações após 2 dias da data da partida" : "Salvar alterações na convocação"}
                size="sm"
                className="w-full sm:w-auto text-xs sm:text-sm"
              >
                {saving ? (
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
                ) : (
                  <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                )}
                <span className="hidden sm:inline">Salvar Alterações</span>
                <span className="sm:hidden">Salvar</span>
              </Button>
              <Button 
                onClick={handleGenerateAccommodationPDF} 
                disabled={generatingAccommodationPDF}
                size="sm"
                className="w-full sm:w-auto text-xs sm:text-sm"
              >
                {generatingAccommodationPDF ? (
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
                ) : (
                  <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                )}
                <span className="hidden sm:inline">PDF Hóspedes</span>
                <span className="sm:hidden">Hóspedes</span>
              </Button>
              <Button 
                onClick={handleGenerateGuestPDF} 
                disabled={generatingGuestPDF}
                size="sm"
                className="w-full sm:w-auto text-xs sm:text-sm"
              >
                {generatingGuestPDF ? (
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
                ) : (
                  <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                )}
                <span className="hidden sm:inline">PDF Operação de Jogo</span>
                <span className="sm:hidden">Operação</span>
              </Button>
            </div>
          </div>

          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="mb-4">
              <TabsTrigger 
                value="design" 
                disabled={!isCallupEditable(callup?.match_date)}
                title={!isCallupEditable(callup?.match_date) ? "Não é possível editar convocações após 2 dias da data da partida" : "Editar design da convocação"}
              >
                <Edit className="h-4 w-4 mr-2" />
                Design
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Visualização
              </TabsTrigger>
              <TabsTrigger value="accommodation">
                <Home className="h-4 w-4 mr-2" />
                Controle de Alojamento
              </TabsTrigger>
            </TabsList>

            <TabsContent value="design">
              {callup && (
                <CallupDesignEditor
                  callup={callup}
                  onCallupChange={(updatedCallup) => setCallup({ ...callup, ...updatedCallup })}
                  players={callupPlayers}
                  onPlayersChange={setCallupPlayers}
                  operationStaff={operationStaff}
                  onOperationStaffChange={setOperationStaff}
                  categoryPlayers={categoryPlayers}
                  clubUsers={clubUsers}
                  collaborators={collaborators}
                  clubInfo={clubInfo}
                  categories={categories}
                  clubId={clubId}
                />
              )}
            </TabsContent>

            <TabsContent value="preview">
              {callup && (
                <CallupPreview
                  callup={callup}
                  players={callupPlayers}
                  clubInfo={clubInfo}
                  selectedFields={selectedFields}
                />
              )}
            </TabsContent>
            <TabsContent value="accommodation">
              {callup && (
                <CallupAccommodationControl
                  callupId={callup.id}
                  collaborators={collaborators}
                  categories={categories}
                  clubId={clubId}
                  defaultCategoryId={callup.category_id}
                />
              )}
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}