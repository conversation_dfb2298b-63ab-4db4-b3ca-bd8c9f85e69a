export interface CollaboratorRole {
  id: number;
  club_id: string | number;
  name: string;
  role_type: 'technical' | 'assistant_technical' | 'other';
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface Collaborator {
  id: number;
  club_id: number;
  full_name: string;
  nickname?: string;
  role: string;
  role_id?: number;
  role_type: 'technical' | 'assistant_technical';
  phone?: string;
  email?: string;
  cpf?: string;
  birth_date?: string;
  credential_number?: string;
  document_id?: string;
  document_id_url?: string;
  certificate_url?: string;
  medical_certificate_url?: string;
  resume_url?: string;
  criminal_record_url?: string;
  zip_code?: string;
  state?: string;
  city?: string;
  address?: string;
  address_number?: string;
  image?: string;
  entry_date?: string;
  status: 'available' | 'unavailable' | 'on_leave';
  registration_number?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}
