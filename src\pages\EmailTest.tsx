import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, <PERSON>Header, <PERSON>Title } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { type EmailData } from "@/services/brevoEmailService";
import { signUp } from "@/api/auth";
import { testEmailSending } from "@/api/testEmail";
import { createUserDirectly } from "@/api/directAuth";

// Brevo API configuration - Hardcoded for reliability
const BREVO_API_KEY = '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';
const SENDER_NAME = 'GameDayNexus';
const SENDER_EMAIL = '<EMAIL>';

// Brevo API endpoint
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';

export default function EmailTest() {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [subject, setSubject] = useState("Teste de Email");
  const [body, setBody] = useState("<h1>Teste de Email</h1><p>Este é um teste de envio de email usando a API do Brevo.</p>");
  const [loading, setLoading] = useState(false);
  const [testAccountLoading, setTestAccountLoading] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  // Log API information on component mount
  useEffect(() => {
    console.log('Configuração da API Brevo:');
    console.log('BREVO_API_KEY (primeiros 10 caracteres):', BREVO_API_KEY.substring(0, 10) + '...');
    console.log('SENDER_NAME:', SENDER_NAME);
    console.log('SENDER_EMAIL:', SENDER_EMAIL);
  }, []);

  // Function to send email directly from this component
  async function sendEmailDirectly(emailData: EmailData): Promise<boolean> {
    try {
      console.log('Tentando enviar email via Brevo API:', {
        to: emailData.to,
        subject: emailData.subject,
      });

      const requestData = {
        sender: {
          name: SENDER_NAME,
          email: SENDER_EMAIL
        },
        to: [
          {
            email: emailData.to
          }
        ],
        subject: emailData.subject,
        htmlContent: emailData.body
      };

      console.log('Dados da requisição:', {
        url: BREVO_API_URL,
        headers: {
          'Content-Type': 'application/json',
          'api-key': BREVO_API_KEY.substring(0, 10) + '...'
        },
        body: requestData
      });

      const response = await fetch(BREVO_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': BREVO_API_KEY
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Resposta da API Brevo:', {
          status: response.status,
          statusText: response.statusText,
          data: errorData
        });
        throw new Error(`Brevo API error: ${JSON.stringify(errorData)}`);
      }

      console.log('Email enviado com sucesso via Brevo API');
      return true;
    } catch (error) {
      console.error('Erro ao enviar email via Brevo API:', error);
      return false;
    }
  }

  const handleSendEmail = async () => {
    if (!email) {
      toast({
        title: "Erro",
        description: "Por favor, informe um email válido",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const result = await sendEmailDirectly({
        to: email,
        subject,
        body,
      });

      if (result) {
        toast({
          title: "Sucesso",
          description: "Email enviado com sucesso!",
        });
      } else {
        toast({
          title: "Erro",
          description: "Falha ao enviar email. Verifique o console para mais detalhes.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Erro ao enviar email:", error);
      toast({
        title: "Erro",
        description: "Falha ao enviar email: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestAccount = async () => {
    if (!email || !name) {
      toast({
        title: "Erro",
        description: "Por favor, informe um email e nome válidos",
        variant: "destructive",
      });
      return;
    }

    setTestAccountLoading(true);
    try {
      // Criar uma conta de teste usando nossa nova função
      const result = await createUserDirectly(
        email,
        name,
        "Game Day Nexus",
        "player"
      );

      if (result.success) {
        toast({
          title: "Sucesso",
          description: "Conta de teste criada e email enviado com sucesso!",
        });
      } else {
        toast({
          title: "Erro",
          description: `Falha ao criar conta: ${result.message}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Erro ao criar conta de teste:", error);
      toast({
        title: "Erro",
        description: "Falha ao criar conta de teste: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setTestAccountLoading(false);
    }
  };

  const handleTestEmail = async () => {
    if (!email) {
      toast({
        title: "Erro",
        description: "Por favor, informe um email válido",
        variant: "destructive",
      });
      return;
    }

    setTestEmailLoading(true);
    try {
      const result = await testEmailSending(email);

      if (result) {
        toast({
          title: "Sucesso",
          description: "Email de teste enviado com sucesso!",
        });
      } else {
        toast({
          title: "Erro",
          description: "Falha ao enviar email de teste. Verifique o console para mais detalhes.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Erro ao enviar email de teste:", error);
      toast({
        title: "Erro",
        description: "Falha ao enviar email de teste: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setTestEmailLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Teste de Envio de Email</CardTitle>
          <CardDescription>
            Use este formulário para testar o envio de emails usando a API do Brevo
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email de Destino</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="name">Nome (para teste de conta)</Label>
            <Input
              id="name"
              type="text"
              placeholder="Nome Completo"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="subject">Assunto</Label>
            <Input
              id="subject"
              placeholder="Assunto do email"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="body">Corpo do Email (HTML)</Label>
            <textarea
              id="body"
              className="w-full min-h-[100px] p-2 border rounded-md"
              placeholder="<h1>Título</h1><p>Conteúdo do email</p>"
              value={body}
              onChange={(e) => setBody(e.target.value)}
            />
          </div>
          <div className="space-y-2 mt-4">
            <p className="text-xs text-gray-500">
              Usando a chave API do Brevo configurada no sistema.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            onClick={handleSendEmail}
            disabled={loading}
            className="w-full"
          >
            {loading ? "Enviando..." : "Enviar Email Personalizado"}
          </Button>

          <div className="flex gap-2 w-full">
            <Button
              onClick={handleTestEmail}
              disabled={testEmailLoading}
              className="w-1/2"
              variant="outline"
            >
              {testEmailLoading ? "Enviando..." : "Testar Email Simples"}
            </Button>

            <Button
              onClick={handleTestAccount}
              disabled={testAccountLoading}
              className="w-1/2"
              variant="secondary"
            >
              {testAccountLoading ? "Criando..." : "Testar Criação de Conta"}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
