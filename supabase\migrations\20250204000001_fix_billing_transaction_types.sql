-- Migração de correção para os tipos de transação de billing

-- 1. <PERSON><PERSON>, remover a constraint atual
ALTER TABLE billing_transactions 
DROP CONSTRAINT IF EXISTS billing_transactions_type_check;

-- 2. Verificar e corrigir todos os tipos existentes
UPDATE billing_transactions 
SET type = 'recebe' 
WHERE type IN ('cobranca', 'recebe');

UPDATE billing_transactions 
SET type = 'paga' 
WHERE type IN ('recebimento', 'paga');

-- 3. Adicionar a constraint correta
ALTER TABLE billing_transactions 
ADD CONSTRAINT billing_transactions_type_check 
CHECK (type IN ('recebe', 'paga'));

-- 4. Verificar se há registros com tipos inválidos e corrigi-los
-- Se houver algum tipo não reconhecido, definir como 'recebe' por padrão
UPDATE billing_transactions 
SET type = 'recebe' 
WHERE type NOT IN ('recebe', 'paga');