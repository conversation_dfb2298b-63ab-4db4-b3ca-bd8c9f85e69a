import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { getMatchPlayerMinutes, getMatchSquad, type MatchPlayerMinutes } from "@/api/matchLineups";
import { getPlayerMatchStatistics, savePlayerMatchStatistics } from "@/api/playerStatistics";
import { saveMatchPlayerMinutes } from "@/api/matchLineups";
import { usePlayersStore } from "@/store/usePlayersStore";
import { getCategoryPlayers } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { toast } from "@/components/ui/use-toast";
import type { Player } from "@/api/api";

interface PlayerStatsEditorProps {
  clubId: number;
  matchId: string;
  categoryId?: number | null;
}

export function PlayerStatsEditor({ clubId, matchId, categoryId }: PlayerStatsEditorProps) {
  const { players, fetchPlayers } = usePlayersStore();
  const { user } = useUser();

  const [matchPlayerMinutes, setMatchPlayerMinutes] = useState<MatchPlayerMinutes[]>([]);
  const [playerStats, setPlayerStats] = useState<Record<string, any>>({});

  const handleStatChange = (
    playerId: string,
    field: string,
    value: number
  ) => {
    setPlayerStats((prev) => ({
      ...prev,
      [playerId]: {
        ...prev[playerId],
        [field]: value,
      },
    }));
  };

  useEffect(() => {
    if (!clubId || !matchId) return;

    async function loadData() {
      let loadedPlayers: Player[] = [];

      // Tentar carregar jogadores do squad da partida
      try {
        const squad = await getMatchSquad(clubId, matchId);
        if (squad && squad.length > 0) {
          loadedPlayers = squad
            .filter((m) => m.player_id && m.player)
            .map((m) => m.player as Player);
        }
      } catch (err) {
        console.error("Erro ao buscar squad da partida:", err);
      }

      // Se não há jogadores do squad, usar categoria da partida
      if (loadedPlayers.length === 0 && categoryId) {
        try {
          loadedPlayers = await getCategoryPlayers(clubId, categoryId, { includeInactive: false });
        } catch (err) {
          console.error("Erro ao buscar jogadores da categoria:", err);
        }
      }

      // Fallback final para todos os jogadores do clube
      if (loadedPlayers.length === 0) {
        if (players.length === 0) {
          await fetchPlayers(clubId);
          loadedPlayers = usePlayersStore.getState().players;
        } else {
          loadedPlayers = players;
        }
      }

      try {
        const minutes = await getMatchPlayerMinutes(clubId, matchId);

        const minutesMap: Record<string, MatchPlayerMinutes> = {};
        minutes.forEach((pm) => {
          minutesMap[pm.player_id] = {
            ...pm,
            player: pm.player || loadedPlayers.find((pl) => pl.id === pm.player_id),
          };
        });

        // Garantir que todos os jogadores carregados apareçam na lista
        loadedPlayers.forEach((p) => {
          if (!minutesMap[p.id]) {
            minutesMap[p.id] = {
              club_id: clubId,
              match_id: matchId,
              player_id: p.id,
              minutes_played: 0,
              started: false,
              player: p,
            } as MatchPlayerMinutes;
          }
        });

        setMatchPlayerMinutes(Object.values(minutesMap));      } catch (err) {
        console.error("Erro ao buscar minutos dos jogadores:", err);
      }

      try {
        const stats = await getPlayerMatchStatistics(clubId, matchId);
        const map: Record<string, any> = {};
        stats.forEach((s) => {
          map[s.player_id] = s;
        });
        setPlayerStats(map);
      } catch (err) {
        console.error("Erro ao buscar estatísticas dos jogadores:", err);
      }
    }

    loadData();
  }, [clubId, matchId, categoryId, players.length, fetchPlayers]);

  const handleSaveAll = async () => {
    if (!user?.id) return;
    const promises = matchPlayerMinutes.map((pm) => {
      const stats = playerStats[pm.player_id] || {};
      const payload = {
        minutes_played: pm.minutes_played,
        goals: stats.goals || 0,
        assists: stats.assists || 0,
        shots: stats.shots || 0,
        shots_on_target: stats.shots_on_target || 0,
        passes: stats.passes || 0,
        passes_completed: stats.passes_completed || 0,
        tackles: stats.tackles || 0,
        interceptions: (stats.tackles || 0) / 2 || 0,
        yellow_cards: stats.yellow_cards || 0,
        red_cards: stats.red_cards || 0,
      };
      return saveMatchPlayerMinutes(
        clubId,
        matchId,
        pm.player_id,
        pm.minutes_played,
        pm.started
      ).then(() =>
        savePlayerMatchStatistics(
          clubId,
          matchId,
          pm.player_id,
          payload,
          user.id
        )
      );
    });

    try {
      await Promise.all(promises);
      toast({ title: "Estatísticas salvas", description: "Todas as estatísticas foram salvas." });
    } catch (error) {
      console.error("Erro ao salvar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar as estatísticas.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Jogador</th>
              <th className="border p-2 text-center">Min</th>
              <th className="border p-2 text-center">Gols</th>
              <th className="border p-2 text-center">Assist</th>
              <th className="border p-2 text-center">Chutes</th>
              <th className="border p-2 text-center">Chutes no Gol</th>
              <th className="border p-2 text-center">Passes</th>
              <th className="border p-2 text-center">Passes Comp.</th>
              <th className="border p-2 text-center">Desarmes</th>
              <th className="border p-2 text-center">CA</th>
              <th className="border p-2 text-center">CV</th>
            </tr>
          </thead>
          <tbody>
            {matchPlayerMinutes.map((pm) => {
              const player: Player | undefined = pm.player || players.find((p) => p.id === pm.player_id);
              if (!player) return null;
              const stats = playerStats[player.id] || {};
              return (
                <tr key={player.id} id={`player-stats-row-${player.id}`} className="hover:bg-gray-50">
                  <td className="border p-2">{player.name}</td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      max="120"
                      className="w-12 p-1 text-center border rounded"
                      value={pm.minutes_played || 0}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 0;
                        setMatchPlayerMinutes((prev) => prev.map((m) => (m.player_id === player.id ? { ...m, minutes_played: value } : m)));
                      }}
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.goals || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "goals",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.assists || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "assists",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.shots || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "shots",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.shots_on_target || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "shots_on_target",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.passes || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "passes",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.passes_completed || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "passes_completed",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.tackles || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "tackles",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.yellow_cards || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "yellow_cards",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                  </td>
                  <td className="border p-2 text-center">
                    <input
                      type="number"
                      min="0"
                      className="w-12 p-1 text-center border rounded"
                      value={stats.red_cards || 0}
                      onChange={(e) =>
                        handleStatChange(
                          player.id,
                          "red_cards",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className="flex justify-end">
        <Button className="bg-green-600 hover:bg-green-700" onClick={handleSaveAll}>
          Salvar Estatísticas
        </Button>
      </div>
    </div>
  );
}