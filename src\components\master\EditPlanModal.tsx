import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { updateMasterPlan, AVAILABLE_MODULES, type MasterPlan, type UpdatePlanData } from '@/api/masterPlans';
import { toast } from 'sonner';

const editPlanSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  price: z.number().min(0, 'Preço deve ser maior ou igual a 0'),
  billing_cycle: z.enum(['monthly', 'yearly']),
  max_users: z.number().nullable(),
  max_players: z.number().nullable(),
  is_active: z.boolean(),
});

interface EditPlanModalProps {
  isOpen: boolean;
  plan: MasterPlan | null;
  onClose: () => void;
  onSuccess: () => void;
}

export const EditPlanModal: React.FC<EditPlanModalProps> = ({
  isOpen,
  plan,
  onClose,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedModules, setSelectedModules] = useState<Record<string, boolean>>({});
  const [selectedFeatures, setSelectedFeatures] = useState<Record<string, any>>({});

  const form = useForm<z.infer<typeof editPlanSchema>>({
    resolver: zodResolver(editPlanSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      billing_cycle: 'monthly',
      max_users: null,
      max_players: null,
      is_active: true,
    },
  });

  // Carregar dados do plano quando o modal abrir
  useEffect(() => {
    if (plan && isOpen) {
      form.reset({
        name: plan.name,
        description: plan.description,
        price: plan.price,
        billing_cycle: plan.billing_cycle,
        max_users: plan.max_users,
        max_players: plan.max_players,
        is_active: plan.is_active,
      });
      
      setSelectedModules(plan.modules || {});
      setSelectedFeatures(plan.features || {});
    }
  }, [plan, isOpen, form]);

  const handleModuleToggle = (moduleKey: string, enabled: boolean) => {
    setSelectedModules(prev => ({
      ...prev,
      [moduleKey]: enabled
    }));
  };

  const handleFeatureChange = (featureKey: string, value: any) => {
    setSelectedFeatures(prev => ({
      ...prev,
      [featureKey]: value
    }));
  };

  const onSubmit = async (values: z.infer<typeof editPlanSchema>) => {
    if (!plan) return;

    try {
      setLoading(true);

      const planData: UpdatePlanData = {
        ...values,
        modules: selectedModules,
        features: selectedFeatures,
      };

      await updateMasterPlan(plan.id, planData);
      
      toast.success('Plano atualizado com sucesso!');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Erro ao atualizar plano:', error);
      toast.error(error.message || 'Erro ao atualizar plano');
    } finally {
      setLoading(false);
    }
  };

  if (!plan) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Plano: {plan.name}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informações Básicas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome do Plano</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Plano Básico" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billing_cycle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ciclo de Cobrança</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o ciclo" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="monthly">Mensal</SelectItem>
                            <SelectItem value="yearly">Anual</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Descreva as características do plano..."
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preço (R$)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="max_users"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Máx. Usuários</FormLabel>
                        <FormControl>
                          <Input 
                            type="number"
                            placeholder="Ilimitado"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="max_players"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Máx. Atletas</FormLabel>
                        <FormControl>
                          <Input 
                            type="number"
                            placeholder="Ilimitado"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <FormItem className="flex flex-col justify-end">
                        <FormLabel>Status</FormLabel>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <span className="text-sm">
                            {field.value ? 'Ativo' : 'Inativo'}
                          </span>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Módulos */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Módulos Inclusos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(AVAILABLE_MODULES).map(([key, label]) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Switch
                        id={`module-${key}`}
                        checked={selectedModules[key] || false}
                        onCheckedChange={(checked) => handleModuleToggle(key, checked)}
                      />
                      <label htmlFor={`module-${key}`} className="text-sm font-medium">
                        {label}
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Features Adicionais */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recursos Adicionais</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Limite de Armazenamento (GB)</label>
                    <Input
                      type="number"
                      placeholder="Ex: 10"
                      value={selectedFeatures.storage_limit || ''}
                      onChange={(e) => handleFeatureChange('storage_limit', parseInt(e.target.value) || null)}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Limite de Chamadas API/mês</label>
                    <Input
                      type="number"
                      placeholder="Ex: 10000"
                      value={selectedFeatures.api_calls_limit || ''}
                      onChange={(e) => handleFeatureChange('api_calls_limit', parseInt(e.target.value) || null)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="custom_branding"
                      checked={selectedFeatures.custom_branding || false}
                      onCheckedChange={(checked) => handleFeatureChange('custom_branding', checked)}
                    />
                    <label htmlFor="custom_branding" className="text-sm font-medium">
                      Marca Personalizada
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="priority_support"
                      checked={selectedFeatures.priority_support || false}
                      onCheckedChange={(checked) => handleFeatureChange('priority_support', checked)}
                    />
                    <label htmlFor="priority_support" className="text-sm font-medium">
                      Suporte Prioritário
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="advanced_reports"
                      checked={selectedFeatures.advanced_reports || false}
                      onCheckedChange={(checked) => handleFeatureChange('advanced_reports', checked)}
                    />
                    <label htmlFor="advanced_reports" className="text-sm font-medium">
                      Relatórios Avançados
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};