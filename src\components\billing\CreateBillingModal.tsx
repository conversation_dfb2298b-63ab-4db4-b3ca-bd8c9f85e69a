import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/hooks/use-toast';
import { generatePixQRCode, generatePixString } from '@/utils/pixGenerator';
import { Download, Copy, QrCode } from 'lucide-react';
import jsPDF from 'jspdf';
import { getPlayersAndCollaborators, Client, CreateBillingTransactionItemData } from '@/api/billing';
import { useCurrentClubId } from '@/context/ClubContext';
import { InventoryItemsSelector } from './InventoryItemsSelector';

interface CreateBillingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  clubName: string;
  clubPixKey?: string;
  clients: Client[];
  tabType: 'players' | 'clients';
}

export function CreateBillingModal({
  open,
  onOpenChange,
  onSuccess,
  clubName,
  clubPixKey,
  clients,
  tabType
}: CreateBillingModalProps) {
  const [type, setType] = useState<'recebe' | 'paga'>('recebe');
  const [entityType, setEntityType] = useState<'player' | 'collaborator' | 'client'>('player');
  const [selectedEntity, setSelectedEntity] = useState<string>('');
  const [customName, setCustomName] = useState('');
  const [customPixKey, setCustomPixKey] = useState('');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string | null>(null);
  const [pixString, setPixString] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [players, setPlayers] = useState<any[]>([]);
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [inventoryItems, setInventoryItems] = useState<CreateBillingTransactionItemData[]>([]);
  const [discount, setDiscount] = useState<number>(0);
  const { toast } = useToast();
  const clubId = useCurrentClubId();

  useEffect(() => {
    if (tabType === 'clients') {
      setEntityType('client');
    } else {
      setEntityType('player');
    }
  }, [tabType]);

  useEffect(() => {
    if (open && tabType === 'players') {
      loadPlayersAndCollaborators();
    }
  }, [open, tabType]);

  // Calcular valor automaticamente baseado nos itens do estoque
  useEffect(() => {
    if (inventoryItems.length > 0) {
      const totalItems = inventoryItems.reduce((sum, item) => sum + (item.quantity * (item.unit_price || 0)), 0);
      const totalWithDiscount = totalItems - (totalItems * (discount / 100));
      setAmount(totalWithDiscount.toFixed(2));
    }
  }, [inventoryItems, discount]);

  const loadPlayersAndCollaborators = async () => {
    try {
      const data = await getPlayersAndCollaborators(clubId);
      setPlayers(data.players);
      setCollaborators(data.collaborators);
    } catch (error) {
      console.error('Erro ao carregar jogadores e colaboradores:', error);
    }
  };

  const getSelectedEntityData = () => {
    if (tabType === 'clients') {
      if (selectedEntity === 'custom') {
        return {
          name: customName,
          pixKey: type === 'recebe' ? (clubPixKey || '') : customPixKey
        };
      } else if (selectedEntity) {
        const client = clients.find(c => c.id.toString() === selectedEntity);
        return {
          name: client?.name || '',
          pixKey: type === 'recebe' ? (clubPixKey || '') : (client?.pix_key || '')
        };
      }
      return { name: '', pixKey: '' };
    } else {
      if (!selectedEntity) return { name: '', pixKey: '' };
      
      let entity;
      if (entityType === 'player') {
        entity = players.find(p => p.id.toString() === selectedEntity);
      } else {
        entity = collaborators.find(c => c.id.toString() === selectedEntity);
      }
      
      return {
        name: entity?.name || '',
        pixKey: type === 'recebe' ? (clubPixKey || '') : (entity?.pix_key || '')
      };
    }
  };

  const handleGenerateQRCode = async () => {
    if (!amount || !description) {
      toast({
        title: 'Campos obrigatórios',
        description: 'Preencha o valor e a descrição',
        variant: 'destructive'
      });
      return;
    }

    // Validações específicas para criação manual de cliente
    if (tabType === 'clients' && selectedEntity === 'custom') {
      if (!customName.trim()) {
        toast({
          title: 'Nome obrigatório',
          description: 'Digite o nome do cliente',
          variant: 'destructive'
        });
        return;
      }
      
      if (type === 'paga' && !customPixKey.trim()) {
        toast({
          title: 'Chave PIX obrigatória',
          description: 'Digite a chave PIX do cliente para recebimento',
          variant: 'destructive'
        });
        return;
      }
    }

    const entityData = getSelectedEntityData();
    if (!entityData.name) {
      toast({
        title: 'Selecione uma entidade',
        description: 'Selecione um jogador, colaborador ou cliente',
        variant: 'destructive'
      });
      return;
    }

    if (!entityData.pixKey) {
      toast({
        title: 'Chave PIX não encontrada',
        description: 'A entidade selecionada não possui chave PIX configurada',
        variant: 'destructive'
      });
      return;
    }

    if (parseFloat(amount) <= 0) {
      toast({
        title: 'Valor inválido',
        description: 'O valor deve ser maior que zero',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      const qrCode = await generatePixQRCode({
        pixKey: entityData.pixKey,
        amount: parseFloat(amount),
        description,
        merchantName: type === 'recebe' ? clubName : entityData.name,
        merchantCity: 'SAO PAULO'
      });

      setQrCodeDataURL(qrCode);
      
      const pixStr = generatePixString({
        pixKey: entityData.pixKey,
        amount: parseFloat(amount),
        description,
        merchantName: type === 'recebe' ? clubName : entityData.name,
        merchantCity: 'SAO PAULO'
      });
      setPixString(pixStr);

      toast({
        title: 'QR Code gerado',
        description: 'QR Code PIX criado com sucesso!'
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o QR Code',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = () => {
    if (!qrCodeDataURL) return;

    const entityData = getSelectedEntityData();
    const pdf = new jsPDF();
    
    // Título
    pdf.setFontSize(20);
    pdf.text(type === 'recebe' ? 'PIX - Clube Recebe' : 'PIX - Clube Paga', 20, 30);
    
    // Informações
    pdf.setFontSize(12);
    pdf.text(`${type === 'recebe' ? 'Quem paga' : 'Quem recebe'}: ${entityData.name}`, 20, 50);
    pdf.text(`Valor: R$ ${parseFloat(amount).toFixed(2)}`, 20, 60);
    pdf.text(`Descrição: ${description}`, 20, 70);
    if (dueDate) {
      pdf.text(`Vencimento: ${new Date(dueDate).toLocaleDateString('pt-BR')}`, 20, 80);
    }
    
    // QR Code
    pdf.addImage(qrCodeDataURL, 'PNG', 20, 90, 80, 80);
    
    // Instruções
    pdf.setFontSize(10);
    pdf.text('Instruções:', 20, 190);
    pdf.text('1. Abra o app do seu banco', 20, 200);
    pdf.text('2. Escolha a opção PIX', 20, 210);
    pdf.text('3. Escaneie o QR Code acima', 20, 220);
    pdf.text('4. Confirme o pagamento', 20, 230);
    
    // Chave PIX
    pdf.text(`Chave PIX: ${entityData.pixKey}`, 20, 250);
    
    const filename = `${type}-${entityData.name.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pdf`;
    pdf.save(filename);
    
    toast({
      title: 'PDF baixado',
      description: 'O arquivo foi salvo com sucesso!'
    });
  };

  const handleCopyPixString = () => {
    if (!pixString) return;
    
    navigator.clipboard.writeText(pixString);
    toast({
      title: 'Copiado',
      description: 'Código PIX copiado para a área de transferência'
    });
  };

  const handleSave = async () => {
    const entityData = getSelectedEntityData();
    
    const transactionData = {
      type,
      entity_type: entityType,
      entity_id: selectedEntity === 'custom' ? undefined : parseInt(selectedEntity),
      entity_name: entityData.name,
      pix_key: entityData.pixKey,
      amount: parseFloat(amount),
      description,
      due_date: dueDate || undefined,
      qr_code_data: pixString,
      items: inventoryItems.length > 0 ? inventoryItems : undefined
    };

    try {
      const { createBillingTransaction } = await import('@/api/billing');
      await createBillingTransaction(clubId, transactionData);
      
      const itemsMessage = inventoryItems.length > 0 
        ? ` com ${inventoryItems.length} item(ns) do estoque`
        : '';
      
      toast({
        title: 'Transação criada',
        description: `A transação foi criada com sucesso${itemsMessage}!`
      });
      
      onSuccess();
      onOpenChange(false);
      handleReset();
    } catch (error) {
      console.error('Erro ao criar transação:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível criar a transação',
        variant: 'destructive'
      });
    }
  };

  const handleReset = () => {
    setType('recebe');
    setEntityType(tabType === 'clients' ? 'client' : 'player');
    setSelectedEntity('');
    setCustomName('');
    setCustomPixKey('');
    setAmount('');
    setDescription('');
    setDueDate('');
    setQrCodeDataURL(null);
    setPixString('');
    setInventoryItems([]);
  };

  const renderEntitySelector = () => {
    if (tabType === 'clients') {
      return (
        <div className="space-y-2">
          <Label>Cliente</Label>
          <Select value={selectedEntity} onValueChange={setSelectedEntity}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione um cliente ou digite manualmente" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="custom">Digitar manualmente</SelectItem>
              {clients.map(client => (
                <SelectItem key={client.id} value={client.id.toString()}>
                  {client.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {selectedEntity === 'custom' && (
            <div className="space-y-2">
              <Input
                placeholder="Nome do cliente"
                value={customName}
                onChange={(e) => setCustomName(e.target.value)}
                required
              />
              {type === 'paga' && (
                <Input
                  placeholder="Chave PIX do cliente (CPF, CNPJ, email, telefone ou chave aleatória)"
                  value={customPixKey}
                  onChange={(e) => setCustomPixKey(e.target.value)}
                  required
                />
              )}
              <p className="text-xs text-muted-foreground">
                {type === 'recebe' 
                  ? 'Clube recebe: será usada a chave PIX do clube'
                  : 'Clube paga: informe a chave PIX do cliente'
                }
              </p>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <Label>Tipo de Entidade</Label>
        <RadioGroup value={entityType} onValueChange={(value: any) => setEntityType(value)}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="player" id="player" />
            <Label htmlFor="player">Jogador</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="collaborator" id="collaborator" />
            <Label htmlFor="collaborator">Colaborador</Label>
          </div>
        </RadioGroup>
        
        <Select value={selectedEntity} onValueChange={setSelectedEntity}>
          <SelectTrigger>
            <SelectValue placeholder={`Selecione um ${entityType === 'player' ? 'jogador' : 'colaborador'}`} />
          </SelectTrigger>
          <SelectContent>
            {(entityType === 'player' ? players : collaborators).map(entity => (
              <SelectItem key={entity.id} value={entity.id.toString()}>
                {entity.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nova Transação PIX</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div>
            <Label>Tipo de Transação</Label>
            <RadioGroup value={type} onValueChange={(value: any) => setType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="recebe" id="recebe" />
                <Label htmlFor="recebe">Clube Recebe (PIX do clube)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="paga" id="paga" />
                <Label htmlFor="paga">Clube Paga (PIX do cliente)</Label>
              </div>
            </RadioGroup>
          </div>

          {renderEntitySelector()}
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Valor (R$)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0,00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="dueDate">Data de Vencimento (Opcional)</Label>
              <Input
                id="dueDate"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Ex: Mensalidade João Silva - Mar/2025"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={2}
            />
          </div>

          {/* Seletor de itens do estoque */}
          <InventoryItemsSelector
            clubId={clubId}
            items={inventoryItems}
            onItemsChange={setInventoryItems}
            disabled={!!qrCodeDataURL} // Desabilitar após gerar QR Code
            discount={discount}
            onDiscountChange={setDiscount}
          />
          
          <div className="flex gap-2">
            <Button 
              onClick={handleGenerateQRCode} 
              disabled={loading}
              className="flex-1"
            >
              <QrCode className="w-4 h-4 mr-2" />
              {loading ? 'Gerando...' : 'Gerar QR Code'}
            </Button>
            {qrCodeDataURL && (
              <Button variant="outline" onClick={handleReset}>
                Limpar
              </Button>
            )}
          </div>
          
          {qrCodeDataURL && (
            <div className="space-y-4 pt-4 border-t">
              <div className="flex justify-center">
                <img 
                  src={qrCodeDataURL} 
                  alt="QR Code PIX" 
                  className="border rounded-lg"
                />
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleDownloadPDF}
                  className="flex-1"
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Baixar PDF
                </Button>
                <Button 
                  onClick={handleCopyPixString}
                  variant="outline"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copiar PIX
                </Button>
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button onClick={handleSave} className="flex-1">
                  Salvar Transação
                </Button>
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancelar
                </Button>
              </div>
              
              <div className="text-xs text-muted-foreground text-center">
                Valor: R$ {parseFloat(amount).toFixed(2)} • {description}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}