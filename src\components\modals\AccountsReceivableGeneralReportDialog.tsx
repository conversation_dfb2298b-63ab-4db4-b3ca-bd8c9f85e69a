import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getAllPendingReceivables, ConsolidatedReceivable } from "@/api/financialReports";
import jsPDF from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

const COLOR_MAP: Record<string, string> = {
    red: "#E53E3E",
    blue: "#3182CE",
    purple: "#805AD5",
    green: "#38A169",
    yellow: "#D69E2E",
    pink: "#D53F8C",
    orange: "#DD6B20",
    teal: "#319795",
    gray: "#718096",
    black: "#1A202C"
  };
  
  function hexToRgb(color: string | undefined | null) {
    if (!color || typeof color !== "string") {
      return { r: 41, g: 128, b: 185 };
    }
    try {
      const hexColor = COLOR_MAP[color.toLowerCase()] || color;
      const hexClean = hexColor.replace(/^#/, "");
      const hexValid = hexClean.length === 3
        ? hexClean.split("").map(c => c + c).join("")
        : hexClean;
      const r = parseInt(hexValid.substring(0, 2), 16) || 0;
      const g = parseInt(hexValid.substring(2, 4), 16) || 0;
      const b = parseInt(hexValid.substring(4, 6), 16) || 0;
      if (isNaN(r) || isNaN(g) || isNaN(b)) {
        throw new Error("Valores RGB inválidos");
      }
      return { r, g, b };
    } catch (error) {
      console.error("Erro ao converter cor:", color, error);
      return { r: 41, g: 128, b: 185 };
    }
  }
  
interface AccountsReceivableGeneralReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any;
  internal: {
    getNumberOfPages: () => number;
    pageSize: { width: number; height: number };
  };
}

export function AccountsReceivableGeneralReportDialog({
  open,
  onOpenChange
}: AccountsReceivableGeneralReportDialogProps) {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [receivables, setReceivables] = useState<ConsolidatedReceivable[]>([]);
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();
  const { toast } = useToast();

  useEffect(() => {
    if (open && clubId) {
      getAllPendingReceivables(clubId).then(setReceivables).catch(console.error);
    }
  }, [open, clubId]);

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }
    if (startDate && endDate && startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const clubInfo = await getClubInfo(clubId);

      let data = receivables;
      if (startDate || endDate) {
        data = data.filter(r => {
          const date = new Date(`${r.transaction_date}T00:00:00`);
          const start = startDate ? new Date(startDate) : null;
          const end = endDate ? new Date(endDate) : null;
          if (start) start.setHours(0, 0, 0, 0);
          if (end) end.setHours(23, 59, 59, 999);
          return (!start || date >= start) && (!end || date <= end);
        });
      }

      const totalAmount = data.reduce((sum, r) => sum + r.amount, 0);
      const totalCount = data.length;

      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;
      const pageWidth = doc.internal.pageSize.width;
      const margin = 15;
      const logoSize = 20;

      doc.setFontSize(18);
      const title = "Relatório Geral de Contas a Receber";
      doc.text(title, margin, 20);

      doc.setFontSize(12);
      let currentY = 30;
      doc.text(clubInfo.name, margin, currentY);
      currentY += 5;
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, margin, currentY);
        currentY += 5;
      }
      if (clubInfo.phone) {
        doc.text(`Telefone: ${clubInfo.phone}`, margin, currentY);
        currentY += 5;
      }

      if (clubInfo.logo_url) {
        try {
          const logoResponse = await fetch(clubInfo.logo_url);
          const logoBlob = await logoResponse.blob();
          const logoUrl = URL.createObjectURL(logoBlob);
          const logoX = pageWidth - margin - logoSize;
          doc.addImage(logoUrl, "JPEG", logoX, 10, logoSize, logoSize);
        } catch (error) {
          console.error("Erro ao carregar logo:", error);
        }
      }

      doc.setFontSize(10);
      const startY = currentY + 5;
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, margin, startY);

      if (startDate || endDate) {
        const periodText = `Período: ${startDate ? format(startDate, "dd/MM/yyyy") : "Início"} a ${endDate ? format(endDate, "dd/MM/yyyy") : "Fim"}`;
        doc.text(periodText, margin, startY + 5);
        currentY = startY + 15;
      } else {
        currentY = startY + 10;
      }

      doc.setFontSize(12);
      doc.text("Resumo Geral:", margin, currentY);
      currentY += 8;
      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, margin, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, margin, currentY);
      currentY += 15;

      const primaryColor = clubInfo.primary_color ? hexToRgb(clubInfo.primary_color) : { r: 41, g: 128, b: 185 };
      const secondaryColor = clubInfo.secondary_color ? hexToRgb(clubInfo.secondary_color) : { r: Math.max(0, primaryColor.r - 30), g: Math.max(0, primaryColor.g - 30), b: Math.max(0, primaryColor.b - 30) };

      const tableData = data.map(item => [
        item.name,
        item.description,
        format(new Date(`${item.transaction_date}T00:00:00`), "dd/MM/yyyy", { locale: ptBR }),
        item.phone || "N/A",
        `R$ ${item.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
      ]);

      autoTable(doc, {
        head: [["Nome", "Descrição", "Data", "Telefone", "Valor"]],
        body: tableData,
        startY: currentY,
        styles: { fontSize: 8, cellPadding: 2 },
        headStyles: {
          fillColor: [secondaryColor.r, secondaryColor.g, secondaryColor.b],
          textColor: 255,
          fontStyle: "bold",
          halign: "center"
        },
        alternateRowStyles: { fillColor: [248, 248, 248] },
        columnStyles: { 4: { halign: "right" } },
        margin: { left: margin, right: margin }      });

      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      const reportTitle = `Contas_a_Receber_Geral_${format(new Date(), "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório de contas a receber foi gerado com sucesso.",
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório Geral de Contas a Receber</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Data de Início</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate || undefined}
                      onSelect={date => date && setStartDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label>Data de Fim</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate || undefined}
                      onSelect={date => date && setEndDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}