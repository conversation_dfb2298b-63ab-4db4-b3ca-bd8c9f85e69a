import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Plus, Trash2, Package } from 'lucide-react';
import { getInventoryProductsForBilling, CreateBillingTransactionItemData } from '@/api/billing';

interface InventoryProduct {
  id: number;
  name: string;
  quantity: number;
  department: string;
  unit_of_measure: string;
  sale_price: number;
}

interface InventoryItemsSelectorProps {
  clubId: number;
  items: CreateBillingTransactionItemData[];
  onItemsChange: (items: CreateBillingTransactionItemData[]) => void;
  disabled?: boolean;
  discount?: number;
  onDiscountChange?: (discount: number) => void;
}

export function InventoryItemsSelector({ 
  clubId, 
  items, 
  onItemsChange, 
  disabled = false, 
  discount = 0, 
  onDiscountChange 
}: InventoryItemsSelectorProps) {
  const [products, setProducts] = useState<InventoryProduct[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [quantity, setQuantity] = useState<string>('1');
  const [unitPrice, setUnitPrice] = useState<string>('0');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadProducts();
  }, [clubId]);

  // Atualizar preço automaticamente quando produto é selecionado
  useEffect(() => {
    const product = getSelectedProduct();
    if (product && product.sale_price) {
      setUnitPrice(product.sale_price.toString());
    }
  }, [selectedProductId]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const data = await getInventoryProductsForBilling(clubId);
      setProducts(data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os produtos do estoque',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const getSelectedProduct = () => {
    return products.find(p => p.id.toString() === selectedProductId);
  };

  const getAvailableQuantity = (productId: number) => {
    const product = products.find(p => p.id === productId);
    if (!product) return 0;
    
    // Subtrair a quantidade já selecionada nos itens
    const usedQuantity = items
      .filter(item => item.product_id === productId)
      .reduce((sum, item) => sum + item.quantity, 0);
    
    return product.quantity - usedQuantity;
  };

  const handleAddItem = () => {
    const product = getSelectedProduct();
    if (!product) {
      toast({
        title: 'Erro',
        description: 'Selecione um produto',
        variant: 'destructive'
      });
      return;
    }

    const quantityNum = parseInt(quantity);
    const unitPriceNum = parseFloat(unitPrice);

    if (isNaN(quantityNum) || quantityNum <= 0) {
      toast({
        title: 'Erro',
        description: 'Quantidade deve ser um número maior que zero',
        variant: 'destructive'
      });
      return;
    }

    if (isNaN(unitPriceNum) || unitPriceNum < 0) {
      toast({
        title: 'Erro',
        description: 'Preço unitário deve ser um número válido',
        variant: 'destructive'
      });
      return;
    }

    const availableQuantity = getAvailableQuantity(product.id);
    if (quantityNum > availableQuantity) {
      toast({
        title: 'Erro',
        description: `Quantidade indisponível. Disponível: ${availableQuantity}`,
        variant: 'destructive'
      });
      return;
    }

    // Verificar se o produto já foi adicionado
    const existingItemIndex = items.findIndex(item => item.product_id === product.id);
    
    if (existingItemIndex >= 0) {
      // Atualizar item existente
      const updatedItems = [...items];
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: updatedItems[existingItemIndex].quantity + quantityNum,
        unit_price: unitPriceNum
      };
      onItemsChange(updatedItems);
    } else {
      // Adicionar novo item
      const newItem: CreateBillingTransactionItemData = {
        product_id: product.id,
        quantity: quantityNum,
        unit_price: unitPriceNum
      };
      onItemsChange([...items, newItem]);
    }

    // Limpar formulário
    setSelectedProductId('');
    setQuantity('1');
    setUnitPrice('0');

    toast({
      title: 'Item adicionado',
      description: `${product.name} foi adicionado à transação`
    });
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    onItemsChange(updatedItems);
    
    toast({
      title: 'Item removido',
      description: 'O item foi removido da transação'
    });
  };

  const getProductName = (productId: number) => {
    const product = products.find(p => p.id === productId);
    return product?.name || 'Produto não encontrado';
  };

  const getProductDepartment = (productId: number) => {
    const product = products.find(p => p.id === productId);
    return product?.department || 'Departamento não encontrado';
  };

  const getTotalValue = () => {
    return items.reduce((sum, item) => sum + (item.quantity * (item.unit_price || 0)), 0);
  };

  const getTotalWithDiscount = () => {
    const total = getTotalValue();
    return total - (total * (discount / 100));
  };

  const getDiscountAmount = () => {
    const total = getTotalValue();
    return total * (discount / 100);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Itens do Estoque
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="w-5 h-5" />
          Itens do Estoque (Opcional)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!disabled && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
            <div>
              <Label htmlFor="product">Produto</Label>
              <Select value={selectedProductId} onValueChange={setSelectedProductId}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um produto" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => {
                    const available = getAvailableQuantity(product.id);
                    return (
                      <SelectItem 
                        key={product.id} 
                        value={product.id.toString()}
                        disabled={available <= 0}
                      >
                        <div className="flex flex-col">
                          <span>{product.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {product.department} • Disponível: {available} {product.unit_of_measure}
                          </span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="quantity">Quantidade</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                max={selectedProductId ? getAvailableQuantity(parseInt(selectedProductId)) : undefined}
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                placeholder="1"
              />
              {selectedProductId && (
                <p className="text-xs text-muted-foreground mt-1">
                  Máximo: {getAvailableQuantity(parseInt(selectedProductId))}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="unitPrice">Preço Unitário</Label>
              <Input
                id="unitPrice"
                type="number"
                min="0"
                step="0.01"
                value={unitPrice}
                onChange={(e) => setUnitPrice(e.target.value)}
                placeholder="0.00"
              />
            </div>

            <div className="flex items-end">
              <Button 
                onClick={handleAddItem}
                disabled={!selectedProductId || !quantity}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar
              </Button>
            </div>
          </div>
        )}

        {/* Campo de desconto */}
        {!disabled && items.length > 0 && onDiscountChange && (
          <div className="p-4 border rounded-lg bg-muted/50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <div>
                <Label htmlFor="discount">Desconto (%)</Label>
                <Input
                  id="discount"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={discount}
                  onChange={(e) => onDiscountChange(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
              <div className="text-sm text-muted-foreground">
                <p>Valor do desconto: {formatCurrency(getDiscountAmount())}</p>
              </div>
              <div className="text-sm font-medium">
                <p>Total com desconto: {formatCurrency(getTotalWithDiscount())}</p>
              </div>
            </div>
          </div>
        )}

        {items.length > 0 && (
          <div className="space-y-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Produto</TableHead>
                    <TableHead>Departamento</TableHead>
                    <TableHead className="text-center">Quantidade</TableHead>
                    <TableHead className="text-right">Preço Unit.</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                    {!disabled && <TableHead className="w-[50px]"></TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {getProductName(item.product_id)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getProductDepartment(item.product_id)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        {item.quantity}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.unit_price || 0)}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(item.quantity * (item.unit_price || 0))}
                      </TableCell>
                      {!disabled && (
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex justify-end">
              <div className="text-right space-y-1">
                <p className="text-sm text-muted-foreground">Subtotal dos Itens</p>
                <p className="text-base">{formatCurrency(getTotalValue())}</p>
                {discount > 0 && (
                  <>
                    <p className="text-sm text-red-600">Desconto ({discount}%): -{formatCurrency(getDiscountAmount())}</p>
                    <p className="text-lg font-semibold text-green-600">Total: {formatCurrency(getTotalWithDiscount())}</p>
                  </>
                )}
                {discount === 0 && (
                  <p className="text-lg font-semibold">Total: {formatCurrency(getTotalValue())}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {items.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum item do estoque selecionado</p>
            <p className="text-sm">Os itens são opcionais para a transação</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}