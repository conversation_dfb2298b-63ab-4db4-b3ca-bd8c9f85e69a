import React, { useState, useEffect, createContext, useContext } from 'react';
import { MasterHeader } from './MasterHeader';
import { MasterSidebar } from './MasterSidebar';
import { useMasterAuth } from '../../hooks/useMasterAuth';
import { 
  getMasterDashboardStatsWithRetry, 
  getMasterRecentActivitiesWithRetry,
  MasterDashboardStats,
  MasterActivity 
} from '@/api/masterDashboard';

interface MasterLayoutContextType {
  stats: MasterDashboardStats;
  activities: MasterActivity[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const MasterLayoutContext = createContext<MasterLayoutContextType | undefined>(undefined);

export const useMasterLayoutContext = () => {
  const context = useContext(MasterLayoutContext);
  if (!context) {
    throw new Error('useMasterLayoutContext must be used within a MasterLayout');
  }
  return context;
};

interface MasterLayoutProps {
  children: React.ReactNode;
}

export const MasterLayout: React.FC<MasterLayoutProps> = ({ children }) => {
  const { masterUser, loading: authLoading } = useMasterAuth();
  const [stats, setStats] = useState<MasterDashboardStats>({
    totalClubs: 0,
    activeClubs: 0,
    suspendedClubs: 0,
    trialClubs: 0,
    monthlyRevenue: 0,
    overduePayments: 0,
    newClubsThisMonth: 0
  });
  const [activities, setActivities] = useState<MasterActivity[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async (forceRefresh = false) => {
    setLoadingData(true);
    setError(null);
    try {
      const [statsData, activitiesData] = await Promise.all([
        getMasterDashboardStatsWithRetry(!forceRefresh),
        getMasterRecentActivitiesWithRetry(10, !forceRefresh)
      ]);
      setStats(statsData);
      setActivities(activitiesData);
    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoadingData(false);
    }
  };

  useEffect(() => {
    if (masterUser) {
      loadData();
    }
  }, [masterUser]);

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!masterUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Acesso Negado</h2>
          <p className="text-gray-600 mb-4">Você não tem permissão para acessar esta área.</p>
          <a
            href="/master/login"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Fazer Login
          </a>
        </div>
      </div>
    );
  }

  const contextValue: MasterLayoutContextType = {
    stats,
    activities,
    loading: loadingData,
    error,
    refreshData: () => loadData(true),
  };

  return (
    <MasterLayoutContext.Provider value={contextValue}>
      <div className="min-h-screen bg-gray-50">
        <MasterHeader notifications={activities} />
        <div className="flex">
          <MasterSidebar stats={stats} />
          <main className="flex-1 ml-64 pt-16">
            <div className="p-6">
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>
    </MasterLayoutContext.Provider>
  );
};

export default MasterLayout;