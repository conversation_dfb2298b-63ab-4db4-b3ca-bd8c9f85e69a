import { useEffect, useState } from 'react';

interface MarkdownRendererProps {
    content: string;
    className?: string;
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
    const [htmlContent, setHtmlContent] = useState('');

    useEffect(() => {
        // Converter markdown simples para HTML
        let html = content
            // Headings
            .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold text-gray-800 mt-6 mb-3 scroll-mt-20">$1</h3>')
            .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4 scroll-mt-20">$1</h2>')
            .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-gray-900 mt-8 mb-6 scroll-mt-20">$1</h1>')

            // Bold
            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')

            // Links
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-700 underline">$1</a>')

            // Code inline
            .replace(/`([^`]+)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">$1</code>')

            // Line breaks
            .replace(/\n\n/g, '</p><p class="text-gray-700 leading-relaxed mb-4">')
            .replace(/\n/g, '<br>');

        // Processar listas
        html = html.replace(/^- (.*$)/gim, '<li class="text-gray-700 leading-relaxed">$1</li>');
        html = html.replace(/(<li.*<\/li>)/s, '<ul class="list-disc list-inside space-y-2 ml-4 mb-4">$1</ul>');

        // Processar listas numeradas
        html = html.replace(/^\d+\. (.*$)/gim, '<li class="text-gray-700 leading-relaxed">$1</li>');

        // Wrap em parágrafos
        if (!html.startsWith('<')) {
            html = '<p class="text-gray-700 leading-relaxed mb-4">' + html + '</p>';
        }

        setHtmlContent(html);
    }, [content]);

    return (
        <div
            className={`prose prose-lg max-w-none ${className}`}
            dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
    );
}