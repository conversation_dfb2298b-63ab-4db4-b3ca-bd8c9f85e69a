import { useState, useEffect } from 'react';
import { SEOHead } from '@/components/seo/SEOHead';
import { NewsletterSignup } from '@/components/blog/NewsletterSignup';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Search, Filter, ArrowRight } from 'lucide-react';
import { generateWebSiteSchema, generateBreadcrumbSchema } from '@/utils/seo/schemaGenerators';
import { useBlogAnalytics } from '@/utils/analytics/blogTracking';

interface BlogPost {
  slug: string;
  title: string;
  description: string;
  publishedAt: string;
  readingTime: number;
  author: string;
  category: string;
  tags: string[];
  featuredImage: string;
  featured?: boolean;
}

// Mock data - substituir por dados reais
const blogPosts: BlogPost[] = [
  {
    slug: 'gestao-clubes-futebol-guia-completo',
    title: 'Gestão de Clubes de Futebol: Guia Completo 2025',
    description: 'Guia definitivo para gestão profissional de clubes: financeiro, atletas, médico e operações. Templates gratuitos + sistema completo.',
    publishedAt: '2025-08-10T08:00:00-03:00',
    readingTime: 12,
    author: 'Game Day Nexus',
    category: 'Gestão Esportiva',
    tags: ['gestão de clubes', 'futebol', 'administração esportiva'],
    featuredImage: '/images/blog/gestao-clubes-featured.jpg',
    featured: true
  },
  {
    slug: 'fluxo-caixa-clubes-template-gratuito',
    title: 'Fluxo de Caixa para Clubes: Template Gratuito',
    description: 'Aprenda a controlar as finanças do seu clube com nossa planilha profissional. Download gratuito + tutorial completo.',
    publishedAt: '2025-08-11T08:00:00-03:00',
    readingTime: 8,
    author: 'Game Day Nexus',
    category: 'Financeiro',
    tags: ['fluxo de caixa', 'financeiro', 'planilha'],
    featuredImage: '/images/blog/fluxo-caixa-featured.jpg'
  },
  {
    slug: 'prontuario-eletronico-atleta-guia-completo',
    title: 'Prontuário Eletrônico do Atleta: Guia Completo',
    description: 'Como organizar e digitalizar prontuários médicos de atletas. Template profissional + conformidade LGPD.',
    publishedAt: '2025-08-12T08:00:00-03:00',
    readingTime: 10,
    author: 'Game Day Nexus',
    category: 'Médico',
    tags: ['prontuário médico', 'atletas', 'saúde'],
    featuredImage: '/images/blog/prontuario-featured.jpg'
  },
  {
    slug: 'convocacao-completa-logistica-futebol',
    title: 'Convocação Completa: Do Jogo à Logística',
    description: 'Passo a passo para criar convocações profissionais integrando partida, alojamento e alimentação.',
    publishedAt: '2025-08-13T08:00:00-03:00',
    readingTime: 6,
    author: 'Game Day Nexus',
    category: 'Logística',
    tags: ['convocação', 'logística', 'organização'],
    featuredImage: '/images/blog/convocacao-featured.jpg'
  }
];

const categories = [
  'Todos',
  'Gestão Esportiva',
  'Financeiro',
  'Médico',
  'Logística',
  'Técnico'
];

export function Blog() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Todos');
  const [filteredPosts, setFilteredPosts] = useState(blogPosts);
  const { trackSearch, trackPageView } = useBlogAnalytics();

  useEffect(() => {
    trackPageView('/blog', 'Blog - Game Day Nexus', 'blog');
  }, [trackPageView]);

  useEffect(() => {
    let filtered = blogPosts;

    // Filter by category
    if (selectedCategory !== 'Todos') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      
      // Track search
      trackSearch(searchTerm, filtered.length);
    }

    setFilteredPosts(filtered);
  }, [searchTerm, selectedCategory, trackSearch]);

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  const websiteSchema = generateWebSiteSchema();
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: 'https://gamedaynexus.com' },
    { name: 'Blog', url: 'https://gamedaynexus.com/blog' }
  ]);

  return (
    <>
      <SEOHead
        title="Blog - Gestão de Clubes de Futebol | Game Day Nexus"
        description="Dicas práticas, templates gratuitos e estratégias para gestão profissional de clubes de futebol. Financeiro, médico, logística e muito mais."
        keywords="blog gestão esportiva, dicas clubes futebol, templates gratuitos, gestão financeira clube"
        url="/blog"
        type="website"
        schema={[websiteSchema, breadcrumbSchema]}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 py-12">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Blog Game Day Nexus
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Estratégias práticas, templates gratuitos e cases de sucesso para revolucionar a gestão do seu clube de futebol.
              </p>
            </div>

            {/* Search and Filter */}
            <div className="max-w-2xl mx-auto">
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Buscar posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-2 bg-white"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Category badges */}
              <div className="flex flex-wrap gap-2 justify-center">
                {categories.map(category => (
                  <Badge
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    className="cursor-pointer hover:bg-blue-100"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Featured Post */}
              {featuredPost && selectedCategory === 'Todos' && !searchTerm && (
                <div className="mb-12">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Post em Destaque</h2>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="md:flex">
                      <div className="md:w-1/2">
                        <img
                          src={featuredPost.featuredImage}
                          alt={featuredPost.title}
                          className="w-full h-64 md:h-full object-cover"
                        />
                      </div>
                      <div className="md:w-1/2 p-6">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge>{featuredPost.category}</Badge>
                          <span className="text-sm text-gray-500">•</span>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Clock className="h-3 w-3" />
                            <span>{featuredPost.readingTime} min</span>
                          </div>
                        </div>
                        
                        <h3 className="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600">
                          <a href={`/blog/${featuredPost.slug}`}>
                            {featuredPost.title}
                          </a>
                        </h3>
                        
                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {featuredPost.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <User className="h-3 w-3" />
                            <span>{featuredPost.author}</span>
                            <span>•</span>
                            <Calendar className="h-3 w-3" />
                            <time dateTime={featuredPost.publishedAt}>
                              {new Date(featuredPost.publishedAt).toLocaleDateString('pt-BR')}
                            </time>
                          </div>
                          
                          <Button variant="outline" size="sm" asChild>
                            <a href={`/blog/${featuredPost.slug}`}>
                              Ler Mais
                              <ArrowRight className="ml-2 h-3 w-3" />
                            </a>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              )}

              {/* Posts Grid */}
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    {searchTerm ? `Resultados para "${searchTerm}"` : 'Todos os Posts'}
                  </h2>
                  <span className="text-sm text-gray-500">
                    {filteredPosts.length} {filteredPosts.length === 1 ? 'post' : 'posts'}
                  </span>
                </div>

                {filteredPosts.length === 0 ? (
                  <div className="text-center py-12">
                    <p className="text-gray-500 text-lg mb-4">
                      Nenhum post encontrado para sua busca.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedCategory('Todos');
                      }}
                    >
                      Limpar Filtros
                    </Button>
                  </div>
                ) : (
                  <div className="grid md:grid-cols-2 gap-6">
                    {regularPosts.map(post => (
                      <Card key={post.slug} className="overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="aspect-video overflow-hidden">
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        
                        <CardHeader className="pb-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="secondary">{post.category}</Badge>
                            <span className="text-sm text-gray-500">•</span>
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <Clock className="h-3 w-3" />
                              <span>{post.readingTime} min</span>
                            </div>
                          </div>
                          
                          <CardTitle className="text-xl hover:text-blue-600 transition-colors">
                            <a href={`/blog/${post.slug}`}>
                              {post.title}
                            </a>
                          </CardTitle>
                        </CardHeader>
                        
                        <CardContent className="pt-0">
                          <CardDescription className="text-gray-600 mb-4 line-clamp-2">
                            {post.description}
                          </CardDescription>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <Calendar className="h-3 w-3" />
                              <time dateTime={post.publishedAt}>
                                {new Date(post.publishedAt).toLocaleDateString('pt-BR')}
                              </time>
                            </div>
                            
                            <Button variant="ghost" size="sm" asChild>
                              <a href={`/blog/${post.slug}`}>
                                Ler Mais
                                <ArrowRight className="ml-1 h-3 w-3" />
                              </a>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-8">
                {/* Newsletter Signup */}
                <NewsletterSignup
                  variant="sidebar"
                  source="blog_sidebar"
                />

                {/* Popular Posts */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Posts Populares</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {blogPosts.slice(0, 3).map(post => (
                      <div key={post.slug} className="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                        <h4 className="font-medium text-sm hover:text-blue-600 transition-colors mb-1">
                          <a href={`/blog/${post.slug}`}>
                            {post.title}
                          </a>
                        </h4>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <time dateTime={post.publishedAt}>
                            {new Date(post.publishedAt).toLocaleDateString('pt-BR')}
                          </time>
                          <span>•</span>
                          <Clock className="h-3 w-3" />
                          <span>{post.readingTime} min</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Categories */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Categorias</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {categories.filter(cat => cat !== 'Todos').map(category => {
                        const count = blogPosts.filter(post => post.category === category).length;
                        return (
                          <button
                            key={category}
                            onClick={() => setSelectedCategory(category)}
                            className="flex items-center justify-between w-full text-left text-sm hover:text-blue-600 transition-colors"
                          >
                            <span>{category}</span>
                            <span className="text-gray-400">({count})</span>
                          </button>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                {/* CTA */}
                <Card className="bg-gradient-to-br from-blue-600 to-indigo-600 text-white">
                  <CardContent className="p-6 text-center">
                    <h3 className="font-bold text-lg mb-2">
                      Pronto para Começar?
                    </h3>
                    <p className="text-blue-100 text-sm mb-4">
                      Teste nossa plataforma gratuitamente por 14 dias.
                    </p>
                    <Button 
                      size="sm" 
                      className="bg-white text-blue-600 hover:bg-gray-100"
                      asChild
                    >
                      <a href="/trial">
                        Teste Grátis
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}