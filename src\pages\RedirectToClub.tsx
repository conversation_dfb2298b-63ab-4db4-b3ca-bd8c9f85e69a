import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getUserClubs } from '@/api/api';
import { isAuthenticated } from '@/utils/auth';

export default function RedirectToClub() {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function handleRedirect() {
      try {
        // Verificar se o usuário está autenticado
        if (!isAuthenticated()) {
          navigate('/', { replace: true });
          return;
        }

        const userId = localStorage.getItem('userId');
        const clubId = localStorage.getItem('clubId');
        
        if (!userId) {
          navigate('/login', { replace: true });
          return;
        }

        // Buscar clubes do usuário
        const clubs = await getUserClubs(userId);
        
        if (clubs.length === 0) {
          // Usuário não tem clubes, redirecionar para login
          localStorage.clear();
          navigate('/login', { replace: true });
          return;
        }

        // Pegar o primeiro clube (ou o clube atual se existir)
        let targetClub = clubs[0];
        
        if (clubId) {
          const currentClub = clubs.find(club => club.id === parseInt(clubId));
          if (currentClub) {
            targetClub = currentClub;
          }
        }

        // Determinar o path de destino
        let targetPath = '/dashboard';
        
        // Se veio de uma URL específica, manter o path
        if (location.pathname !== '/redirect-to-club') {
          targetPath = location.pathname;
        }

        // Redirecionar para a URL com slug
        const clubSlug = targetClub.slug || `clube-${targetClub.id}`;
        navigate(`/${clubSlug}${targetPath}`, { replace: true });
        
      } catch (error) {
        console.error('Erro ao redirecionar:', error);
        // Em caso de erro, limpar dados e redirecionar para login
        localStorage.clear();
        navigate('/login', { replace: true });
      } finally {
        setLoading(false);
      }
    }

    handleRedirect();
  }, [navigate, location.pathname]);

  if (!loading) {
    return null; // Não renderizar nada após o redirecionamento
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-team-blue to-team-blue/60">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue mx-auto mb-4"></div>
        <h2 className="text-2xl font-bold mb-4 text-team-blue">Redirecionando...</h2>
        <p className="text-gray-600">Aguarde enquanto redirecionamos você para a área correta.</p>
      </div>
    </div>
  );
}