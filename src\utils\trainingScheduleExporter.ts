import { jsPDF } from "jspdf";
import { format, startOfWeek, addDays, isSameDay, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import type { Training } from "@/api/api";
import type { ClubInfo } from "@/api/api";

export interface TrainingScheduleExportOptions {
  weekStartDate: Date;
  categoryFilter?: string;
  clubInfo: ClubInfo;
}

export interface TrainingScheduleData {
  trainings: Training[];
}

// Função auxiliar para converter hex para RGB
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 31, g: 41, b: 55 };
};

// Função auxiliar para determinar se um horário é manhã ou tarde
const isMorning = (timeString: string): boolean => {
  if (!timeString) return false;
  const time = timeString.split('-')[0] || timeString;
  const hour = parseInt(time.split(':')[0] || '0');
  return hour < 12;
};

// Função auxiliar para carregar imagem
const loadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Erro ao carregar imagem: ${url}`));
    img.src = url;
  });
};

/**
 * Gera um PDF da grade semanal de treinos em formato paisagem
 */
export async function generateTrainingSchedulePDF(
  data: TrainingScheduleData,
  options: TrainingScheduleExportOptions,
  filename: string = 'programação-semanal.pdf'
): Promise<void> {
  // Criar documento PDF em formato paisagem
  const doc = new jsPDF('landscape', 'mm', 'a4');

  // Carregar logo do clube se disponível
  let clubLogo: HTMLImageElement | null = null;
  if (options.clubInfo.logo_url) {
    try {
      clubLogo = await loadImage(options.clubInfo.logo_url);
    } catch (error) {
      console.log('Erro ao carregar logo do clube:', error);
    }
  }

  // Dimensões da página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Cores do clube (usar cores padrão se não definidas)
  const primaryColor = options.clubInfo.primary_color || '#1f2937';
  const secondaryColor = options.clubInfo.secondary_color || '#ef4444';

  // Converter hex para RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 31, g: 41, b: 55 };
  };

  const primaryRgb = hexToRgb(primaryColor);
  const secondaryRgb = hexToRgb(secondaryColor);

  // Configurar fonte
  doc.setFont("helvetica");

  // Cabeçalho
  let currentY = 12;

  // Adicionar emblema do clube (se carregado)
  if (clubLogo) {
    try {
      // Adicionar logo no canto superior esquerdo
      doc.addImage(clubLogo, 'PNG', margin, 8, 16, 16);
    } catch (error) {
      console.log('Erro ao adicionar logo do clube ao PDF:', error);
    }
  }

  // Nome do clube
  doc.setFontSize(18);
  doc.setFont("helvetica", "bold");
  doc.setTextColor(primaryRgb.r, primaryRgb.g, primaryRgb.b);
  doc.text(options.clubInfo.name || 'Clube', pageWidth / 2, currentY, { align: 'center' });
  currentY += 6;

  // Título
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text('Programação Semanal', pageWidth / 2, currentY, { align: 'center' });
  currentY += 5;

  // Período
  const weekStart = startOfWeek(options.weekStartDate, { weekStartsOn: 1 }); // Segunda-feira
  const weekEnd = addDays(weekStart, 6); // Domingo

  doc.setFontSize(12);
  doc.setFont("helvetica", "bold");
  doc.setTextColor(0, 0, 0);
  const periodText = `Período: ${format(weekStart, 'dd/MM/yyyy', { locale: ptBR })} a ${format(weekEnd, 'dd/MM/yyyy', { locale: ptBR })}`;
  doc.text(periodText, pageWidth / 2, currentY + 5, { align: 'center' });

  // Categoria (se filtrada)
  if (options.categoryFilter) {
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.text(`Categoria: ${options.categoryFilter}`, pageWidth / 2, currentY + 9, { align: 'center' });
  }

  currentY += 15;

  // Filtrar treinos da semana
  const weekTrainings = data.trainings.filter(training => {
    const trainingDate = parseISO(training.date);
    return trainingDate >= weekStart && trainingDate <= weekEnd;
  });

  // Organizar treinos por dia da semana
  const dayNames = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo'];
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  // Configurações da tabela
  const tableStartY = currentY;
  const labelColWidth = 40; // coluna de rótulos ligeiramente menor
  const colWidth = (pageWidth - 2 * margin - labelColWidth) / 7;
  const rowHeight = 15;

  // Desenhar cabeçalho da tabela
  doc.setFillColor(secondaryRgb.r, secondaryRgb.g, secondaryRgb.b);
  doc.rect(margin, tableStartY, labelColWidth, rowHeight, 'F');
  doc.rect(margin + labelColWidth, tableStartY, colWidth * 7, rowHeight, 'F');

  // Texto do cabeçalho
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(10);
  doc.setFont("helvetica", "bold");
  doc.text('DATA', margin + 5, tableStartY + 8);

  // Datas dos dias
  weekDays.forEach((day, index) => {
    const x = margin + labelColWidth + (index * colWidth);
    doc.text(format(day, 'dd/MM/yyyy'), x + colWidth / 2, tableStartY + 5, { align: 'center' });
    doc.text(format(day, 'EEEE', { locale: ptBR }).substring(0, 3), x + colWidth / 2, tableStartY + 11, { align: 'center' });
  });

  currentY = tableStartY + rowHeight;

  // Linha "Dia da Semana"
  doc.setFillColor(primaryRgb.r, primaryRgb.g, primaryRgb.b);
  doc.rect(margin, currentY, labelColWidth, rowHeight, 'F');
  doc.setTextColor(255, 255, 255);
  doc.text('Dia da Semana', margin + 5, currentY + 8);

  // Células dos dias da semana - usar cor mais clara
  weekDays.forEach((day, index) => {
    const x = margin + labelColWidth + (index * colWidth);
    // Usar cor de fundo branca/clara ao invés de preta
    doc.setFillColor(250, 250, 250);
    doc.rect(x, currentY, colWidth, rowHeight, 'F');
    // Adicionar borda
    doc.setDrawColor(200, 200, 200);
    doc.rect(x, currentY, colWidth, rowHeight);
    // Texto em preto
    doc.setTextColor(0, 0, 0);
    doc.setFont("helvetica", "normal");
    doc.text(dayNames[index], x + colWidth / 2, currentY + 8, { align: 'center' });
  });

  currentY += rowHeight;

  // Função para desenhar linha da tabela
  const drawTableRow = (label: string, isHeader: boolean = false) => {
    // Garantir tamanho consistente para o texto da primeira coluna
    doc.setFontSize(14);
    if (isHeader) {
      doc.setFillColor(primaryRgb.r, primaryRgb.g, primaryRgb.b);
      doc.rect(margin, currentY, labelColWidth, rowHeight, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFont("helvetica", "bold");
    } else {
      doc.setFillColor(secondaryRgb.r, secondaryRgb.g, secondaryRgb.b);
      doc.rect(margin, currentY, labelColWidth, rowHeight, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFont("helvetica", "bold");
    }

    doc.text(label, margin + 5, currentY + 8);

    // Desenhar células dos dias
    weekDays.forEach((day, index) => {
      const x = margin + labelColWidth + (index * colWidth);
      doc.setDrawColor(200, 200, 200);
      doc.rect(x, currentY, colWidth, rowHeight);
    });

    currentY += rowHeight;
  };

  // Função para preencher dados do treino
  const fillTrainingData = (dayIndex: number, content: string, yOffset: number = 0) => {
    const x = margin + labelColWidth + (dayIndex * colWidth);
    doc.setTextColor(0, 0, 0);
    doc.setFont("helvetica", "normal");
    doc.setFontSize(8);

    // Quebrar texto se necessário
    const lines = doc.splitTextToSize(content, colWidth - 4);
    lines.forEach((line: string, lineIndex: number) => {
      doc.text(line, x + 2, currentY - rowHeight + 4 + yOffset + (lineIndex * 3));
    });
  };


  // Seção Manhã
  currentY += 10;
  doc.setFontSize(16);
  doc.setFont("helvetica", "bold");
  doc.setTextColor(0, 0, 0);
  doc.text('Período 1', margin, currentY);
  currentY += 10;

  drawTableRow('Local');

  // Preencher treinos da manhã
  weekDays.forEach((day, dayIndex) => {
    const morningTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour < 12;
    });

    if (morningTrainings.length > 0) {
      const locations = [...new Set(morningTrainings.map(t => t.location))];
      fillTrainingData(dayIndex, locations.join(', '));
    }
  });

  drawTableRow('Horário');

  weekDays.forEach((day, dayIndex) => {
    const morningTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour < 12;
    });

    if (morningTrainings.length > 0) {
      const details = morningTrainings.map(t => {
        const time = t.time || t.start_time || '';
        const category = t.category_name ? ` (${t.category_name})` : '';
        return `${time}${category}`;
      });
      fillTrainingData(dayIndex, details.join('\n'));
    }
  });

  drawTableRow('Tipo de Treino');

  weekDays.forEach((day, dayIndex) => {
    const morningTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour < 12;
    });

    if (morningTrainings.length > 0) {
      const types = morningTrainings
        .map(t => t.type)
        .filter((type): type is string => !!type && type.trim() !== '');
      if (types.length > 0) {
        fillTrainingData(dayIndex, types.join('\n'));
      }
    }
  });

  // Seção Tarde
  currentY += 10;
  doc.setFontSize(16);
  doc.setFont("helvetica", "bold");
  doc.setTextColor(0, 0, 0);
  doc.text('Período 2', margin, currentY);
  currentY += 10;

  drawTableRow('Local');

  // Preencher treinos da tarde
  weekDays.forEach((day, dayIndex) => {
    const afternoonTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour >= 12;
    });

    if (afternoonTrainings.length > 0) {
      const locations = [...new Set(afternoonTrainings.map(t => t.location))];
      fillTrainingData(dayIndex, locations.join(', '));
    }
  });

  drawTableRow('Horário');

  weekDays.forEach((day, dayIndex) => {
    const afternoonTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour >= 12;
    });

    if (afternoonTrainings.length > 0) {
      const details = afternoonTrainings.map(t => {
        const time = t.time || t.start_time || '';
        const category = t.category_name ? ` (${t.category_name})` : '';
        return `${time}${category}`;
      });
      fillTrainingData(dayIndex, details.join('\n'));
    }
  });

  drawTableRow('Tipo de Treino');

  weekDays.forEach((day, dayIndex) => {
    const afternoonTrainings = weekTrainings.filter(t => {
      if (!isSameDay(parseISO(t.date), day)) return false;
      const time = t.start_time || t.time?.split('-')[0] || '';
      const hour = parseInt(time.split(':')[0] || '0');
      return hour >= 12;
    });

    if (afternoonTrainings.length > 0) {
      const types = afternoonTrainings
        .map(t => t.type)
        .filter((type): type is string => !!type && type.trim() !== '');
      if (types.length > 0) {
        fillTrainingData(dayIndex, types.join('\n'));
      }
    }
  });
  // Rodapé
  currentY = pageHeight - 5;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(
    `Gerado em ${format(new Date(), 'dd/MM/yyyy HH:mm')} - ${options.clubInfo.name}`,
    pageWidth / 2,
    currentY,
    { align: 'center' }
  );

  // Salvar PDF
  doc.save(filename);
}