-- Migração para atualizar os tipos de transação de billing
-- De: 'cobranca', 'recebimento' 
-- Para: 'recebe', 'paga'

-- 1. <PERSON><PERSON>, remover a constraint antiga
ALTER TABLE billing_transactions 
DROP CONSTRAINT IF EXISTS billing_transactions_type_check;

-- 2. Atualizar os dados existentes
UPDATE billing_transactions 
SET type = 'recebe' 
WHERE type = 'cobranca';

UPDATE billing_transactions 
SET type = 'paga' 
WHERE type = 'recebimento';

-- 3. Adicionar a nova constraint
ALTER TABLE billing_transactions 
ADD CONSTRAINT billing_transactions_type_check 
CHECK (type IN ('recebe', 'paga'));

-- 4. Atualizar o coment<PERSON><PERSON> da coluna
COMMENT ON COLUMN billing_transactions.type IS 'Tipo: recebe (clube recebe dinheiro) ou paga (clube paga dinheiro)';