import type { Player } from "@/api/api";
import type { MatchSquadMember } from "@/api/matchLineups";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface LineupValidationOptions {
  requireGoleiro?: boolean;
  maxPlayers?: number;
  minPlayers?: number;
  allowDuplicateNumbers?: boolean;
  checkPlayerStatus?: boolean;
}

/**
 * Valida uma escalação completa
 */
export function validateLineup(
  lineup: Record<string, Player | undefined>,
  formation: string,
  options: LineupValidationOptions = {}
): ValidationResult {
  const {
    requireGoleiro = true,
    maxPlayers = 11,
    minPlayers = 11,
    allowDuplicateNumbers = false,
    checkPlayerStatus = true
  } = options;

  const errors: string[] = [];
  const warnings: string[] = [];

  // Extrair jogadores válidos da escalação
  const players = Object.values(lineup).filter(Boolean) as Player[];
  
  // Validar número de jogadores
  if (players.length < minPlayers) {
    errors.push(`Escalação incompleta: ${players.length}/${minPlayers} jogadores`);
  }
  
  if (players.length > maxPlayers) {
    errors.push(`Muitos jogadores na escalação: ${players.length}/${maxPlayers} jogadores`);
  }

  // Validar presença de goleiro
  if (requireGoleiro) {
    const hasGoleiro = Object.entries(lineup).some(([position, player]) => 
      position === 'GK' && player
    );
    
    if (!hasGoleiro) {
      errors.push("Escalação deve ter um goleiro");
    }
  }

  // Validar números de camisa únicos
  if (!allowDuplicateNumbers) {
    const numbers = players.map(p => p.number).filter(Boolean);
    const duplicateNumbers = numbers.filter((num, index) => numbers.indexOf(num) !== index);
    
    if (duplicateNumbers.length > 0) {
      errors.push(`Números de camisa duplicados: ${duplicateNumbers.join(', ')}`);
    }
  }

  // Validar status dos jogadores
  if (checkPlayerStatus) {
    const unavailablePlayers = players.filter(p => 
      p.status === 'lesionado' || p.status === 'suspenso' || p.status === 'inativo'
    );
    
    if (unavailablePlayers.length > 0) {
      const suspendedPlayers = unavailablePlayers.filter(p => p.status === 'suspenso');
      const injuredPlayers = unavailablePlayers.filter(p => p.status === 'lesionado');
      const inactivePlayers = unavailablePlayers.filter(p => p.status === 'inativo');
      
      if (suspendedPlayers.length > 0) {
        errors.push(`Jogadores suspensos na escalação: ${suspendedPlayers.map(p => p.name).join(', ')}`);
      }
      if (injuredPlayers.length > 0) {
        errors.push(`Jogadores lesionados na escalação: ${injuredPlayers.map(p => p.name).join(', ')}`);
      }
      if (inactivePlayers.length > 0) {
        errors.push(`Jogadores inativos na escalação: ${inactivePlayers.map(p => p.name).join(', ')}`);
      }
    }

    const recoveringPlayers = players.filter(p => p.status === 'em recuperação');
    if (recoveringPlayers.length > 0) {
      warnings.push(`Jogadores em recuperação: ${recoveringPlayers.map(p => p.name).join(', ')}`);
    }
  }

  // Validar formação
  const formationErrors = validateFormation(lineup, formation);
  errors.push(...formationErrors);

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Valida se a escalação está de acordo com a formação selecionada
 */
export function validateFormation(
  lineup: Record<string, Player | undefined>,
  formation: string
): string[] {
  const errors: string[] = [];
  
  const formationPositions: Record<string, string[]> = {
    "4-4-2": ["GK", "RB", "CB1", "CB2", "LB", "RM", "CM1", "CM2", "LM", "ST1", "ST2"],
    "4-3-3": ["GK", "RB", "CB1", "CB2", "LB", "CDM", "CM1", "CM2", "RW", "ST", "LW"],
    "3-5-2": ["GK", "CB1", "CB2", "CB3", "RWB", "CM1", "CM2", "CM3", "LWB", "ST1", "ST2"],
    "4-2-3-1": ["GK", "RB", "CB1", "CB2", "LB", "CDM1", "CDM2", "CAM1", "CAM2", "CAM3", "ST"],
    "5-3-2": ["GK", "RB", "CB1", "CB2", "CB3", "LB", "CM1", "CM2", "CM3", "ST1", "ST2"],
  };

  const expectedPositions = formationPositions[formation];
  if (!expectedPositions) {
    errors.push(`Formação inválida: ${formation}`);
    return errors;
  }

  // Verificar se todas as posições da formação estão preenchidas
  const missingPositions = expectedPositions.filter(pos => !lineup[pos]);
  if (missingPositions.length > 0) {
    errors.push(`Posições não preenchidas na formação ${formation}: ${missingPositions.join(', ')}`);
  }

  // Verificar se há posições extras que não pertencem à formação
  const extraPositions = Object.keys(lineup).filter(pos => 
    lineup[pos] && !expectedPositions.includes(pos)
  );
  if (extraPositions.length > 0) {
    errors.push(`Posições extras que não pertencem à formação ${formation}: ${extraPositions.join(', ')}`);
  }

  return errors;
}

/**
 * Valida o squad completo da partida
 */
export function validateMatchSquad(
  squad: MatchSquadMember[],
  options: {
    maxSubstitutes?: number;
    maxTechnicalStaff?: number;
    maxStaff?: number;
    maxExecutive?: number;
  } = {}
): ValidationResult {
  const {
    maxSubstitutes = 12,
    maxTechnicalStaff = 10,
    maxStaff = 5,
    maxExecutive = 5
  } = options;

  const errors: string[] = [];
  const warnings: string[] = [];

  // Contar membros por papel
  const starters = squad.filter(m => m.role === 'starter');
  const substitutes = squad.filter(m => m.role === 'substitute');
  const technicalStaff = squad.filter(m => m.role === 'technical_staff');
  const staff = squad.filter(m => m.role === 'staff');
  const executive = squad.filter(m => m.role === 'executive');

  // Validar limites
  if (starters.length !== 11) {
    errors.push(`Número incorreto de titulares: ${starters.length}/11`);
  }

  if (substitutes.length > maxSubstitutes) {
    errors.push(`Muitos reservas: ${substitutes.length}/${maxSubstitutes}`);
  }

  if (technicalStaff.length > maxTechnicalStaff) {
    warnings.push(`Muitos membros da comissão técnica: ${technicalStaff.length}/${maxTechnicalStaff}`);
  }

  if (staff.length > maxStaff) {
    warnings.push(`Muitos membros do staff: ${staff.length}/${maxStaff}`);
  }

  if (executive.length > maxExecutive) {
    warnings.push(`Muitos membros da diretoria: ${executive.length}/${maxExecutive}`);
  }

  // Validar duplicações
  const playerIds = squad.filter(m => m.player_id).map(m => m.player_id);
  const duplicatePlayerIds = playerIds.filter((id, index) => playerIds.indexOf(id) !== index);
  
  if (duplicatePlayerIds.length > 0) {
    errors.push("Jogadores duplicados no squad");
  }

  const userIds = squad.filter(m => m.user_id).map(m => m.user_id);
  const duplicateUserIds = userIds.filter((id, index) => userIds.indexOf(id) !== index);
  
  if (duplicateUserIds.length > 0) {
    errors.push("Usuários duplicados no squad");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Valida uma substituição
 */
export function validateSubstitution(
  playerOutId: string,
  playerInId: string,
  squad: MatchSquadMember[],
  substitutions: any[] = []
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Verificar se o jogador que sai está em campo
  const playerOut = squad.find(m => m.player_id === playerOutId && m.role === 'starter');
  if (!playerOut) {
    // Verificar se já entrou como substituto
    const hasEntered = substitutions.some(sub => sub.player_in_id === playerOutId);
    if (!hasEntered) {
      errors.push("Jogador que sai não está em campo");
    }
  }

  // Verificar se o jogador que entra está disponível
  const playerIn = squad.find(m => m.player_id === playerInId && m.role === 'substitute');
  if (!playerIn) {
    errors.push("Jogador que entra não está nos reservas");
  }

  // Verificar se o jogador que entra já não entrou antes
  const alreadyEntered = substitutions.some(sub => sub.player_in_id === playerInId);
  if (alreadyEntered) {
    errors.push("Jogador que entra já participou da partida");
  }

  // Verificar se o jogador que sai já não saiu antes
  const alreadyLeft = substitutions.some(sub => sub.player_out_id === playerOutId);
  if (alreadyLeft) {
    errors.push("Jogador que sai já foi substituído");
  }

  // Verificar limite de substituições (máximo 5 por time)
  if (substitutions.length >= 5) {
    errors.push("Limite de substituições atingido (5 máximo)");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Valida se um jogador pode ser adicionado a uma posição específica
 */
export function validatePlayerPosition(
  player: Player,
  position: string
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Mapeamento de compatibilidade de posições
  const positionCompatibility: Record<string, string[]> = {
    'GK': ['Goleiro'],
    'RB': ['Lateral Direito', 'Lateral', 'Zagueiro'],
    'CB1': ['Zagueiro', 'Lateral'],
    'CB2': ['Zagueiro', 'Lateral'],
    'CB3': ['Zagueiro', 'Lateral'],
    'LB': ['Lateral Esquerdo', 'Lateral', 'Zagueiro'],
    'RWB': ['Lateral Direito', 'Ala', 'Lateral'],
    'LWB': ['Lateral Esquerdo', 'Ala', 'Lateral'],
    'CDM': ['Volante', 'Meio-campo'],
    'CDM1': ['Volante', 'Meio-campo'],
    'CDM2': ['Volante', 'Meio-campo'],
    'CM1': ['Meio-campo', 'Volante'],
    'CM2': ['Meio-campo', 'Volante'],
    'CM3': ['Meio-campo', 'Volante'],
    'RM': ['Meio-campo', 'Ponta'],
    'LM': ['Meio-campo', 'Ponta'],
    'CAM1': ['Meia Atacante', 'Meio-campo'],
    'CAM2': ['Meia Atacante', 'Meio-campo'],
    'CAM3': ['Meia Atacante', 'Meio-campo'],
    'RW': ['Ponta Direita', 'Atacante', 'Ponta'],
    'LW': ['Ponta Esquerda', 'Atacante', 'Ponta'],
    'ST': ['Atacante', 'Centroavante'],
    'ST1': ['Atacante', 'Centroavante'],
    'ST2': ['Atacante', 'Centroavante'],
  };

  const compatiblePositions = positionCompatibility[position] || [];
  
  if (!compatiblePositions.includes(player.position)) {
    warnings.push(`${player.name} joga como ${player.position}, mas está sendo escalado em uma posição de ${compatiblePositions.join('/')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
