import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { generatePixQRCode } from '@/utils/pixGenerator';
import { Download, Copy } from 'lucide-react';
import jsPDF from 'jspdf';

interface PixQRCodeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pixKey: string;
  clubName: string;
  clubLogoUrl?: string;
}

export function PixQRCodeModal({ open, onOpenChange, pixKey, clubName, clubLogoUrl }: PixQRCodeModalProps) {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string | null>(null);
  const [pixString, setPixString] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Função para adicionar logo do clube no centro do QR Code
  const addLogoToQRCode = async (qrCodeDataURL: string, logoUrl: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      const qrImage = new Image();
      const logoImage = new Image();
      
      // Configurar CORS para as imagens
      qrImage.crossOrigin = 'anonymous';
      logoImage.crossOrigin = 'anonymous';
      
      let qrLoaded = false;
      let logoLoaded = false;
      
      const checkBothLoaded = () => {
        if (qrLoaded && logoLoaded) {
          try {
            // Configurar canvas com o tamanho do QR Code
            canvas.width = qrImage.width;
            canvas.height = qrImage.height;
            
            // Desenhar o QR Code
            ctx.drawImage(qrImage, 0, 0);
            
            // Calcular tamanho e posição da logo (20% do tamanho do QR Code)
            const logoSize = canvas.width * 0.2;
            const x = (canvas.width - logoSize) / 2;
            const y = (canvas.height - logoSize) / 2;
            
            // Desenhar fundo branco circular para a logo
            const radius = logoSize / 2 + 5;
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(x + logoSize / 2, y + logoSize / 2, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Desenhar borda cinza ao redor do fundo
            ctx.strokeStyle = '#e5e5e5';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Criar clipping circular para a logo
            ctx.save();
            ctx.beginPath();
            ctx.arc(x + logoSize / 2, y + logoSize / 2, logoSize / 2, 0, 2 * Math.PI);
            ctx.clip();
            
            // Desenhar a logo
            ctx.drawImage(logoImage, x, y, logoSize, logoSize);
            ctx.restore();
            
            resolve(canvas.toDataURL());
          } catch (error) {
            reject(error);
          }
        }
      };
      
      qrImage.onload = () => {
        qrLoaded = true;
        checkBothLoaded();
      };
      
      logoImage.onload = () => {
        logoLoaded = true;
        checkBothLoaded();
      };
      
      qrImage.onerror = () => reject(new Error('Erro ao carregar QR Code'));
      logoImage.onerror = () => reject(new Error('Erro ao carregar logo do clube'));
      
      qrImage.src = qrCodeDataURL;
      logoImage.src = logoUrl;
    });
  };

  const handleGenerateQRCode = async () => {
    if (!amount || !description) {
      toast({
        title: 'Campos obrigatórios',
        description: 'Preencha o valor e a descrição',
        variant: 'destructive'
      });
      return;
    }

    if (parseFloat(amount) <= 0) {
      toast({
        title: 'Valor inválido',
        description: 'O valor deve ser maior que zero',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      let qrCode = await generatePixQRCode({
        pixKey,
        amount: parseFloat(amount),
        description,
        merchantName: clubName,
        merchantCity: 'SAO PAULO'
      });

      // Se há logo do clube, adicionar no centro do QR Code
      if (clubLogoUrl) {
        try {
          qrCode = await addLogoToQRCode(qrCode, clubLogoUrl);
        } catch (logoError) {
          console.warn('Erro ao adicionar logo ao QR Code:', logoError);
          // Continua com o QR Code sem logo
        }
      }

      setQrCodeDataURL(qrCode);
      
      // Gerar string PIX para cópia
      const { generatePixString } = await import('@/utils/pixGenerator');
      const pixStr = generatePixString({
        pixKey,
        amount: parseFloat(amount),
        description,
        merchantName: clubName,
        merchantCity: 'SAO PAULO'
      });
      setPixString(pixStr);

      toast({
        title: 'QR Code gerado',
        description: 'QR Code PIX criado com sucesso!'
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o QR Code',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = () => {
    if (!qrCodeDataURL) return;

    const pdf = new jsPDF();
    
    // Título
    pdf.setFontSize(20);
    pdf.text('Pagamento PIX', 20, 30);
    
    // Informações
    pdf.setFontSize(12);
    pdf.text(`Clube: ${clubName}`, 20, 50);
    pdf.text(`Valor: R$ ${parseFloat(amount).toFixed(2)}`, 20, 60);
    pdf.text(`Descrição: ${description}`, 20, 70);
    
    // QR Code
    pdf.addImage(qrCodeDataURL, 'PNG', 20, 80, 80, 80);
    
    // Instruções
    pdf.setFontSize(10);
    pdf.text('Instruções:', 20, 180);
    pdf.text('1. Abra o app do seu banco', 20, 190);
    pdf.text('2. Escolha a opção PIX', 20, 200);
    pdf.text('3. Escaneie o QR Code acima', 20, 210);
    pdf.text('4. Confirme o pagamento', 20, 220);
    
    // Chave PIX (opcional)
    pdf.text(`Chave PIX: ${pixKey}`, 20, 240);
    
    pdf.save(`pix-${description.replace(/\s+/g, '-').toLowerCase()}.pdf`);
    
    toast({
      title: 'PDF baixado',
      description: 'O arquivo foi salvo com sucesso!'
    });
  };

  const handleCopyPixString = () => {
    if (!pixString) return;
    
    navigator.clipboard.writeText(pixString);
    toast({
      title: 'Copiado',
      description: 'Código PIX copiado para a área de transferência'
    });
  };

  const handleReset = () => {
    setAmount('');
    setDescription('');
    setQrCodeDataURL(null);
    setPixString('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Gerar QR Code PIX</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="amount">Valor (R$)</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0.01"
              placeholder="0,00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Ex: Mensalidade João Silva - Mar/2025"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={2}
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handleGenerateQRCode} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Gerando...' : 'Gerar QR Code'}
            </Button>
            {qrCodeDataURL && (
              <Button variant="outline" onClick={handleReset}>
                Novo
              </Button>
            )}
          </div>
          
          {qrCodeDataURL && (
            <div className="space-y-4 pt-4 border-t">
              <div className="flex justify-center">
                <img 
                  src={qrCodeDataURL} 
                  alt="QR Code PIX" 
                  className="border rounded-lg"
                />
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleDownloadPDF}
                  className="flex-1"
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Baixar PDF
                </Button>
                <Button 
                  onClick={handleCopyPixString}
                  variant="outline"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copiar PIX
                </Button>
              </div>
              
              <div className="text-xs text-muted-foreground text-center">
                Valor: R$ {parseFloat(amount).toFixed(2)} • {description}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}