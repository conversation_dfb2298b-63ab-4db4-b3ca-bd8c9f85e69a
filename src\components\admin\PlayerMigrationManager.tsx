import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Loader2, 
  Database, 
  Users, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  Download
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/context/UserContext';
import { toast } from 'sonner';

interface MigrationStatus {
  total_players: number;
  migrated_players: number;
  pending_players: number;
  players_without_cpf: number;
  global_players_count: number;
  global_documents_count: number;
}

interface DuplicateCPF {
  cpf_number: string;
  player_count: number;
  player_names: string[];
  club_names: string[];
}

interface MigrationResult {
  club_id: number;
  club_name: string;
  migrated_count: number;
  linked_count: number;
  error_count: number;
}

export function PlayerMigrationManager() {
  const { user } = useUser();
  const [status, setStatus] = useState<MigrationStatus | null>(null);
  const [duplicates, setDuplicates] = useState<DuplicateCPF[]>([]);
  const [migrationResults, setMigrationResults] = useState<MigrationResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [migrating, setMigrating] = useState(false);

  // Verificar se o usuário tem permissão (apenas administradores)
  const canManageMigration = user?.club_info?.master_plan_id === 1; // Assumindo que plano 1 é master

  useEffect(() => {
    if (canManageMigration) {
      loadMigrationStatus();
      loadDuplicates();
    }
  }, [canManageMigration]);

  const loadMigrationStatus = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('check_migration_status');
      
      if (error) {
        console.error('Erro ao verificar status:', error);
        toast.error('Erro ao verificar status da migração');
        return;
      }

      if (data && data.length > 0) {
        setStatus(data[0]);
      }
    } catch (error) {
      console.error('Erro ao carregar status:', error);
      toast.error('Erro ao carregar status');
    } finally {
      setLoading(false);
    }
  };

  const loadDuplicates = async () => {
    try {
      const { data, error } = await supabase.rpc('find_duplicate_cpf_players');
      
      if (error) {
        console.error('Erro ao buscar duplicatas:', error);
        return;
      }

      setDuplicates(data || []);
    } catch (error) {
      console.error('Erro ao carregar duplicatas:', error);
    }
  };

  const runMigration = async () => {
    if (!confirm('Tem certeza que deseja executar a migração? Esta operação não pode ser desfeita.')) {
      return;
    }

    setMigrating(true);
    try {
      const { data, error } = await supabase.rpc('migrate_all_players_to_global');
      
      if (error) {
        console.error('Erro na migração:', error);
        toast.error('Erro durante a migração');
        return;
      }

      setMigrationResults(data || []);
      toast.success('Migração concluída com sucesso!');
      
      // Recarregar status
      await loadMigrationStatus();
      await loadDuplicates();
    } catch (error) {
      console.error('Erro na migração:', error);
      toast.error('Erro durante a migração');
    } finally {
      setMigrating(false);
    }
  };

  const exportMigrationReport = () => {
    if (!migrationResults.length) return;

    const csvContent = [
      'Clube ID,Nome do Clube,Migrados,Vinculados,Erros',
      ...migrationResults.map(result => 
        `${result.club_id},"${result.club_name}",${result.migrated_count},${result.linked_count},${result.error_count}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `migration-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (!canManageMigration) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Você não tem permissão para acessar o gerenciador de migração.
        </AlertDescription>
      </Alert>
    );
  }

  const migrationProgress = status ? 
    Math.round((status.migrated_players / status.total_players) * 100) : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gerenciador de Migração</h2>
          <p className="text-muted-foreground">
            Migração de jogadores para o sistema global de transferências
          </p>
        </div>
        <Button onClick={loadMigrationStatus} disabled={loading} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Status da Migração */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Status da Migração
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : status ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{status.total_players}</div>
                  <div className="text-sm text-muted-foreground">Total de Jogadores</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{status.migrated_players}</div>
                  <div className="text-sm text-muted-foreground">Migrados</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{status.pending_players}</div>
                  <div className="text-sm text-muted-foreground">Pendentes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{status.players_without_cpf}</div>
                  <div className="text-sm text-muted-foreground">Sem CPF</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{status.global_players_count}</div>
                  <div className="text-sm text-muted-foreground">Jogadores Globais</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{status.global_documents_count}</div>
                  <div className="text-sm text-muted-foreground">Documentos Globais</div>
                </div>
              </div>

              <Separator />

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Progresso da Migração</span>
                  <span className="text-sm text-muted-foreground">{migrationProgress}%</span>
                </div>
                <Progress value={migrationProgress} className="h-2" />
              </div>

              {status.pending_players > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Existem {status.pending_players} jogadores pendentes de migração.
                    {status.players_without_cpf > 0 && (
                      <> {status.players_without_cpf} jogadores não possuem CPF e precisam ser atualizados manualmente.</>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Clique em "Atualizar" para carregar o status
            </div>
          )}
        </CardContent>
      </Card>

      {/* Duplicatas de CPF */}
      {duplicates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              CPFs Duplicados ({duplicates.length})
            </CardTitle>
            <CardDescription>
              Estes CPFs estão cadastrados para múltiplos jogadores e precisam ser resolvidos antes da migração
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {duplicates.map((duplicate, index) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-mono text-sm">{duplicate.cpf_number}</span>
                    <Badge variant="destructive">{duplicate.player_count} jogadores</Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div>
                      <strong>Jogadores:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {duplicate.player_names.map((name, i) => (
                          <li key={i}>{name}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <strong>Clubes:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {duplicate.club_names.map((club, i) => (
                          <li key={i}>{club}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Ações de Migração */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Executar Migração
          </CardTitle>
          <CardDescription>
            Execute a migração de todos os jogadores para o sistema global
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {duplicates.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Existem CPFs duplicados que devem ser resolvidos antes da migração.
                  A migração pode falhar ou produzir resultados inesperados.
                </AlertDescription>
              </Alert>
            )}

            <Button 
              onClick={runMigration} 
              disabled={migrating}
              className="w-full"
              size="lg"
            >
              {migrating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Executando Migração...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Executar Migração Completa
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Resultados da Migração */}
      {migrationResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Resultados da Migração
            </CardTitle>
            <CardDescription className="flex items-center justify-between">
              <span>Resultados da última execução</span>
              <Button onClick={exportMigrationReport} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exportar CSV
              </Button>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {migrationResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{result.club_name}</div>
                    <div className="text-sm text-muted-foreground">ID: {result.club_id}</div>
                  </div>
                  <div className="flex gap-2">
                    {result.migrated_count > 0 && (
                      <Badge variant="default">{result.migrated_count} migrados</Badge>
                    )}
                    {result.linked_count > 0 && (
                      <Badge variant="secondary">{result.linked_count} vinculados</Badge>
                    )}
                    {result.error_count > 0 && (
                      <Badge variant="destructive">{result.error_count} erros</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}