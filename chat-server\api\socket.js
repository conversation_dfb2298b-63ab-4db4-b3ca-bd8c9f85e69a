// Importar todas as dependências diretamente aqui para evitar problemas de path
import 'dotenv/config';
import { Server } from 'socket.io';
import { createClient } from '@supabase/supabase-js';

// Configuração Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim())
  : [
    'http://localhost:5173',
    'http://localhost:3000',
    'https://www.gamedaynexus.com.br'
  ];

console.log('Allowed origins:', allowedOrigins);

const io = new Server({
  path: '/api/socket',
  cors: {
    origin: allowedOrigins,
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['polling', 'websocket'],
  allowEIO3: true
});

// Cache de usuários conectados
const connectedUsers = new Map();

// Middleware de autenticação simplificado para Vercel
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    const clubId = socket.handshake.auth.clubId;

    console.log('🔐 Tentativa de autenticação:', {
      hasToken: !!token,
      hasClubId: !!clubId,
      tokenLength: token ? token.length : 0
    });

    if (!token) {
      console.warn('❌ Auth handshake sem token');
      return next(new Error('No token provided'));
    }
    if (!clubId) {
      console.warn('❌ Auth handshake sem clubId');
      return next(new Error('No club provided'));
    }

    // Timeout para evitar travamento na Vercel
    const authTimeout = setTimeout(() => {
      console.error('❌ Timeout na autenticação');
      next(new Error('Authentication timeout'));
    }, 5000);

    try {
      const { data: { user }, error } = await supabase.auth.getUser(token);

      clearTimeout(authTimeout);

      if (error) {
        console.error('❌ Erro na autenticação Supabase:', error.message);
        return next(new Error(`Authentication failed: ${error.message}`));
      }

      if (!user) {
        console.warn('❌ Usuário não encontrado');
        return next(new Error('User not found'));
      }

      console.log('✅ Usuário autenticado:', user.email);
      socket.userId = user.id;
      socket.userEmail = user.email;
      socket.userName = user.email; // Usar email como nome por enquanto
      socket.clubId = Number(clubId);
      socket.clubName = `Clube ${clubId}`;

      next();
    } catch (authError) {
      clearTimeout(authTimeout);
      console.error('❌ Erro na autenticação:', authError);
      next(new Error('Authentication failed'));
    }
  } catch (error) {
    console.error('❌ Erro geral na autenticação:', error);
    next(new Error('Authentication failed'));
  }
});

// Eventos do Socket.IO (versão simplificada para teste)
io.on('connection', async (socket) => {
  console.log(`🟢 ${socket.userName} conectado ao clube ${socket.clubName || socket.clubId}`);

  socket.on('disconnect', () => {
    console.log(`🔴 ${socket.userName} desconectado`);
  });
});

// Handler para Vercel
export default function handler(req, res) {
  console.log('SocketHandler chamado:', {
    method: req.method,
    url: req.url,
    origin: req.headers.origin,
    allowedOrigins
  });

  // Configurar CORS
  const origin = req.headers.origin;
  const isAllowedOrigin = allowedOrigins.includes(origin) ||
    allowedOrigins.includes('*') ||
    (!origin && req.headers.host);

  if (origin && !isAllowedOrigin) {
    console.log('Origin não permitida:', origin);
    return res.status(403).json({ error: 'Origin not allowed' });
  }

  // Definir headers CORS
  if (origin && isAllowedOrigin) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else if (allowedOrigins.includes('*')) {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Responder a preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Inicializar Socket.IO se necessário
  if (!res.socket.server.io) {
    console.log('Iniciando Socket.IO na Vercel...');
    res.socket.server.io = io;
    io.attach(res.socket.server, {
      path: '/api/socket',
      cors: {
        origin: allowedOrigins,
        methods: ['GET', 'POST'],
        credentials: true
      }
    });
  } else {
    console.log('Socket.IO já está rodando');
  }

  res.end();
}