import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

export default function PaymentReceiptSuccess() {
  return (
    <div className="container max-w-2xl py-10">
      <Card>
        <CardHeader>
          <CardTitle className="text-center text-green-600 flex items-center justify-center gap-2">
            <CheckCircle className="h-6 w-6" />
            Comprovante Enviado com Sucesso!
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-lg">
              Seu comprovante de pagamento foi enviado com sucesso.
            </p>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">Próximos Passos:</h3>
              <ul className="text-sm text-green-800 space-y-1 text-left">
                <li>• O clube irá verificar seu comprovante</li>
                <li>• Você receberá uma confirmação por email</li>
                <li>• Após a aprovação, o agendamento será liberado</li>
                <li>• Em caso de dúvidas, entre em contato com o clube</li>
              </ul>
            </div>

            <p className="text-muted-foreground">
              Obrigado por escolher nosso clube!
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}