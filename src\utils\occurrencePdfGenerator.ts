import { PDFDocument, rgb } from 'pdf-lib';
import { ClubInfo, PlayerOccurrence } from '@/api/api';

export interface OccurrencePdfData {
  title: string;
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  status: 'active' | 'resolved' | 'archived';
  resolution_notes?: string | null;
  created_at?: string;
  player_name?: string;
  signature_url?: string | null;
  // Campos de assinatura digital
  signed_at?: string | null;
  signed_by_name?: string | null;
  signed_by_role?: string | null;
}

function loadEmblem(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!url) return reject(new Error('URL nao fornecida'));
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) return reject(new Error('contexto 2D ausente'));
      ctx.drawImage(img, 0, 0);
      resolve(canvas.toDataURL('image/png'));
    };
    img.onerror = reject;
    const u = new URL(url);
    u.searchParams.append('t', Date.now().toString());
    img.src = u.toString();
  });
}

export async function generateOccurrencePDF(
  occurrence: OccurrencePdfData,
  clubInfo: ClubInfo
): Promise<Blob> {
  const doc = await PDFDocument.create();
  let page = doc.addPage([600, 800]);
  const { width, height } = page.getSize();
  const margin = 50;
  let y = height - margin;
  const fontSize = 12;
  const footerY = 100;

  const drawText = (text: string, x: number, yPos: number, size: number) => {
    page.drawText(text, { x, y: yPos, size, color: rgb(0, 0, 0) });
    return yPos - 20;
  };

  const addText = (label: string, value: string) => {
    return drawText(`${label}: ${value}`, margin, y, fontSize);
  };

  const clubName = clubInfo?.name || 'Clube';
  y = drawText(clubName, margin, y, 16);

  if (clubInfo?.logo_url) {
    try {
      const dataUrl = await loadEmblem(clubInfo.logo_url);
      const img = await doc.embedPng(dataUrl);
      const size = 50;
      page.drawImage(img, {
        x: width - margin - size,
        y: y + 10,
        width: size,
        height: size,
      });
    } catch {
      // ignore logo errors
    }
  }

  y -= 40;
  page.drawLine({
    start: { x: margin, y: y + 10 },
    end: { x: width - margin, y: y + 10 },
    thickness: 1,
    color: rgb(0.8, 0.8, 0.8),
  });
  y -= 20;

  y = drawText('REGISTRO DE OCORRÊNCIA', margin, y, 18);
  y -= 20;

  y = addText('Título', occurrence.title || 'Não informado');
  if (occurrence.player_name) {
    y = addText('Jogador', occurrence.player_name);
  }
  if (occurrence.created_at) {
    const d = new Date(occurrence.created_at);
    y = addText('Data', d.toLocaleDateString('pt-BR'));
  }
  const typeText = {
    occurrence: 'Ocorrência',
    divergence: 'Divergência',
    punishment: 'Punição',
    training_release: 'Liberação p/ Treinar',
    training_game_release: 'Liberação p/ Treino e Jogo',
  }[occurrence.type as
    | 'occurrence'
    | 'divergence'
    | 'punishment'
    | 'training_release'
    | 'training_game_release'] || occurrence.type;
  y = addText('Tipo', typeText);
  const severityText = { low: 'Baixa', medium: 'Média', high: 'Alta' }[
    occurrence.severity
  ];
  y = addText('Severidade', severityText);
  const statusText = { active: 'Ativa', resolved: 'Resolvida', archived: 'Arquivada' }[
    occurrence.status
  ];
  y = addText('Status', statusText);

  y -= 10;
  y = drawText('Descrição:', margin, y, fontSize);
  const descriptionLines = occurrence.description.match(/[^\n]{1,80}/g) || [];
  for (const line of descriptionLines) {
    y = drawText(line, margin + 10, y, fontSize);
    if (y < footerY) {
      page = doc.addPage([width, height]);
      y = height - margin - 20;
    }
  }

  if (occurrence.resolution_notes) {
    y -= 10;
    y = drawText('Notas de Resolução:', margin, y, fontSize);
    const notesLines = occurrence.resolution_notes.match(/[^\n]{1,80}/g) || [];
    for (const line of notesLines) {
      y = drawText(line, margin + 10, y, fontSize);
      if (y < footerY) {
        page = doc.addPage([width, height]);
        y = height - margin - 20;
      }
    }
  }

  // Seção de assinatura digital
  if (occurrence.signed_at) {
    y -= 30;
    page.drawLine({
      start: { x: margin, y: y + 10 },
      end: { x: width - margin, y: y + 10 },
      thickness: 1,
      color: rgb(0.8, 0.8, 0.8),
    });
    y -= 10;
    y = drawText('ASSINATURA DIGITAL', margin, y, 14);
    y -= 10;
    
    if (occurrence.signed_by_name) {
      y = addText('Assinado por', occurrence.signed_by_name);
    }
    if (occurrence.signed_by_role) {
      y = addText('Função', occurrence.signed_by_role);
    }
    const signedDate = new Date(occurrence.signed_at);
    y = addText('Data da Assinatura', signedDate.toLocaleDateString('pt-BR') + ' às ' + signedDate.toLocaleTimeString('pt-BR'));
    
    y -= 10;
    y = drawText('[VERIFICADO] Documento assinado digitalmente', margin, y, 10);
    y = drawText('A integridade deste documento foi verificada atraves de assinatura digital.', margin, y, 10);
  }

  // Assinatura por imagem (se existir)
  if (occurrence.signature_url) {
    y -= 20;
    y = drawText('Assinatura por Imagem:', margin, y, fontSize);
    try {
      const dataUrl = await loadEmblem(occurrence.signature_url);
      const img = await doc.embedPng(dataUrl);
      const imgWidth = 200;
      const imgHeight = (img.height / img.width) * imgWidth;
      page.drawImage(img, { x: margin, y: y - imgHeight, width: imgWidth, height: imgHeight });
      y -= imgHeight;
    } catch {
      // ignore errors
    }
  }

  const footerText = `Gerado em: ${new Date().toLocaleDateString('pt-BR')}`;
  drawText(footerText, margin, 40, 10);

  const pdfBytes = await doc.save();
  return new Blob([pdfBytes], { type: 'application/pdf' });
}