import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  Users,
  BarChart3,
  Brain,
  AlertTriangle,
  Info
} from 'lucide-react';
import { TrainingDrill, TrainingElement } from './InteractiveTrainingBuilder';
import { useAnalysisCalculations, AnalysisMetrics } from '@/hooks/useAnalysisCalculations';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';

interface TacticalAnalysisProps {
  drill: TrainingDrill | null;
  elements: TrainingElement[];
}

function TacticalAnalysisContent({ drill, elements }: TacticalAnalysisProps) {
  const [analysisType, setAnalysisType] = useState<'overview' | 'tactical' | 'physical' | 'spatial'>('overview');
  
  // Use the custom hook for analysis calculations
  const { metrics } = useAnalysisCalculations(drill, elements);

  const getIntensityColor = (value: number) => {
    if (value >= 80) return 'text-red-600';
    if (value >= 60) return 'text-orange-600';
    if (value >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getRecommendations = (metrics: AnalysisMetrics) => {
    const recommendations = [];
    
    if (metrics.intensity > 80) {
      recommendations.push({
        type: 'warning',
        title: 'Alta Intensidade',
        description: 'Considere adicionar períodos de recuperação entre os exercícios.',
        icon: AlertTriangle
      });
    }
    
    if (metrics.spatialCoverage < 30) {
      recommendations.push({
        type: 'info',
        title: 'Baixa Cobertura Espacial',
        description: 'Distribua melhor os elementos pelo campo para maior aproveitamento do espaço.',
        icon: Info
      });
    }
    
    if (metrics.playerInteraction < 40) {
      recommendations.push({
        type: 'info',
        title: 'Pouca Interação',
        description: 'Aproxime os jogadores para aumentar a interação e cooperação.',
        icon: Users
      });
    }
    
    if (metrics.complexity > 90) {
      recommendations.push({
        type: 'warning',
        title: 'Alta Complexidade',
        description: 'Simplifique o exercício para melhor compreensão dos jogadores.',
        icon: Brain
      });
    }
    
    return recommendations;
  };

  if (!drill || !metrics) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BarChart3 className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Selecione um drill para ver a análise tática
          </p>
        </CardContent>
      </Card>
    );
  }

  const tacticalData = [
    { name: 'Ataque', value: metrics.tacticalFocus.attacking, color: '#ef4444' },
    { name: 'Defesa', value: metrics.tacticalFocus.defending, color: '#3b82f6' },
    { name: 'Transição', value: metrics.tacticalFocus.transition, color: '#10b981' },
    { name: 'Bola Parada', value: metrics.tacticalFocus.setpieces, color: '#f59e0b' }
  ];

  const physicalData = [
    { subject: 'Cardio', A: metrics.physicalDemand.cardio, fullMark: 100 },
    { subject: 'Força', A: metrics.physicalDemand.strength, fullMark: 100 },
    { subject: 'Agilidade', A: metrics.physicalDemand.agility, fullMark: 100 },
    { subject: 'Coordenação', A: metrics.physicalDemand.coordination, fullMark: 100 }
  ];

  const skillData = [
    { name: 'Técnico', value: metrics.skillDevelopment.technical },
    { name: 'Tático', value: metrics.skillDevelopment.tactical },
    { name: 'Físico', value: metrics.skillDevelopment.physical },
    { name: 'Mental', value: metrics.skillDevelopment.mental }
  ];

  const recommendations = getRecommendations(metrics);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Brain className="h-4 w-4" />
          Análise Tática Avançada
        </CardTitle>
        <CardDescription className="text-xs">
          Análise detalhada do drill com métricas e recomendações
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs value={analysisType} onValueChange={(value: any) => setAnalysisType(value)}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="text-xs">Visão Geral</TabsTrigger>
            <TabsTrigger value="tactical" className="text-xs">Tático</TabsTrigger>
            <TabsTrigger value="physical" className="text-xs">Físico</TabsTrigger>
            <TabsTrigger value="spatial" className="text-xs">Espacial</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Métricas principais */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Intensidade</span>
                  <span className={getIntensityColor(metrics.intensity)}>
                    {Math.round(metrics.intensity)}%
                  </span>
                </div>
                <Progress value={metrics.intensity} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Complexidade</span>
                  <span className={getIntensityColor(metrics.complexity)}>
                    {Math.round(metrics.complexity)}%
                  </span>
                </div>
                <Progress value={metrics.complexity} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Cobertura Espacial</span>
                  <span className={getIntensityColor(metrics.spatialCoverage)}>
                    {Math.round(metrics.spatialCoverage)}%
                  </span>
                </div>
                <Progress value={metrics.spatialCoverage} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Interação</span>
                  <span className={getIntensityColor(metrics.playerInteraction)}>
                    {Math.round(metrics.playerInteraction)}%
                  </span>
                </div>
                <Progress value={metrics.playerInteraction} className="h-2" />
              </div>
            </div>

            {/* Desenvolvimento de habilidades */}
            <div>
              <h4 className="text-xs font-medium mb-2">Desenvolvimento de Habilidades</h4>
              <ResponsiveContainer width="100%" height={120}>
                <BarChart data={skillData}>
                  <CartesianGrid strokeDasharray="3,3" />
                  <XAxis dataKey="name" tick={{ fontSize: 10 }} />
                  <YAxis tick={{ fontSize: 10 }} />
                  <Tooltip />
                  <Bar dataKey="value" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="tactical" className="space-y-4">
            <div>
              <h4 className="text-xs font-medium mb-2">Foco Tático</h4>
              <ResponsiveContainer width="100%" height={150}>
                <PieChart>
                  <Pie
                    data={tacticalData}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    dataKey="value"
                  >
                    {tacticalData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              {tacticalData.map(item => (
                <div key={item.name} className="flex items-center gap-2 text-xs">
                  <div 
                    className="w-3 h-3 rounded" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span>{item.name}: {item.value}%</span>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="physical" className="space-y-4">
            <div>
              <h4 className="text-xs font-medium mb-2">Demanda Física</h4>
              <ResponsiveContainer width="100%" height={150}>
                <RadarChart data={physicalData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" tick={{ fontSize: 10 }} />
                  <PolarRadiusAxis tick={{ fontSize: 8 }} />
                  <Radar
                    name="Demanda"
                    dataKey="A"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="spatial" className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <span className="font-medium">Elementos no campo:</span>
                <span className="ml-1">{elements.length}</span>
              </div>
              <div>
                <span className="font-medium">Jogadores:</span>
                <span className="ml-1">{elements.filter(e => e.type === 'player').length}</span>
              </div>
              <div>
                <span className="font-medium">Equipamentos:</span>
                <span className="ml-1">{elements.filter(e => e.type !== 'player').length}</span>
              </div>
              <div>
                <span className="font-medium">Cobertura:</span>
                <span className="ml-1">{Math.round(metrics.spatialCoverage)}%</span>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Recomendações */}
        {recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-xs font-medium">Recomendações</h4>
            <div className="space-y-2">
              {recommendations.map((rec, index) => {
                const Icon = rec.icon;
                return (
                  <div
                    key={index}
                    className={`p-2 rounded-lg border text-xs ${
                      rec.type === 'warning' 
                        ? 'border-orange-200 bg-orange-50' 
                        : 'border-blue-200 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      <Icon className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium">{rec.title}</p>
                        <p className="text-muted-foreground">{rec.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Resumo do drill */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="font-medium">Categoria:</span>
              <span className="ml-1 capitalize">{drill.category}</span>
            </div>
            <div>
              <span className="font-medium">Dificuldade:</span>
              <span className="ml-1 capitalize">{drill.difficulty}</span>
            </div>
            <div>
              <span className="font-medium">Duração:</span>
              <span className="ml-1">{Math.floor(drill.totalDuration / 60)}min</span>
            </div>
            <div>
              <span className="font-medium">Jogadores:</span>
              <span className="ml-1">{drill.playersRequired}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
export function TacticalAnalysis({ drill, elements }: TacticalAnalysisProps) {
  return (
    <ErrorBoundary>
      <TacticalAnalysisContent drill={drill} elements={elements} />
    </ErrorBoundary>
  );
}