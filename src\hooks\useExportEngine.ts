import { useState, useCallback, useRef, useEffect } from 'react';
import { TrainingDrill } from '@/components/training/InteractiveTrainingBuilder';
import { 
  ExportEngine, 
  ExportOptions, 
  ExportResult, 
  ExportProgress,
  ExportJob,
  createConfiguredExportEngine 
} from '@/lib/exporters';

export interface UseExportEngineOptions {
  autoCleanup?: boolean;
  maxConcurrentJobs?: number;
}

export interface ExportEngineHook {
  // Export methods
  exportDrill: (drill: TrainingDrill, options: ExportOptions) => Promise<ExportResult>;
  exportMultiple: (drills: TrainingDrill[], format: any, options?: Partial<ExportOptions>) => Promise<ExportResult[]>;
  queueExport: (drill: TrainingDrill, options: ExportOptions, priority?: 'low' | 'normal' | 'high') => string;
  
  // Job management
  getJobStatus: (jobId: string) => ExportJob | null;
  cancelJob: (jobId: string) => boolean;
  getAllJobs: () => ExportJob[];
  clearQueue: () => void;
  
  // Progress tracking
  onProgress: (jobId: string, callback: (progress: ExportProgress) => void) => void;
  offProgress: (jobId: string) => void;
  
  // State
  isExporting: boolean;
  queueLength: number;
  activeJobsCount: number;
  availableFormats: string[];
  
  // Utilities
  downloadResult: (result: ExportResult) => void;
  getEngine: () => ExportEngine;
}

export const useExportEngine = (options: UseExportEngineOptions = {}): ExportEngineHook => {
  const {
    autoCleanup = true,
    maxConcurrentJobs = 2
  } = options;

  const engineRef = useRef<ExportEngine | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [queueLength, setQueueLength] = useState(0);
  const [activeJobsCount, setActiveJobsCount] = useState(0);
  const [availableFormats, setAvailableFormats] = useState<string[]>([]);

  // Initialize engine
  useEffect(() => {
    if (!engineRef.current) {
      engineRef.current = createConfiguredExportEngine();
      engineRef.current.setMaxConcurrentJobs(maxConcurrentJobs);
      setAvailableFormats(engineRef.current.getAvailableFormats());
    }

    return () => {
      if (autoCleanup && engineRef.current) {
        engineRef.current.destroy();
        engineRef.current = null;
      }
    };
  }, [autoCleanup, maxConcurrentJobs]);

  // Update state periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (engineRef.current) {
        const newQueueLength = engineRef.current.getQueueLength();
        const newActiveJobsCount = engineRef.current.getActiveJobsCount();
        
        setQueueLength(newQueueLength);
        setActiveJobsCount(newActiveJobsCount);
        setIsExporting(newActiveJobsCount > 0);
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Export single drill
  const exportDrill = useCallback(async (drill: TrainingDrill, options: ExportOptions): Promise<ExportResult> => {
    if (!engineRef.current) {
      throw new Error('Export engine not initialized');
    }

    setIsExporting(true);
    try {
      const result = await engineRef.current.exportDrill(drill, options);
      return result;
    } finally {
      setIsExporting(false);
    }
  }, []);

  // Export multiple drills
  const exportMultiple = useCallback(async (
    drills: TrainingDrill[], 
    format: any, 
    options?: Partial<ExportOptions>
  ): Promise<ExportResult[]> => {
    if (!engineRef.current) {
      throw new Error('Export engine not initialized');
    }

    setIsExporting(true);
    try {
      const results = await engineRef.current.exportMultiple(drills, format, options);
      return results;
    } finally {
      setIsExporting(false);
    }
  }, []);

  // Queue export for background processing
  const queueExport = useCallback((
    drill: TrainingDrill, 
    options: ExportOptions, 
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): string => {
    if (!engineRef.current) {
      throw new Error('Export engine not initialized');
    }

    return engineRef.current.queueExport(drill, options, priority);
  }, []);

  // Get job status
  const getJobStatus = useCallback((jobId: string): ExportJob | null => {
    if (!engineRef.current) return null;
    return engineRef.current.getJobStatus(jobId);
  }, []);

  // Cancel job
  const cancelJob = useCallback((jobId: string): boolean => {
    if (!engineRef.current) return false;
    return engineRef.current.cancelJob(jobId);
  }, []);

  // Get all jobs
  const getAllJobs = useCallback((): ExportJob[] => {
    if (!engineRef.current) return [];
    return engineRef.current.getAllJobs();
  }, []);

  // Clear queue
  const clearQueue = useCallback((): void => {
    if (!engineRef.current) return;
    engineRef.current.clearQueue();
  }, []);

  // Progress tracking
  const onProgress = useCallback((jobId: string, callback: (progress: ExportProgress) => void): void => {
    if (!engineRef.current) return;
    engineRef.current.onProgress(jobId, callback);
  }, []);

  const offProgress = useCallback((jobId: string): void => {
    if (!engineRef.current) return;
    engineRef.current.offProgress(jobId);
  }, []);

  // Download result utility
  const downloadResult = useCallback((result: ExportResult): void => {
    if (!result.success || !result.data) {
      console.error('Cannot download failed export result');
      return;
    }

    try {
      // Create download link
      const url = URL.createObjectURL(result.data as Blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = result.filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Cleanup
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  }, []);

  // Get engine instance
  const getEngine = useCallback((): ExportEngine => {
    if (!engineRef.current) {
      throw new Error('Export engine not initialized');
    }
    return engineRef.current;
  }, []);

  return {
    exportDrill,
    exportMultiple,
    queueExport,
    getJobStatus,
    cancelJob,
    getAllJobs,
    clearQueue,
    onProgress,
    offProgress,
    isExporting,
    queueLength,
    activeJobsCount,
    availableFormats,
    downloadResult,
    getEngine
  };
};

// Hook for specific export formats
export const usePDFExport = () => {
  const exportEngine = useExportEngine();
  
  const exportToPDF = useCallback(async (drill: TrainingDrill, options: Partial<any> = {}) => {
    const pdfOptions = {
      format: 'pdf' as const,
      quality: 'high' as const,
      pageSize: 'A4' as const,
      orientation: 'landscape' as const,
      includeInstructions: true,
      includeTimeline: false,
      template: 'landscape' as const,
      ...options
    };
    
    return exportEngine.exportDrill(drill, pdfOptions);
  }, [exportEngine]);
  
  return { exportToPDF, ...exportEngine };
};

export const useImageExport = () => {
  const exportEngine = useExportEngine();
  
  const exportToImage = useCallback(async (drill: TrainingDrill, options: Partial<any> = {}) => {
    const imageOptions = {
      format: 'image' as const,
      quality: 'high' as const,
      imageFormat: 'png' as const,
      width: 1920,
      height: 1080,
      backgroundColor: '#ffffff',
      includeField: true,
      ...options
    };
    
    return exportEngine.exportDrill(drill, imageOptions);
  }, [exportEngine]);
  
  return { exportToImage, ...exportEngine };
};

export const useVideoExport = () => {
  const exportEngine = useExportEngine();
  
  const exportToVideo = useCallback(async (drill: TrainingDrill, options: Partial<any> = {}) => {
    const videoOptions = {
      format: 'video' as const,
      quality: 'high' as const,
      videoFormat: 'webm' as const,
      width: 1920,
      height: 1080,
      fps: 30,
      duration: 10000,
      compression: 'medium' as const,
      ...options
    };
    
    return exportEngine.exportDrill(drill, videoOptions);
  }, [exportEngine]);
  
  return { exportToVideo, ...exportEngine };
};

export const useDataExport = () => {
  const exportEngine = useExportEngine();
  
  const exportToJSON = useCallback(async (drill: TrainingDrill, options: Partial<any> = {}) => {
    const jsonOptions = {
      format: 'json' as const,
      quality: 'high' as const,
      includeAnimations: true,
      includeTrajectories: true,
      minify: false,
      ...options
    };
    
    return exportEngine.exportDrill(drill, jsonOptions);
  }, [exportEngine]);
  
  return { exportToJSON, ...exportEngine };
};