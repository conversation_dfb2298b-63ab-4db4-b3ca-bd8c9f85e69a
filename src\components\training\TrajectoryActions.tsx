import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Target,
  Zap,
  ArrowRight,
  RotateCcw,
  Play,
  Pause,
  Settings,
  Trash2,
  Copy,
  Edit,
  Plus,
  Timer,
  Activity,
  Users,
  Circle,
  Square,
  Triangle,
  Star,
  Eye,
  EyeOff,
  TrendingUp,
  Save
} from 'lucide-react';
import { TrajectoryPoint, Trajectory } from './TrajectorySystem';

export interface TrajectoryAction {
  id: string;
  timestamp: number;
  type: 'pass' | 'shoot' | 'dribble' | 'tackle' | 'run' | 'walk' | 'stop' | 'receive' | 'cross' | 'header';
  targetElementId?: string;
  intensity: number; // 0-100
  duration: number; // milliseconds
  properties: {
    direction?: number; // degrees
    power?: number; // 0-100
    accuracy?: number; // 0-100
    technique?: 'inside' | 'outside' | 'laces' | 'header' | 'chest' | 'thigh';
    bodyPart?: 'left_foot' | 'right_foot' | 'head' | 'chest' | 'thigh';
    height?: 'ground' | 'low' | 'medium' | 'high';
    spin?: 'none' | 'topspin' | 'backspin' | 'sidespin';
  };
  visualFeedback: {
    color: string;
    icon: string;
    size: 'small' | 'medium' | 'large';
    animation: 'none' | 'pulse' | 'bounce' | 'flash' | 'shake';
    showTrail: boolean;
    showImpact: boolean;
  };
  soundEffect?: string;
  description?: string;
}

export interface ActionTrigger {
  id: string;
  actionId: string;
  condition: 'time' | 'position' | 'proximity' | 'speed' | 'direction';
  value: number;
  tolerance: number;
  active: boolean;
}

export interface ElementInteraction {
  id: string;
  sourceElementId: string;
  targetElementId: string;
  type: 'pass' | 'tackle' | 'block' | 'support' | 'mark' | 'press';
  timestamp: number;
  success: boolean;
  intensity: number;
  distance: number;
  angle: number;
  properties: {
    ballInvolved: boolean;
    contactType?: 'direct' | 'indirect' | 'aerial';
    outcome?: 'successful' | 'intercepted' | 'missed' | 'blocked';
    followUp?: string; // ID of next action
  };
}

interface TrajectoryActionsProps {
  trajectories: Trajectory[];
  onTrajectoriesChange: (trajectories: Trajectory[]) => void;
  selectedTrajectoryId?: string;
  onSelectedTrajectoryChange?: (id: string | null) => void;
  currentTime: number;
  isPlaying: boolean;
  onTimeChange?: (time: number) => void;
}

export function TrajectoryActions({
  trajectories,
  onTrajectoriesChange,
  selectedTrajectoryId,
  onSelectedTrajectoryChange,
  currentTime,
  isPlaying,
  onTimeChange
}: TrajectoryActionsProps) {
  const [actions, setActions] = useState<TrajectoryAction[]>([]);
  const [triggers, setTriggers] = useState<ActionTrigger[]>([]);
  const [interactions, setInteractions] = useState<ElementInteraction[]>([]);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [isCreatingAction, setIsCreatingAction] = useState(false);
  const [actionPreview, setActionPreview] = useState<Partial<TrajectoryAction> | null>(null);
  const [showVisualFeedback, setShowVisualFeedback] = useState(true);
  const [actionLibrary, setActionLibrary] = useState<TrajectoryAction[]>([]);

  const selectedTrajectory = selectedTrajectoryId 
    ? trajectories.find(t => t.id === selectedTrajectoryId)
    : null;

  // Action type configurations
  const actionTypes = {
    pass: {
      icon: Target,
      color: '#3b82f6',
      defaultIntensity: 70,
      defaultDuration: 1000,
      properties: ['direction', 'power', 'accuracy', 'technique', 'height']
    },
    shoot: {
      icon: Zap,
      color: '#ef4444',
      defaultIntensity: 90,
      defaultDuration: 800,
      properties: ['direction', 'power', 'accuracy', 'technique', 'height']
    },
    dribble: {
      icon: RotateCcw,
      color: '#10b981',
      defaultIntensity: 60,
      defaultDuration: 2000,
      properties: ['direction', 'technique', 'bodyPart']
    },
    tackle: {
      icon: Activity,
      color: '#f59e0b',
      defaultIntensity: 80,
      defaultDuration: 500,
      properties: ['direction', 'power', 'technique']
    },
    run: {
      icon: TrendingUp,
      color: '#8b5cf6',
      defaultIntensity: 50,
      defaultDuration: 3000,
      properties: ['direction']
    },
    walk: {
      icon: Users,
      color: '#06b6d4',
      defaultIntensity: 30,
      defaultDuration: 5000,
      properties: ['direction']
    },
    stop: {
      icon: Square,
      color: '#6b7280',
      defaultIntensity: 0,
      defaultDuration: 1000,
      properties: []
    },
    receive: {
      icon: Circle,
      color: '#84cc16',
      defaultIntensity: 40,
      defaultDuration: 500,
      properties: ['bodyPart', 'technique']
    },
    cross: {
      icon: ArrowRight,
      color: '#f97316',
      defaultIntensity: 75,
      defaultDuration: 1200,
      properties: ['direction', 'power', 'accuracy', 'height']
    },
    header: {
      icon: Triangle,
      color: '#ec4899',
      defaultIntensity: 65,
      defaultDuration: 600,
      properties: ['direction', 'power', 'accuracy']
    }
  };

  // Create new action
  const createAction = useCallback((type: TrajectoryAction['type']) => {
    if (!selectedTrajectory) return;

    const config = actionTypes[type];
    const newAction: TrajectoryAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: currentTime,
      type,
      intensity: config.defaultIntensity,
      duration: config.defaultDuration,
      properties: {
        direction: 0,
        power: config.defaultIntensity,
        accuracy: 80,
        technique: 'inside',
        bodyPart: 'right_foot',
        height: 'medium',
        spin: 'none'
      },
      visualFeedback: {
        color: config.color,
        icon: config.icon.name,
        size: 'medium',
        animation: 'pulse',
        showTrail: true,
        showImpact: true
      },
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} action`
    };

    setActions(prev => [...prev, newAction]);
    setSelectedAction(newAction.id);
    setIsCreatingAction(false);
  }, [selectedTrajectory, currentTime]);

  // Update action
  const updateAction = useCallback((actionId: string, updates: Partial<TrajectoryAction>) => {
    setActions(prev => prev.map(action => 
      action.id === actionId ? { ...action, ...updates } : action
    ));
  }, []);

  // Delete action
  const deleteAction = useCallback((actionId: string) => {
    setActions(prev => prev.filter(action => action.id !== actionId));
    if (selectedAction === actionId) {
      setSelectedAction(null);
    }
  }, [selectedAction]);

  // Create trigger
  const createTrigger = useCallback((actionId: string, condition: ActionTrigger['condition']) => {
    const newTrigger: ActionTrigger = {
      id: `trigger_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      actionId,
      condition,
      value: condition === 'time' ? currentTime : 0,
      tolerance: condition === 'time' ? 100 : 10,
      active: true
    };

    setTriggers(prev => [...prev, newTrigger]);
  }, [currentTime]);

  // Update trigger
  const updateTrigger = useCallback((triggerId: string, updates: Partial<ActionTrigger>) => {
    setTriggers(prev => prev.map(trigger => 
      trigger.id === triggerId ? { ...trigger, ...updates } : trigger
    ));
  }, []);

  // Delete trigger
  const deleteTrigger = useCallback((triggerId: string) => {
    setTriggers(prev => prev.filter(trigger => trigger.id !== triggerId));
  }, []);

  // Create interaction
  const createInteraction = useCallback((
    sourceId: string, 
    targetId: string, 
    type: ElementInteraction['type']
  ) => {
    const newInteraction: ElementInteraction = {
      id: `interaction_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      sourceElementId: sourceId,
      targetElementId: targetId,
      type,
      timestamp: currentTime,
      success: true,
      intensity: 70,
      distance: 0,
      angle: 0,
      properties: {
        ballInvolved: ['pass', 'tackle'].includes(type),
        contactType: 'direct',
        outcome: 'successful'
      }
    };

    setInteractions(prev => [...prev, newInteraction]);
  }, [currentTime]);

  // Check triggers
  const checkTriggers = useCallback(() => {
    if (!isPlaying) return;

    const activeActions: TrajectoryAction[] = [];

    triggers.forEach(trigger => {
      if (!trigger.active) return;

      const action = actions.find(a => a.id === trigger.actionId);
      if (!action) return;

      let shouldTrigger = false;

      switch (trigger.condition) {
        case 'time':
          shouldTrigger = Math.abs(currentTime - trigger.value) <= trigger.tolerance;
          break;
        case 'position':
          // Check if element is at specific position
          if (selectedTrajectory) {
            const currentPoint = getCurrentTrajectoryPoint(selectedTrajectory, currentTime);
            if (currentPoint) {
              const distance = Math.sqrt(
                Math.pow(currentPoint.x - (trigger.value % 1000), 2) + 
                Math.pow(currentPoint.y - Math.floor(trigger.value / 1000), 2)
              );
              shouldTrigger = distance <= trigger.tolerance;
            }
          }
          break;
        case 'speed':
          if (selectedTrajectory) {
            const currentPoint = getCurrentTrajectoryPoint(selectedTrajectory, currentTime);
            if (currentPoint && currentPoint.speed) {
              shouldTrigger = Math.abs(currentPoint.speed - trigger.value) <= trigger.tolerance;
            }
          }
          break;
        case 'proximity':
          // Check proximity to other elements
          shouldTrigger = checkElementProximity(trigger.value, trigger.tolerance);
          break;
        case 'direction':
          if (selectedTrajectory) {
            const direction = getCurrentTrajectoryDirection(selectedTrajectory, currentTime);
            if (direction !== null) {
              shouldTrigger = Math.abs(direction - trigger.value) <= trigger.tolerance;
            }
          }
          break;
      }

      if (shouldTrigger) {
        activeActions.push(action);
      }
    });

    // Execute active actions
    activeActions.forEach(action => {
      executeAction(action);
    });
  }, [isPlaying, currentTime, triggers, actions, selectedTrajectory]);

  // Execute action
  const executeAction = useCallback((action: TrajectoryAction) => {
    if (!showVisualFeedback) return;

    // Create visual feedback
    const feedbackElement = document.createElement('div');
    feedbackElement.className = `action-feedback action-${action.type}`;
    feedbackElement.style.cssText = `
      position: absolute;
      background: ${action.visualFeedback.color};
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      pointer-events: none;
      z-index: 1000;
      animation: ${action.visualFeedback.animation} 0.5s ease-in-out;
    `;
    feedbackElement.textContent = action.type.toUpperCase();

    // Position feedback near trajectory point
    if (selectedTrajectory) {
      const point = getCurrentTrajectoryPoint(selectedTrajectory, currentTime);
      if (point) {
        feedbackElement.style.left = `${point.x}px`;
        feedbackElement.style.top = `${point.y - 30}px`;
      }
    }

    document.body.appendChild(feedbackElement);

    // Remove feedback after animation
    setTimeout(() => {
      if (feedbackElement.parentNode) {
        feedbackElement.parentNode.removeChild(feedbackElement);
      }
    }, 1000);

    // Play sound effect if available
    if (action.soundEffect) {
      const audio = new Audio(action.soundEffect);
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore audio play errors
      });
    }
  }, [showVisualFeedback, selectedTrajectory, currentTime]);

  // Helper functions
  const getCurrentTrajectoryPoint = (trajectory: Trajectory, time: number): TrajectoryPoint | null => {
    if (trajectory.points.length === 0) return null;

    // Find closest point at or before the current time
    let closestPoint = trajectory.points[0];
    for (const point of trajectory.points) {
      if (point.timestamp <= time) {
        closestPoint = point;
      } else {
        break;
      }
    }

    return closestPoint;
  };

  const getCurrentTrajectoryDirection = (trajectory: Trajectory, time: number): number | null => {
    const currentPoint = getCurrentTrajectoryPoint(trajectory, time);
    if (!currentPoint) return null;

    const currentIndex = trajectory.points.findIndex(p => p === currentPoint);
    if (currentIndex === -1 || currentIndex === trajectory.points.length - 1) return null;

    const nextPoint = trajectory.points[currentIndex + 1];
    const dx = nextPoint.x - currentPoint.x;
    const dy = nextPoint.y - currentPoint.y;

    return Math.atan2(dy, dx) * (180 / Math.PI);
  };

  const checkElementProximity = (distance: number, tolerance: number): boolean => {
    // This would check proximity to other elements in the field
    // Implementation depends on the field element system
    return false;
  };

  // Save action to library
  const saveActionToLibrary = useCallback((action: TrajectoryAction) => {
    const savedAction = {
      ...action,
      id: `library_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: 0 // Reset timestamp for library
    };
    setActionLibrary(prev => [...prev, savedAction]);
  }, []);

  // Load action from library
  const loadActionFromLibrary = useCallback((libraryAction: TrajectoryAction) => {
    const loadedAction = {
      ...libraryAction,
      id: `action_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: currentTime
    };
    setActions(prev => [...prev, loadedAction]);
    setSelectedAction(loadedAction.id);
  }, [currentTime]);

  // Check triggers on time change
  useEffect(() => {
    checkTriggers();
  }, [checkTriggers]);

  const selectedActionData = selectedAction ? actions.find(a => a.id === selectedAction) : null;
  const actionTriggers = selectedAction ? triggers.filter(t => t.actionId === selectedAction) : [];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Zap className="h-4 w-4" />
          Ações de Trajetória
        </CardTitle>
        <CardDescription className="text-xs">
          Configure ações, triggers e interações entre elementos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Action Creation */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Criar Ação</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCreatingAction(!isCreatingAction)}
              disabled={!selectedTrajectory}
            >
              <Plus className="h-3 w-3 mr-1" />
              Nova Ação
            </Button>
          </div>

          {!selectedTrajectory && (
            <p className="text-xs text-muted-foreground">
              Selecione uma trajetória para criar ações
            </p>
          )}

          {isCreatingAction && (
            <div className="grid grid-cols-2 gap-1 p-2 border rounded-lg">
              {Object.entries(actionTypes).map(([type, config]) => {
                const IconComponent = config.icon;
                return (
                  <Button
                    key={type}
                    variant="outline"
                    size="sm"
                    onClick={() => createAction(type as TrajectoryAction['type'])}
                    className="text-xs flex items-center gap-1"
                  >
                    <IconComponent className="h-3 w-3" />
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                );
              })}
            </div>
          )}
        </div>

        {/* Actions List */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">
            Ações ({actions.length})
          </Label>
          
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {actions.map(action => {
              const config = actionTypes[action.type];
              const IconComponent = config.icon;
              return (
                <div
                  key={action.id}
                  className={`p-2 rounded border cursor-pointer ${
                    selectedAction === action.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-muted hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedAction(action.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-3 w-3" style={{ color: config.color }} />
                      <span className="text-xs font-medium">{action.type}</span>
                      <Badge variant="secondary" className="text-xs">
                        {(action.timestamp / 1000).toFixed(1)}s
                      </Badge>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          saveActionToLibrary(action);
                        }}
                      >
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteAction(action.id);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="text-xs text-muted-foreground">
                    Intensidade: {action.intensity}% • Duração: {action.duration}ms
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Selected Action Configuration */}
        {selectedActionData && (
          <div className="space-y-3 border-t pt-3">
            <Label className="text-xs font-medium">Configurar Ação</Label>
            
            <div>
              <Label className="text-xs">Descrição</Label>
              <Input
                value={selectedActionData.description || ''}
                onChange={(e) => updateAction(selectedActionData.id, { description: e.target.value })}
                className="text-xs"
                placeholder="Descrição da ação..."
              />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Timestamp (s)</Label>
                <Input
                  type="number"
                  value={(selectedActionData.timestamp / 1000).toFixed(1)}
                  onChange={(e) => updateAction(selectedActionData.id, { 
                    timestamp: parseFloat(e.target.value) * 1000 
                  })}
                  className="text-xs"
                  step="0.1"
                />
              </div>
              
              <div>
                <Label className="text-xs">Duração (ms)</Label>
                <Input
                  type="number"
                  value={selectedActionData.duration}
                  onChange={(e) => updateAction(selectedActionData.id, { 
                    duration: parseInt(e.target.value) 
                  })}
                  className="text-xs"
                />
              </div>
            </div>

            <div>
              <Label className="text-xs">Intensidade (%)</Label>
              <Slider
                value={[selectedActionData.intensity]}
                onValueChange={([intensity]) => updateAction(selectedActionData.id, { intensity })}
                max={100}
                min={0}
                step={5}
                className="mt-1"
              />
            </div>

            {/* Action Properties */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Propriedades</Label>
              
              {actionTypes[selectedActionData.type].properties.includes('direction') && (
                <div>
                  <Label className="text-xs">Direção (°)</Label>
                  <Slider
                    value={[selectedActionData.properties.direction || 0]}
                    onValueChange={([direction]) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, direction }
                    })}
                    max={360}
                    min={0}
                    step={5}
                    className="mt-1"
                  />
                </div>
              )}

              {actionTypes[selectedActionData.type].properties.includes('power') && (
                <div>
                  <Label className="text-xs">Força (%)</Label>
                  <Slider
                    value={[selectedActionData.properties.power || 50]}
                    onValueChange={([power]) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, power }
                    })}
                    max={100}
                    min={0}
                    step={5}
                    className="mt-1"
                  />
                </div>
              )}

              {actionTypes[selectedActionData.type].properties.includes('accuracy') && (
                <div>
                  <Label className="text-xs">Precisão (%)</Label>
                  <Slider
                    value={[selectedActionData.properties.accuracy || 80]}
                    onValueChange={([accuracy]) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, accuracy }
                    })}
                    max={100}
                    min={0}
                    step={5}
                    className="mt-1"
                  />
                </div>
              )}

              {actionTypes[selectedActionData.type].properties.includes('technique') && (
                <div>
                  <Label className="text-xs">Técnica</Label>
                  <Select
                    value={selectedActionData.properties.technique || 'inside'}
                    onValueChange={(technique) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, technique }
                    })}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="inside">Parte interna</SelectItem>
                      <SelectItem value="outside">Parte externa</SelectItem>
                      <SelectItem value="laces">Peito do pé</SelectItem>
                      <SelectItem value="header">Cabeça</SelectItem>
                      <SelectItem value="chest">Peito</SelectItem>
                      <SelectItem value="thigh">Coxa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {actionTypes[selectedActionData.type].properties.includes('bodyPart') && (
                <div>
                  <Label className="text-xs">Parte do Corpo</Label>
                  <Select
                    value={selectedActionData.properties.bodyPart || 'right_foot'}
                    onValueChange={(bodyPart) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, bodyPart }
                    })}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left_foot">Pé esquerdo</SelectItem>
                      <SelectItem value="right_foot">Pé direito</SelectItem>
                      <SelectItem value="head">Cabeça</SelectItem>
                      <SelectItem value="chest">Peito</SelectItem>
                      <SelectItem value="thigh">Coxa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {actionTypes[selectedActionData.type].properties.includes('height') && (
                <div>
                  <Label className="text-xs">Altura</Label>
                  <Select
                    value={selectedActionData.properties.height || 'medium'}
                    onValueChange={(height) => updateAction(selectedActionData.id, {
                      properties: { ...selectedActionData.properties, height }
                    })}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ground">Rasteiro</SelectItem>
                      <SelectItem value="low">Baixo</SelectItem>
                      <SelectItem value="medium">Médio</SelectItem>
                      <SelectItem value="high">Alto</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* Visual Feedback Configuration */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Feedback Visual</Label>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Cor</Label>
                  <div className="grid grid-cols-4 gap-1 mt-1">
                    {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'].map(color => (
                      <button
                        key={color}
                        className={`w-6 h-6 rounded border-2 ${
                          selectedActionData.visualFeedback.color === color ? 'border-primary' : 'border-muted'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => updateAction(selectedActionData.id, {
                          visualFeedback: { ...selectedActionData.visualFeedback, color }
                        })}
                      />
                    ))}
                  </div>
                </div>
                
                <div>
                  <Label className="text-xs">Tamanho</Label>
                  <Select
                    value={selectedActionData.visualFeedback.size}
                    onValueChange={(size) => updateAction(selectedActionData.id, {
                      visualFeedback: { ...selectedActionData.visualFeedback, size }
                    })}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Pequeno</SelectItem>
                      <SelectItem value="medium">Médio</SelectItem>
                      <SelectItem value="large">Grande</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Animação</Label>
                  <Select
                    value={selectedActionData.visualFeedback.animation}
                    onValueChange={(animation) => updateAction(selectedActionData.id, {
                      visualFeedback: { ...selectedActionData.visualFeedback, animation }
                    })}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Nenhuma</SelectItem>
                      <SelectItem value="pulse">Pulso</SelectItem>
                      <SelectItem value="bounce">Salto</SelectItem>
                      <SelectItem value="flash">Flash</SelectItem>
                      <SelectItem value="shake">Tremor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Mostrar Rastro</Label>
                  <Switch
                    checked={selectedActionData.visualFeedback.showTrail}
                    onCheckedChange={(showTrail) => updateAction(selectedActionData.id, {
                      visualFeedback: { ...selectedActionData.visualFeedback, showTrail }
                    })}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Mostrar Impacto</Label>
                <Switch
                  checked={selectedActionData.visualFeedback.showImpact}
                  onCheckedChange={(showImpact) => updateAction(selectedActionData.id, {
                    visualFeedback: { ...selectedActionData.visualFeedback, showImpact }
                  })}
                />
              </div>
            </div>
          </div>
        )}

        {/* Triggers */}
        {selectedActionData && (
          <div className="space-y-2 border-t pt-3">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">
                Triggers ({actionTriggers.length})
              </Label>
              <Select onValueChange={(condition) => createTrigger(selectedActionData.id, condition as ActionTrigger['condition'])}>
                <SelectTrigger className="w-32 text-xs">
                  <SelectValue placeholder="Novo Trigger" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="time">Tempo</SelectItem>
                  <SelectItem value="position">Posição</SelectItem>
                  <SelectItem value="proximity">Proximidade</SelectItem>
                  <SelectItem value="speed">Velocidade</SelectItem>
                  <SelectItem value="direction">Direção</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1 max-h-24 overflow-y-auto">
              {actionTriggers.map(trigger => (
                <div
                  key={trigger.id}
                  className="flex items-center justify-between p-2 rounded border border-muted"
                >
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={trigger.active}
                      onCheckedChange={(active) => updateTrigger(trigger.id, { active })}
                    />
                    <span className="text-xs font-medium">{trigger.condition}</span>
                    <Badge variant="outline" className="text-xs">
                      {trigger.value}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4"
                    onClick={() => deleteTrigger(trigger.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Global Settings */}
        <div className="space-y-2 border-t pt-3">
          <Label className="text-xs font-medium">Configurações Globais</Label>
          
          <div className="flex items-center justify-between">
            <Label className="text-xs">Feedback Visual</Label>
            <Switch
              checked={showVisualFeedback}
              onCheckedChange={setShowVisualFeedback}
            />
          </div>
        </div>

        {/* Action Library */}
        {actionLibrary.length > 0 && (
          <div className="space-y-2 border-t pt-3">
            <Label className="text-xs font-medium">
              Biblioteca de Ações ({actionLibrary.length})
            </Label>
            
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {actionLibrary.map(libraryAction => {
                const config = actionTypes[libraryAction.type];
                const IconComponent = config.icon;
                return (
                  <div
                    key={libraryAction.id}
                    className="flex items-center justify-between p-2 rounded border border-muted hover:border-primary/50 cursor-pointer"
                    onClick={() => loadActionFromLibrary(libraryAction)}
                  >
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-3 w-3" style={{ color: config.color }} />
                      <span className="text-xs font-medium">{libraryAction.type}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4"
                      onClick={(e) => {
                        e.stopPropagation();
                        setActionLibrary(prev => prev.filter(a => a.id !== libraryAction.id));
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dicas:</strong> Crie ações em pontos específicos da trajetória e configure triggers 
            para executá-las automaticamente. Use feedback visual para destacar momentos importantes.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}