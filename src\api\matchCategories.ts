import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

export type MatchCategory = Database["public"]["Tables"]["match_categories"]["Row"];

export async function addMatchCategories(clubId: number, matchId: string, categoryIds: number[]): Promise<void> {
  if (categoryIds.length === 0) return;
  const insertData = categoryIds.map((cid) => ({ club_id: clubId, match_id: matchId, category_id: cid }));
  const { error } = await supabase.from("match_categories").insert(insertData);
  if (error) {
    console.error("Erro ao salvar categorias da partida:", error);
    throw new Error(`Erro ao salvar categorias da partida: ${error.message}`);
  }
}

export async function getMatchCategories(clubId: number, matchId: string): Promise<MatchCategory[]> {
  const { data, error } = await supabase
    .from("match_categories")
    .select("*")
    .eq("club_id", clubId)
    .eq("match_id", matchId);
  if (error) {
    console.error("Erro ao buscar categorias da partida:", error);
    throw new Error(`Erro ao buscar categorias da partida: ${error.message}`);
  }
  return data || [];
}