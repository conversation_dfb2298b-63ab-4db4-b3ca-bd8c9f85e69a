import { create } from "zustand";
import { Category, PlayerCategory, getCategories, createCategory, updateCategory, deleteCategory, assignPlayerToCategory, removePlayerFromCategory, getPlayerCategories, getCategoryPlayers, migrateYouthPlayers } from "@/api/categories";
import { CATEGORY_PERMISSIONS } from "@/constants/permissions";
import { usePermissionsStore } from "./usePermissionsStore";

const hasPermission = (permission: string) => {
  const { role, permissions } = usePermissionsStore.getState();
  if (role === "president") return true;
  if (role === "admin" && !permission.startsWith("president.")) return true;
  return !!permissions[permission];
};

interface CategoriesState {
  categories: Category[];
  selectedCategory: Category | null;
  loading: boolean;
  error: string | null;
  fetchCategories: (clubId: number) => Promise<void>;
  addCategory: (clubId: number, category: Omit<Category, "id" | "club_id" | "created_at">) => Promise<void>;
  updateCategory: (clubId: number, id: number, category: Partial<Category>) => Promise<void>;
  deleteCategory: (clubId: number, id: number) => Promise<void>;
  assignPlayerToCategory: (clubId: number, playerId: string, categoryId: number) => Promise<void>;
  removePlayerFromCategory: (clubId: number, playerId: string, categoryId: number) => Promise<void>;
  getPlayerCategories: (clubId: number, playerId: string) => Promise<Category[]>;
  getCategoryPlayers: (clubId: number, categoryId: number) => Promise<any[]>;
  setSelectedCategory: (category: Category | null) => void;
  migrateYouthPlayers: (clubId: number) => Promise<void>;
}

export const useCategoriesStore = create<CategoriesState>((set, get) => ({
  categories: [],
  selectedCategory: null,
  loading: false,
  error: null,

  fetchCategories: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const categories = await getCategories(clubId);
      set({ categories, loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar categorias", loading: false });
    }
  },

  addCategory: async (clubId: number, category: Omit<Category, "id" | "club_id" | "created_at">): Promise<void> => {
    if (!hasPermission(CATEGORY_PERMISSIONS.CREATE)) {
      set({ error: "Sem permissão para criar categorias" });
      return;
    }
    set({ loading: true, error: null });
    try {
      const newCategory = await createCategory(clubId, category);
      set((state) => ({ categories: [...state.categories, newCategory], loading: false }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao adicionar categoria", loading: false });
    }
  },

  updateCategory: async (clubId: number, id: number, category: Partial<Category>): Promise<void> => {
    if (!hasPermission(CATEGORY_PERMISSIONS.EDIT)) {
      set({ error: "Sem permissão para editar categorias" });
      return;
    }
    set({ loading: true, error: null });
    try {
      const updated = await updateCategory(clubId, id, category);
      set((state) => ({ 
        categories: state.categories.map(c => c.id === id ? updated : c), 
        loading: false 
      }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao atualizar categoria", loading: false });
    }
  },

  deleteCategory: async (clubId: number, id: number): Promise<void> => {
    if (!hasPermission(CATEGORY_PERMISSIONS.DELETE)) {
      set({ error: "Sem permissão para excluir categorias" });
      return;
    }
    set({ loading: true, error: null });
    try {
      await deleteCategory(clubId, id);
      set((state) => ({ 
        categories: state.categories.filter(c => c.id !== id), 
        loading: false 
      }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao excluir categoria", loading: false });
    }
  },

  assignPlayerToCategory: async (clubId: number, playerId: string, categoryId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await assignPlayerToCategory(clubId, playerId, categoryId);
      set({ loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao associar jogador à categoria", loading: false });
    }
  },

  removePlayerFromCategory: async (clubId: number, playerId: string, categoryId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await removePlayerFromCategory(clubId, playerId, categoryId);
      set({ loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao remover jogador da categoria", loading: false });
    }
  },

  getPlayerCategories: async (clubId: number, playerId: string): Promise<Category[]> => {
    set({ loading: true, error: null });
    try {
      const categories = await getPlayerCategories(clubId, playerId);
      set({ loading: false });
      return categories;
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar categorias do jogador", loading: false });
      return [];
    }
  },

  getCategoryPlayers: async (clubId: number, categoryId: number): Promise<any[]> => {
    set({ loading: true, error: null });
    try {
      const players = await getCategoryPlayers(clubId, categoryId);
      set({ loading: false });
      return players;
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar jogadores da categoria", loading: false });
      return [];
    }
  },

  setSelectedCategory: (category: Category | null) => {
    set({ selectedCategory: category });
  },

  migrateYouthPlayers: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await migrateYouthPlayers(clubId);
      set({ loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao migrar jogadores da base juvenil", loading: false });
    }
  }
}));
