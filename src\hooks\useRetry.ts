import { useState, useCallback } from 'react';

interface UseRetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  backoffMultiplier?: number;
  onError?: (error: Error, attempt: number) => void;
  onSuccess?: (result: any, attempt: number) => void;
}

interface UseRetryReturn<T> {
  execute: () => Promise<T>;
  isLoading: boolean;
  error: Error | null;
  retryCount: number;
  canRetry: boolean;
  reset: () => void;
}

export function useRetry<T>(
  asyncFunction: () => Promise<T>,
  options: UseRetryOptions = {}
): UseRetryReturn<T> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    backoffMultiplier = 2,
    onError,
    onSuccess
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const execute = useCallback(async (): Promise<T> => {
    setIsLoading(true);
    setError(null);

    let lastError: Error;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        const result = await asyncFunction();
        
        // Success
        setIsLoading(false);
        setRetryCount(attempt);
        
        if (onSuccess) {
          onSuccess(result, attempt);
        }
        
        return result;
      } catch (err) {
        lastError = err as Error;
        attempt++;

        if (onError) {
          onError(lastError, attempt);
        }

        // If we've exhausted all retries, break
        if (attempt > maxRetries) {
          break;
        }

        // Wait before retrying with exponential backoff
        const delay = initialDelay * Math.pow(backoffMultiplier, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries exhausted
    setIsLoading(false);
    setError(lastError!);
    setRetryCount(attempt - 1);
    
    throw lastError!;
  }, [asyncFunction, maxRetries, initialDelay, backoffMultiplier, onError, onSuccess]);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setRetryCount(0);
  }, []);

  const canRetry = retryCount < maxRetries && !isLoading;

  return {
    execute,
    isLoading,
    error,
    retryCount,
    canRetry,
    reset
  };
}

// Specialized hook for master API calls
export function useMasterApiRetry<T>(
  apiCall: () => Promise<T>,
  options: UseRetryOptions = {}
) {
  return useRetry(apiCall, {
    maxRetries: 3,
    initialDelay: 1000,
    backoffMultiplier: 1.5,
    onError: (error, attempt) => {
      console.error(`Master API call failed (attempt ${attempt}):`, error);
      
      // Don't retry on specific errors
      if (error.message.includes('não encontrada') || 
          error.message.includes('Campos não encontrados') ||
          error.message.includes('PGRST202') ||
          error.message.includes('PGRST116')) {
        throw error; // Stop retrying on these specific errors
      }
    },
    ...options
  });
}