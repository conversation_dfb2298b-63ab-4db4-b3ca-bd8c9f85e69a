import { supabase } from "@/integrations/supabase/client";
import { TrainingDrill, DrillStep, TrainingElement, DrawingElement } from '@/components/training/InteractiveTrainingBuilder';

// =====================================================
// TIPOS PARA API
// =====================================================

export interface DrillFilters {
  category?: string;
  difficulty?: string;
  searchTerm?: string;
  isTemplate?: boolean;
  limit?: number;
  offset?: number;
}

export interface TrainingSettings {
  field?: {
    showGrid?: boolean;
    gridSize?: number;
    fieldColor?: string;
    backgroundColor?: string;
    showCoordinates?: boolean;
    showMeasurements?: boolean;
    fieldScale?: number;
    showZones?: boolean;
  };
  ui?: {
    theme?: string;
    language?: string;
    animations?: boolean;
    tooltips?: boolean;
    confirmActions?: boolean;
    autoSave?: boolean;
    autoSaveInterval?: number;
    soundEffects?: boolean;
    notifications?: boolean;
  };
  performance?: {
    renderQuality?: string;
    maxElements?: number;
    animationFPS?: number;
    enableGPU?: boolean;
    cacheElements?: boolean;
    optimizeRendering?: boolean;
  };
  controls?: {
    mouseWheelZoom?: boolean;
    keyboardShortcuts?: boolean;
    touchGestures?: boolean;
    snapToGrid?: boolean;
    snapDistance?: number;
    multiSelect?: boolean;
    dragSensitivity?: number;
  };
  export?: {
    defaultFormat?: string;
    defaultQuality?: string;
    includeWatermark?: boolean;
    includeBranding?: boolean;
    autoBackup?: boolean;
    cloudSync?: boolean;
  };
}

// =====================================================
// FUNÇÕES PARA DRILLS
// =====================================================

export async function createDrill(drill: Omit<TrainingDrill, 'id' | 'createdAt' | 'updatedAt'>, clubId: number, userId: string) {
  try {
    // Criar o drill principal
    const { data: drillData, error: drillError } = await supabase
      .from('training_drills')
      .insert({
        club_id: clubId,
        name: drill.name,
        description: drill.description,
        category: drill.category,
        difficulty: drill.difficulty,
        total_duration: drill.totalDuration,
        players_required: drill.playersRequired,
        equipment_needed: drill.equipmentNeeded,
        objectives: drill.objectives,
        created_by: userId
      })
      .select()
      .single();

    if (drillError) throw drillError;

    // Criar os passos
    for (let i = 0; i < drill.steps.length; i++) {
      const step = drill.steps[i];
      const { data: stepData, error: stepError } = await supabase
        .from('drill_steps')
        .insert({
          drill_id: drillData.id,
          step_order: i + 1,
          name: step.name,
          description: step.description,
          duration: step.duration
        })
        .select()
        .single();

      if (stepError) throw stepError;

      // Criar elementos do passo
      if (step.elements && step.elements.length > 0) {
        const elementsToInsert = step.elements.map(element => ({
          step_id: stepData.id,
          element_type: element.type,
          position_x: element.position.x,
          position_y: element.position.y,
          properties: element.properties
        }));

        const { error: elementsError } = await supabase
          .from('training_elements')
          .insert(elementsToInsert);

        if (elementsError) throw elementsError;
      }

      // Criar desenhos do passo
      if (step.drawings && step.drawings.length > 0) {
        const drawingsToInsert = step.drawings.map(drawing => ({
          step_id: stepData.id,
          drawing_type: drawing.type,
          points: drawing.points,
          properties: drawing.properties
        }));

        const { error: drawingsError } = await supabase
          .from('training_drawings')
          .insert(drawingsToInsert);

        if (drawingsError) throw drawingsError;
      }
    }

    return { success: true, data: drillData };
  } catch (error) {
    console.error('Erro ao criar drill:', error);
    return { success: false, error };
  }
}

export async function updateDrill(drillId: string, drill: TrainingDrill) {
  try {
    const { data, error } = await supabase
      .from('training_drills')
      .update({
        name: drill.name,
        description: drill.description,
        category: drill.category,
        difficulty: drill.difficulty,
        total_duration: drill.totalDuration,
        players_required: drill.playersRequired,
        equipment_needed: drill.equipmentNeeded,
        objectives: drill.objectives
      })
      .eq('id', drillId)
      .select()
      .single();

    if (error) throw error;

    // Remover passos existentes e dados relacionados
    const { data: existingSteps, error: stepsError } = await supabase
      .from('drill_steps')
      .select('id')
      .eq('drill_id', drillId);

    if (stepsError) throw stepsError;

    if (existingSteps && existingSteps.length > 0) {
      const stepIds = existingSteps.map((s: any) => s.id);

      await supabase.from('training_elements').delete().in('step_id', stepIds);
      await supabase.from('training_drawings').delete().in('step_id', stepIds);
      await supabase.from('drill_steps').delete().eq('drill_id', drillId);
    }

    // Inserir passos atualizados
    for (let i = 0; i < drill.steps.length; i++) {
      const step = drill.steps[i];
      const { data: stepData, error: stepError } = await supabase
        .from('drill_steps')
        .insert({
          drill_id: drillId,
          step_order: i + 1,
          name: step.name,
          description: step.description,
          duration: step.duration
        })
        .select()
        .single();

      if (stepError) throw stepError;

      if (step.elements && step.elements.length > 0) {
        const elementsToInsert = step.elements.map(element => ({
          step_id: stepData.id,
          element_type: element.type,
          position_x: element.position.x,
          position_y: element.position.y,
          properties: element.properties
        }));

        const { error: elementsError } = await supabase
          .from('training_elements')
          .insert(elementsToInsert);

        if (elementsError) throw elementsError;
      }

      if (step.drawings && step.drawings.length > 0) {
        const drawingsToInsert = step.drawings.map(drawing => ({
          step_id: stepData.id,
          drawing_type: drawing.type,
          points: drawing.points,
          properties: drawing.properties
        }));

        const { error: drawingsError } = await supabase
          .from('training_drawings')
          .insert(drawingsToInsert);

        if (drawingsError) throw drawingsError;
      }
    }

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao atualizar drill:', error);
    return { success: false, error };
  }
}

export async function deleteDrill(drillId: string) {
  try {
    const { error } = await supabase
      .from('training_drills')
      .delete()
      .eq('id', drillId);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Erro ao deletar drill:', error);
    return { success: false, error };
  }
}

export async function getDrill(drillId: string): Promise<TrainingDrill | null> {
  try {
    // Buscar drill principal
    const { data: drillData, error: drillError } = await supabase
      .from('training_drills')
      .select('*')
      .eq('id', drillId)
      .single();

    if (drillError) throw drillError;

    // Buscar passos
    const { data: stepsData, error: stepsError } = await supabase
      .from('drill_steps')
      .select('*')
      .eq('drill_id', drillId)
      .order('step_order');

    if (stepsError) throw stepsError;

    // Buscar elementos e desenhos para cada passo
    const steps: DrillStep[] = [];
    for (const stepData of stepsData) {
      // Buscar elementos
      const { data: elementsData, error: elementsError } = await supabase
        .from('training_elements')
        .select('*')
        .eq('step_id', stepData.id);

      if (elementsError) throw elementsError;

      // Buscar desenhos
      const { data: drawingsData, error: drawingsError } = await supabase
        .from('training_drawings')
        .select('*')
        .eq('step_id', stepData.id);

      if (drawingsError) throw drawingsError;

      const elements: TrainingElement[] = elementsData.map(el => ({
        id: el.id,
        type: el.element_type,
        position: { x: el.position_x, y: el.position_y },
        properties: el.properties
      }));

      const drawings: DrawingElement[] = drawingsData.map(dr => ({
        id: dr.id,
        type: dr.drawing_type,
        points: dr.points,
        properties: dr.properties
      }));

      steps.push({
        id: stepData.id,
        name: stepData.name,
        description: stepData.description,
        duration: stepData.duration,
        elements,
        annotations: [], // TODO: implementar anotações se necessário
        drawings
      });
    }

    const drill: TrainingDrill = {
      id: drillData.id,
      name: drillData.name,
      description: drillData.description,
      category: drillData.category,
      difficulty: drillData.difficulty,
      steps,
      totalDuration: drillData.total_duration,
      playersRequired: drillData.players_required,
      equipmentNeeded: drillData.equipment_needed || [],
      objectives: drillData.objectives || [],
      createdAt: new Date(drillData.created_at),
      updatedAt: new Date(drillData.updated_at)
    };

    return drill;
  } catch (error) {
    console.error('Erro ao buscar drill:', error);
    return null;
  }
}

export async function searchDrills(clubId: number, filters: DrillFilters = {}) {
  try {
    let query = supabase
    .from('training_drills')
    .select(
      `id, name, description, category, difficulty, total_duration, players_required, is_template, created_at`
    )
    .order('created_at', { ascending: false });

  // club specific or public
  query = query.or(`club_id.eq.${clubId},is_public.eq.true`);

  if (filters.category) {
    query = query.eq('category', filters.category);
  }
  if (filters.difficulty) {
    query = query.eq('difficulty', filters.difficulty);
  }
  if (filters.isTemplate !== undefined && filters.isTemplate !== null) {
    query = query.eq('is_template', filters.isTemplate);
  }
  if (filters.searchTerm) {
    query = query.or(
      `name.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`
    );
  }

  const limit = filters.limit || 20;
  const offset = filters.offset || 0;
  query = query.range(offset, offset + limit - 1);

  const { data, error } = await query;

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao buscar drills:', error);
    return { success: false, error, data: [] };
  }
}

// =====================================================
// FUNÇÕES PARA CONFIGURAÇÕES
// =====================================================

export async function saveUserSettings(userId: string, clubId: number, category: string, settings: any) {
  try {
    const { data, error } = await supabase
      .from('training_settings')
      .upsert({
        user_id: userId,
        club_id: clubId,
        settings_category: category,
        settings_data: settings
      })
      .select()
      .single();

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao salvar configurações:', error);
    return { success: false, error };
  }
}

export async function getUserSettings(userId: string, clubId: number): Promise<TrainingSettings> {
  try {
    const { data, error } = await supabase
      .from('training_settings')
      .select('*')
      .eq('user_id', userId)
      .eq('club_id', clubId);

    if (error) throw error;

    const settings: TrainingSettings = {};
    data.forEach(setting => {
      settings[setting.settings_category as keyof TrainingSettings] = setting.settings_data;
    });

    return settings;
  } catch (error) {
    console.error('Erro ao buscar configurações:', error);
    return {};
  }
}

// =====================================================
// FUNÇÕES PARA FAVORITOS
// =====================================================

export async function addToFavorites(userId: string, drillId: string) {
  try {
    const { data, error } = await supabase
      .from('training_favorites')
      .insert({
        user_id: userId,
        drill_id: drillId
      })
      .select()
      .single();

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao adicionar aos favoritos:', error);
    return { success: false, error };
  }
}

export async function removeFromFavorites(userId: string, drillId: string) {
  try {
    const { error } = await supabase
      .from('training_favorites')
      .delete()
      .eq('user_id', userId)
      .eq('drill_id', drillId);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Erro ao remover dos favoritos:', error);
    return { success: false, error };
  }
}

export async function getUserFavorites(userId: string) {
  try {
    const { data, error } = await supabase
      .from('training_favorites')
      .select(`
        drill_id,
        training_drills (
          id,
          name,
          description,
          category,
          difficulty,
          total_duration,
          players_required,
          created_at
        )
      `)
      .eq('user_id', userId);

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao buscar favoritos:', error);
    return { success: false, error, data: [] };
  }
}

// =====================================================
// FUNÇÕES PARA ANÁLISE TÁTICA
// =====================================================

export async function calculateDrillAnalysis(drillId: string) {
  try {
    const { data, error } = await supabase.rpc('calculate_drill_metrics', {
      drill_uuid: drillId
    });

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao calcular análise:', error);
    return { success: false, error };
  }
}

export async function saveDrillAnalysis(drillId: string, analysisType: string, score: number, metrics: any, recommendations: any[]) {
  try {
    const { data, error } = await supabase
      .from('training_analysis')
      .upsert({
        drill_id: drillId,
        analysis_type: analysisType,
        score,
        metrics,
        recommendations
      })
      .select()
      .single();

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Erro ao salvar análise:', error);
    return { success: false, error };
  }
}
