import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useTheme } from "@/context/ThemeContext";
import { useUser } from "@/context/UserContext";
import { useCurrentClubId } from "@/context/ClubContext";
import { usePermission } from "@/hooks/usePermission";
import { useClubNavigation } from "@/hooks/useClubNavigation";
import { ClubLink } from "@/components/ClubLink";
import { EVALUATION_PERMISSIONS, INVENTORY_PERMISSIONS, OPPONENT_PERMISSIONS, GAME_OPERATION_PERMISSIONS } from "@/constants/permissions";
import { getPlayerByUserId } from "@/api/players";
import {
  ChevronLeft,
  LayoutDashboard,
  Users,
  Calendar,
  BarChart2,
  Heart,
  Settings,
  Trophy,
  GraduationCap,
  DollarSign,
  MessageSquare,
  Shield,
  Award,
  Home,
  Building,
  UserCog,
  FileText,
  ClipboardList,
  Clipboard,
  Package,
  FileEdit,
  Utensils,
  User,
  Briefcase,
  Receipt,
} from "lucide-react";

// Definição dos itens de navegação com suas permissões necessárias
const navigationItems = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard, permission: null }, // Acessível a todos
  { name: "Elenco", href: "/elenco", icon: Users, permission: "players.view" },
  {
    name: "Pré Cadastro",
    href: "/avaliacao",
    icon: ClipboardList,
    permissions: [
      EVALUATION_PERMISSIONS.TABS.PLAYERS,
      EVALUATION_PERMISSIONS.TABS.INVITATIONS,
      EVALUATION_PERMISSIONS.TABS.NEW_INVITATION,
      EVALUATION_PERMISSIONS.TABS.DASHBOARD,
    ],
  },
  { name: "Gerenciar Fichas", href: "/fichas", icon: FileEdit, permission: "players.view" },
  { name: "Categorias", href: "/categorias", icon: GraduationCap, permission: "categories.view" },
  // { name: "Departamentos", href: "/departamentos", icon: Building, permission: "departments.view" },
  { name: "Usuários", href: "/usuarios", icon: UserCog, permission: "users.view" },
  { name: "Escalação", href: "/escalacao", icon: Users, permission: "matches.lineup" },
  { name: "Mapeamento", href: "/mapeamento", icon: Users, permission: "mapping.view", customCheck: true },
  { name: "Treinamentos", href: "/treinamentos", icon: BarChart2, permission: "trainings.view" },
  { name: "Partidas", href: "/partidas", icon: Trophy, permission: "matches.view" },
  { name: "Jogos Passados", href: "/jogos-passados", icon: Trophy, permission: "matches.view" },
  { name: "Adversários", href: "/adversarios", icon: Shield, permission: OPPONENT_PERMISSIONS.VIEW },
  { name: "Competições", href: "/competicoes", icon: Award, permission: "matches.view" },
  { name: "Convocação", href: "/convocacao", icon: Clipboard, permission: "callups.view" },
  { name: "Operação de Jogo", href: "/operacao", icon: Briefcase, permission: GAME_OPERATION_PERMISSIONS.VIEW },
  { name: "Alimentação", href: "/alimentacao", icon: Utensils, permission: "accommodations.view" },
  { name: "Alojamentos", href: "/alojamentos", icon: Home, permission: "accommodations.view" },
  { name: "Agenda", href: "/agenda", icon: Calendar, permission: "agenda.view" },
  {
    name: "Meu Perfil",
    href: "/meu-perfil",
    icon: User,
    permission: "collaborators.view_own",
    excludeRoles: ["admin", "president"],
  },
  { name: "Médico", href: "/medico", icon: Heart, permission: "medical.view" },
  { name: "Administrativo", href: "/administrativo", icon: ClipboardList, permission: "administrative.view" },
  { name: "Estoque", href: "/estoque", icon: Package, permission: "inventory.view" },
  { name: "Solicitar Itens", href: "/solicitar-estoque", icon: Package, permission: INVENTORY_PERMISSIONS.REQUESTS.CREATE, roles: ['player'] },
  { name: "Financeiro", href: "/financeiro", icon: DollarSign, permission: "finances.view" },
  { name: "Cobranças", href: "/cobrancas", icon: Receipt, permission: "finances.view" },
  { name: "Mensalidades", href: "/mensalidades", icon: Calendar, permission: "finances.view" },
  { name: "Relatórios", href: "/relatorios", icon: FileText, permission: "reports.view" },
  { name: "Configurações", href: "/configuracoes", icon: Settings, permission: "settings.view" },
  // { name: "Logs de Auditoria", href: "/audit-logs", icon: ClipboardList, permission: "audit_logs.view" },
];

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const { logo, theme, clubName } = useTheme();
  const { user, loading } = useUser();
  const clubId = useCurrentClubId().toString();
  const { isLoaded, can, canAny, role } = usePermission();
  const { getClubUrl } = useClubNavigation();
  const [navigation, setNavigation] = useState<typeof navigationItems>([]);
  const [playerProfile, setPlayerProfile] = useState<any>(null);

  // Carregar perfil do jogador se o usuário for um jogador
  useEffect(() => {
    const loadPlayerProfile = async () => {
      if (isLoaded && role === 'player' && user?.id && clubId) {
        try {
          const player = await getPlayerByUserId(parseInt(clubId), user.id);
          setPlayerProfile(player);
        } catch (error) {
          console.error("Erro ao carregar perfil do jogador:", error);
        }
      }
    };

    loadPlayerProfile();
  }, [isLoaded, role, user?.id, clubId]);

  // Filtrar itens de navegação com base nas permissões do usuário
  useEffect(() => {
    // Função para filtrar itens de navegação
    const filterNavItems = () => {
      if (isLoaded) {
        let filteredNavigation = navigationItems.filter(item => {
          // Ocultar para determinados roles, se especificado
          if (item.excludeRoles && item.excludeRoles.includes(role)) {
            return false;
          }
          // Filtrar por role, se especificado
          if (item.roles && !item.roles.includes(role)) {
            return false;
          }
          // Se não requer permissão, mostrar sempre
          if (item.permission === null && !item.permissions) return true;

          try {
            // Verificação customizada para mapeamento
            if (item.customCheck && item.href === "/mapeamento") {
              // Aplicar regras específicas de mapeamento
              switch (role) {
                case "president":
                case "admin":
                  return true;
                case "collaborator":
                case "medical":
                  return can("mapping.view");
                case "player":
                  return true; // Jogadores podem ver mapeamento da sua categoria
                default:
                  return can("mapping.view");
              }
            }

            if (item.permission) {
              return can(item.permission);
            }
            if (item.permissions) {
              return canAny(item.permissions);
            }
          } catch (error) {
            console.warn(
              `Erro ao verificar permissão ${item.permission || item.permissions}:`,
              error
            );
          }
          return false;
        });

        // Se for um jogador e tiver um perfil, adicionar link para o perfil
        if (role === 'player' && playerProfile) {
          filteredNavigation = [
            ...filteredNavigation,
            {
              name: "Meu Perfil",
              href: `/jogador/${playerProfile.id}`,
              icon: Users,
              permission: "players.view_own"
            }
          ];
        }

        setNavigation(filteredNavigation);
      } else {
        // Enquanto as permissões não são carregadas, mostrar apenas o Dashboard
        setNavigation(
          navigationItems.filter(item => item.permission === null && !item.permissions)
        );
      }
    };

    // Executar a filtragem
    filterNavItems();
  }, [isLoaded, playerProfile, role]);

  return (
    <div
      className={cn(
        "h-screen transition-all duration-300 ease-in-out border-r flex flex-col",
        collapsed ? "w-16" : "w-64",
        "bg-white shadow-sm"
      )}
    >
      {/* Logo section */}
      <div className="p-4 flex items-center justify-between h-16 border-b" style={{ background: theme.colors.primary }}>
        {!collapsed && (
          <div className="flex items-center gap-2">
            {logo ? (
              <img
                src={logo}
                alt="Logo do clube"
                className="h-8 w-8 rounded shadow border bg-white"
                style={{ borderColor: theme.colors.secondary }}
              />
            ) : (
              <span
                className="h-7 w-7 rounded bg-white flex items-center justify-center font-bold text-xs shadow border flex-shrink-0"
                style={{ color: theme.colors.primary, borderColor: theme.colors.secondary }}
              >
                FC
              </span>
            )}
            <span className="font-bold text-sm text-white truncate">{clubName}</span>
          </div>
        )}
        {collapsed && (
          <div className="mx-auto h-8 w-8 rounded bg-white flex items-center justify-center font-bold text-sm shadow border"
            style={{ color: theme.colors.primary, borderColor: theme.colors.secondary }}>
            {logo ? (
              <img src={logo} alt="Logo do clube" className="h-8 w-8 rounded" />
            ) : (
              "FC"
            )}
          </div>
        )}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="p-1 rounded-full flex items-center justify-center border border-white/40 bg-white/30 hover:bg-white/60 transition-colors"
          aria-label={collapsed ? "Expandir" : "Recolher"}
          style={{ color: theme.colors.secondary }}
        >
          <ChevronLeft
            className={cn("h-5 w-5 transition-transform",
              collapsed ? "rotate-180" : ""
            )}
          />
        </button>
      </div>

      {/* Navigation section */}
      <nav className="flex-grow py-4 px-2 overflow-y-auto">
        <ul className="space-y-1">
          {navigation.map((item) => {
            const isActive = location.pathname === getClubUrl(item.href);
            return (
              <li key={item.name}>
                <ClubLink
                  to={item.href}
                  className={cn(
                    "flex items-center gap-x-3 px-3 py-2 rounded-md text-sm font-medium transition-all",
                    isActive
                      ? "text-white"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                  style={isActive ? { backgroundColor: theme.colors.primary } : {}}
                  title={item.name}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  {!collapsed && <span>{item.name}</span>}
                </ClubLink>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User profile section */}
      <div className="p-3 border-t">
        {role === 'player' ? (
          // Para jogadores, mostrar informações do clube
          <div className="flex items-center gap-3 p-2 rounded-md">
            {logo ? (
              <img
                src={logo}
                alt="Logo do clube"
                className="h-8 w-8 rounded shadow border bg-white"
                style={{ borderColor: theme.colors.secondary }}
              />
            ) : (
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center font-medium"
                style={{
                  backgroundColor: `${theme.colors.primary}20`,
                  color: theme.colors.primary
                }}
              >
                FC
              </div>
            )}
            {!collapsed && (
              <div className="flex flex-col">
                <span className="text-xs font-medium">{clubName}</span>
                <span className="text-xs text-gray-500">{user ? user.name : "Jogador"}</span>
              </div>
            )}
          </div>
        ) : (
          // Para outros usuários, mostrar link para perfil
          <ClubLink to="/perfil" className="flex items-center gap-3 hover:bg-gray-100 p-2 rounded-md transition-colors">
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center font-medium"
              style={{
                backgroundColor: `${theme.colors.primary}20`,
                color: theme.colors.primary
              }}
            >
              {user && user.name ? user.name.slice(0, 2).toUpperCase() : "US"}
            </div>
            {!collapsed && (
              <div className="flex flex-col">
                <span className="text-xs font-medium">{user ? user.name : "Usuário"}</span>
                <span className="text-xs text-gray-500">{user ? user.email : ""}</span>
              </div>
            )}
          </ClubLink>
        )}
      </div>
    </div>
  );
}