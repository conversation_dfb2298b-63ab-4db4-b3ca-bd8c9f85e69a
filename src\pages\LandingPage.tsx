import { useEffect, useRef, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion } from "framer-motion";
import { Check, TrendingUp, Shield, Calendar, Users, Award, ChevronRight, Sparkles, Zap, Target, BarChart3, Heart, Eye, Play, Pause, ChevronLeft, Star, Globe, Flame, ArrowRight, ArrowUp, ArrowDown, Activity, Trophy, Rocket, Brain, Cpu, Database, Lock, Wifi, CreditCard, Hospital, Building, MessageCircle, FileText, MapPin, Instagram } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Chatbot } from "@/components/landing/Chatbot";

// Registrar plugins do GSAP
gsap.registerPlugin(ScrollTrigger);

// Estilos CSS customizados para as animações avançadas
const stackedCardsStyles = `
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Performance optimization */
  .gpu-accelerated {
    will-change: transform, opacity, filter;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .stacked-cards-container {
      min-height: 500px;
      height: auto;
    }
    
    .stacked-card-mobile {
      position: relative !important;
      transform: none !important;
      opacity: 1 !important;
      filter: none !important;
      margin-bottom: 1rem;
    }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .stacked-card {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .stacked-card:hover {
    transform: translateY(-5px) scale(1.02);
  }

  /* Cursor personalizado */
  .stacked-cards-container {
    cursor: url('/cursors/grab.svg') 12 12, grab;
    touch-action: pan-y pinch-zoom; /* Permite scroll vertical mas previne horizontal */
  }
  
  .stacked-cards-container:active {
    cursor: url('/cursors/grabbing.svg') 12 12, grabbing;
  }

  .stacked-cards-container.dragging {
    cursor: url('/cursors/grabbing.svg') 12 12, grabbing;
    touch-action: none; /* Previne todos os gestos durante o drag */
  }

  /* Gradientes animados com brilho */
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animated-gradient {
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
  }

  /* Padrão hexagonal */
  .hex-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(255,255,255,0.05) 2px, transparent 2px);
    background-size: 100px 100px;
    background-position: 0 0, 50px 50px;
  }

  /* Grid pattern */
  .grid-pattern {
    background-image: 
      linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Neon glow effect */
  @keyframes neonGlow {
    0%, 100% { 
      box-shadow: 
        0 0 20px rgba(255,255,255,0.1),
        0 0 40px rgba(255,255,255,0.1),
        0 0 60px rgba(255,255,255,0.1);
    }
    50% { 
      box-shadow: 
        0 0 30px rgba(255,255,255,0.2),
        0 0 60px rgba(255,255,255,0.2),
        0 0 90px rgba(255,255,255,0.1);
    }
  }

  .neon-glow {
    animation: neonGlow 3s ease-in-out infinite;
  }

  /* Partículas flutuantes */
  @keyframes particleFloat {
    0% { 
      transform: translateY(0px) translateX(0px) scale(1);
      opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { 
      transform: translateY(-100px) translateX(20px) scale(0.5);
      opacity: 0;
    }
  }

  .particle {
    animation: particleFloat 6s ease-in-out infinite;
  }

  /* Ondas de luz no hover */
  @keyframes lightWave {
    0% {
      transform: translateX(-100%) translateY(-100%) rotate(45deg);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(100%) translateY(100%) rotate(45deg);
      opacity: 0;
    }
  }

  .light-wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: lightWave 2s ease-in-out;
    pointer-events: none;
  }

  /* Efeito holográfico */
  @keyframes holographic {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .holographic {
    background: linear-gradient(45deg, 
      transparent 30%, 
      rgba(255,255,255,0.1) 50%, 
      transparent 70%
    );
    background-size: 200% 200%;
    animation: holographic 4s ease-in-out infinite;
  }

  /* Efeito de magnetismo */
  @keyframes magneticPull {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
  }

  .magnetic-hover:hover {
    animation: magneticPull 0.6s ease-in-out;
  }

  /* Parallax mouse effect */
  .parallax-layer {
    transition: transform 0.3s ease-out;
  }

  /* Shimmer effect para loading */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
  }

  /* Breathing effect */
  @keyframes breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  .breathe {
    animation: breathe 4s ease-in-out infinite;
  }

  /* Ripple effect */
  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }

  .ripple-effect {
    position: relative;
    overflow: hidden;
  }

  .ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    transform: translate(-50%, -50%);
    animation: ripple 0.6s linear;
  }

  /* Glow pulse effect */
  @keyframes glowPulse {
    0%, 100% { 
      filter: drop-shadow(0 0 5px rgba(255,255,255,0.3));
    }
    50% { 
      filter: drop-shadow(0 0 20px rgba(255,255,255,0.6));
    }
  }

  .glow-pulse {
    animation: glowPulse 2s ease-in-out infinite;
  }
`;

// Dados do carrossel otimizado para alta conversão
const heroCarouselData = [
  {
    id: 1,
    title: "Economize R$ 50.000/Ano na Gestão do Seu Clube",
    subtitle: "Mais de 500 clubes já eliminaram planilhas e burocracias",
    description: "Reduza 70% dos custos administrativos em 30 dias. Sistema completo que paga por si mesmo no primeiro mês de uso. ROI comprovado de 400%.",
    image: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Rocket,
    color: "from-blue-600 via-purple-600 to-pink-600",
    stats: { economy: "R$ 50K+", clubs: "500+", roi: "400%" },
    features: ["ROI em 30 Dias", "Economia Garantida", "Setup Gratuito", "Suporte 24/7"],
    cta: "ECONOMIZAR R$ 50K AGORA",
    ctaSecondary: "Ver Como Outros Clubes Economizaram"
  },
  {
    id: 2,
    title: "Reduza 35% das Lesões dos Seus Atletas",
    subtitle: "IA que prevê lesões antes que aconteçam",
    description: "Nossa inteligência artificial monitora 24/7 e alerta sobre riscos de lesão. Atletas mais saudáveis = mais vitórias. Economize R$ 30K/ano em tratamentos.",
    image: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Brain,
    color: "from-green-500 via-teal-500 to-cyan-500",
    stats: { reduction: "35%", monitoring: "24/7", savings: "R$ 30K" },
    features: ["IA Preditiva", "Monitoramento 24/7", "Alertas Automáticos", "Economia em Tratamentos"],
    cta: "PROTEGER MEUS ATLETAS AGORA",
    ctaSecondary: "Ver Demonstração da IA"
  },
  {
    id: 3,
    title: "Elimine Todas as Planilhas em 1 Dia",
    subtitle: "Tudo integrado, tudo automático",
    description: "Chega de perder tempo com Excel. Sistema que funciona sozinho e libera sua equipe para focar no que importa: vencer. Ganhe 20h/semana de produtividade.",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Cpu,
    color: "from-orange-500 via-red-500 to-pink-500",
    stats: { time: "20h/sem", automation: "100%", efficiency: "+300%" },
    features: ["Zero Planilhas", "Automação Total", "Integração Completa", "Produtividade +300%"],
    cta: "ELIMINAR PLANILHAS HOJE",
    ctaSecondary: "Ver Sistema Funcionando"
  },
  {
    id: 4,
    title: "Segurança Bancária para Seus Dados",
    subtitle: "Mesma proteção que bancos usam",
    description: "Criptografia militar, backup automático e conformidade LGPD. Seus dados mais seguros que no cofre do banco. Certificação ISO 27001.",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Lock,
    color: "from-indigo-600 via-blue-600 to-purple-600",
    stats: { uptime: "99.99%", security: "Militar", compliance: "100%" },
    features: ["Criptografia Militar", "Backup Automático", "ISO 27001", "LGPD Compliant"],
    cta: "PROTEGER MEUS DADOS AGORA",
    ctaSecondary: "Ver Certificações de Segurança"
  }
];

// Dados dos módulos otimizados para conversão
const modulesData = [
  {
    id: 1,
    title: "Nunca Mais Perca um Atleta por Burocracia",
    description: "Cadastre 100 atletas em 10 minutos. Sistema que elimina 90% da papelada e garante que nenhum talento seja perdido por documentação.",
    icon: Users,
    color: "from-blue-600 to-indigo-700",
    features: ["Cadastro em 10 Minutos", "90% Menos Papelada", "Documentação Automática", "Contratos Inteligentes", "Alertas de Vencimento", "Backup na Nuvem"],
    benefit: "Economize 15 horas/semana de trabalho manual",
    impact: "Cadastre 100 atletas em apenas 10 minutos",
    cta: "CADASTRAR ATLETAS EM 10 MIN"
  },
  {
    id: 2,
    title: "Receba 95% das Mensalidades em Dia",
    description: "Sistema que cobra sozinho via PIX e WhatsApp. Reduza inadimplência de 40% para 5% automaticamente. Aumente sua receita sem esforço.",
    icon: "💳",
    color: "from-emerald-500 to-teal-600",
    features: ["Cobrança Automática PIX", "WhatsApp Integrado", "95% Taxa de Pagamento", "Reduz Inadimplência 90%", "Relatórios em Tempo Real", "Sem Trabalho Manual"],
    benefit: "Aumente sua receita em 60% no primeiro mês",
    impact: "De 40% para 5% de inadimplência automaticamente",
    cta: "RECEBER MAIS MENSALIDADES"
  },
  {
    id: 3,
    title: "Previna Lesões Antes que Aconteçam",
    description: "IA que monitora seus atletas 24/7 e alerta sobre riscos. Reduza lesões em 35% e economize milhares em tratamentos médicos.",
    icon: Shield,
    color: "from-red-500 to-pink-600",
    features: ["IA Monitora 24/7", "Alertas de Risco", "35% Menos Lesões", "Prontuários Digitais", "Economia em Tratamentos", "Relatórios Médicos"],
    benefit: "Economize R$ 30.000/ano em tratamentos médicos",
    impact: "35% menos lesões = atletas sempre prontos",
    cta: "PROTEGER MEUS ATLETAS"
  },
  {
    id: 4,
    title: "Saiba Onde Está Cada Centavo",
    description: "Controle financeiro que mostra lucro real em tempo real. Integração PIX automática e relatórios que seu contador vai amar.",
    icon: TrendingUp,
    color: "from-green-600 to-emerald-700",
    features: ["Lucro em Tempo Real", "PIX Automático", "Relatórios Profissionais", "Controle Total", "Economia Garantida", "Contador Aprovado"],
    benefit: "Economize R$ 50.000/ano com controle preciso",
    impact: "Visão 360° das finanças = decisões certas",
    cta: "VER MINHA SITUAÇÃO FINANCEIRA"
  },
  {
    id: 5,
    title: "Treinos de Nível Profissional em 5 Minutos",
    description: "Editor 3D usado por técnicos da Série A. Crie exercícios profissionais que impressionam atletas e pais. Melhore a performance em 40%.",
    icon: Target,
    color: "from-purple-600 to-violet-700",
    features: ["Editor 3D Profissional", "Usado na Série A", "1000+ Exercícios", "Treinos em 5 Min", "Impressiona Pais", "Performance +40%"],
    benefit: "Melhore performance dos atletas em 40%",
    impact: "Treinos profissionais em apenas 5 minutos",
    cta: "CRIAR TREINOS PROFISSIONAIS"
  },
  {
    id: 6,
    title: "Partidas e Competições",
    description: "Sistema completo de partidas com escalação tática",
    icon: Trophy,
    color: "from-yellow-500 to-yellow-600",
    features: ["Escalação Visual Interativa", "Eventos em Tempo Real", "Controle de Suspensões", "Estatísticas Avançadas", "Histórico Completo", "Análise de Adversários"],
    benefit: "Escalações profissionais com análise tática",
    impact: "30% melhoria no aproveitamento das partidas"
  },
  {
    id: 7,
    title: "Convocações Profissionais",
    description: "Convocações com design profissional e logística completa",
    icon: "📋",
    color: "from-blue-600 to-blue-700",
    features: ["Design Personalizado com Logo", "Geração Automática de PDFs", "Controle de Alojamentos", "Lista de Passageiros", "Integração com Alimentação", "Notificações Automáticas"],
    benefit: "Convocações profissionais em 2 cliques",
    impact: "90% redução no tempo de criação"
  },
  {
    id: 8,
    title: "Estoque Inteligente",
    description: "Gestão inteligente com alertas automáticos",
    icon: Database,
    color: "from-orange-500 to-orange-600",
    features: ["Controle por Departamentos", "Alertas de Estoque Baixo", "Solicitações Digitais", "Controle de Validade", "Relatórios de Consumo", "Assinatura Digital"],
    benefit: "Nunca mais fique sem materiais essenciais",
    impact: "95% redução em faltas de material"
  },
  {
    id: 9,
    title: "Alimentação Controlada",
    description: "Controle nutricional completo para máxima performance",
    icon: "🍽️",
    color: "from-green-400 to-green-500",
    features: ["Planejamento de Cardápios", "Controle de Presença Digital", "Relatórios Nutricionais", "Gestão de Dietas Especiais", "Controle de Custos", "Múltiplos Locais de Refeição"],
    benefit: "Nutrição controlada para máxima performance",
    impact: "25% melhoria na performance física"
  },
  {
    id: 10,
    title: "Alojamentos Organizados",
    description: "Gestão completa de hospedagem e logística",
    icon: "🏨",
    color: "from-cyan-500 to-cyan-600",
    features: ["Controle de Capacidade", "Distribuição Automática", "Gestão de Quartos de Hotel", "Check-in/Check-out Digital", "Relatórios de Ocupação", "Integração com Convocações"],
    benefit: "Logística de hospedagem sem complicações",
    impact: "100% organização em viagens"
  },
  {
    id: 11,
    title: "Usuários e Permissões",
    description: "Controle de acesso profissional e seguro",
    icon: "🔐",
    color: "from-gray-500 to-gray-600",
    features: ["Permissões Granulares", "Perfis Personalizáveis", "Auditoria Completa", "Departamentos Organizados", "Convites Automáticos", "Hierarquia de Acesso"],
    benefit: "Cada pessoa vê apenas o que precisa",
    impact: "100% segurança dos dados"
  },
  {
    id: 12,
    title: "Comunicação Integrada",
    description: "Chat interno e notificações inteligentes",
    icon: "💬",
    color: "from-pink-500 to-pink-600",
    features: ["Chat em Tempo Real", "Notificações Push", "Central de Avisos", "Status Online", "Histórico de Conversas", "Grupos por Departamento"],
    benefit: "Comunicação instantânea em toda organização",
    impact: "80% melhoria na comunicação interna"
  },
  {
    id: 13,
    title: "Relatórios Profissionais",
    description: "Relatórios em PDF com identidade visual do clube",
    icon: BarChart3,
    color: "from-indigo-500 to-indigo-600",
    features: ["Design Personalizado", "Logo do Clube Integrado", "Múltiplos Tipos de Relatório", "Exportação Automática", "Templates Profissionais", "Dados em Tempo Real"],
    benefit: "Relatórios profissionais com sua marca",
    impact: "100% profissionalização da documentação"
  },
  {
    id: 14,
    title: "Avaliação de Atletas",
    description: "Sistema completo de pré-cadastro e avaliação",
    icon: "⭐",
    color: "from-yellow-400 to-yellow-500",
    features: ["Convites Públicos para Avaliação", "Formulários Personalizáveis", "Dashboard de Estatísticas", "Sistema de Pagamento", "Controle de Status", "Relatórios de Aprovação"],
    benefit: "Organize avaliações como um clube profissional",
    impact: "300% melhoria na organização de peneiras"
  },
  {
    id: 15,
    title: "Administrativo Completo",
    description: "RH, documentos e tarefas em um só lugar",
    icon: "🏢",
    color: "from-teal-500 to-teal-600",
    features: ["Gestão de Colaboradores", "Documentos Digitais", "Sistema Kanban de Tarefas", "Controle de Fornecedores", "Assinatura Digital", "Lembretes Automáticos"],
    benefit: "Administração profissional e organizada",
    impact: "60% redução em tarefas administrativas"
  }
];

// Dados dos módulos específicos para o carrossel avançado
const specificModulesData = [
  {
    id: 1,
    title: "Gestão de Atletas",
    subtitle: "Controle completo dos seus atletas",
    description: "Cadastro completo dos atletas com documentação digitalizada, histórico detalhado de cartões amarelos/vermelhos e minutagem em jogos",
    image: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Users,
    color: "from-blue-500 via-cyan-500 to-teal-500",
    features: [
      { title: "Cadastro Completo", description: "Documentação digitalizada de todos os atletas" },
      { title: "Histórico de Cartões", description: "Controle detalhado de cartões amarelos e vermelhos" },
      { title: "Minutagem em Jogos", description: "Registro preciso do tempo de jogo de cada atleta" },
      { title: "Estatísticas Individuais", description: "Estatísticas individuais e coletivas das partidas" }
    ],
    stats: { players: "1000+", matches: "500+", stats: "100%" }
  },
  {
    id: 2,
    title: "Departamento Médico",
    subtitle: "Cuidado total com a saúde dos atletas",
    description: "Controle de lesões, agendamento de consultas e sessões de fisioterapia com relatórios individuais de desempenho",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Shield,
    color: "from-red-500 via-pink-500 to-rose-500",
    features: [
      { title: "Controle de Lesões", description: "Monitoramento completo de lesões e recuperação" },
      { title: "Agendamento de Consultas", description: "Sistema integrado de agendamento médico" },
      { title: "Sessões de Fisioterapia", description: "Controle e acompanhamento de fisioterapia" },
      { title: "Prontuários Digitais", description: "Relatórios individuais de desempenho por atleta" }
    ],
    stats: { consultations: "2000+", recovery: "95%", prevention: "80%" }
  },
  {
    id: 3,
    title: "Gestão Financeira",
    subtitle: "Controle total das finanças do clube",
    description: "Contas a pagar e receber, livro caixa e gestão de fluxo financeiro com controle da folha de pagamento",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: TrendingUp,
    color: "from-green-500 via-emerald-500 to-teal-500",
    features: [
      { title: "Contas a Pagar e Receber", description: "Controle completo de receitas e despesas" },
      { title: "Livro Caixa", description: "Registro detalhado de todas as movimentações" },
      { title: "Gestão de Fluxo Financeiro", description: "Acompanhamento do fluxo de caixa em tempo real" },
      { title: "Folha de Pagamento", description: "Controle da folha de pagamento do clube" }
    ],
    stats: { transactions: "5000+", accuracy: "99.9%", savings: "30%" }
  },
  {
    id: 4,
    title: "Gestão Técnica e Esportiva",
    subtitle: "Planejamento técnico profissional",
    description: "Elabore treinos animados e crie microciclos de treinamento, monte convocações, escalações e planos de jogo",
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Target,
    color: "from-purple-500 via-violet-500 to-indigo-500",
    features: [
      { title: "Treinos Animados", description: "Elaboração de treinos com animações visuais" },
      { title: "Microciclos de Treinamento", description: "Criação de microciclos de treinamento estruturados" },
      { title: "Convocações", description: "Sistema completo de convocações de atletas" },
      { title: "Escalações e Planos de Jogo", description: "Monte escalações e desenvolva planos táticos" }
    ],
    stats: { trainings: "1000+", tactics: "500+", efficiency: "85%" }
  },
  {
    id: 5,
    title: "Gestão de Estoque",
    subtitle: "Controle inteligente do inventário",
    description: "Estoque de alimentos, medicamentos, materiais esportivos, mobília e loja do clube com controle por categoria, quantidade e validade",
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Database,
    color: "from-yellow-500 via-orange-500 to-red-500",
    features: [
      { title: "Controle por Categoria", description: "Organização por categorias: alimentos, medicamentos, materiais" },
      { title: "Controle de Quantidade", description: "Monitoramento preciso de quantidades em estoque" },
      { title: "Controle de Validade", description: "Alertas automáticos para produtos próximos ao vencimento" },
      { title: "Loja do Clube", description: "Gestão completa da loja e produtos do clube" }
    ],
    stats: { items: "3000+", categories: "10+", efficiency: "95%" }
  },
  {
    id: 6,
    title: "Gestão de Viagens",
    subtitle: "Organização completa de viagens",
    description: "Controle suas viagens com organização de alojamento e lista de passageiros para uma logística perfeita",
    image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Award,
    color: "from-orange-500 via-amber-500 to-yellow-500",
    features: [
      { title: "Organização de Alojamento", description: "Gestão completa de hospedagem para viagens" },
      { title: "Lista de Passageiros", description: "Controle detalhado de todos os viajantes" },
      { title: "Controle de Viagens", description: "Planejamento e acompanhamento de todas as viagens" },
      { title: "Logística Completa", description: "Organização total da logística de deslocamento" }
    ],
    stats: { trips: "200+", passengers: "5000+", satisfaction: "98%" }
  }
];

// Componente de Partículas Flutuantes
function FloatingParticles({ count = 15 }: { count?: number }) {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(count)].map((_, i) => (
        <div
          key={i}
          className="particle absolute w-1 h-1 bg-white rounded-full opacity-60"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 6}s`,
            animationDuration: `${4 + Math.random() * 4}s`
          }}
        />
      ))}
    </div>
  );
}

// Componente de Ondas de Luz
function LightWave({ trigger }: { trigger: boolean }) {
  return trigger ? <div className="light-wave" /> : null;
}

// Componente de Loading Skeleton
function LoadingSkeleton() {
  return (
    <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse rounded-3xl">
      <div className="p-8 md:p-12 h-full flex flex-col justify-between">
        <div className="space-y-6">
          <div className="w-20 h-20 bg-gray-300 rounded-3xl animate-pulse" />
          <div className="space-y-3">
            <div className="h-8 bg-gray-300 rounded animate-pulse" />
            <div className="h-6 bg-gray-300 rounded animate-pulse w-3/4" />
          </div>
          <div className="space-y-2">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-300 rounded animate-pulse" style={{ width: `${60 + Math.random() * 30}%` }} />
            ))}
          </div>
        </div>
        <div className="h-12 bg-gray-300 rounded-xl animate-pulse" />
      </div>
    </div>
  );
}

// Função para obter gradientes específicos por módulo
const getModuleGradient = (moduleColor: string) => {
  const gradients = {
    'blue': 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
    'emerald': 'linear-gradient(135deg, #11998e 0%, #38ef7d 50%, #06ffa5 100%)',
    'red': 'linear-gradient(135deg, #ff416c 0%, #ff4b2b 50%, #ff6b6b 100%)',
    'green': 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 50%, #88d8a3 100%)',
    'purple': 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
    'yellow': 'linear-gradient(135deg, #f7971e 0%, #ffd200 50%, #ffb347 100%)',
    'orange': 'linear-gradient(135deg, #ff8a00 0%, #e52e71 50%, #ff6b6b 100%)',
    'teal': 'linear-gradient(135deg, #00b4db 0%, #0083b0 50%, #00d2ff 100%)',
    'pink': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    'indigo': 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
    'gray': 'linear-gradient(135deg, #bdc3c7 0%, #2c3e50 50%, #4b6cb7 100%)',
    'cyan': 'linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #74b9ff 100%)'
  };

  const colorKey = Object.keys(gradients).find(key => moduleColor.includes(key));
  return gradients[colorKey as keyof typeof gradients] || gradients.blue;
};

// Componente de Background Avançado com Parallax
function AdvancedBackground({
  moduleColor,
  isActive,
  isHovered
}: {
  moduleColor: string;
  isActive: boolean;
  isHovered: boolean;
}) {
  const [showLightWave, setShowLightWave] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });

  useEffect(() => {
    if (isHovered && isActive) {
      setShowLightWave(true);
      const timer = setTimeout(() => setShowLightWave(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isHovered, isActive]);

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMousePosition({
      x: (e.clientX - rect.left) / rect.width,
      y: (e.clientY - rect.top) / rect.height
    });
  };

  return (
    <div
      className="absolute inset-0 overflow-hidden"
      onMouseMove={handleMouseMove}
    >
      {/* Base: Gradiente animado com brilho - sempre visível */}
      <div
        className="absolute inset-0 animated-gradient"
        style={{
          background: getModuleGradient(moduleColor),
          backgroundSize: '400% 400%'
        }}
      />

      {/* Camada parallax 1 */}
      <div
        className="absolute inset-0 parallax-layer opacity-30"
        style={{
          transform: `translate(${mousePosition.x * 10}px, ${mousePosition.y * 10}px)`,
          background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, rgba(255, 255, 255, 0.3) 0%, transparent 50%)`
        }}
      />

      {/* Camada parallax 2 */}
      <div
        className="absolute inset-0 parallax-layer opacity-20"
        style={{
          transform: `translate(${mousePosition.x * -5}px, ${mousePosition.y * -5}px)`,
          background: `radial-gradient(circle at ${(1 - mousePosition.x) * 100}% ${(1 - mousePosition.y) * 100}%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)`
        }}
      />

      {/* Overlay: Padrão geométrico sutil */}
      <div className="absolute inset-0 hex-pattern opacity-30" />
      <div className="absolute inset-0 grid-pattern opacity-20" />

      {/* Efeito holográfico */}
      <div className="absolute inset-0 holographic" />

      {/* Partículas flutuantes */}
      <FloatingParticles count={isActive ? 25 : 12} />

      {/* Ondas de luz no hover */}
      <LightWave trigger={showLightWave} />

      {/* Brilho adicional para card ativo */}
      {isActive && (
        <>
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5" />
          <div className="absolute inset-0 glow-pulse" />
        </>
      )}

      {/* Efeito de respiração para card ativo */}
      {isActive && (
        <div className="absolute inset-0 breathe opacity-20">
          <div className="w-full h-full bg-gradient-to-br from-white/20 via-transparent to-white/10 rounded-3xl" />
        </div>
      )}
    </div>
  );
}

// Hook para efeitos sonoros e hápticos
const useSound = () => {
  const playTransitionSound = () => {
    try {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.volume = 0.1;
      audio.play().catch(() => { }); // Silencioso se falhar
    } catch (error) {
      // Silencioso se não conseguir tocar
    }
  };

  const triggerHapticFeedback = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    try {
      if ('vibrate' in navigator) {
        const patterns = {
          light: [10],
          medium: [20],
          heavy: [30]
        };
        navigator.vibrate(patterns[type]);
      }
    } catch (error) {
      // Silencioso se não conseguir vibrar
    }
  };

  return { playTransitionSound, triggerHapticFeedback };
};



// Componente de Stacked Cards para Módulos
function StackedModulesCards() {
  const [currentCard, setCurrentCard] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { playTransitionSound, triggerHapticFeedback } = useSound();

  // Gerenciar touch-action dinamicamente
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    if (isDragging) {
      container.style.touchAction = 'none';
      // Prevenir scroll da página durante o drag
      document.body.style.overflow = 'hidden';
    } else {
      container.style.touchAction = 'pan-y pinch-zoom';
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isDragging]);

  // Função para renderizar ícone (string emoji ou componente Lucide)
  const renderIcon = (icon: any) => {
    if (typeof icon === 'string') {
      return <span className="text-4xl md:text-5xl">{icon}</span>;
    } else {
      const IconComponent = icon;
      return <IconComponent className="w-10 h-10 md:w-12 md:h-12 text-white" />;
    }
  };

  // Navegação para próximo card
  const nextCard = () => {
    if (isTransitioning) return;
    playTransitionSound();
    triggerHapticFeedback('light');
    setCurrentCard((prev) => (prev + 1) % modulesData.length);
  };

  // Navegação para card anterior
  const prevCard = () => {
    if (isTransitioning) return;
    playTransitionSound();
    triggerHapticFeedback('light');
    setCurrentCard((prev) => (prev - 1 + modulesData.length) % modulesData.length);
  };

  // Ir para card específico
  const goToCard = (index: number) => {
    if (index === currentCard || isTransitioning) return;
    playTransitionSound();
    triggerHapticFeedback('medium');
    setCurrentCard(index);
  };

  // Handlers para drag
  const handleMouseDown = (e: React.MouseEvent) => {
    // Só ativar drag com botão esquerdo do mouse
    if (e.button !== 0) return;

    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });

    // Prevenir seleção de texto durante o drag
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    // Só atualizar se estiver arrastando horizontalmente
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      e.preventDefault();
      setDragOffset({ x: deltaX, y: deltaY });
    }
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (!isDragging) return;

    e.preventDefault();
    setIsDragging(false);

    // Se arrastou mais de 100px para a esquerda, vai para próximo
    if (dragOffset.x < -100) {
      nextCard();
    }
    // Se arrastou mais de 100px para a direita, vai para anterior
    else if (dragOffset.x > 100) {
      prevCard();
    }

    setDragOffset({ x: 0, y: 0 });
  };

  // Touch handlers para mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    // Só permitir um toque
    if (e.touches.length !== 1) return;

    const touch = e.touches[0];
    setIsDragging(true);
    setDragStart({ x: touch.clientX, y: touch.clientY });

    // Não prevenir o comportamento padrão aqui - deixar para o touchmove decidir
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || e.touches.length !== 1) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - dragStart.x;
    const deltaY = touch.clientY - dragStart.y;

    // Determinar se é um movimento horizontal (drag do card) ou vertical (scroll da página)
    const isHorizontalDrag = Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10;

    if (isHorizontalDrag) {
      // É um drag horizontal - prevenir scroll da página
      e.preventDefault();
      e.stopPropagation();
      setDragOffset({ x: deltaX, y: deltaY });
    } else if (Math.abs(deltaY) > 10) {
      // É um scroll vertical - cancelar o drag
      setIsDragging(false);
      setDragOffset({ x: 0, y: 0 });
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging) return;

    e.preventDefault();
    e.stopPropagation();

    setIsDragging(false);

    if (dragOffset.x < -50) {
      nextCard();
    } else if (dragOffset.x > 50) {
      prevCard();
    }

    setDragOffset({ x: 0, y: 0 });
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: stackedCardsStyles }} />
      <div className="relative w-full max-w-6xl mx-auto px-2 sm:px-4">
        {/* Container dos Cards Empilhados */}
        <div
          ref={containerRef}
          className={`stacked-cards-container relative h-[500px] sm:h-[600px] md:h-[700px] perspective-1000 ${isDragging ? 'dragging' : ''}`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {modulesData.map((module, index) => {
            const isActive = index === currentCard;
            const isNext = index === (currentCard + 1) % modulesData.length;
            const isPrev = index === (currentCard - 1 + modulesData.length) % modulesData.length;
            const isVisible = isActive || isNext || isPrev || index === (currentCard + 2) % modulesData.length;

            let zIndex = modulesData.length - Math.abs(index - currentCard);
            let scale = 1;
            let translateY = 0;
            let translateX = 0;
            let rotateY = 0;
            let opacity = 1;
            let blur = 0;

            if (isActive) {
              scale = 1;
              translateY = 0;
              translateX = isDragging ? dragOffset.x * 0.5 : 0;
              rotateY = isDragging ? dragOffset.x * 0.1 : 0;
              opacity = 1;
              blur = 0;
              zIndex = 100;
            } else if (isNext) {
              scale = 0.95;
              translateY = 20;
              translateX = 30;
              rotateY = -5;
              opacity = 0.8;
              blur = 1;
              zIndex = 90;
            } else if (isPrev) {
              scale = 0.95;
              translateY = 20;
              translateX = -30;
              rotateY = 5;
              opacity = 0.8;
              blur = 1;
              zIndex = 90;
            } else if (index === (currentCard + 2) % modulesData.length) {
              scale = 0.9;
              translateY = 40;
              translateX = 60;
              rotateY = -10;
              opacity = 0.6;
              blur = 2;
              zIndex = 80;
            } else {
              scale = 0.85;
              translateY = 60;
              opacity = 0.3;
              blur = 3;
              zIndex = 70;
            }

            return (
              <motion.div
                key={module.id}
                className="absolute inset-0 w-full"
                style={{
                  zIndex,
                  display: isVisible ? 'block' : 'none'
                }}
                animate={{
                  scale,
                  y: translateY,
                  x: translateX,
                  rotateY,
                  opacity,
                  filter: `blur(${blur}px)`
                }}
                transition={{
                  duration: 0.6,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
                onClick={() => !isActive && goToCard(index)}
              >
                <Card
                  className={`h-full border-0 shadow-2xl overflow-hidden cursor-pointer gpu-accelerated relative magnetic-hover ${isActive ? 'neon-glow' : ''}`}
                  onMouseEnter={() => setHoveredCard(index)}
                  onMouseLeave={() => setHoveredCard(null)}
                >
                  {/* Background Avançado */}
                  <AdvancedBackground
                    moduleColor={module.color}
                    isActive={isActive}
                    isHovered={hoveredCard === index}
                  />

                  <CardContent className="p-4 sm:p-6 md:p-8 lg:p-12 h-full flex flex-col justify-between relative z-20">
                    {/* Overlay de brilho adicional no hover */}
                    {hoveredCard === index && (
                      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5 pointer-events-none" />
                    )}

                    {/* Conteúdo do Card */}
                    <div className="relative z-30 text-white">
                      {/* Ícone com efeito avançado */}
                      <div className="mb-4 sm:mb-6">
                        <div className={`w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-2xl sm:rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center shadow-xl relative overflow-hidden ${isActive ? 'breathe glow-pulse' : 'magnetic-hover'}`}>
                          {/* Brilho interno do ícone */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent rounded-3xl" />

                          {/* Efeito de ondas concêntricas para card ativo */}
                          {isActive && (
                            <>
                              <div className="absolute inset-0 rounded-3xl border-2 border-white/30 animate-ping" />
                              <div className="absolute inset-0 rounded-3xl border border-white/20 animate-pulse" style={{ animationDelay: '0.5s' }} />
                            </>
                          )}

                          <div className="relative z-10">
                            {renderIcon(module.icon)}
                          </div>

                          {/* Partículas orbitando ao redor do ícone para card ativo */}
                          {isActive && (
                            <div className="absolute inset-0">
                              {[...Array(8)].map((_, i) => (
                                <div
                                  key={i}
                                  className="absolute w-1.5 h-1.5 bg-white rounded-full opacity-80"
                                  style={{
                                    left: `${50 + Math.cos(i * 45 * Math.PI / 180) * 45}%`,
                                    top: `${50 + Math.sin(i * 45 * Math.PI / 180) * 45}%`,
                                    animation: `particleFloat ${3 + i * 0.2}s ease-in-out infinite`,
                                    animationDelay: `${i * 0.3}s`
                                  }}
                                />
                              ))}
                            </div>
                          )}

                          {/* Efeito de brilho rotativo */}
                          {hoveredCard === index && (
                            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/20 to-transparent animate-spin" style={{ animationDuration: '3s' }} />
                          )}
                        </div>
                      </div>

                      {/* Título e Descrição */}
                      <div className="mb-4 sm:mb-6 md:mb-8">
                        <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-2 sm:mb-3 md:mb-4 leading-tight">
                          {module.title}
                        </h3>
                        <p className="text-sm sm:text-base md:text-lg lg:text-xl text-white/90 leading-relaxed mb-3 sm:mb-4 md:mb-6">
                          {module.description}
                        </p>

                        {/* Benefício Principal */}
                        {module.benefit && (
                          <div className="mb-3 sm:mb-4 md:mb-6">
                            <Badge className={`bg-gradient-to-r from-white/30 to-white/20 text-white border border-white/40 px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2 text-xs sm:text-sm backdrop-blur-sm shadow-lg magnetic-hover ${isActive ? 'glow-pulse' : ''}`}>
                              <ArrowUp className={`w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 ${isActive ? 'animate-bounce' : ''}`} />
                              {module.benefit}
                            </Badge>
                          </div>
                        )}
                      </div>

                      {/* Features Principais */}
                      <div className="mb-4 sm:mb-6 md:mb-8">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-3">
                          {module.features.slice(0, 4).map((feature, featureIndex) => (
                            <motion.div
                              key={featureIndex}
                              className="flex items-center text-white/90 group magnetic-hover"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.3, delay: featureIndex * 0.1 }}
                            >
                              <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-white/80 to-white/60 mr-2 sm:mr-3 flex-shrink-0 shadow-sm ${isActive ? 'glow-pulse' : 'group-hover:animate-pulse'}`}></div>
                              <span className="text-xs sm:text-sm md:text-base leading-tight group-hover:text-white transition-colors duration-300">{feature}</span>
                            </motion.div>
                          ))}
                        </div>
                        {module.features.length > 4 && (
                          <div className="mt-2 sm:mt-3 text-white/70 text-xs sm:text-sm bg-white/10 rounded-lg px-2 py-1 sm:px-3 sm:py-1 backdrop-blur-sm magnetic-hover">
                            +{module.features.length - 4} funcionalidades adicionais
                          </div>
                        )}
                      </div>

                      {/* Métrica de Impacto */}
                      {module.impact && (
                        <div className="mb-3 sm:mb-4 md:mb-6">
                          <div className={`bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-sm rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 border border-white/20 shadow-lg relative overflow-hidden magnetic-hover ${isActive ? 'breathe' : ''}`}>
                            {/* Brilho sutil no fundo */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent" />

                            {/* Efeito de shimmer para card ativo */}
                            {isActive && (
                              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
                            )}

                            <div className="relative z-10">
                              <div className="text-xs sm:text-sm text-white/80 mb-1 flex items-center">
                                <Trophy className={`w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-yellow-300 ${isActive ? 'glow-pulse' : ''}`} />
                                Resultado Comprovado:
                              </div>
                              <div className="text-sm sm:text-base md:text-lg font-semibold text-white">{module.impact}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* CTA */}
                    <div className="relative z-30 mt-auto">
                      <a href={`https://wa.me/5519987111198?text=Olá! Quero ${module.cta || 'conhecer mais sobre este módulo'} do Game Day Nexus. Quando posso começar?`} target="_blank" rel="noopener noreferrer">
                        <Button className={`w-full bg-gradient-to-r from-white/30 to-white/20 hover:from-white/40 hover:to-white/30 backdrop-blur-sm text-white border border-white/40 font-bold py-2 sm:py-2.5 md:py-3 px-3 sm:px-4 md:px-6 rounded-lg sm:rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl relative overflow-hidden group magnetic-hover ${isActive ? 'breathe' : ''}`}>
                          {/* Efeito de brilho no hover */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />

                          {/* Efeito de pulso para card ativo */}
                          {isActive && (
                            <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/20 to-white/10 animate-pulse rounded-lg sm:rounded-xl" />
                          )}

                          <div className="relative z-10 flex items-center justify-center">
                            <Rocket className={`w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mr-1 sm:mr-2 ${isActive ? 'glow-pulse' : 'group-hover:animate-pulse'}`} />
                            <span className="text-xs sm:text-sm md:text-base">{module.cta || "QUERO ESTE RESULTADO"}</span>
                            <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                          </div>
                        </Button>
                      </a>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Controles de Navegação */}
        <div className="flex items-center justify-center mt-4 sm:mt-6 md:mt-8 space-x-3 sm:space-x-4 md:space-x-6">
          {/* Botão Anterior */}
          <Button
            variant="outline"
            size="icon"
            onClick={prevCard}
            className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-white/10 backdrop-blur-sm border-white/20 text-gray-700 hover:bg-white/20 transition-all duration-300 magnetic-hover glow-pulse"
          >
            <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
          </Button>

          {/* Indicadores */}
          <div className="flex space-x-1.5 sm:space-x-2">
            {modulesData.map((_, index) => (
              <button
                key={index}
                onClick={() => goToCard(index)}
                className={`w-2 h-2 sm:w-2.5 sm:h-2.5 md:w-3 md:h-3 rounded-full transition-all duration-300 magnetic-hover ${index === currentCard
                    ? 'bg-blue-600 scale-125 shadow-lg glow-pulse'
                    : 'bg-gray-300 hover:bg-gray-400'
                  }`}
              />
            ))}
          </div>

          {/* Botão Próximo */}
          <Button
            variant="outline"
            size="icon"
            onClick={nextCard}
            className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-white/10 backdrop-blur-sm border-white/20 text-gray-700 hover:bg-white/20 transition-all duration-300 magnetic-hover glow-pulse"
          >
            <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
          </Button>
        </div>

        {/* Contador e Progresso */}
        <div className="text-center mt-3 sm:mt-4 space-y-2 sm:space-y-3">
          <div className="flex items-center justify-center space-x-2 sm:space-x-4">
            <span className="text-gray-600 text-xs sm:text-sm font-medium">
              {currentCard + 1} de {modulesData.length} módulos
            </span>
            <div className="flex items-center space-x-1 sm:space-x-2">
              <div className="w-12 sm:w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500 glow-pulse"
                  style={{ width: `${((currentCard + 1) / modulesData.length) * 100}%` }}
                />
              </div>
              <span className="text-xs text-gray-500">
                {Math.round(((currentCard + 1) / modulesData.length) * 100)}%
              </span>
            </div>
          </div>
        </div>

        {/* Instruções de Uso */}
        <div className="text-center mt-4 sm:mt-6">
          <p className="text-gray-500 text-xs sm:text-sm flex items-center justify-center space-x-2">
            <span className="hidden md:inline flex items-center">
              <span className="w-4 h-4 mr-1">🖱️</span>
              Arraste os cards ou use as setas para navegar
            </span>
            <span className="md:hidden flex items-center">
              <span className="w-4 h-4 mr-1">👆</span>
              Deslize para navegar entre os módulos
            </span>
          </p>
        </div>
      </div>
    </>
  );
}

export default function LandingPage() {
  const headerRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const pricingRef = useRef<HTMLDivElement>(null);

  // Estados para o carrossel hero
  const [currentHeroSlide, setCurrentHeroSlide] = useState(0);
  const [isHeroPlaying, setIsHeroPlaying] = useState(true);
  const [isHeroHovered, setIsHeroHovered] = useState(false);
  const heroIntervalRef = useRef<any>(null);

  // Estados para o carrossel de módulos específicos
  const [currentModuleSlide, setCurrentModuleSlide] = useState(0);
  const [isModulePlaying, setIsModulePlaying] = useState(true);
  const [isModuleHovered, setIsModuleHovered] = useState(false);
  const [moduleTransitioning, setModuleTransitioning] = useState(false);
  const moduleIntervalRef = useRef<any>(null);

  // Estado para o header transparente
  const [isScrolled, setIsScrolled] = useState(false);

  // Estado para a sidebar mobile
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Controle do carrossel hero
  useEffect(() => {
    if (isHeroPlaying && !isHeroHovered) {
      heroIntervalRef.current = setInterval(() => {
        setCurrentHeroSlide((prev) => (prev + 1) % heroCarouselData.length);
      }, 6000);
    } else {
      if (heroIntervalRef.current) {
        clearInterval(heroIntervalRef.current);
      }
    }

    return () => {
      if (heroIntervalRef.current) {
        clearInterval(heroIntervalRef.current);
      }
    };
  }, [isHeroPlaying, isHeroHovered]);

  // Funções do carrossel hero
  const nextHeroSlide = () => {
    setCurrentHeroSlide((prev) => (prev + 1) % heroCarouselData.length);
  };

  const prevHeroSlide = () => {
    setCurrentHeroSlide((prev) => (prev - 1 + heroCarouselData.length) % heroCarouselData.length);
  };

  const goToHeroSlide = (index: number) => {
    setCurrentHeroSlide(index);
  };

  // Controle do carrossel de módulos específicos
  useEffect(() => {
    if (isModulePlaying && !isModuleHovered && !moduleTransitioning) {
      moduleIntervalRef.current = setInterval(() => {
        setModuleTransitioning(true);
        setTimeout(() => {
          setCurrentModuleSlide((prev) => (prev + 1) % specificModulesData.length);
          setModuleTransitioning(false);
        }, 300);
      }, 7000);
    } else {
      if (moduleIntervalRef.current) {
        clearInterval(moduleIntervalRef.current);
      }
    }

    return () => {
      if (moduleIntervalRef.current) {
        clearInterval(moduleIntervalRef.current);
      }
    };
  }, [isModulePlaying, isModuleHovered, moduleTransitioning]);

  // Funções do carrossel de módulos específicos
  const nextModuleSlide = () => {
    if (moduleTransitioning) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide((prev) => (prev + 1) % specificModulesData.length);
      setModuleTransitioning(false);
    }, 300);
  };

  const prevModuleSlide = () => {
    if (moduleTransitioning) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide((prev) => (prev - 1 + specificModulesData.length) % specificModulesData.length);
      setModuleTransitioning(false);
    }, 300);
  };

  const goToModuleSlide = (index: number) => {
    if (moduleTransitioning || index === currentModuleSlide) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide(index);
      setModuleTransitioning(false);
    }, 300);
  };

  // Detectar scroll para header transparente
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
      // Fechar sidebar ao rolar
      if (isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobileMenuOpen]);

  // Fechar sidebar com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileMenuOpen]);

  // Prevenir scroll do body quando sidebar estiver aberta
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  useEffect(() => {
    // Limpar triggers existentes para evitar conflitos
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());

    // Animação apenas do header - remover outras para evitar conflitos com Framer Motion
    const headerTl = gsap.timeline();
    headerTl.from(".hero-title", {
      y: 50,
      opacity: 0,
      duration: 1,
      ease: "power3.out",
    });
    headerTl.from(".hero-subtitle", {
      y: 30,
      opacity: 0,
      duration: 0.8,
      ease: "power3.out",
    }, "-=0.6");
    headerTl.from(".hero-cta", {
      y: 20,
      opacity: 0,
      duration: 0.6,
      ease: "power3.out",
    }, "-=0.4");

    return () => {
      // Limpar instâncias do ScrollTrigger
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Removido fadeInUpVariants para evitar conflitos - usando animações inline

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-primary/5 to-white">
      {/* Header/Navigation - Responsivo Melhorado */}
      <motion.header
        className={`fixed top-0 left-0 right-0 z-50 px-4 py-1 sm:px-6 sm:py-2 md:px-12 lg:px-20 transition-all duration-500 ease-out ${isScrolled
          ? 'bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5'
          : 'bg-transparent border-b border-transparent'
          }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          {/* Logo - Responsivo */}
          <motion.div
            className="flex items-center gap-2 sm:gap-3"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <img src={isScrolled ? '/logo-branca.png' : '/logo-branca.png'} alt="Game Day Nexus Logo" className="h-10 sm:h-16 w-auto transition-all duration-300" />
            <div className="flex flex-col">
              <span className={`text-lg sm:text-xl font-bold transition-colors duration-300 ${isScrolled ? 'text-gray-900' : 'text-white'
                }`}>
                Game Day Nexus
              </span>
              <span className={`text-xs font-medium transition-colors duration-300 hidden sm:block ${isScrolled ? 'text-blue-600' : 'text-blue-200'
                }`}>
                Gestão Esportiva Inteligente
              </span>
            </div>
          </motion.div>

          {/* Navigation - Desktop Only */}
          <nav className="hidden lg:flex items-center gap-8">
            {[
              { href: "#features", label: "Recursos" },
              { href: "#testimonials", label: "Depoimentos" },
              { href: "#pricing", label: "Preços" },
              { href: "/blog", label: "Blog", isExternal: true },
              { href: "/ajuda", label: "Ajuda", isExternal: true, icon: "❓" }
            ].map((item, index) => (
              <motion.a
                key={item.href}
                href={item.href}
                className={`font-medium transition-all duration-300 hover:scale-105 ${isScrolled
                  ? 'text-gray-600 hover:text-blue-600'
                  : 'text-white/80 hover:text-white'
                  }`}
                whileHover={{ y: -2 }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                {item.label}
              </motion.a>
            ))}
          </nav>

          {/* Action Buttons - Responsivo */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Botão Entrar - Oculto em mobile muito pequeno */}
            <Link to="/login" className="block">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="sm"
                  className={`font-medium transition-all duration-300 rounded-lg ${isScrolled
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md hover:from-blue-700 hover:to-purple-700'
                    : 'bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm border border-white/20'
                    }`}
                >
                  Entrar
                </Button>
              </motion.div>
            </Link>

            <a href="https://wa.me/5519987111198?text=Olá! Gostaria de conhecer mais sobre o Game Day Nexus e começar gratuitamente." target="_blank" rel="noopener noreferrer" className="hidden sm:block">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <Button
                  size="sm"
                  className={`font-semibold px-3 py-2 sm:px-6 sm:py-2.5 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl text-xs sm:text-sm ${isScrolled
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                    : 'bg-white text-blue-600 hover:bg-gray-50'
                    }`}>
                  <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Começar Grátis</span>
                  <span className="sm:hidden">Grátis</span>
                  <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2" />
                </Button>
              </motion.div>
            </a>

            {/* Mobile Menu Button */}
            <motion.button
              onClick={() => setIsMobileMenuOpen(true)}
              className={`lg:hidden p-2 rounded-xl transition-all duration-300 ${isScrolled
                ? 'text-gray-600 hover:bg-gray-100'
                : 'text-white hover:bg-white/10'
                }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </motion.button>
          </div>
        </div>

        {/* Gradient border effect */}
        <div className={`absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent transition-opacity duration-500 ${isScrolled ? 'opacity-100' : 'opacity-0'
          }`} />
      </motion.header>

      {/* Mobile Sidebar */}
      {isMobileMenuOpen && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 lg:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed top-0 right-0 h-full w-72 max-w-[80vw] bg-white shadow-2xl z-50 lg:hidden flex flex-col"
          >
            {/* Header da Sidebar - Mais compacto */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center gap-2">
                <img src="/logo-branca.png" alt="Game Day Nexus Logo" className="h-12 w-auto" />
                <div className="flex flex-col">
                  <span className="text-sm font-bold text-gray-900">Game Day Nexus</span>
                  <span className="text-xs text-blue-600">Gestão Esportiva</span>
                </div>
              </div>

              {/* Botão Fechar */}
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Conteúdo da Sidebar */}
            <div className="flex flex-col h-full">
              {/* Navegação */}
              <nav className="flex-1 p-6 space-y-4">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4">Navegação</h3>

                {[
                  { href: "#features", label: "Recursos", icon: "⚡" },
                  { href: "#testimonials", label: "Depoimentos", icon: "💬" },
                  { href: "#pricing", label: "Preços", icon: "💰" },
                  { href: "/blog", label: "Blog", icon: "📝", isExternal: true },
                  { href: "/ajuda", label: "Ajuda", icon: "❓", isExternal: true }
                ].map((item) => (
                  item.isExternal ? (
                    <Link
                      key={item.href}
                      to={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="flex items-center gap-3 p-1 rounded-xl hover:bg-gray-50 transition-colors duration-200 group"
                    >
                      <span className="text-xl">{item.icon}</span>
                      <span className="font-medium text-gray-700 group-hover:text-blue-600">{item.label}</span>
                    </Link>
                  ) : (
                    <a
                      key={item.href}
                      href={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="flex items-center gap-3 p-1 rounded-xl hover:bg-gray-50 transition-colors duration-200 group"
                    >
                      <span className="text-xl">{item.icon}</span>
                      <span className="font-medium text-gray-700 group-hover:text-blue-600">{item.label}</span>
                    </a>
                  )
                ))}
              </nav>

              {/* Botões de Ação */}
              <div className="p-6 border-t border-gray-200 space-y-3">
                <Link to="/login" onClick={() => setIsMobileMenuOpen(false)}>
                  <Button className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 font-medium py-3 mb-2">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Fazer Login
                  </Button>
                </Link>

                <a
                  href="https://wa.me/5519987111198?text=Olá! Gostaria de entrar em contato sobre o Game Day Nexus."
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Button className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    Contato WhatsApp
                  </Button>
                </a>
              </div>
            </div>
          </motion.div>
        </>
      )}

      {/* Hero Section - Carrossel Espetacular */}
      <section ref={headerRef} className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20 pb-16 sm:pb-12">
        {/* Background dinâmico */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
          {/* Partículas animadas */}
          <div className="absolute inset-0">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${2 + Math.random() * 3}s`
                }}
              />
            ))}
          </div>

          {/* Efeitos de luz */}
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 right-0 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-cyan-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
        </div>

        {/* Conteúdo do carrossel */}
        <div
          className="relative z-10 w-full max-w-7xl mx-auto px-6 md:px-12 lg:px-20"
          onMouseEnter={() => setIsHeroHovered(true)}
          onMouseLeave={() => setIsHeroHovered(false)}
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px]">
            {/* Conteúdo textual */}
            <div className="space-y-8 text-white">
              <div className="space-y-4">
                <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
                  <Sparkles className="w-4 h-4 mr-2" />
                  {heroCarouselData[currentHeroSlide].subtitle}
                </Badge>

                <h1 className="hero-title text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  {heroCarouselData[currentHeroSlide].title}
                </h1>

                <p className="hero-subtitle text-xl md:text-2xl text-blue-200 leading-relaxed max-w-2xl">
                  {heroCarouselData[currentHeroSlide].description}
                </p>
              </div>

              {/* Estatísticas impressionantes */}
              <div className="grid grid-cols-3 gap-6">
                {Object.entries(heroCarouselData[currentHeroSlide].stats).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-white">{value}</div>
                    <div className="text-sm text-blue-200 capitalize">{key}</div>
                  </div>
                ))}
              </div>

              {/* Features do slide atual */}
              <div className="flex flex-wrap gap-3">
                {heroCarouselData[currentHeroSlide].features.map((feature, index) => (
                  <Badge key={index} className="bg-white/10 text-white border-white/20 px-3 py-1">
                    <Star className="w-3 h-3 mr-1" />
                    {feature}
                  </Badge>
                ))}
              </div>

              {/* Botões de ação */}
              <div className="hero-cta flex flex-col sm:flex-row gap-4">
                <a href="https://wa.me/5519987111198?text=Olá! Quero economizar R$ 50.000/ano com o Game Day Nexus. Gostaria de começar o teste grátis!" target="_blank" rel="noopener noreferrer">
                  <Button size="lg" className={`bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} hover:scale-105 transform transition-all duration-300 shadow-xl text-white font-bold px-8 py-4 relative overflow-hidden group`}>
                    {/* Efeito de brilho */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                    <div className="relative z-10 flex items-center">
                      <Rocket className="w-5 h-5 mr-2" />
                      {heroCarouselData[currentHeroSlide].cta || "COMEÇAR TESTE GRÁTIS"}
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </div>
                  </Button>
                </a>
                <a href="https://wa.me/5519987111198?text=Olá! Gostaria de ver como outros clubes economizaram com o Game Day Nexus." target="_blank" rel="noopener noreferrer">
                  <Button size="lg" variant="outline" className="border-white/30 text-black hover:bg-white/10 backdrop-blur-sm px-8 py-4 font-semibold">
                    <Eye className="w-5 h-5 mr-2" />
                    {heroCarouselData[currentHeroSlide].ctaSecondary || "Ver Demonstração"}
                  </Button>
                </a>
              </div>
            </div>

            {/* Imagem do slide */}
            <div className="relative">
              <div className="relative overflow-hidden rounded-3xl shadow-2xl transform hover:scale-105 transition-all duration-500">
                <img
                  src={heroCarouselData[currentHeroSlide].image}
                  alt={heroCarouselData[currentHeroSlide].title}
                  className="w-full h-96 lg:h-[500px] object-cover"
                />
                <div className={`absolute inset-0 bg-gradient-to-t ${heroCarouselData[currentHeroSlide].color} opacity-30`}></div>

                {/* Ícone flutuante */}
                <div className="absolute top-6 right-6">
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} flex items-center justify-center shadow-lg animate-float`}>
                    {(() => {
                      const IconComponent = heroCarouselData[currentHeroSlide].icon;
                      return <IconComponent className="w-8 h-8 text-white" />;
                    })()}
                  </div>
                </div>
              </div>

              {/* Efeito de brilho */}
              <div className={`absolute -inset-1 bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} rounded-2xl sm:rounded-3xl blur opacity-30 animate-pulse`}></div>
            </div>
          </div>
        </div>

        {/* Controles do carrossel - Responsivos */}
        <div className="absolute bottom-8 sm:bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-3 sm:space-x-6 z-20">
          {/* Botão anterior */}
          <Button
            variant="ghost"
            size="icon"
            onClick={prevHeroSlide}
            className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300"
          >
            <ChevronLeft className="w-4 h-4 sm:w-6 sm:h-6" />
          </Button>

          {/* Indicadores */}
          <div className="flex space-x-2 sm:space-x-3">
            {heroCarouselData.map((_, index) => (
              <button
                key={index}
                onClick={() => goToHeroSlide(index)}
                className={`w-3 h-3 sm:w-4 sm:h-4 rounded-full transition-all duration-300 ${index === currentHeroSlide
                  ? 'bg-white scale-125 shadow-lg'
                  : 'bg-white/40 hover:bg-white/60'
                  }`}
              />
            ))}
          </div>

          {/* Botão próximo */}
          <Button
            variant="ghost"
            size="icon"
            onClick={nextHeroSlide}
            className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300"
          >
            <ChevronRight className="w-4 h-4 sm:w-6 sm:h-6" />
          </Button>

          {/* Controle play/pause - Oculto em mobile pequeno */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsHeroPlaying(!isHeroPlaying)}
            className="hidden sm:flex w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 ml-2 sm:ml-4 transition-all duration-300"
          >
            {isHeroPlaying ? <Pause className="w-4 h-4 sm:w-6 sm:h-6" /> : <Play className="w-4 h-4 sm:w-6 sm:h-6" />}
          </Button>
        </div>

        {/* Indicador de progresso */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20 mb-4 sm:mb-0">
          <div
            className={`h-full bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} transition-all duration-300`}
            style={{ width: `${((currentHeroSlide + 1) / heroCarouselData.length) * 100}%` }}
          />
        </div>
      </section>

      {/* Features - Módulos do Sistema */}
      <section id="features" ref={featuresRef} className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 md:px-12 lg:px-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-12 sm:mb-16"
          >
            <Badge className="bg-blue-100 text-blue-800 px-3 py-1.5 sm:px-4 sm:py-2 mb-4 text-xs sm:text-sm">
              <Zap className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              Módulos Integrados
            </Badge>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4 sm:mb-6">
              15 Módulos que Economizam R$ 50.000/Ano
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
              Cada módulo foi criado para resolver problemas reais e gerar economia imediata. Veja como 500+ clubes já transformaram sua gestão e aumentaram seus lucros.
            </p>

            {/* Seção de ROI e Métricas */}
            <div className="mt-8 sm:mt-12 grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-blue-600">70%</div>
                <div className="text-xs sm:text-sm text-gray-600">Redução no Tempo</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-green-600">300%</div>
                <div className="text-xs sm:text-sm text-gray-600">Aumento Eficiência</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-purple-600">500+</div>
                <div className="text-xs sm:text-sm text-gray-600">Clubes Ativos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-orange-600">95%</div>
                <div className="text-xs sm:text-sm text-gray-600">Satisfação</div>
              </div>
            </div>
          </motion.div>

          {/* Stacked Cards Container */}
          <StackedModulesCards />

          {/* Seção de integração */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
            className="mt-20 text-center"
          >
            <Card className="bg-gradient-to-r from-blue-600 to-purple-600 border-0 shadow-2xl overflow-hidden">
              <CardContent className="p-12 relative">
                {/* Partículas de fundo */}
                <div className="absolute inset-0">
                  {[...Array(20)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white/30 rounded-full animate-pulse"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 2}s`
                      }}
                    />
                  ))}
                </div>

                <div className="relative z-10 text-white">
                  <div className="flex items-center justify-center mb-6">
                    <div className="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center animate-float">
                      <Cpu className="w-10 h-10 text-white" />
                    </div>
                  </div>

                  <h3 className="text-3xl md:text-4xl font-bold mb-4">
                    🏆 RESULTADOS COMPROVADOS
                  </h3>
                  <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                    Mais de 500 clubes já transformaram sua gestão. Economia média de R$ 50.000/ano por clube com ROI de 400% no primeiro ano.
                  </p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold">500+</div>
                      <div className="text-sm text-blue-200">Clubes Transformados</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold">50K+</div>
                      <div className="text-sm text-blue-200">Atletas Gerenciados</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold">200K+</div>
                      <div className="text-sm text-blue-200">Partidas Registradas</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold">99.9%</div>
                      <div className="text-sm text-blue-200">Uptime Garantido</div>
                    </div>
                  </div>

                  {/* ROI Section */}
                  <div className="mt-8 pt-8 border-t border-white/20">
                    <h4 className="text-2xl font-bold mb-4">💰 RETORNO SOBRE INVESTIMENTO</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-300">70%</div>
                        <div className="text-blue-200">Redução Tempo Admin</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-300">300%</div>
                        <div className="text-blue-200">Aumento Eficiência</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-300">40%</div>
                        <div className="text-blue-200">Melhoria Performance</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Módulos Específicos - Carrossel Avançado */}
      <section className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
        </div>

        {/* Partículas flutuantes */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Header da seção */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white border border-white/20 mb-6">
              <Cpu className="w-4 h-4 mr-2" />
              Módulos Específicos
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Funcionalidades que Transformam
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Cada módulo foi desenvolvido para resolver problemas reais dos clubes. Veja como nossa tecnologia pode revolucionar sua gestão esportiva.
            </p>
          </motion.div>

          {/* Carrossel de módulos específicos - Responsivo */}
          <div
            className="relative"
            onMouseEnter={() => setIsModuleHovered(true)}
            onMouseLeave={() => setIsModuleHovered(false)}
          >
            <div className="relative overflow-hidden rounded-2xl sm:rounded-3xl bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 shadow-2xl">
              {/* Conteúdo do slide atual */}
              <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 p-4 sm:p-6 md:p-8 lg:p-16 min-h-[500px] sm:min-h-[600px] items-center">
                {/* Lado esquerdo - Conteúdo */}
                <div className={`space-y-6 sm:space-y-8 text-white transition-all duration-700 ${moduleTransitioning ? 'opacity-0 transform translate-x-8' : 'opacity-100 transform translate-x-0'}`}>
                  <div className="space-y-4 sm:space-y-6">
                    {/* Badge e título */}
                    <div className="space-y-3 sm:space-y-4 text-center lg:text-left">
                      <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-white/10 backdrop-blur-sm rounded-full text-white border border-white/20 text-xs sm:text-sm">
                        {(() => {
                          const IconComponent = specificModulesData[currentModuleSlide].icon;
                          return <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />;
                        })()}
                        {specificModulesData[currentModuleSlide].subtitle}
                      </div>

                      <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight">
                        {specificModulesData[currentModuleSlide].title}
                      </h3>

                      <p className="text-base sm:text-lg lg:text-xl text-gray-300 leading-relaxed">
                        {specificModulesData[currentModuleSlide].description}
                      </p>
                    </div>

                    {/* Estatísticas */}
                    <div className="grid grid-cols-3 gap-3 sm:gap-6">
                      {Object.entries(specificModulesData[currentModuleSlide].stats).map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-white">{value}</div>
                          <div className="text-xs sm:text-sm text-gray-400 capitalize">{key}</div>
                        </div>
                      ))}
                    </div>

                    {/* Features detalhadas */}
                    <div className="space-y-3 sm:space-y-4">
                      {specificModulesData[currentModuleSlide].features.map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300"
                        >
                          <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} mt-1.5 sm:mt-2 flex-shrink-0`}></div>
                          <div>
                            <h4 className="font-semibold text-white mb-1 text-sm sm:text-base">{feature.title}</h4>
                            <p className="text-gray-400 text-xs sm:text-sm">{feature.description}</p>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Botões de ação */}
                    <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start">
                      <a href={`https://wa.me/5519987111198?text=Olá! Gostaria de ver uma demonstração do módulo "${specificModulesData[currentModuleSlide].title}" do Game Day Nexus.`} target="_blank" rel="noopener noreferrer">
                        <Button className={`w-full sm:w-auto bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} hover:scale-105 transform transition-all duration-300 shadow-lg text-white font-semibold px-4 py-2.5 sm:px-6 sm:py-3 text-sm sm:text-base`}>
                          <Eye className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                          Ver Demonstração
                        </Button>
                      </a>
                      <a href={`https://wa.me/5519987111198?text=Olá! Gostaria de saber mais sobre o módulo "${specificModulesData[currentModuleSlide].title}" do Game Day Nexus.`} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" className="w-full sm:w-auto border-white/30 text-black hover:bg-white/10 backdrop-blur-sm px-4 py-2.5 sm:px-6 sm:py-3 text-sm sm:text-base">
                          <Heart className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                          Saiba Mais
                        </Button>
                      </a>
                    </div>
                  </div>
                </div>

                {/* Lado direito - Imagem */}
                <div className={`relative transition-all duration-700 order-first lg:order-last ${moduleTransitioning ? 'opacity-0 transform translate-x-8 scale-95' : 'opacity-100 transform translate-x-0 scale-100'}`}>
                  <div className="relative overflow-hidden rounded-xl sm:rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-500">
                    <img
                      src={specificModulesData[currentModuleSlide].image}
                      alt={specificModulesData[currentModuleSlide].title}
                      className="w-full h-48 sm:h-64 md:h-80 lg:h-96 xl:h-[500px] object-cover"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-t ${specificModulesData[currentModuleSlide].color} opacity-30`}></div>

                    {/* Ícone flutuante */}
                    <div className="absolute top-3 right-3 sm:top-4 sm:right-4 lg:top-6 lg:right-6">
                      <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 rounded-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} flex items-center justify-center shadow-lg animate-float`}>
                        {(() => {
                          const IconComponent = specificModulesData[currentModuleSlide].icon;
                          return <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-white" />;
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Efeito de brilho */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} rounded-xl sm:rounded-2xl blur opacity-30 animate-pulse`}></div>
                </div>
              </div>
            </div>

            {/* Controles do carrossel - Responsivos */}
            <div className="flex items-center justify-center mt-6 sm:mt-8 space-x-3 sm:space-x-6">
              {/* Botão anterior */}
              <Button
                variant="ghost"
                size="icon"
                onClick={prevModuleSlide}
                disabled={moduleTransitioning}
                className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300 disabled:opacity-50"
              >
                <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
              </Button>

              {/* Indicadores */}
              <div className="flex space-x-2 sm:space-x-3">
                {specificModulesData.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToModuleSlide(index)}
                    disabled={moduleTransitioning}
                    className={`w-2.5 h-2.5 sm:w-3 sm:h-3 lg:w-4 lg:h-4 rounded-full transition-all duration-300 ${index === currentModuleSlide
                      ? 'bg-white scale-125 shadow-lg'
                      : 'bg-white/40 hover:bg-white/60'
                      } disabled:opacity-50`}
                  />
                ))}
              </div>

              {/* Botão próximo */}
              <Button
                variant="ghost"
                size="icon"
                onClick={nextModuleSlide}
                disabled={moduleTransitioning}
                className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300 disabled:opacity-50"
              >
                <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
              </Button>

              {/* Controle play/pause - Oculto em mobile pequeno */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsModulePlaying(!isModulePlaying)}
                className="hidden sm:flex w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 ml-2 sm:ml-4 transition-all duration-300"
              >
                {isModulePlaying ? <Pause className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" /> : <Play className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />}
              </Button>
            </div>

            {/* Indicador de progresso */}
            <div className="mt-4 sm:mt-6 h-1 bg-white/20 rounded-full overflow-hidden">
              <div
                className={`h-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} transition-all duration-300 rounded-full`}
                style={{ width: `${((currentModuleSlide + 1) / specificModulesData.length) * 100}%` }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials - Depoimentos Modernos */}
      <section id="testimonials" className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-16"
          >
            <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 mb-4">
              <Heart className="w-4 h-4 mr-2" />
              Depoimentos
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Clubes que Confiam em Nós
            </h2>
            <p className="text-xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
              Mais de 500 clubes em todo o Brasil já transformaram sua gestão. Veja os resultados reais de quem usa nossa plataforma.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Atlético Municipal",
                role: "Presidente",
                avatar: "AM",
                color: "from-red-500 to-red-600",
                quote: "Revolucionou completamente nossa gestão. Economizamos 20 horas por semana e aumentamos nossa eficiência em 300%. O ROI foi impressionante.",
                rating: 5,
                improvement: "Economia de R$ 45.000/ano"
              },
              {
                name: "Sport Club Profissional",
                role: "Médica do Clube",
                avatar: "SC",
                color: "from-green-500 to-green-600",
                quote: "A redução de lesões foi impressionante. Nossos atletas nunca estiveram tão bem. O sistema de prevenção com IA é revolucionário.",
                rating: 5,
                improvement: "35% menos lesões"
              },
              {
                name: "FC Campeão",
                role: "Diretor Financeiro",
                avatar: "FC",
                color: "from-blue-500 to-blue-600",
                quote: "O controle financeiro ficou perfeito. Sabemos exatamente onde está cada centavo. A integração PIX facilitou muito os recebimentos.",
                rating: 5,
                improvement: "60% menos inadimplência"
              },
              {
                name: "Juventude Esportiva",
                role: "Coordenador Técnico",
                avatar: "JE",
                color: "from-purple-500 to-purple-600",
                quote: "O editor de treinos 3D é fantástico. Criamos exercícios profissionais em minutos. A organização dos treinos melhorou 85%.",
                rating: 5,
                improvement: "85% melhoria treinos"
              },
              {
                name: "União Atlética",
                role: "Gerente Administrativo",
                avatar: "UA",
                color: "from-orange-500 to-orange-600",
                quote: "Eliminamos todas as planilhas. Tudo integrado, tudo automático. A produtividade da equipe aumentou drasticamente.",
                rating: 5,
                improvement: "70% menos tempo admin"
              },
              {
                name: "Esporte Clube Brasil",
                role: "Diretor de Base",
                avatar: "EB",
                color: "from-teal-500 to-teal-600",
                quote: "O sistema de avaliação de atletas organizou completamente nossas peneiras. Agora temos um processo profissional de captação.",
                rating: 5,
                improvement: "300% melhoria peneiras"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="group"
              >
                <Card className="h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
                  <CardContent className="p-8 relative">
                    {/* Quote icon */}
                    <div className="absolute top-4 right-4 opacity-20">
                      <div className="text-6xl text-white font-serif">"</div>
                    </div>

                    {/* Rating stars */}
                    <div className="flex mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>

                    {/* Quote */}
                    <p className="text-white text-lg leading-relaxed mb-6 italic">
                      "{testimonial.quote}"
                    </p>

                    {/* Improvement badge */}
                    <Badge className="bg-green-500/20 text-green-300 border-green-500/30 mb-6">
                      <ArrowUp className="w-3 h-3 mr-1" />
                      {testimonial.improvement}
                    </Badge>

                    {/* Author */}
                    <div className="flex items-center">
                      <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${testimonial.color} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                        {testimonial.avatar}
                      </div>
                      <div className="ml-4">
                        <h4 className="font-bold text-white text-lg">{testimonial.name}</h4>
                        <p className="text-blue-200 text-sm">{testimonial.role}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Stats section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          >
            {[
              { number: "500+", label: "Clubes Ativos", icon: Trophy },
              { number: "10K+", label: "Jogadores", icon: Users },
              { number: "50K+", label: "Partidas", icon: Activity },
              { number: "99.9%", label: "Uptime", icon: Wifi }
            ].map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-white">
                  <div className="w-16 h-16 rounded-full bg-white/10 flex items-center justify-center mx-auto mb-4 animate-float">
                    <IconComponent className="w-8 h-8" />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold mb-2">{stat.number}</div>
                  <div className="text-blue-200">{stat.label}</div>
                </div>
              );
            })}
          </motion.div>
        </div>
      </section>

      {/* Pricing - Planos Modernos */}
      <section id="pricing" ref={pricingRef} className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-16"
          >
            <Badge className="bg-purple-100 text-purple-800 px-4 py-2 mb-4">
              <Database className="w-4 h-4 mr-2" />
              Planos Flexíveis
            </Badge>
            <h2 className="pricing-title text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
              Invista Menos que 1 Salário e Economize R$ 50.000/Ano
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              <span className="font-bold text-green-600">🔥 OFERTA LIMITADA:</span> 30 dias grátis + migração gratuita + setup incluído. Apenas para os primeiros 50 clubes este mês.
            </p>

            {/* Banner de urgência */}
            <div className="mt-6 inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full font-bold text-lg shadow-lg animate-pulse">
              <span className="mr-2">⏰</span>
              RESTAM APENAS 12 VAGAS ESTE MÊS
            </div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {/* Plano Básico */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
              className="pricing-card group"
            >
              <Card className="h-full bg-white border-2 border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 relative overflow-hidden">
                <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600"></div>
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Users className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">STARTER</h3>
                    <div className="mb-2">
                      <span className="text-lg text-gray-500 line-through">R$ 499</span>
                      <span className="text-5xl font-bold text-green-600 ml-2">R$399</span>
                      <span className="text-gray-500 text-lg">/mês</span>
                    </div>
                    <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-bold mb-4">
                      ECONOMIZE R$ 15.000/ANO
                    </div>
                    <p className="text-gray-600">Menos que 1 salário mínimo para economizar R$ 15K. Ideal para clubes em crescimento.</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {[
                      "Gestão de elenco",
                      "Partidas e estatísticas",
                      "Treinamentos básicos",
                      "1 Temporada",
                      "Até 30 jogadores"
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                          <Check className="w-3 h-3 text-blue-600" />
                        </div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <a href="https://wa.me/5519987111198?text=Olá! Gostaria de conhecer mais sobre o Game Day Nexus e começar gratuitamente." target="_blank" rel="noopener noreferrer">
                    <Button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      <Rocket className="w-4 h-4 mr-2" />
                      Começar Grátis
                    </Button>
                  </a>
                </CardContent>
              </Card>
            </motion.div>

            {/* Plano Profissional - Destaque */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
              className="pricing-card group relative"
            >
              {/* Badge Popular */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 text-sm font-bold shadow-lg">
                  <Star className="w-4 h-4 mr-1" />
                  MAIS POPULAR
                </Badge>
              </div>

              <Card className="h-full bg-gradient-to-br from-purple-600 to-blue-600 border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 transform scale-105 hover:scale-110 hover:-translate-y-4 relative overflow-hidden">
                {/* Partículas de fundo */}
                <div className="absolute inset-0">
                  {[...Array(15)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white/30 rounded-full animate-pulse"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 2}s`
                      }}
                    />
                  ))}
                </div>

                <CardContent className="p-8 relative z-10 text-white">
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Trophy className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold mb-2">CAMPEÃO</h3>
                    <div className="mb-2">
                      <span className="text-lg text-purple-200 line-through">R$ 999</span>
                      <span className="text-5xl font-bold ml-2">R$899</span>
                      <span className="text-purple-200 text-lg">/mês</span>
                    </div>
                    <div className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold mb-4">
                      ECONOMIZE R$ 50.000/ANO + IA INCLUSA
                    </div>
                    <p className="text-purple-100">ROI de 1000% no primeiro ano. Inclui IA para prevenção de lesões.</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {[
                      "Tudo do plano Básico",
                      "Departamento médico",
                      "Gestão financeira",
                      "5 Temporadas",
                      "Até 100 jogadores",
                      "Suporte prioritário"
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-3">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-white">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button className="w-full bg-white text-purple-600 hover:bg-gray-100 font-bold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    <Flame className="w-4 h-4 mr-2" />
                    Assinar Agora
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Plano Enterprise */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
              className="pricing-card group"
            >
              <Card className="h-full bg-white border-2 border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 relative overflow-hidden">
                <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-green-500 to-emerald-600"></div>
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Award className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                    <div className="mb-4">
                      <span className="text-5xl font-bold text-gray-900">R$1299</span>
                      <span className="text-gray-500 text-lg">/mês</span>
                    </div>
                    <p className="text-gray-600">Solução completa para clubes profissionais. Recursos ilimitados e suporte 24/7.</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {[
                      "Tudo do plano Profissional",
                      "Base juvenil completa",
                      "Analytics avançado",
                      "Temporadas ilimitadas",
                      "Jogadores ilimitados",
                      "Suporte 24/7"
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
                          <Check className="w-3 h-3 text-green-600" />
                        </div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <a href="https://wa.me/5519987111198?text=Olá! Gostaria de conhecer mais sobre o plano Enterprise do Game Day Nexus." target="_blank" rel="noopener noreferrer">
                    <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      <Globe className="w-4 h-4 mr-2" />
                      Contate-nos
                    </Button>
                  </a>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Garantia e benefícios */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
            className="mt-16 text-center"
          >
            <div className="grid md:grid-cols-3 gap-8">
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <Check className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">30 dias grátis</div>
                  <div className="text-sm text-gray-600">Sem cartão de crédito</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <Lock className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Migração gratuita</div>
                  <div className="text-sm text-gray-600">Seus dados seguros</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <Heart className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Suporte em português</div>
                  <div className="text-sm text-gray-600">Equipe especializada</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to action */}
      <section className="py-20 px-6 md:px-12 lg:px-20 bg-team-blue text-white">
        <div className="max-w-5xl mx-auto text-center">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            💰 ECONOMIZE R$ 50.000 AINDA ESTE ANO
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-blue-100 mb-6 max-w-3xl mx-auto"
          >
            Mais de 500 clubes já economizaram milhões. Seja o próximo a eliminar planilhas, reduzir lesões e aumentar receitas.
          </motion.p>

          {/* Banner de urgência final */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 rounded-full font-bold text-lg mb-10 inline-block animate-pulse"
          >
            ⚡ ÚLTIMAS 48H - SETUP GRATUITO + 50% OFF
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <a href="https://wa.me/5519987111198?text=🚀 QUERO ECONOMIZAR R$ 50.000/ANO! Gostaria de começar o teste grátis do Game Day Nexus com setup gratuito." target="_blank" rel="noopener noreferrer">
              <Button size="lg" className="bg-white text-team-blue hover:bg-gray-100 font-bold px-8 py-4 text-lg relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-200 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                <div className="relative z-10 flex items-center">
                  <Rocket className="w-5 h-5 mr-2" />
                  ECONOMIZAR R$ 50K AGORA
                  <ArrowRight className="w-5 h-5 ml-2" />
                </div>
              </Button>
            </a>
            <a href="https://wa.me/5519987111198?text=💰 Quero ver como outros clubes economizaram R$ 50.000/ano com o Game Day Nexus. Podem me mostrar casos reais?" target="_blank" rel="noopener noreferrer">
              <Button size="lg" variant="outline" className="border-white text-black hover:bg-white/20 px-8 py-4 text-lg font-bold">
                <Trophy className="w-5 h-5 mr-2" />
                Ver Casos de Sucesso
              </Button>
            </a>
          </motion.div>

          {/* Benefícios do CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-blue-200"
          >
            <div className="text-center">
              <Check className="w-5 h-5 mx-auto mb-1" />
              <div>Sem cartão de crédito</div>
            </div>
            <div className="text-center">
              <Check className="w-5 h-5 mx-auto mb-1" />
              <div>Suporte completo incluído</div>
            </div>
            <div className="text-center">
              <Check className="w-5 h-5 mx-auto mb-1" />
              <div>Migração de dados gratuita</div>
            </div>
            <div className="text-center">
              <Check className="w-5 h-5 mx-auto mb-1" />
              <div>Treinamento da equipe incluído</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Seção de Funcionalidades Exclusivas */}
      <section className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white border border-white/20 mb-6">
              <Sparkles className="w-4 h-4 mr-2" />
              Exclusivo Game Day Nexus
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              ⭐ Diferenciais Únicos no Mercado
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Funcionalidades que você não encontra em nenhum outro sistema de gestão esportiva.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "IA para Prevenção de Lesões",
                description: "Algoritmo exclusivo que analisa padrões e prevê lesões antes que aconteçam",
                color: "from-green-500 to-emerald-600",
                benefit: "35% redução em lesões"
              },
              {
                icon: "⚽",
                title: "Editor de Treinos 3D",
                description: "Crie exercícios visuais profissionais com animações e trajetórias",
                color: "from-purple-500 to-violet-600",
                benefit: "Treinos em 5 minutos"
              },
              {
                icon: CreditCard,
                title: "Integração PIX Nativa",
                description: "Sistema de pagamentos PIX integrado com QR Codes automáticos",
                color: "from-blue-500 to-cyan-600",
                benefit: "60% menos inadimplência"
              },
              {
                icon: FileText,
                title: "Assinatura Digital Certificada",
                description: "Documentos com validade jurídica e certificação digital",
                color: "from-orange-500 to-red-600",
                benefit: "100% digital"
              },
              {
                icon: MessageCircle,
                title: "Chat em Tempo Real",
                description: "Comunicação instantânea entre todos os departamentos do clube",
                color: "from-pink-500 to-rose-600",
                benefit: "80% melhoria comunicação"
              },
              {
                icon: Database,
                title: "API Robusta para Integrações",
                description: "Conecte com qualquer sistema externo através da nossa API",
                color: "from-teal-500 to-cyan-600",
                benefit: "Integrações ilimitadas"
              }
            ].map((feature, index) => {
              const renderIcon = (icon: any) => {
                if (typeof icon === 'string') {
                  return <span className="text-3xl">{icon}</span>;
                } else {
                  const IconComponent = icon;
                  return <IconComponent className="w-8 h-8 text-white" />;
                }
              };

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <Card className="h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
                    <CardContent className="p-6 relative">
                      {/* Ícone */}
                      <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        {renderIcon(feature.icon)}
                      </div>

                      {/* Conteúdo */}
                      <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-300 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-300 mb-4 leading-relaxed">
                        {feature.description}
                      </p>

                      {/* Benefício */}
                      <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                        <ArrowUp className="w-3 h-3 mr-1" />
                        {feature.benefit}
                      </Badge>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-6">
              Pronto para ter acesso a essas funcionalidades exclusivas?
            </h3>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="https://wa.me/5519987111198?text=Olá! Gostaria de conhecer as funcionalidades exclusivas do Game Day Nexus." target="_blank" rel="noopener noreferrer">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Conhecer Funcionalidades
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Seção de Autoridade e Credibilidade */}
      <section className="py-16 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-12"
          >
            <Badge className="bg-gold-100 text-gold-800 px-4 py-2 mb-4">
              <Trophy className="w-4 h-4 mr-2" />
              Reconhecimento no Mercado
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gold-600 to-orange-600 bg-clip-text text-transparent mb-6">
              🏆 ERP Esportivo Líder no Brasil
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Credenciais */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-white shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Certificação ISO 27001</h3>
                  <p className="text-gray-600">Segurança de dados certificada internacionalmente</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-white shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center mx-auto mb-4">
                    <Check className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Compliance LGPD 100%</h3>
                  <p className="text-gray-600">Totalmente adequado à Lei Geral de Proteção de Dados</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-white shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                    <Star className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">4.9/5 Estrelas</h3>
                  <p className="text-gray-600">Avaliação média dos nossos clientes</p>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Estatísticas de Satisfação */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 md:p-12 text-white text-center"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-8">Satisfação Garantida</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div>
                <div className="text-3xl md:text-4xl font-bold mb-2">95%</div>
                <div className="text-blue-200">Satisfação dos Usuários</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold mb-2">98%</div>
                <div className="text-blue-200">Retenção de Clientes</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold mb-2">99.9%</div>
                <div className="text-blue-200">Uptime Garantido</div>
              </div>
              <div>
                <div className="text-3xl md:text-4xl font-bold mb-2">24/7</div>
                <div className="text-blue-200">Suporte em Português</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-gray-300 py-12 px-6 md:px-12 lg:px-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <img src="/logo-branca.png" alt="Game Day Nexus Logo" className="h-10 w-auto" />
                <span className="font-bold text-xl text-white">Game Day Nexus</span>
              </div>
              <p className="text-gray-400 mb-4">
                O ERP esportivo nº1 do Brasil. Mais de 500 clubes confiam em nossa plataforma.
              </p>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span>99.9% Uptime</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  <span>ISO 27001</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-white mb-4">Módulos</h3>
              <ul className="space-y-2 text-sm">
                <li><a href="#features" className="hover:text-white transition-colors">Gestão de Atletas</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Mensalidades</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Departamento Médico</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Gestão Financeira</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Treinamentos</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Ver todos os 15 módulos</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-white mb-4">Empresa</h3>
              <ul className="space-y-2 text-sm">
                <li><a href="#testimonials" className="hover:text-white transition-colors">Casos de Sucesso</a></li>
                <li><a href="#pricing" className="hover:text-white transition-colors">Planos e Preços</a></li>
                <li><a href="https://wa.me/5519987111198" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Suporte</a></li>
                <li><a href="https://wa.me/5519987111198" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Demonstração</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-white mb-4">Contato</h3>
              <ul className="space-y-3 text-sm">
                <li>
                  <a href="https://wa.me/5519987111198" target="_blank" rel="noopener noreferrer" className="flex items-center hover:text-white transition-colors">
                    <span className="text-green-500 mr-2">📱</span>
                    <span>WhatsApp: (19) 98711-1198</span>
                  </a>
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 mr-2">📧</span>
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center">
                  <span className="text-purple-500 mr-2">🌐</span>
                  <span>Brasil - Suporte em Português</span>
                </li>
              </ul>

              {/* CTA no Footer */}
              <div className="mt-6">
                <a href="https://wa.me/5519987111198?text=Olá! Gostaria de começar meu teste grátis de 30 dias." target="_blank" rel="noopener noreferrer">
                  <Button className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 text-sm">
                    <Rocket className="w-4 h-4 mr-2" />
                    Teste Grátis 30 Dias
                  </Button>
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8">
            {/* Estatísticas no Footer */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 text-center">
              <div>
                <div className="text-2xl font-bold text-white">500+</div>
                <div className="text-xs text-gray-400">Clubes Ativos</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">50K+</div>
                <div className="text-xs text-gray-400">Atletas</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">95%</div>
                <div className="text-xs text-gray-400">Satisfação</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">24/7</div>
                <div className="text-xs text-gray-400">Suporte</div>
              </div>
            </div>

            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm">&copy; 2025 Game Day Nexus. Todos os direitos reservados. CNPJ: XX.XXX.XXX/0001-XX</p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <a href="#" className="text-sm hover:text-white transition-colors">Política de Privacidade</a>
                <a href="#" className="text-sm hover:text-white transition-colors">Termos de Uso</a>
                <a href="#" className="text-sm hover:text-white transition-colors">LGPD</a>
                <a href="https://www.instagram.com/gamedaynexus/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Instagram</span>
                  <Instagram className="h-6 w-6" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
      <Chatbot />
    </div>
  );
}