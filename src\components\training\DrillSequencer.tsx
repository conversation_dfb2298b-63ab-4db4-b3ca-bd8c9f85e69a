import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Plus,
  Trash2,
  Edit,
  Clock,
  Target,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  GripVertical,
  Save,
  FolderOpen,
  Repeat,
  ArrowRight,
  Timer,
  Layers
} from 'lucide-react';
import { TrainingDrill, DrillStep } from './InteractiveTrainingBuilder';
import { useToast } from '@/components/ui/use-toast';

interface DrillSequencerProps {
  drill: TrainingDrill | null;
  currentStepIndex: number;
  onStepChange: (index: number) => void;
  onDrillUpdate: (drill: TrainingDrill) => void;
  isPlaying: boolean;
  onPlayStateChange: (playing: boolean) => void;
  playbackSpeed: number;
  onSpeedChange: (speed: number) => void;
}

// Sortable Step Item Component
function SortableStepItem({ 
  step, 
  index, 
  isActive, 
  isEditing, 
  onEdit, 
  onSave, 
  onCancel, 
  onDelete, 
  onSelect,
  stepForm,
  onStepFormChange
}: {
  step: DrillStep;
  index: number;
  isActive: boolean;
  isEditing: boolean;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete: () => void;
  onSelect: () => void;
  stepForm: { name: string; description: string; duration: number; transitionTime: number };
  onStepFormChange: (updates: Partial<typeof stepForm>) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: step.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-2 rounded border ${
        isActive ? 'border-primary bg-primary/5' : 'border-muted'
      } ${isDragging ? 'shadow-lg' : ''}`}
    >
      {isEditing ? (
        <div className="space-y-2">
          <Input
            value={stepForm.name}
            onChange={(e) => onStepFormChange({ name: e.target.value })}
            className="text-xs"
            placeholder="Nome do passo"
          />
          <Textarea
            value={stepForm.description}
            onChange={(e) => onStepFormChange({ description: e.target.value })}
            className="text-xs"
            placeholder="Descrição"
            rows={2}
          />
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Duração (s)</Label>
              <Input
                type="number"
                value={stepForm.duration}
                onChange={(e) => onStepFormChange({ duration: parseInt(e.target.value) || 300 })}
                className="text-xs"
                min="1"
              />
            </div>
            <div>
              <Label className="text-xs">Transição (s)</Label>
              <Input
                type="number"
                value={stepForm.transitionTime}
                onChange={(e) => onStepFormChange({ transitionTime: parseInt(e.target.value) || 5 })}
                className="text-xs"
                min="0"
              />
            </div>
          </div>
          <div className="flex gap-1">
            <Button variant="outline" size="sm" onClick={onSave}>
              Salvar
            </Button>
            <Button variant="ghost" size="sm" onClick={onCancel}>
              Cancelar
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <div
                {...attributes}
                {...listeners}
                className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
              >
                <GripVertical className="h-3 w-3 text-muted-foreground" />
              </div>
              <Badge variant="outline" className="text-xs">
                {index + 1}
              </Badge>
              <span className="text-xs font-medium">{step.name}</span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={onSelect}
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={onEdit}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          {step.description && (
            <p className="text-xs text-muted-foreground mb-1">
              {step.description}
            </p>
          )}
          
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{formatTime(step.duration)}</span>
            <span>•</span>
            <span>{step.elements.length} elementos</span>
            {(step as any).transitionTime > 0 && (
              <>
                <span>•</span>
                <ArrowRight className="h-3 w-3" />
                <span>{formatTime((step as any).transitionTime || 5)}</span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export function DrillSequencer({
  drill,
  currentStepIndex,
  onStepChange,
  onDrillUpdate,
  isPlaying,
  onPlayStateChange,
  playbackSpeed,
  onSpeedChange
}: DrillSequencerProps) {
  const { toast } = useToast();
  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [stepForm, setStepForm] = useState({
    name: '',
    description: '',
    duration: 300,
    transitionTime: 5
  });
  const [playbackTime, setPlaybackTime] = useState(0);
  const [showSequenceDialog, setShowSequenceDialog] = useState(false);
  const [sequenceName, setSequenceName] = useState('');
  const [loopMode, setLoopMode] = useState(false);
  const [autoTransition, setAutoTransition] = useState(true);

  const currentStep = drill?.steps[currentStepIndex];

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Enhanced timer for playback with transitions and loop mode
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isPlaying && currentStep) {
      interval = setInterval(() => {
        setPlaybackTime(prev => {
          const stepDuration = currentStep.duration * 1000;
          const transitionTime = ((currentStep as any).transitionTime || 5) * 1000;
          const totalStepTime = stepDuration + (autoTransition ? transitionTime : 0);
          
          const newTime = prev + (1000 / playbackSpeed);
          
          if (newTime >= totalStepTime) {
            // Check if we should advance to next step
            if (currentStepIndex < (drill?.steps.length || 0) - 1) {
              // Move to next step
              onStepChange(currentStepIndex + 1);
              return 0;
            } else {
              // End of sequence
              if (loopMode) {
                // Loop back to first step
                onStepChange(0);
                return 0;
              } else {
                // Stop playback
                onPlayStateChange(false);
                return 0;
              }
            }
          }
          return newTime;
        });
      }, 1000 / playbackSpeed);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPlaying, currentStep, currentStepIndex, drill?.steps.length, onStepChange, onPlayStateChange, playbackSpeed, autoTransition, loopMode]);

  const handleAddStep = () => {
    if (!drill) return;

    const newStep: DrillStep = {
      id: `step_${Date.now()}`,
      name: `Passo ${drill.steps.length + 1}`,
      description: '',
      duration: 300,
      elements: [],
      annotations: [],
      drawings: []
    };

    const updatedDrill = {
      ...drill,
      steps: [...drill.steps, newStep],
      totalDuration: drill.totalDuration + 300
    };

    onDrillUpdate(updatedDrill);
  };

  const handleDeleteStep = (stepId: string) => {
    if (!drill || drill.steps.length <= 1) return;

    const stepIndex = drill.steps.findIndex(s => s.id === stepId);
    const stepToDelete = drill.steps[stepIndex];
    
    const updatedDrill = {
      ...drill,
      steps: drill.steps.filter(s => s.id !== stepId),
      totalDuration: drill.totalDuration - stepToDelete.duration
    };

    onDrillUpdate(updatedDrill);
    
    // Ajustar índice atual se necessário
    if (currentStepIndex >= stepIndex && currentStepIndex > 0) {
      onStepChange(currentStepIndex - 1);
    }
  };

  const handleUpdateStep = (stepId: string, updates: Partial<DrillStep>) => {
    if (!drill) return;

    const updatedSteps = drill.steps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    );

    const updatedDrill = {
      ...drill,
      steps: updatedSteps,
      totalDuration: updatedSteps.reduce((total, step) => total + step.duration, 0)
    };

    onDrillUpdate(updatedDrill);
  };

  const handleEditStep = (step: DrillStep) => {
    setEditingStep(step.id);
    setStepForm({
      name: step.name,
      description: step.description,
      duration: step.duration,
      transitionTime: (step as any).transitionTime || 5
    });
  };

  // Handle drag end for reordering steps
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id && drill) {
      const oldIndex = drill.steps.findIndex(step => step.id === active.id);
      const newIndex = drill.steps.findIndex(step => step.id === over.id);

      const reorderedSteps = arrayMove(drill.steps, oldIndex, newIndex);
      
      const updatedDrill = {
        ...drill,
        steps: reorderedSteps
      };

      onDrillUpdate(updatedDrill);

      // Update current step index if needed
      if (currentStepIndex === oldIndex) {
        onStepChange(newIndex);
      } else if (currentStepIndex === newIndex) {
        onStepChange(oldIndex);
      } else if (oldIndex < currentStepIndex && newIndex >= currentStepIndex) {
        onStepChange(currentStepIndex - 1);
      } else if (oldIndex > currentStepIndex && newIndex <= currentStepIndex) {
        onStepChange(currentStepIndex + 1);
      }

      toast({
        title: "Sequência reordenada",
        description: "A ordem dos passos foi atualizada com sucesso.",
      });
    }
  }, [drill, currentStepIndex, onDrillUpdate, onStepChange, toast]);

  // Save sequence to database
  const handleSaveSequence = useCallback(async () => {
    if (!drill) return;

    try {
      // Save the drill with updated steps including transition times
      const updatedSteps = drill.steps.map((step, index) => ({
        ...step,
        transitionTime: (step as any).transitionTime || 5
      }));

      const drillToSave = {
        ...drill,
        steps: updatedSteps,
        totalDuration: updatedSteps.reduce((total, step) => total + step.duration + ((step as any).transitionTime || 0), 0)
      };

      // Here we would call the API to save the drill sequence
      // For now, we'll simulate the save and show success
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      toast({
        title: "Sequência salva",
        description: `Sequência "${drill.name}" foi salva com sucesso.`,
      });
    } catch (error) {
      console.error('Erro ao salvar sequência:', error);
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar a sequência.",
        variant: "destructive"
      });
    }
  }, [drill, toast]);

  // Timeline visualization component
  const TimelineVisualization = () => {
    if (!drill) return null;

    const totalDuration = drill.steps.reduce((total, step) => total + step.duration + ((step as any).transitionTime || 0), 0);
    let currentTime = 0;

    return (
      <div className="space-y-2">
        <Label className="text-xs font-medium">Timeline Visual</Label>
        <div className="relative h-8 bg-muted rounded overflow-hidden">
          {drill.steps.map((step, index) => {
            const stepDuration = step.duration;
            const transitionTime = (step as any).transitionTime || 0;
            const stepWidth = ((stepDuration + transitionTime) / totalDuration) * 100;
            const stepLeft = (currentTime / totalDuration) * 100;
            
            currentTime += stepDuration + transitionTime;

            return (
              <div key={step.id} className="absolute h-full flex">
                {/* Step block */}
                <div
                  className={`h-full flex items-center justify-center text-xs font-medium cursor-pointer transition-colors ${
                    index === currentStepIndex 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-secondary hover:bg-secondary/80'
                  }`}
                  style={{
                    left: `${stepLeft}%`,
                    width: `${(stepDuration / totalDuration) * 100}%`
                  }}
                  onClick={() => onStepChange(index)}
                  title={`${step.name} - ${formatTime(stepDuration)}`}
                >
                  {index + 1}
                </div>
                
                {/* Transition block */}
                {transitionTime > 0 && (
                  <div
                    className="h-full bg-muted-foreground/20 border-l border-r border-muted-foreground/40"
                    style={{
                      left: `${stepLeft + (stepDuration / totalDuration) * 100}%`,
                      width: `${(transitionTime / totalDuration) * 100}%`
                    }}
                    title={`Transição - ${formatTime(transitionTime)}`}
                  />
                )}
              </div>
            );
          })}
          
          {/* Current playback position indicator */}
          {isPlaying && currentStep && (
            <div
              className="absolute top-0 w-0.5 h-full bg-red-500 z-10"
              style={{
                left: `${((drill.steps.slice(0, currentStepIndex).reduce((sum, s) => sum + s.duration + ((s as any).transitionTime || 0), 0) + playbackTime / 1000) / totalDuration) * 100}%`
              }}
            />
          )}
        </div>
        
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>0:00</span>
          <span>{formatTime(totalDuration)}</span>
        </div>
      </div>
    );
  };

  const handleSaveStep = () => {
    if (!editingStep) return;

    handleUpdateStep(editingStep, stepForm);
    setEditingStep(null);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    return formatTime(seconds);
  };

  if (!drill) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Target className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Crie um novo drill para começar
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Play className="h-4 w-4" />
          Sequenciador de Drill
        </CardTitle>
        <CardDescription className="text-xs">
          Gerencie passos e controle a reprodução
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Informações do drill */}
        <div className="space-y-2">
          <div>
            <Label className="text-xs font-medium">Nome do Drill</Label>
            <Input
              value={drill.name}
              onChange={(e) => onDrillUpdate({ ...drill, name: e.target.value })}
              className="text-xs"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs font-medium">Categoria</Label>
              <Select
                value={drill.category}
                onValueChange={(value: any) => onDrillUpdate({ ...drill, category: value })}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tactical">Tático</SelectItem>
                  <SelectItem value="technical">Técnico</SelectItem>
                  <SelectItem value="physical">Físico</SelectItem>
                  <SelectItem value="transition">Transição</SelectItem>
                  <SelectItem value="finishing">Finalização</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label className="text-xs font-medium">Dificuldade</Label>
              <Select
                value={drill.difficulty}
                onValueChange={(value: any) => onDrillUpdate({ ...drill, difficulty: value })}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Iniciante</SelectItem>
                  <SelectItem value="intermediate">Intermediário</SelectItem>
                  <SelectItem value="advanced">Avançado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Controles de reprodução */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Reprodução</Label>
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Velocidade:</span>
              <Select
                value={playbackSpeed.toString()}
                onValueChange={(value) => onSpeedChange(parseFloat(value))}
              >
                <SelectTrigger className="w-16 h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.5">0.5x</SelectItem>
                  <SelectItem value="1">1x</SelectItem>
                  <SelectItem value="1.5">1.5x</SelectItem>
                  <SelectItem value="2">2x</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Opções de reprodução */}
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="loopMode"
                checked={loopMode}
                onChange={(e) => setLoopMode(e.target.checked)}
                className="h-3 w-3"
              />
              <Label htmlFor="loopMode" className="text-xs">
                Modo Loop
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoTransition"
                checked={autoTransition}
                onChange={(e) => setAutoTransition(e.target.checked)}
                className="h-3 w-3"
              />
              <Label htmlFor="autoTransition" className="text-xs">
                Transição Auto
              </Label>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => onStepChange(Math.max(0, currentStepIndex - 1))}
              disabled={currentStepIndex === 0}
            >
              <SkipBack className="h-4 w-4" />
            </Button>
            
            <Button
              variant={isPlaying ? "secondary" : "default"}
              size="icon"
              className="h-8 w-8"
              onClick={() => onPlayStateChange(!isPlaying)}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                onPlayStateChange(false);
                setPlaybackTime(0);
              }}
            >
              <Square className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => onStepChange(Math.min(drill.steps.length - 1, currentStepIndex + 1))}
              disabled={currentStepIndex === drill.steps.length - 1}
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress bar do step atual */}
          {currentStep && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>{formatDuration(playbackTime)}</span>
                <span>{formatTime(currentStep.duration)}</span>
              </div>
              <Progress 
                value={(playbackTime / (currentStep.duration * 1000)) * 100} 
                className="h-2"
              />
            </div>
          )}
        </div>

        <Separator />

        {/* Timeline Visual */}
        <TimelineVisualization />

        <Separator />

        {/* Lista de passos com drag & drop */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Passos do Drill</Label>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleSaveSequence}>
                <Save className="h-3 w-3 mr-1" />
                Salvar Sequência
              </Button>
              <Button variant="outline" size="sm" onClick={handleAddStep}>
                <Plus className="h-3 w-3 mr-1" />
                Adicionar
              </Button>
            </div>
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={drill.steps.map(step => step.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {drill.steps.map((step, index) => (
                  <SortableStepItem
                    key={step.id}
                    step={step}
                    index={index}
                    isActive={index === currentStepIndex}
                    isEditing={editingStep === step.id}
                    onEdit={() => handleEditStep(step)}
                    onSave={handleSaveStep}
                    onCancel={() => setEditingStep(null)}
                    onDelete={() => handleDeleteStep(step.id)}
                    onSelect={() => onStepChange(index)}
                    stepForm={stepForm}
                    onStepFormChange={(updates) => setStepForm(prev => ({ ...prev, ...updates }))}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        {/* Resumo do drill */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>Duração: {formatTime(drill.totalDuration)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>Jogadores: {drill.playersRequired}</span>
            </div>
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>Passos: {drill.steps.length}</span>
            </div>
            <div className="flex items-center gap-1">
              <Settings className="h-3 w-3" />
              <span>Equipamentos: {drill.equipmentNeeded.length}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
