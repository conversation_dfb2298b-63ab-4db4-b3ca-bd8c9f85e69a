import { supabase } from "@/integrations/supabase/client";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";

export interface MasterPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  billing_cycle: 'monthly' | 'yearly';
  max_users: number | null;
  max_players: number | null;
  modules: Record<string, boolean>;
  features: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface CreatePlanData {
  name: string;
  description: string;
  price: number;
  billing_cycle: 'monthly' | 'yearly';
  max_users?: number;
  max_players?: number;
  modules: Record<string, boolean>;
  features: Record<string, any>;
}

export interface UpdatePlanData extends Partial<CreatePlanData> {
  is_active?: boolean;
}

// Buscar todos os planos
export const getMasterPlans = async (): Promise<MasterPlan[]> => {
  try {
    // Data validation
    const { data, error } = await supabase
      .from('master_plans')
      .select('*')
      .order('price', { ascending: true });
      
    if (error) {
      console.error('Database error in getMasterPlans:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao buscar planos: ${error.message}`);
    }
    
    return data || [];
  } catch (error: any) {
    console.error('Erro ao buscar planos:', error);
    if (error.message.includes('Campos não encontrados')) {
      throw error; // Re-throw specific field errors
    }
    throw new Error(error.message || 'Erro ao buscar planos');
  }
};

// Buscar plano por ID
export const getMasterPlanById = async (id: number): Promise<MasterPlan> => {
  try {
    // Data validation
    if (!id || id <= 0) {
      throw new Error('ID do plano é obrigatório e deve ser um número positivo');
    }

    const { data, error } = await supabase
      .from('master_plans')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) {
      console.error('Database error in getMasterPlanById:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      if (error.code === 'PGRST116') {
        throw new Error('Plano não encontrado');
      }
      throw new Error(`Erro ao buscar plano: ${error.message}`);
    }

    if (!data) {
      throw new Error('Plano não encontrado');
    }
    
    return data;
  } catch (error: any) {
    console.error('Erro ao buscar plano:', error);
    if (error.message.includes('Campos não encontrados') || error.message.includes('obrigatório')) {
      throw error; // Re-throw specific validation errors
    }
    throw new Error(error.message || 'Erro ao buscar plano');
  }
};

// Criar novo plano
export const createMasterPlan = async (planData: CreatePlanData): Promise<MasterPlan> => {
  try {
    // Data validation
    if (!planData.name || planData.name.trim().length === 0) {
      throw new Error('Nome do plano é obrigatório');
    }
    if (!planData.description || planData.description.trim().length === 0) {
      throw new Error('Descrição do plano é obrigatória');
    }
    if (planData.price < 0) {
      throw new Error('Preço deve ser um valor positivo');
    }
    if (!['monthly', 'yearly'].includes(planData.billing_cycle)) {
      throw new Error('Ciclo de cobrança deve ser "monthly" ou "yearly"');
    }

    const { data, error } = await supabase
      .from('master_plans')
      .insert({
        ...planData,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
      
    if (error) {
      console.error('Database error in createMasterPlan:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_plans. Verifique se a migração foi aplicada.');
      }
      if (error.code === '23505') {
        throw new Error('Já existe um plano com este nome');
      }
      throw new Error(`Erro ao criar plano: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error('Erro ao criar plano:', error);
    if (error.message.includes('obrigatório') || error.message.includes('deve ser') || error.message.includes('Campos não encontrados')) {
      throw error; // Re-throw specific validation errors
    }
    throw new Error(error.message || 'Erro ao criar plano');
  }
};

// Atualizar plano
export const updateMasterPlan = async (id: number, planData: UpdatePlanData): Promise<MasterPlan> => {
  try {
    const { data, error } = await supabase
      .from('master_plans')
      .update({
        ...planData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao atualizar plano:', error);
    throw error;
  }
};

// Deletar plano
export const deleteMasterPlan = async (id: number): Promise<void> => {
  try {
    await ensureAuthenticated();
    // Verificar se há clubes usando este plano
    const { data: clubsUsingPlan, error: checkError } = await supabase
      .from('club_info')
      .select('id')
      .eq('master_plan_id', id)
      .limit(1);
      
    if (checkError) throw checkError;
    
    if (clubsUsingPlan && clubsUsingPlan.length > 0) {
      throw new Error('Não é possível excluir um plano que está sendo usado por clubes');
    }
    
    const { error } = await supabase
      .from('master_plans')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
  } catch (error) {
    console.error('Erro ao deletar plano:', error);
    throw error;
  }
};

// Ativar/Desativar plano
export const togglePlanStatus = async (id: number, isActive: boolean): Promise<MasterPlan> => {
  try {
    const { data, error } = await supabase
      .from('master_plans')
      .update({ 
        is_active: isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao alterar status do plano:', error);
    throw error;
  }
};

// Buscar estatísticas dos planos
export const getPlanStats = async () => {
  try {
    await ensureAuthenticated();
    const { data: stats, error } = await supabase
      .from('club_info')
      .select(`
        master_plan_id,
        master_plans:master_plan_id (name)
      `)
      .not('master_plan_id', 'is', null);
      
    if (error) throw error;
    
    // Contar clubes por plano
    const planCounts = stats?.reduce((acc: Record<string, number>, club: any) => {
      const planName = club.master_plans?.name || 'Sem Plano';
      acc[planName] = (acc[planName] || 0) + 1;
      return acc;
    }, {}) || {};
    
    return planCounts;
  } catch (error) {
    console.error('Erro ao buscar estatísticas dos planos:', error);
    return {};
  }
};

// Módulos disponíveis no sistema
export const AVAILABLE_MODULES = {
  dashboard: 'Dashboard',
  players: 'Gestão de Atletas',
  matches: 'Partidas',
  trainings: 'Treinamentos',
  medical: 'Área Médica',
  finances: 'Financeiro',
  administrative: 'Administrativo',
  accommodations: 'Alojamentos',
  callups: 'Convocações',
  categories: 'Categorias',
  reports: 'Relatórios',
  analytics: 'Analytics',
  communication: 'Comunicação',
  inventory: 'Estoque',
  billing: 'Cobrança'
};

// Features disponíveis
export const AVAILABLE_FEATURES = {
  max_users: 'Máximo de Usuários',
  max_players: 'Máximo de Atletas',
  storage_limit: 'Limite de Armazenamento (GB)',
  api_calls_limit: 'Limite de Chamadas API',
  custom_branding: 'Marca Personalizada',
  priority_support: 'Suporte Prioritário',
  advanced_reports: 'Relatórios Avançados',
  integrations: 'Integrações',
  backup_frequency: 'Frequência de Backup'
};