import React, { useState, useEffect } from 'react';
import { 
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Building2,
  Users,
  AlertTriangle,
  CheckCircle,
  Pause,
  Play,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  Key
} from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRequireMasterAuth } from '@/hooks/useMasterAuth';
import { 
  getMasterClubs, 
  getAvailablePlans,
  suspendMasterClub,
  reactivateMasterClub,
  changeClubPlan,
  resendWelcomeEmail,
  resetPresidentPassword,
  MasterClub,
  MasterPlan,
  ClubFilters
} from '@/api/masterClubs';
import { CreateClubModal } from '@/components/master/CreateClubModal';
import { EditClubModal } from '@/components/master/EditClubModal';
import { ClubDetailsModal } from '@/components/master/ClubDetailsModal';
import { ResetPresidentPasswordModal } from '@/components/master/ResetPresidentPasswordModal';
import { MasterErrorMessage } from '@/components/master/ErrorMessages';
import { ClubsPageSkeleton } from '@/components/master/SkeletonLoaders';
import { toast } from 'sonner';

export const MasterClubs: React.FC = () => {
  const { masterUser, loading: authLoading } = useRequireMasterAuth(['clubs.view'], ['super_admin', 'admin']);
  
  const [clubs, setClubs] = useState<MasterClub[]>([]);
  const [plans, setPlans] = useState<MasterPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ClubFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  
  // Modais
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingClub, setEditingClub] = useState<MasterClub | null>(null);
  const [viewingClub, setViewingClub] = useState<MasterClub | null>(null);
  const [resetPasswordClub, setResetPasswordClub] = useState<MasterClub | null>(null);

  useEffect(() => {
    if (masterUser) {
      loadData();
    }
  }, [masterUser, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [clubsData, plansData] = await Promise.all([
        getMasterClubs({ ...filters, search: searchTerm }).catch(error => {
          console.error('Error loading clubs:', error);
          throw new Error(`Erro ao carregar clubes: ${error.message}`);
        }),
        getAvailablePlans().catch(error => {
          console.error('Error loading plans:', error);
          // Don't throw for plans - just log and continue with empty array
          return [];
        })
      ]);
      
      // Validate and process clubs data with fallbacks
      const processedClubs = clubsData.map(club => ({
        ...club,
        master_plans: club.master_plans || {
          id: 0,
          name: 'Plano não definido',
          description: null,
          price: 0,
          billing_cycle: 'monthly' as const,
          max_users: 0,
          max_players: 0,
          max_storage_gb: 1,
          modules: {},
          features: {},
          is_active: false,
          is_trial: false,
          trial_days: 0,
          sort_order: 999
        }
      }));
      
      setClubs(processedClubs);
      setPlans(plansData);
      setRetryCount(0); // Reset retry count on success
      
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      setError(error.message || 'Erro ao carregar dados dos clubes');
      
      // Set empty arrays on error to prevent crashessim,
      setClubs([]);
      setPlans([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = async () => {
    setRetryCount(prev => prev + 1);
    await loadData();
  };

  const handleSearch = () => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  const handleFilterChange = (key: keyof ClubFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSuspendClub = async (club: MasterClub) => {
    if (!confirm(`Tem certeza que deseja suspender o clube "${club.name}"?`)) {
      return;
    }

    try {
      await suspendMasterClub(club.id, 'Suspenso manualmente pelo administrador');
      await loadData();
    } catch (error: any) {
      console.error('Error suspending club:', error);
      setError(`Erro ao suspender clube: ${error.message}`);
    }
  };

  const handleReactivateClub = async (club: MasterClub) => {
    if (!confirm(`Tem certeza que deseja reativar o clube "${club.name}"?`)) {
      return;
    }

    try {
      await reactivateMasterClub(club.id);
      await loadData();
    } catch (error: any) {
      console.error('Error reactivating club:', error);
      setError(`Erro ao reativar clube: ${error.message}`);
    }
  };

  const handleChangePlan = async (club: MasterClub, newPlanId: number) => {
    try {
      await changeClubPlan(club.id, newPlanId, 'Alteração manual pelo administrador');
      await loadData();
    } catch (error: any) {
      console.error('Error changing plan:', error);
      setError(`Erro ao alterar plano: ${error.message}`);
    }
  };

  const handleResendWelcomeEmail = async (club: MasterClub) => {
    if (!confirm(`Reenviar email de boas-vindas para "${club.name}"?\n\nUma nova senha temporária será gerada e enviada para ${club.email}.`)) {
      return;
    }

    try {
      setLoading(true);
      await resendWelcomeEmail(club.id);
      // Usar toast se disponível, senão alert
      if (typeof toast !== 'undefined') {
        toast.success(`Email de boas-vindas reenviado para ${club.email}`);
      } else {
        alert(`Email de boas-vindas reenviado para ${club.email}`);
      }
    } catch (error: any) {
      console.error('Error resending email:', error);
      if (typeof toast !== 'undefined') {
        toast.error(error.message || 'Erro ao reenviar email');
      } else {
        alert(`Erro ao reenviar email: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResetPresidentPassword = async (newPassword: string, sendEmail: boolean) => {
    if (!resetPasswordClub) return;

    try {
      await resetPresidentPassword(resetPasswordClub.id, newPassword, sendEmail);
      
      if (typeof toast !== 'undefined') {
        toast.success(
          sendEmail 
            ? `Senha redefinida e email enviado para ${resetPasswordClub.email}`
            : `Senha redefinida com sucesso`
        );
      } else {
        alert(
          sendEmail 
            ? `Senha redefinida e email enviado para ${resetPasswordClub.email}`
            : `Senha redefinida com sucesso`
        );
      }
    } catch (error: any) {
      console.error('Error resetting password:', error);
      throw error; // Re-throw para que o modal possa lidar com o erro
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case 'trial':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspenso</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'current':
        return <Badge className="bg-green-100 text-green-800">Em Dia</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Em Atraso</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Use the enhanced error message component
  const ErrorState = () => (
    <MasterErrorMessage
      error={error || 'Erro desconhecido'}
      onRetry={handleRetry}
      retryCount={retryCount}
      isRetrying={loading}
      showDetails={true}
    />
  );

  // Skeleton loader for stats cards
  const StatsCardSkeleton = () => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-16" />
          </div>
          <Skeleton className="h-8 w-8" />
        </div>
      </CardContent>
    </Card>
  );

  // Skeleton loader for table rows
  const TableRowSkeleton = () => (
    <TableRow>
      <TableCell>
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-3 w-48" />
        </div>
      </TableCell>
      <TableCell>
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-3 w-20" />
        </div>
      </TableCell>
      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
    </TableRow>
  );

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando clubes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestão de Clubes</h1>
          <p className="text-gray-600">
            Gerencie todos os clubes cadastrados na plataforma
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            onClick={() => loadData()}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Clube
          </Button>
        </div>
      </div>

      {/* Error state */}
      {error && <ErrorState />}

      {/* Estatísticas rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {loading ? (
          <>
            <StatsCardSkeleton />
            <StatsCardSkeleton />
            <StatsCardSkeleton />
            <StatsCardSkeleton />
          </>
        ) : (
          <>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total de Clubes</p>
                    <p className="text-2xl font-bold text-gray-900">{clubs.length}</p>
                  </div>
                  <Building2 className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Clubes Ativos</p>
                    <p className="text-2xl font-bold text-green-600">
                      {clubs.filter(c => c.subscription_status === 'active').length}
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Em Trial</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {clubs.filter(c => c.is_trial || (c.master_plans?.is_trial && c.subscription_status === 'trial')).length}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pagamentos em Atraso</p>
                    <p className="text-2xl font-bold text-red-600">
                      {clubs.filter(c => c.payment_status === 'overdue').length}
                    </p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nome, email ou documento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select onValueChange={(value) => handleFilterChange('subscription_status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Status da Assinatura" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Status</SelectItem>
                <SelectItem value="active">Ativo</SelectItem>
                <SelectItem value="trial">Trial</SelectItem>
                <SelectItem value="suspended">Suspenso</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
              </SelectContent>
            </Select>

            <Select onValueChange={(value) => handleFilterChange('payment_status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Status do Pagamento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Status</SelectItem>
                <SelectItem value="current">Em Dia</SelectItem>
                <SelectItem value="overdue">Em Atraso</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
              </SelectContent>
            </Select>

            <Select onValueChange={(value) => handleFilterChange('master_plan_id', value === 'all' ? undefined : parseInt(value))}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Plano" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Planos</SelectItem>
                {plans.map(plan => (
                  <SelectItem key={plan.id} value={plan.id.toString()}>
                    {plan.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button onClick={handleSearch}>
              <Filter className="w-4 h-4 mr-2" />
              Filtrar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de clubes */}
      <Card>
        <CardHeader>
          <CardTitle>Clubes Cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Clube</TableHead>
                <TableHead>Plano</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Pagamento</TableHead>
                <TableHead>Próximo Vencimento</TableHead>
                <TableHead>Criado em</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Show skeleton rows while loading
                [...Array(5)].map((_, i) => <TableRowSkeleton key={i} />)
              ) : (
                clubs.map((club) => (
                  <TableRow key={club.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{club.name}</div>
                        <div className="text-sm text-gray-500">{club.email || 'Email não informado'}</div>
                        {(club.is_trial || (club.master_plans?.is_trial && club.subscription_status === 'trial')) && (
                          <Badge variant="outline" className="mt-1">Trial</Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {club.master_plans?.name || 'Plano não definido'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {club.master_plans?.price ? 
                            `${formatCurrency(club.master_plans.price)}/${club.master_plans.billing_cycle === 'yearly' ? 'ano' : 'mês'}` :
                            'Preço não definido'
                          }
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      {getStatusBadge(club.subscription_status)}
                    </TableCell>
                    
                    <TableCell>
                      {getPaymentStatusBadge(club.payment_status)}
                    </TableCell>
                    
                    <TableCell>
                      {club.next_payment_date ? formatDate(club.next_payment_date) : '-'}
                    </TableCell>
                    
                    <TableCell>
                      {formatDate(club.created_at)}
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Ações</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          
                          <DropdownMenuItem onClick={() => setViewingClub(club)}>
                            <Eye className="w-4 h-4 mr-2" />
                            Ver Detalhes
                          </DropdownMenuItem>
                          
                          <DropdownMenuItem onClick={() => setEditingClub(club)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Editar
                          </DropdownMenuItem>

                          {club.email && (
                           <>
                           <DropdownMenuItem
                             onClick={() => handleResendWelcomeEmail(club)}
                             className="text-blue-600"
                           >
                             <RefreshCw className="w-4 h-4 mr-2" />
                             Reenviar Email
                           </DropdownMenuItem>
                           <DropdownMenuItem
                             onClick={() => setResetPasswordClub(club)}
                             className="text-blue-600"
                           >
                             <Key className="w-4 h-4 mr-2" />
                             Redefinir Senha
                           </DropdownMenuItem>
                         </>                          )}
                          
                          {club.subscription_status === 'active' ? (
                            <DropdownMenuItem 
                              onClick={() => handleSuspendClub(club)}
                              className="text-red-600"
                            >
                              <Pause className="w-4 h-4 mr-2" />
                              Suspender
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => handleReactivateClub(club)}
                              className="text-green-600"
                            >
                              <Play className="w-4 h-4 mr-2" />
                              Reativar
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {!loading && clubs.length === 0 && (
            <div className="text-center py-8">
              <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                {error ? 'Não foi possível carregar os clubes' : 'Nenhum clube encontrado'}
              </p>
              {error && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRetry}
                  className="mt-4"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Tentar Novamente
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modais */}
      {showCreateModal && (
        <CreateClubModal
          plans={plans}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            loadData();
          }}
        />
      )}

      {editingClub && (
        <EditClubModal
          club={editingClub}
          plans={plans}
          onClose={() => setEditingClub(null)}
          onSuccess={() => {
            setEditingClub(null);
            loadData();
          }}
        />
      )}

      {viewingClub && (
        <ClubDetailsModal
          club={viewingClub}
          onClose={() => setViewingClub(null)}
          onEdit={(club) => {
            setViewingClub(null);
            setEditingClub(club);
          }}
        />
      )}

      {resetPasswordClub && (
        <ResetPresidentPasswordModal
          club={resetPasswordClub}
          onClose={() => setResetPasswordClub(null)}
          onConfirm={handleResetPresidentPassword}
        />
      )}
    </div>
  );
};