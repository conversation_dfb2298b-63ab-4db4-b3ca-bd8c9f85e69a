import { supabase } from '@/integrations/supabase/client';
import type { ChatRoom, ChatMessage, ChatRoomParticipant, UserPresence, SendMessageData } from '@/types/chat';

export const chatApi = {
  // Buscar salas do clube
  async getRooms(clubId: string): Promise<ChatRoom[]> {
    const { data, error } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('club_id', clubId)
      .order('updated_at', { ascending: false });

    if (error) throw error;

    return data || [];
  },

  // Buscar mensagens de uma sala
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        user:auth.users!inner(id, email),
        reply_message:chat_messages!reply_to(
          id,
          content,
          user_id
        )
      `)
      .eq('room_id', roomId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return data?.map(msg => ({
      ...msg,
      user: { id: msg.user.id, name: msg.user.email },
      reply_message: msg.reply_message || undefined
    })).reverse() || [];
  },

  // Enviar mensagem
  async sendMessage(messageData: SendMessageData): Promise<ChatMessage> {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        room_id: messageData.room_id,
        content: messageData.content,
        message_type: messageData.message_type || 'text',
        metadata: messageData.metadata,
        reply_to: messageData.reply_to
      })
      .select(`
        *,
        user:auth.users!inner(id, email)
      `)
      .single();

    if (error) throw error;

    return {
      ...data,
      user: { id: data.user.id, name: data.user.email }
    };
  },

  // Editar mensagem
  async editMessage(messageId: string, content: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .update({ 
        content, 
        edited_at: new Date().toISOString() 
      })
      .eq('id', messageId);

    if (error) throw error;
  },

  // Deletar mensagem
  async deleteMessage(messageId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('id', messageId);

    if (error) throw error;
  },

  // Buscar participantes de uma sala
  async getRoomParticipants(roomId: string): Promise<ChatRoomParticipant[]> {
    const { data, error } = await supabase
      .from('chat_room_participants')
      .select(`
        *,
        user:auth.users!inner(id, email)
      `)
      .eq('room_id', roomId);

    if (error) throw error;

    return data?.map(participant => ({
      ...participant,
      user: { id: participant.user.id, name: participant.user.email }
    })) || [];
  },

  // Entrar em uma sala
  async joinRoom(roomId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_room_participants')
      .insert({ room_id: roomId })
      .select()
      .single();

    if (error && !error.message.includes('duplicate')) {
      throw error;
    }
  },

  // Sair de uma sala
  async leaveRoom(roomId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_room_participants')
      .delete()
      .eq('room_id', roomId);

    if (error) throw error;
  },

  // Marcar mensagens como lidas
  async markAsRead(roomId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_room_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('room_id', roomId);

    if (error) throw error;
  },

  // Buscar usuários online do clube
  async getOnlineUsers(clubId: string): Promise<UserPresence[]> {
    const { data, error } = await supabase
      .from('user_presence')
      .select('*')
      .eq('club_id', clubId)
      .neq('status', 'offline');

    if (error) throw error;

    return data || [];
  },

  // Atualizar status de presença
  async updatePresence(status: 'online' | 'away' | 'busy' | 'offline'): Promise<void> {
    const { error } = await supabase
      .from('user_presence')
      .upsert({ 
        status,
        updated_at: new Date().toISOString()
      });

    if (error) throw error;
  },

  // Criar nova sala
  async createRoom(clubId: string, name: string, description?: string): Promise<ChatRoom> {
    const { data, error } = await supabase
      .from('chat_rooms')
      .insert({
        club_id: clubId,
        name,
        description
      })
      .select()
      .single();

    if (error) throw error;

    // Adicionar criador como participante admin
    await supabase
      .from('chat_room_participants')
      .insert({
        room_id: data.id,
        is_admin: true
      });

    return data;
  },

  // Subscrições em tempo real
  subscribeToRoom(roomId: string, onMessage: (message: ChatMessage) => void) {
    return supabase
      .channel(`room:${roomId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${roomId}`
        },
        async (payload) => {
          // Buscar dados completos da mensagem
          const { data } = await supabase
            .from('chat_messages')
            .select(`
              *,
              user:auth.users!inner(id, email)
            `)
            .eq('id', payload.new.id)
            .single();

          if (data) {
            onMessage({
              ...data,
              user: { id: data.user.id, name: data.user.email }
            });
          }
        }
      )
      .subscribe();
  },

  subscribeToPresence(clubId: string, onPresenceChange: (users: UserPresence[]) => void) {
    return supabase
      .channel(`presence:${clubId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
          filter: `club_id=eq.${clubId}`
        },
        async () => {
          // Recarregar lista de usuários online
          const users = await this.getOnlineUsers(clubId);
          onPresenceChange(users);
        }
      )
      .subscribe();
  }
};