import React, { useCallback, useRef, useState } from 'react';
import { useDrag } from 'react-dnd';
import { TrainingElement } from './InteractiveTrainingBuilder';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface DraggableElementProps {
  element: TrainingElement;
  isSelected: boolean;
  fieldDimensions: { width: number; height: number };
  showPlayerNames: boolean;
  onUpdate: (updates: Partial<TrainingElement>) => void;
  onSelect: (multiSelect?: boolean) => void;
  onDelete: () => void;
  drawingMode: 'select' | 'draw' | 'erase';
}

export function DraggableElement({
  element,
  isSelected,
  fieldDimensions,
  showPlayerNames,
  onUpdate,
  onSelect,
  onDelete,
  drawingMode
}: DraggableElementProps) {
  const [isDragging, setIsDragging] = useState(false);

  // Configuração do drag
  const [{ opacity }, drag] = useDrag(() => ({
    type: element.type,
    item: () => {
      setIsDragging(true);
      return { id: element.id, type: element.type };
    },
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.5 : 1,
    }),
    end: (item, monitor) => {
      setIsDragging(false);
      const dropResult = monitor.getDropResult();
      if (dropResult) {
        // Handle drop result if needed
      }
    },
  }));

  const elementRef = useRef<SVGGElement>(null);
  drag(elementRef);

  // Converter posição percentual para pixels
  const pixelPosition = {
    x: (element.position.x / 100) * fieldDimensions.width,
    y: (element.position.y / 100) * fieldDimensions.height,
  };

  const rotation = element.properties.rotation || 0;

  // Handler para movimento do elemento
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (drawingMode === 'erase') {
      onDelete();
      return;
    }
    
    const startX = e.clientX;
    const startY = e.clientY;
    const startPosX = element.position.x;
    const startPosY = element.position.y;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;
      
      const newX = startPosX + (deltaX / fieldDimensions.width) * 100;
      const newY = startPosY + (deltaY / fieldDimensions.height) * 100;
      
      // Limitar às bordas do campo
      const clampedX = Math.max(0, Math.min(100, newX));
      const clampedY = Math.max(0, Math.min(100, newY));
      
      onUpdate({
        position: { x: clampedX, y: clampedY }
      });
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [drawingMode, element.position, fieldDimensions, onUpdate, onDelete]);

  // Handler para seleção
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (drawingMode === 'erase') {
      onDelete();
      return;
    }
    onSelect(e.ctrlKey || e.metaKey);
  }, [drawingMode, onDelete, onSelect]);

  // Handler para duplo clique (edição)
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (drawingMode === 'erase') {
      onDelete();
      return;
    }
    // Aqui você pode abrir um modal de edição
    console.log('Editar elemento:', element);
  }, [drawingMode, element, onDelete]);

  // Renderizar diferentes tipos de elementos
  const renderElement = () => {
    const baseProps = {
      onMouseDown: handleMouseDown,
      onClick: handleClick,
      onDoubleClick: handleDoubleClick,
      style: { opacity },
      className: `cursor-move ${isSelected ? 'ring-2 ring-primary ring-offset-1' : ''}`
    };

    switch (element.type) {
      case 'cone':
        return (
          <g {...baseProps}>
            <polygon
              points="0,-8 -6,8 6,8"
              fill={element.properties.color || '#f97316'}
              stroke="#000"
              strokeWidth="1"
            />
            {element.properties.label && (
              <text
                x="0"
                y="20"
                textAnchor="middle"
                fontSize="10"
                fill="#000"
                fontWeight="bold"
              >
                {element.properties.label}
              </text>
            )}
          </g>
        );

      case 'ball':
        const ballSize = element.properties.size === 'small' ? 4 : 
                        element.properties.size === 'large' ? 8 : 6;
        return (
          <g {...baseProps}>
            <circle
              r={ballSize}
              fill="#ffffff"
              stroke="#000"
              strokeWidth="1"
            />
            <path
              d={`M -${ballSize} 0 Q 0 -${ballSize} ${ballSize} 0 Q 0 ${ballSize} -${ballSize} 0`}
              fill="none"
              stroke="#000"
              strokeWidth="1"
            />
            <path
              d={`M 0 -${ballSize} Q ${ballSize} 0 0 ${ballSize} Q -${ballSize} 0 0 -${ballSize}`}
              fill="none"
              stroke="#000"
              strokeWidth="1"
            />
          </g>
        );

      case 'goal':
        const goalSize = element.properties.size === 'small' ? 15 : 
                        element.properties.size === 'large' ? 35 : 25;
        return (
          <g {...baseProps}>
            <rect
              x={-goalSize/2}
              y={-8}
              width={goalSize}
              height="16"
              fill="none"
              stroke="#8b5cf6"
              strokeWidth="2"
            />
            <path
              d={`M ${-goalSize/2} -8 L ${-goalSize/2-5} -8 L ${-goalSize/2-5} 8 L ${-goalSize/2} 8`}
              fill="none"
              stroke="#8b5cf6"
              strokeWidth="2"
            />
            <path
              d={`M ${goalSize/2} -8 L ${goalSize/2+5} -8 L ${goalSize/2+5} 8 L ${goalSize/2} 8`}
              fill="none"
              stroke="#8b5cf6"
              strokeWidth="2"
            />
          </g>
        );

      case 'player':
        return (
          <g {...baseProps}>
            <circle
              r="12"
              fill={element.properties.color || '#3b82f6'}
              stroke="#fff"
              strokeWidth="2"
            />
            {element.properties.playerNumber ? (
              <text
                x="0"
                y="4"
                textAnchor="middle"
                fontSize="10"
                fill="#fff"
                fontWeight="bold"
              >
                {element.properties.playerNumber}
              </text>
            ) : (
              <>
                <circle cx="0" cy="-3" r="3" fill="#fff" />
                <path
                  d="M -4 5 a4 3 0 0 1 8 0 v1"
                  fill="#fff"
                />
              </>
            )}
            {showPlayerNames && element.properties.playerName && (
              <text
                x="0"
                y="25"
                textAnchor="middle"
                fontSize="8"
                fill="#000"
                fontWeight="bold"
              >
                {element.properties.playerName.split(' ')[0]}
              </text>
            )}
          </g>
        );

      case 'marker':
        return (
          <g {...baseProps}>
            <rect
              x="-8"
              y="-8"
              width="16"
              height="16"
              fill={element.properties.color || '#ef4444'}
              stroke="#000"
              strokeWidth="1"
              rx="2"
            />
            {element.properties.label && (
              <text
                x="0"
                y="20"
                textAnchor="middle"
                fontSize="8"
                fill="#000"
                fontWeight="bold"
              >
                {element.properties.label}
              </text>
            )}
          </g>
        );

      case 'annotation':
        return (
          <g {...baseProps}>
            <rect
              x="-20"
              y="-10"
              width="40"
              height="20"
              fill="#fff"
              stroke="#000"
              strokeWidth="1"
              rx="4"
              fillOpacity="0.9"
            />
            <text
              x="0"
              y="2"
              textAnchor="middle"
              fontSize="8"
              fill="#000"
            >
              {element.properties.text || 'Texto'}
            </text>
          </g>
        );

      default:
        return (
          <g {...baseProps}>
            <circle r="8" fill="#gray" stroke="#000" strokeWidth="1" />
          </g>
        );
    }
  };

  return (
    <g
      ref={elementRef}
      transform={`translate(${pixelPosition.x}, ${pixelPosition.y}) rotate(${rotation})`}
      className={isDragging ? 'pointer-events-none' : ''}
    >
      {renderElement()}
      
      {/* Indicador de seleção */}
      {isSelected && (
        <circle
          r="18"
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          strokeDasharray="4,2"
          opacity="0.7"
        />
      )}
      
      {/* Handles de redimensionamento para elementos selecionados */}
      {isSelected && ['goal', 'marker'].includes(element.type) && (
        <>
          <circle
            cx="15"
            cy="0"
            r="3"
            fill="#3b82f6"
            stroke="#fff"
            strokeWidth="1"
            className="cursor-ew-resize"
          />
          <circle
            cx="-15"
            cy="0"
            r="3"
            fill="#3b82f6"
            stroke="#fff"
            strokeWidth="1"
            className="cursor-ew-resize"
          />
        </>
      )}
    </g>
  );
}