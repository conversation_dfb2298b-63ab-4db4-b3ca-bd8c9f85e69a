import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface ChangePasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isFirstLogin?: boolean;
  onSuccess?: () => void;
}

export function ChangePasswordDialog({
  open,
  onOpenChange,
  isFirstLogin = false,
  onSuccess,
}: ChangePasswordDialogProps) {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleChangePassword = async () => {
    try {
      setError("");
      setLoading(true);

      // Validar campos
      if (!isFirstLogin && !currentPassword) {
        throw new Error("A senha atual é obrigatória");
      }

      if (!newPassword) {
        throw new Error("A nova senha é obrigatória");
      }

      if (newPassword.length < 6) {
        throw new Error("A nova senha deve ter pelo menos 6 caracteres");
      }
      if (newPassword.length > 64) {
        throw new Error("A nova senha deve ter no m\u00e1ximo 64 caracteres");
      }

      if (newPassword !== confirmPassword) {
        throw new Error("As senhas não coincidem");
      }

      // Atualizar senha no Supabase Auth
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) {
        throw new Error(`Erro ao atualizar senha: ${updateError.message}`);
      }

      toast({
        title: "Sucesso",
        description: "Sua senha foi atualizada com sucesso",
      });

      // Limpar campos
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

      // Fechar o modal
      onOpenChange(false);

      // Callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao alterar senha:", err);
      setError(err.message || "Erro ao alterar senha");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isFirstLogin ? "Alterar Senha Temporária" : "Alterar Senha"}
          </DialogTitle>
          <DialogDescription>
            {isFirstLogin
              ? "Você está usando uma senha temporária. Por favor, altere-a para uma senha segura de sua escolha."
              : "Preencha os campos abaixo para alterar sua senha."}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {!isFirstLogin && (
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Senha Atual</Label>
              <Input
                id="currentPassword"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Digite sua senha atual"
                required
                minLength={6}
                maxLength={64}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="newPassword">Nova Senha</Label>
            <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Digite sua nova senha"
                required
                minLength={6}
                maxLength={64}
              />
            </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirme sua nova senha"
                required
                minLength={6}
                maxLength={64}
              />
            </div>

          {error && <div className="text-red-500 text-sm">{error}</div>}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleChangePassword} disabled={loading}>
            {loading ? "Alterando..." : "Alterar Senha"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
