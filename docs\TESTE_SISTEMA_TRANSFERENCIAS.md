# Guia de Teste - Sistema de Transferência de Atletas

## Pré-requisitos

1. ✅ Execute o script de atualização:
```sql
\i sql/update-player-transfer-system.sql
```

2. ✅ Execute o script de teste:
```sql
\i sql/test-transfer-system.sql
```

3. ✅ Migre alguns jogadores para teste:
```sql
\i sql/migrate-sample-players.sql
```

## Cenários de Teste

### 1. Teste de Busca por CPF - Jogador Não Encontrado

1. Acesse a página **Elenco**
2. Clique em **"Adicionar Jogador"**
3. Na aba **"Buscar por CPF"**, digite um CPF inexistente: `123.456.789-01`
4. Clique em **"Buscar"**

**Resultado esperado**: Mensagem "Jogador não encontrado no sistema. Será criado um novo registro."

### 2. Teste de Busca por CPF - Jogador Encontrado e Disponível

1. Execute no banco para pegar um CPF de teste:
```sql
SELECT cpf_number, name, status 
FROM players 
WHERE global_player_id IS NOT NULL 
  AND status = 'inativo' 
LIMIT 1;
```

2. Use esse CPF na busca
3. Clique em **"Buscar"**

**Resultado esperado**: 
- Jogador encontrado
- Dados pré-preenchidos
- Documentos disponíveis (se houver)
- Botão "Transferir Jogador" habilitado

### 3. Teste de Busca por CPF - Jogador Ativo em Outro Clube

1. Execute no banco para pegar um CPF de jogador ativo:
```sql
SELECT cpf_number, name, status 
FROM players 
WHERE global_player_id IS NOT NULL 
  AND status IN ('ativo', 'disponivel') 
LIMIT 1;
```

2. Use esse CPF na busca
3. Clique em **"Buscar"**

**Resultado esperado**:
- Jogador encontrado
- ⚠️ Alerta vermelho: "Jogador já está ativo no clube: [Nome do Clube]"
- Botão "Jogador Ativo em Outro Clube" desabilitado

### 4. Teste de Transferência Completa

1. Marque um jogador como "inativo" em um clube:
```sql
UPDATE players 
SET status = 'inativo' 
WHERE cpf_number = 'CPF_DO_JOGADOR' 
  AND club_id = CLUBE_ATUAL;
```

2. Em outro clube, busque por esse CPF
3. Preencha os dados específicos do clube:
   - Posição: "Atacante"
   - Número: "10"
   - Data de entrada: hoje
   - Observações: "Transferido via sistema"

4. Clique em **"Confirmar Transferência"**

**Resultado esperado**:
- ✅ Mensagem de sucesso
- Jogador criado no novo clube
- Documentos copiados automaticamente
- Badge "Transferido" no card do jogador

### 5. Teste de Validação de CPF

1. Digite CPFs inválidos:
   - `123.456.789-00` (dígito verificador inválido)
   - `111.111.111-11` (todos os dígitos iguais)
   - `123.456.789` (incompleto)

**Resultado esperado**: Mensagem "CPF inválido"

## Verificações no Banco de Dados

### Verificar Jogador Global Criado
```sql
SELECT * FROM global_players WHERE cpf_number = 'CPF_TESTADO';
```

### Verificar Transferência Registrada
```sql
SELECT 
  pt.*,
  gp.name as player_name,
  ci.name as to_club_name
FROM player_transfers pt
JOIN global_players gp ON gp.id = pt.global_player_id
JOIN club_info ci ON ci.id = pt.to_club_id
ORDER BY pt.created_at DESC
LIMIT 5;
```

### Verificar Documentos Copiados
```sql
SELECT 
  gpd.document_type,
  gpd.file_url,
  ci.name as original_club
FROM global_player_documents gpd
JOIN club_info ci ON ci.id = gpd.original_club_id
WHERE gpd.global_player_id = 'GLOBAL_PLAYER_ID';
```

### Verificar Jogador no Novo Clube
```sql
SELECT 
  p.name,
  p.is_transfer,
  p.transfer_id,
  ci.name as club_name
FROM players p
JOIN club_info ci ON ci.id = p.club_id
WHERE p.global_player_id = 'GLOBAL_PLAYER_ID'
ORDER BY p.created_at DESC;
```

## Problemas Comuns e Soluções

### ❌ Erro: "cannot change return type of existing function"
**Solução**: Execute o script de atualização que remove e recria as funções.

### ❌ Jogador não aparece na busca
**Verificar**:
1. CPF está correto no banco
2. Jogador foi migrado para o sistema global
3. Função `search_player_by_cpf` está funcionando

### ❌ Documentos não são copiados
**Verificar**:
1. Permissões do storage
2. URLs dos documentos originais
3. Função `copyPlayerDocuments` na API

### ❌ Badge de transferência não aparece
**Verificar**:
1. Campo `is_transfer` está TRUE
2. Campo `transfer_id` está preenchido
3. Componente `PlayerTransferBadge` foi importado

## Limpeza Após Testes

Para limpar dados de teste:
```sql
-- CUIDADO: Isso remove todos os dados do sistema de transferência
SELECT cleanup_transfer_system();
```

## Próximos Passos

Após os testes básicos funcionarem:

1. **Teste com dados reais** de jogadores
2. **Teste de performance** com muitos jogadores
3. **Teste de concorrência** (dois clubes buscando o mesmo jogador)
4. **Teste de rollback** em caso de erro
5. **Teste de migração completa** de todos os jogadores

## Suporte

Se encontrar problemas:

1. Verifique os logs do Supabase
2. Execute `sql/test-transfer-system.sql`
3. Verifique as políticas RLS
4. Confirme que as funções foram criadas corretamente