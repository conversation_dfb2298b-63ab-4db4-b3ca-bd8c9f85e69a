-- Script SQL para adicionar novas permissões médicas ao sistema
-- Execução: Execute este script no Supabase SQL Editor

-- Atualizar permissões para usuários com papel 'president'
-- Adicionar as novas permissões aos presidentes existentes
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{
  "medical.view": true,
  "medical.create": true,
  "medical.edit": true,
  "medical.delete": true,
  "medical.records.view": true,
  "medical.records.create": true,
  "medical.records.edit": true,
  "medical.records.delete": true,
  "medical.rehabilitation.view": true,
  "medical.rehabilitation.create": true,
  "medical.rehabilitation.edit": true,
  "medical.rehabilitation.delete": true
}'::jsonb
WHERE role = 'president';

-- Atualizar permissões para usuários com papel 'admin'
-- Adicionar as novas permissões aos administradores existentes
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{
  "medical.view": true,
  "medical.create": true,
  "medical.edit": true,
  "medical.delete": true,
  "medical.records.view": true,
  "medical.records.create": true,
  "medical.records.edit": true,
  "medical.records.delete": true,
  "medical.rehabilitation.view": true,
  "medical.rehabilitation.create": true,
  "medical.rehabilitation.edit": true,
  "medical.rehabilitation.delete": true
}'::jsonb
WHERE role = 'admin';

-- Atualizar permissões para usuários com papel 'medical' (médicos)
-- Adicionar as novas permissões aos médicos existentes
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{
  "medical.view": true,
  "medical.create": true,
  "medical.edit": true,
  "medical.delete": true,
  "medical.records.view": true,
  "medical.records.create": true,
  "medical.records.edit": true,
  "medical.records.delete": true,
  "medical.rehabilitation.view": true,
  "medical.rehabilitation.create": true,
  "medical.rehabilitation.edit": true,
  "medical.rehabilitation.delete": true
}'::jsonb
WHERE role = 'medical';

-- Atualizar permissões para usuários com papel 'manager'
-- Adicionar apenas permissões de visualização para gerentes
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{
  "medical.view": true,
  "medical.records.view": true,
  "medical.rehabilitation.view": true
}'::jsonb
WHERE role = 'manager';

-- Verificar quantos usuários foram atualizados por papel
SELECT 
    role,
    COUNT(*) as users_updated,
    COUNT(CASE WHEN permissions ? 'medical.records.view' THEN 1 END) as users_with_records_view,
    COUNT(CASE WHEN permissions ? 'medical.records.create' THEN 1 END) as users_with_records_create,
    COUNT(CASE WHEN permissions ? 'medical.rehabilitation.view' THEN 1 END) as users_with_rehab_view,
    COUNT(CASE WHEN permissions ? 'medical.rehabilitation.create' THEN 1 END) as users_with_rehab_create
FROM club_members 
WHERE role IN ('president', 'admin', 'medical', 'manager')
GROUP BY role
ORDER BY role;

-- Verificar se as permissões foram aplicadas corretamente
SELECT 
    cm.role,
    cm.permissions ? 'medical.records.view' as has_records_view,
    cm.permissions ? 'medical.records.create' as has_records_create,
    cm.permissions ? 'medical.records.edit' as has_records_edit,
    cm.permissions ? 'medical.records.delete' as has_records_delete,
    cm.permissions ? 'medical.rehabilitation.view' as has_rehab_view,
    cm.permissions ? 'medical.rehabilitation.create' as has_rehab_create,
    cm.permissions ? 'medical.rehabilitation.edit' as has_rehab_edit,
    cm.permissions ? 'medical.rehabilitation.delete' as has_rehab_delete
FROM club_members cm
WHERE role IN ('president', 'admin', 'medical', 'manager')
LIMIT 5;