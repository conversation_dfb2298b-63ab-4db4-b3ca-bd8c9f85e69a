# Configurar Vercel - Passo a Passo

## 1. Pegar a SERVICE_ROLE KEY do Supabase

1. Acesse: https://supabase.com/dashboard/project/qoujacltecwxvymynbsh
2. Vá em **Settings** > **API**
3. Copie a chave **service_role** (secret) - NÃO a anon public!

## 2. Adicionar no Vercel via terminal

```bash
cd chat-server
npx vercel env add SUPABASE_SERVICE_KEY
```

Quando perguntar o valor, cole a SERVICE_ROLE KEY que você copiou.
Selecione: Production, Preview, Development

## 3. Redeploy

```bash
npx vercel --prod
```

## 4. Testar

O chat vai funcionar imediatamente! 🚀

---

**DICA**: A SERVICE_ROLE KEY é diferente da ANON_KEY. Ela começa com `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdWphY2x0ZWN3eHZ5bXluYnNoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSI...`