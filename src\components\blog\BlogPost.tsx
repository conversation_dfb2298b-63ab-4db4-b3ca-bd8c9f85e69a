import { ReactNode } from 'react';
import { SEOHead } from '@/components/seo/SEOHead';
import { FAQSection, type FAQItem } from '@/components/blog/FAQSection';
import { LeadMagnet } from '@/components/blog/LeadMagnet';
import { BlogCTA } from '@/components/blog/BlogCTA';
import { generateArticleSchema, generateBreadcrumbSchema } from '@/utils/seo/schemaGenerators';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, Clock, User, Tag } from 'lucide-react';

interface BlogPostProps {
  // SEO Data
  title: string;
  description: string;
  keywords?: string;
  slug: string;
  
  // Content Data
  content: ReactNode;
  publishedAt: string;
  updatedAt?: string;
  author?: string;
  category?: string;
  tags?: string[];
  featuredImage?: string;
  readingTime?: number;
  
  // Components
  faqs?: FAQItem[];
  leadMagnet?: {
    title: string;
    description: string;
    downloadUrl: string;
    fileName: string;
    type: 'planilha' | 'pdf' | 'template' | 'checklist' | 'calculadora';
    preview?: string;
    benefits?: string[];
  };
  
  // CTAs
  ctas?: Array<{
    title: string;
    description: string;
    buttonText?: string;
    variant?: 'trial' | 'demo' | 'contact' | 'download';
    position: 'top' | 'middle' | 'bottom';
  }>;
}

export function BlogPost({
  title,
  description,
  keywords,
  slug,
  content,
  publishedAt,
  updatedAt,
  author = 'Game Day Nexus',
  category,
  tags = [],
  featuredImage,
  readingTime,
  faqs,
  leadMagnet,
  ctas = []
}: BlogPostProps) {
  const url = `/blog/${slug}`;
  const fullImageUrl = featuredImage ? 
    (featuredImage.startsWith('http') ? featuredImage : `https://gamedaynexus.com${featuredImage}`) :
    'https://gamedaynexus.com/images/blog-default-og.jpg';

  // Generate schemas
  const articleSchema = generateArticleSchema({
    headline: title,
    description,
    image: fullImageUrl,
    url: `https://gamedaynexus.com${url}`,
    datePublished: publishedAt,
    dateModified: updatedAt || publishedAt,
    author,
    section: category,
    keywords: tags,
    wordCount: typeof content === 'string' ? content.split(' ').length : undefined
  });

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: 'https://gamedaynexus.com' },
    { name: 'Blog', url: 'https://gamedaynexus.com/blog' },
    ...(category ? [{ name: category, url: `https://gamedaynexus.com/blog/categoria/${category.toLowerCase()}` }] : []),
    { name: title, url: `https://gamedaynexus.com${url}` }
  ]);

  const topCTAs = ctas.filter(cta => cta.position === 'top');
  const middleCTAs = ctas.filter(cta => cta.position === 'middle');
  const bottomCTAs = ctas.filter(cta => cta.position === 'bottom');

  return (
    <>
      <SEOHead
        title={title}
        description={description}
        keywords={keywords}
        image={fullImageUrl}
        url={url}
        type="article"
        publishedTime={publishedAt}
        modifiedTime={updatedAt}
        author={author}
        section={category}
        tags={tags}
        schema={[articleSchema, breadcrumbSchema]}
      />

      <article className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-8">
          {/* Breadcrumbs */}
          <nav className="text-sm text-gray-500 mb-4">
            <span>Home</span>
            <span className="mx-2">›</span>
            <span>Blog</span>
            {category && (
              <>
                <span className="mx-2">›</span>
                <span>{category}</span>
              </>
            )}
            <span className="mx-2">›</span>
            <span className="text-gray-900">{title}</span>
          </nav>

          {/* Title */}
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {title}
          </h1>

          {/* Meta info */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <span>{author}</span>
            </div>
            
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <time dateTime={publishedAt}>
                {new Date(publishedAt).toLocaleDateString('pt-BR')}
              </time>
            </div>
            
            {readingTime && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{readingTime} min de leitura</span>
              </div>
            )}
            
            {category && (
              <div className="flex items-center gap-1">
                <Tag className="h-4 w-4" />
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                  {category}
                </span>
              </div>
            )}
          </div>

          {/* Featured Image */}
          {featuredImage && (
            <div className="mb-8">
              <img
                src={featuredImage}
                alt={title}
                className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                loading="eager"
              />
            </div>
          )}
        </header>

        {/* Top CTAs */}
        {topCTAs.map((cta, index) => (
          <BlogCTA
            key={`top-${index}`}
            title={cta.title}
            description={cta.description}
            buttonText={cta.buttonText}
            variant={cta.variant}
          />
        ))}

        {/* Content */}
        <div className="prose prose-lg prose-gray max-w-none mb-12">
          {content}
        </div>

        {/* Middle CTAs */}
        {middleCTAs.map((cta, index) => (
          <BlogCTA
            key={`middle-${index}`}
            title={cta.title}
            description={cta.description}
            buttonText={cta.buttonText}
            variant={cta.variant}
          />
        ))}

        {/* Lead Magnet */}
        {leadMagnet && (
          <div className="my-12">
            <LeadMagnet
              title={leadMagnet.title}
              description={leadMagnet.description}
              downloadUrl={leadMagnet.downloadUrl}
              fileName={leadMagnet.fileName}
              type={leadMagnet.type}
              preview={leadMagnet.preview}
              benefits={leadMagnet.benefits}
            />
          </div>
        )}

        {/* FAQ Section */}
        {faqs && faqs.length > 0 && (
          <FAQSection faqs={faqs} />
        )}

        {/* Bottom CTAs */}
        {bottomCTAs.map((cta, index) => (
          <BlogCTA
            key={`bottom-${index}`}
            title={cta.title}
            description={cta.description}
            buttonText={cta.buttonText}
            variant={cta.variant}
          />
        ))}

        {/* Tags */}
        {tags.length > 0 && (
          <div className="mt-12 pt-8 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Tags:</h3>
            <div className="flex flex-wrap gap-2">
              {tags.map(tag => (
                <span
                  key={tag}
                  className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Related Posts */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <h3 className="text-2xl font-bold mb-6">Continue Lendo</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h4 className="font-semibold mb-2">
                  <a href="/blog/mensalidades-pix-reduzindo-inadimplencia" className="text-blue-600 hover:text-blue-800">
                    Mensalidades com PIX: Reduzindo Inadimplência
                  </a>
                </h4>
                <p className="text-gray-600 text-sm">
                  Aprenda como implementar pagamentos via PIX e reduzir a inadimplência do seu clube em até 60%.
                </p>
              </CardContent>
            </Card>
            
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h4 className="font-semibold mb-2">
                  <a href="/blog/escalacao-tatica-guia-visual" className="text-blue-600 hover:text-blue-800">
                    Escalação Tática: Guia Visual Completo
                  </a>
                </h4>
                <p className="text-gray-600 text-sm">
                  Domine as principais formações táticas e aprenda a usar o editor visual de escalação.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </article>
    </>
  );
}