import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useCurrentClubId } from '@/context/ClubContext';
import { 
  DollarSign, 
  Calendar, 
  Upload,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  CreditCard,
  FileText,
  Download,
  QrCode,
  Copy
} from 'lucide-react';
import {
  getPlayerMonthlyFees,
  createMonthlyFeePixCharge,
  uploadMonthlyFeeReceipt,
  getMonthlyFeeReceipts,
  PlayerMonthlyFee,
  MonthlyFeeReceipt
} from '@/api/monthlyFees';
import { getPlayerByUserId } from '@/api/players';
import { generatePixQRCode } from '@/utils/pixGenerator';

const MONTHS = [
  'Janeiro', 'Fever<PERSON>', '<PERSON><PERSON><PERSON>', 'Abril', '<PERSON><PERSON>', 'Jun<PERSON>',
  'Jul<PERSON>', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
];

interface PlayerMonthlyFeesPortalProps {
  userId: string;
}

export function PlayerMonthlyFeesPortal({ userId }: PlayerMonthlyFeesPortalProps) {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  
  const [player, setPlayer] = useState<any>(null);
  const [monthlyFees, setMonthlyFees] = useState<PlayerMonthlyFee[]>([]);
  const [receipts, setReceipts] = useState<MonthlyFeeReceipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFee, setSelectedFee] = useState<PlayerMonthlyFee | null>(null);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [pixData, setPixData] = useState<any>(null);
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  // Verificar se userId é válido
  if (!userId || userId === 'undefined') {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
          <p>Usuário não identificado</p>
          <p className="text-sm text-muted-foreground">
            Não foi possível carregar as informações de mensalidades
          </p>
        </CardContent>
      </Card>
    );
  }

  useEffect(() => {
    loadData();
  }, [clubId, userId]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Buscar dados do jogador
      const playerData = await getPlayerByUserId(clubId, userId);
      if (!playerData) {
        throw new Error('Jogador não encontrado');
      }
      setPlayer(playerData);

      // Buscar mensalidades do jogador
      const feesData = await getPlayerMonthlyFees(clubId, {
        player_id: playerData.id
      });
      setMonthlyFees(feesData);

      // Buscar comprovantes
      const receiptsData = await getMonthlyFeeReceipts(clubId, {
        player_id: playerData.id
      });
      setReceipts(receiptsData);
      
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível carregar suas mensalidades',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShowPayment = async (fee: PlayerMonthlyFee) => {
    try {
      setSelectedFee(fee);
      
      // Criar/buscar cobrança PIX
      const pixResponse = await createMonthlyFeePixCharge(clubId, fee.id);
      setPixData(pixResponse);

      // Gerar QR Code
      const qrCode = await generatePixQRCode({
        pixKey: pixResponse.pix_code.split('|')[0] || '', // Extrair chave PIX do código
        amount: fee.final_amount || fee.amount,
        description: `${fee.fee_setting_name} - ${fee.reference_month}/${fee.reference_year}`,
        merchantName: 'CLUBE DE FUTEBOL',
        merchantCity: 'SAO PAULO'
      });
      setQrCodeDataURL(qrCode);
      
      setIsPaymentDialogOpen(true);
    } catch (error: any) {
      console.error('Erro ao gerar pagamento:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao gerar dados de pagamento',
        variant: 'destructive'
      });
    }
  };

  const handleUploadReceipt = async () => {
    if (!uploadFile || !selectedFee) return;

    try {
      setUploading(true);
      await uploadMonthlyFeeReceipt(clubId, selectedFee.id, player.id, uploadFile);
      
      toast({
        title: 'Sucesso',
        description: 'Comprovante enviado com sucesso! Aguarde a análise.'
      });
      
      setIsUploadDialogOpen(false);
      setUploadFile(null);
      loadData(); // Recarregar dados
    } catch (error: any) {
      console.error('Erro ao enviar comprovante:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao enviar comprovante',
        variant: 'destructive'
      });
    } finally {
      setUploading(false);
    }
  };

  const handleCopyPixCode = () => {
    if (pixData?.pix_code) {
      navigator.clipboard.writeText(pixData.pix_code);
      toast({
        title: 'Copiado!',
        description: 'Código PIX copiado para a área de transferência'
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Pago</Badge>;
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />Em Atraso</Badge>;
      case 'cancelled':
        return <Badge variant="outline"><XCircle className="w-3 h-3 mr-1" />Cancelado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getReceiptStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-500">Aprovado</Badge>;
      case 'pending':
        return <Badge variant="secondary">Analisando</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejeitado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="flex justify-center items-center h-20">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!player) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
          <p>Jogador não encontrado ou sem acesso às mensalidades</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com informações do jogador */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Minhas Mensalidades
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div>
              <h3 className="font-semibold">{player.name}</h3>
              <p className="text-sm text-muted-foreground">
                {monthlyFees.length} mensalidade(s) encontrada(s)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de mensalidades */}
      <div className="space-y-4">
        {monthlyFees.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma mensalidade encontrada</p>
              <p className="text-sm text-muted-foreground">
                Suas mensalidades aparecerão aqui quando forem geradas
              </p>
            </CardContent>
          </Card>
        ) : (
          monthlyFees.map((fee) => {
            const feeReceipts = receipts.filter(r => r.monthly_fee_id === fee.id);
            const latestReceipt = feeReceipts[0]; // Mais recente primeiro
            
            return (
              <Card key={fee.id} className={isOverdue(fee.due_date) && fee.status !== 'paid' ? 'border-red-200' : ''}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">{fee.fee_setting_name}</h3>
                      <p className="text-muted-foreground">
                        {MONTHS[fee.reference_month - 1]} de {fee.reference_year}
                      </p>
                      {fee.category_name && (
                        <Badge variant="outline" className="mt-1">
                          {fee.category_name}
                        </Badge>
                      )}
                    </div>
                    <div className="text-right">
                      {getStatusBadge(fee.status)}
                      <div className="mt-2">
                        <div className="text-2xl font-bold">
                          {formatCurrency(fee.final_amount || fee.amount)}
                        </div>
                        {fee.late_fee_applied > 0 && (
                          <div className="text-sm text-red-600">
                            +{formatCurrency(fee.late_fee_applied)} multa
                          </div>
                        )}
                        {fee.discount_applied > 0 && (
                          <div className="text-sm text-green-600">
                            -{formatCurrency(fee.discount_applied)} desconto
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Vencimento</p>
                      <p className={`font-medium ${isOverdue(fee.due_date) && fee.status !== 'paid' ? 'text-red-600' : ''}`}>
                        {formatDate(fee.due_date)}
                        {isOverdue(fee.due_date) && fee.status !== 'paid' && (
                          <span className="ml-2 text-xs">(Em atraso)</span>
                        )}
                      </p>
                    </div>

                    {fee.paid_at && (
                      <div>
                        <p className="text-sm text-muted-foreground">Pago em</p>
                        <p className="font-medium text-green-600">
                          {formatDate(fee.paid_at)}
                        </p>
                      </div>
                    )}

                    {latestReceipt && (
                      <div>
                        <p className="text-sm text-muted-foreground">Último comprovante</p>
                        <div className="flex items-center gap-2">
                          {getReceiptStatusBadge(latestReceipt.status)}
                          <span className="text-xs text-muted-foreground">
                            {formatDate(latestReceipt.uploaded_at)}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {fee.status !== 'paid' && (
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleShowPayment(fee)}
                        className="flex-1"
                      >
                        <CreditCard className="w-4 h-4 mr-2" />
                        Ver Dados de Pagamento
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedFee(fee);
                          setIsUploadDialogOpen(true);
                        }}
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Enviar Comprovante
                      </Button>
                    </div>
                  )}

                  {/* Mostrar comprovantes enviados */}
                  {feeReceipts.length > 0 && (
                    <div className="mt-4 pt-4 border-t">
                      <h4 className="font-medium mb-2">Comprovantes Enviados</h4>
                      <div className="space-y-2">
                        {feeReceipts.map((receipt) => (
                          <div key={receipt.id} className="flex items-center justify-between p-2 bg-muted rounded">
                            <div className="flex items-center gap-2">
                              <FileText className="w-4 h-4" />
                              <span className="text-sm">{receipt.file_name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {getReceiptStatusBadge(receipt.status)}
                              <span className="text-xs text-muted-foreground">
                                {formatDate(receipt.uploaded_at)}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Dialog de pagamento */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Dados para Pagamento</DialogTitle>
          </DialogHeader>
          
          {selectedFee && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="font-semibold">{selectedFee.fee_setting_name}</h3>
                <p className="text-muted-foreground">
                  {MONTHS[selectedFee.reference_month - 1]}/{selectedFee.reference_year}
                </p>
                <div className="text-2xl font-bold mt-2">
                  {formatCurrency(selectedFee.final_amount || selectedFee.amount)}
                </div>
              </div>

              {qrCodeDataURL && (
                <div className="text-center">
                  <p className="text-sm font-medium mb-2">QR Code PIX</p>
                  <img 
                    src={qrCodeDataURL} 
                    alt="QR Code PIX" 
                    className="mx-auto border rounded-lg"
                    style={{ maxWidth: '200px' }}
                  />
                </div>
              )}

              {pixData?.pix_code && (
                <div>
                  <p className="text-sm font-medium mb-2">Código PIX (Copia e Cola)</p>
                  <div className="flex gap-2">
                    <div className="flex-1 p-2 bg-muted rounded text-xs font-mono break-all">
                      {pixData.pix_code}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyPixCode}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                <p>• Abra o app do seu banco</p>
                <p>• Escolha a opção PIX</p>
                <p>• Escaneie o QR Code ou cole o código</p>
                <p>• Confirme o pagamento</p>
                <p>• Envie o comprovante através do botão "Enviar Comprovante"</p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
              Fechar
            </Button>
            <Button
              onClick={() => {
                setIsPaymentDialogOpen(false);
                setIsUploadDialogOpen(true);
              }}
            >
              <Upload className="w-4 h-4 mr-2" />
              Enviar Comprovante
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de upload de comprovante */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enviar Comprovante de Pagamento</DialogTitle>
          </DialogHeader>
          
          {selectedFee && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">{selectedFee.fee_setting_name}</h3>
                <p className="text-muted-foreground">
                  {MONTHS[selectedFee.reference_month - 1]}/{selectedFee.reference_year} - {formatCurrency(selectedFee.final_amount || selectedFee.amount)}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Selecione o comprovante
                </label>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                  className="w-full p-2 border rounded"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Formatos aceitos: JPG, PNG, PDF (máx. 10MB)
                </p>
              </div>

              {uploadFile && (
                <div className="p-2 bg-muted rounded">
                  <p className="text-sm">
                    <strong>Arquivo selecionado:</strong> {uploadFile.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Tamanho: {(uploadFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setIsUploadDialogOpen(false);
                setUploadFile(null);
              }}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleUploadReceipt}
              disabled={!uploadFile || uploading}
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enviando...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Enviar Comprovante
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}