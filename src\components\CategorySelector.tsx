import { useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { useCurrentClubId } from "@/context/ClubContext";

interface CategorySelectorProps {
  onCategoryChange: (categoryId: number | null) => void;
  className?: string;
  defaultValue?: string;
}

export function CategorySelector({ onCategoryChange, className, defaultValue }: CategorySelectorProps) {
  const clubId = useCurrentClubId();
  const { categories, selectedCategory, fetchCategories, setSelectedCategory, loading } = useCategoriesStore();

  useEffect(() => {
    fetchCategories(clubId);
  }, [clubId, fetchCategories]);

  const handleCategoryChange = (value: string) => {
    if (value === "all") {
      setSelectedCategory(null);
      onCategoryChange(null);

      // Disparar um evento personalizado para notificar outras partes da aplicação
      const event = new CustomEvent('categoryChanged', { detail: { categoryId: null } });
      window.dispatchEvent(event);
    } else {
      const category = categories.find(c => c.id.toString() === value);
      if (category) {
        setSelectedCategory(category);
        onCategoryChange(category.id);

        // Disparar um evento personalizado para notificar outras partes da aplicação
        const event = new CustomEvent('categoryChanged', { detail: { categoryId: category.id } });
        window.dispatchEvent(event);
      }
    }
  };

  return (
    <Select
      value={selectedCategory ? selectedCategory.id.toString() : defaultValue || "all"}
      onValueChange={handleCategoryChange}
      disabled={loading}
      className={className}
    >
      <SelectTrigger className="h-8 text-xs sm:text-sm bg-white/90 hover:bg-white text-black border-gray-300">
        <SelectValue placeholder="Cat." />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">Todas</SelectItem>
        {categories.map((category) => (
          <SelectItem key={category.id} value={category.id.toString()}>
            {category.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
