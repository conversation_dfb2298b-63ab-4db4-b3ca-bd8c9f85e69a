import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { userId } = await req.json()

    if (!userId) {
      throw new Error('userId é obrigatório')
    }

    // 1. Get user data before deletion for audit log
    const { data: userData, error: getUserError } = await supabaseAdmin
      .from('master_users')
      .select('*')
      .eq('id', userId)
      .single()

    if (getUserError) {
      throw new Error(`Usuário não encontrado: ${getUserError.message}`)
    }

    // 2. Delete from master_users table first
    const { error: deleteUserError } = await supabaseAdmin
      .from('master_users')
      .delete()
      .eq('id', userId)

    if (deleteUserError) {
      throw new Error(`Erro ao excluir usuário da tabela master: ${deleteUserError.message}`)
    }

    // 3. Delete from auth.users
    const { error: deleteAuthError } = await supabaseAdmin.auth.admin.deleteUser(userId)

    if (deleteAuthError) {
      console.warn('Erro ao excluir usuário do auth (pode já ter sido excluído):', deleteAuthError)
      // Don't throw error here as the user might already be deleted from auth
    }

    // 4. Log the action in audit logs
    await supabaseAdmin
      .from('master_audit_logs')
      .insert({
        user_id: userId, // The deleted user (in production, use the current user's ID)
        action: 'delete_master_user',
        entity_type: 'master_user',
        entity_id: userId,
        old_values: {
          name: userData.name,
          email: userData.email,
          role: userData.role,
          is_active: userData.is_active
        },
        details: {
          message: 'Usuário master excluído',
          deleted_by: 'system' // In production, pass the deleter's ID
        }
      })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Usuário master excluído com sucesso'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Erro ao excluir usuário master:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})