import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { FileUploader } from "@/components/ui/file-uploader";
import {
  getEvaluationPaymentByToken,
  uploadPaymentReceipt,
  EvaluationPayment
} from "@/api/evaluationPayments";
import { Upload, CheckCircle, AlertCircle } from "lucide-react";

export default function PaymentReceipt() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const token = searchParams.get("token") || "";

  const [payment, setPayment] = useState<EvaluationPayment | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load payment data
  useEffect(() => {
    const loadPayment = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!token) {
          setError("Token de pagamento não fornecido");
          return;
        }

        const paymentData = await getEvaluationPaymentByToken(token);

        if (!paymentData) {
          setError("Token de pagamento inválido ou expirado");
          return;
        }

        if (paymentData.status !== 'pending') {
          setError("Este pagamento já foi processado");
          return;
        }

        setPayment(paymentData);
      } catch (err: any) {
        console.error("Erro ao carregar pagamento:", err);
        setError(err.message || "Erro ao carregar dados do pagamento");
      } finally {
        setLoading(false);
      }
    };

    loadPayment();
  }, [token]);

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!receiptFile) {
      toast({
        title: "Erro",
        description: "Selecione um arquivo para enviar",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      await uploadPaymentReceipt(token, receiptFile);

      toast({
        title: "Sucesso",
        description: "Comprovante enviado com sucesso! O clube irá verificar o pagamento em breve.",
      });

      // Redirect to success page
      navigate("/payment-receipt-success");

    } catch (err: any) {
      console.error("Erro ao enviar comprovante:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar comprovante",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container max-w-2xl py-10">
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">Carregando dados do pagamento...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error if token is invalid
  if (error || !payment) {
    return (
      <div className="container max-w-2xl py-10">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-red-600 flex items-center justify-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Erro no Pagamento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="mb-4">{error || "Não foi possível carregar os dados do pagamento."}</p>
              <p>Entre em contato com o clube para obter assistência.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show upload form
  return (
    <div className="container max-w-2xl py-10">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Enviar Comprovante de Pagamento
          </CardTitle>
          <CardDescription>
            Envie o comprovante do pagamento PIX para confirmar sua transação.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Payment Details */}
          <div className="mb-6 p-4 bg-muted rounded-lg">
            <h3 className="font-medium mb-2">Detalhes do Pagamento</h3>
            <div className="space-y-1 text-sm">
              <p><strong>Atleta:</strong> {payment.player?.name}</p>
              <p><strong>Valor:</strong> {formatCurrency(payment.amount)}</p>
              <p><strong>Período:</strong> {payment.period_description}</p>
              <p><strong>Chave PIX:</strong> {payment.pix_key}</p>
            </div>
          </div>

          {/* Upload Form */}
          <form onSubmit={handleUpload} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="receipt">Comprovante de Pagamento*</Label>
              <FileUploader
                id="receipt"
                accept=".pdf,.jpg,.jpeg,.png"
                maxSize={5}
                onFileSelect={setReceiptFile}
                currentFile={receiptFile}
              />
              <p className="text-xs text-muted-foreground">
                Aceitos: PDF, JPG, PNG (máximo 5MB)
              </p>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={uploading || !receiptFile}
                className="flex items-center gap-2"
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Enviando...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    Enviar Comprovante
                  </>
                )}
              </Button>
            </div>
          </form>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Instruções:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Certifique-se de que o comprovante está legível</li>
              <li>• O valor deve corresponder exatamente ao solicitado</li>
              <li>• Após o envio, o clube verificará o pagamento</li>
              <li>• Você receberá uma confirmação por email</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}