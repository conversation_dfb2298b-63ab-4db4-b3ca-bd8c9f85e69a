import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { createBillingTransaction } from "./billing";
import { generatePixString } from "@/utils/pixGenerator";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";

// Types
export interface MonthlyFeeSetting {
  id: number;
  club_id: number;
  category_id?: number;
  name: string;
  amount: number;
  due_day: number;
  reminder_days_before: number;
  late_fee_percentage: number;
  discount_percentage: number;
  discount_days_before: number;
  is_active: boolean;
  pix_key?: string;
  description?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  // Campos adicionais para joins
  category_name?: string;
}

export interface PlayerMonthlyFee {
  id: number;
  club_id: number;
  player_id: string;
  fee_setting_id: number;
  reference_month: number;
  reference_year: number;
  amount: number;
  due_date: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  paid_at?: string;
  payment_method?: string;
  billing_transaction_id?: number;
  late_fee_applied: number;
  discount_applied: number;
  final_amount?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  // Campos adicionais para joins
  player_name?: string;
  player_email?: string;
  fee_setting_name?: string;
  category_name?: string;
}

export interface MonthlyFeeReceipt {
  id: number;
  club_id: number;
  monthly_fee_id: number;
  player_id: string;
  file_path: string;
  file_name: string;
  file_size?: number;
  mime_type?: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: string;
  reviewed_at?: string;
  review_notes?: string;
  uploaded_at: string;
  // Campos adicionais para joins
  player_name?: string;
  monthly_fee_reference?: string;
}

export interface CreateMonthlyFeeSettingData {
  category_id?: number;
  name: string;
  amount: number;
  due_day: number;
  reminder_days_before?: number;
  late_fee_percentage?: number;
  discount_percentage?: number;
  discount_days_before?: number;
  pix_key?: string;
  description?: string;
}

export interface MonthlyFeeEmailLog {
  id: number;
  club_id: number;
  monthly_fee_id?: number;
  player_id: string;
  email_type: 'reminder' | 'overdue' | 'payment_link' | 'receipt_confirmation';
  recipient_email: string;
  subject: string;
  sent_at: string;
  success: boolean;
  error_message?: string;
  email_data?: any;
}

// Configurações de Mensalidades
export const getMonthlyFeeSettings = async (clubId: number): Promise<MonthlyFeeSetting[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_settings')
    .select(`
      *,
      categories:category_id (
        name
      )
    `)
    .eq('club_id', clubId)
    .order('name');

  if (error) throw error;

  return (data || []).map(item => ({
    ...item,
    category_name: item.categories?.name || null
  }));
};

export const createMonthlyFeeSetting = async (
  clubId: number,
  settingData: CreateMonthlyFeeSettingData
): Promise<MonthlyFeeSetting> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_settings')
    .insert({
      club_id: clubId,
      ...settingData
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const updateMonthlyFeeSetting = async (
  clubId: number,
  id: number,
  updates: Partial<CreateMonthlyFeeSettingData>
): Promise<MonthlyFeeSetting> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_settings')
    .update(updates)
    .eq('id', id)
    .eq('club_id', clubId)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const deleteMonthlyFeeSetting = async (clubId: number, id: number): Promise<void> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { error } = await supabaseWithClubId
    .from('monthly_fee_settings')
    .delete()
    .eq('id', id)
    .eq('club_id', clubId);

  if (error) throw error;
};

// Mensalidades dos Jogadores
export const getPlayerMonthlyFees = async (
  clubId: number,
  filters?: {
    player_id?: string;
    status?: string;
    reference_month?: number;
    reference_year?: number;
    category_id?: number;
  }
): Promise<PlayerMonthlyFee[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  let query = supabaseWithClubId
    .from('player_monthly_fees')
    .select(`
      *,
      players:player_id (
        name,
        email
      ),
      monthly_fee_settings:fee_setting_id (
        name,
        categories:category_id (
          name
        )
      )
    `)
    .eq('club_id', clubId);

  if (filters?.player_id) {
    query = query.eq('player_id', filters.player_id);
  }

  if (filters?.status) {
    query = query.eq('status', filters.status);
  }

  if (filters?.reference_month) {
    query = query.eq('reference_month', filters.reference_month);
  }

  if (filters?.reference_year) {
    query = query.eq('reference_year', filters.reference_year);
  }

  const { data, error } = await query.order('due_date', { ascending: false });

  if (error) throw error;

  return (data || []).map(item => ({
    ...item,
    player_name: item.players?.name || 'Jogador não encontrado',
    player_email: item.players?.email || null,
    fee_setting_name: item.monthly_fee_settings?.name || 'Configuração não encontrada',
    category_name: item.monthly_fee_settings?.categories?.name || null
  }));
};

export const generateMonthlyFeesForMonth = async (
  clubId: number,
  year: number,
  month: number
): Promise<number> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .rpc('generate_monthly_fees_for_month', {
      p_club_id: clubId,
      p_year: year,
      p_month: month
    });

  if (error) throw error;
  return data || 0;
};

export const updateOverdueMonthlyFees = async (clubId: number): Promise<number> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .rpc('update_overdue_monthly_fees', {
      p_club_id: clubId
    });

  if (error) throw error;
  return data || 0;
};

export const markMonthlyFeeAsPaid = async (
  clubId: number,
  monthlyFeeId: number,
  paymentMethod: string = 'pix',
  billingTransactionId?: number
): Promise<boolean> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .rpc('mark_monthly_fee_as_paid', {
      p_club_id: clubId,
      p_monthly_fee_id: monthlyFeeId,
      p_payment_method: paymentMethod,
      p_billing_transaction_id: billingTransactionId
    });

  if (error) throw error;
  return data || false;
};

// Criar cobrança PIX para mensalidade
export const createMonthlyFeePixCharge = async (
  clubId: number,
  monthlyFeeId: number
): Promise<{ billing_transaction_id: number; pix_code: string; qr_code_data: string }> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  // Buscar dados da mensalidade
  const { data: monthlyFee, error: feeError } = await supabaseWithClubId
    .from('player_monthly_fees')
    .select(`
      *,
      players:player_id (
        name,
        email
      ),
      monthly_fee_settings:fee_setting_id (
        name,
        pix_key
      )
    `)
    .eq('id', monthlyFeeId)
    .eq('club_id', clubId)
    .single();

  if (feeError) throw feeError;
  if (!monthlyFee) throw new Error('Mensalidade não encontrada');

  // Verificar se já existe uma transação de billing
  if (monthlyFee.billing_transaction_id) {
    const { data: existingTransaction } = await supabaseWithClubId
      .from('billing_transactions')
      .select('id, qr_code_data')
      .eq('id', monthlyFee.billing_transaction_id)
      .single();

    if (existingTransaction) {
      return {
        billing_transaction_id: existingTransaction.id,
        pix_code: existingTransaction.qr_code_data || '',
        qr_code_data: existingTransaction.qr_code_data || ''
      };
    }
  }

  // Buscar chave PIX (da configuração ou do clube)
  let pixKey = monthlyFee.monthly_fee_settings?.pix_key;

  if (!pixKey) {
    await ensureAuthenticated();
    const { data: clubInfo } = await supabaseWithClubId
      .from('club_info')
      .select('pix_key')
      .eq('id', clubId)
      .single();

    pixKey = clubInfo?.pix_key;
  }

  if (!pixKey) {
    throw new Error('Chave PIX não configurada para este clube');
  }

  // Calcular valor final (com multas/descontos se aplicável)
  const currentDate = new Date();
  const dueDate = new Date(monthlyFee.due_date);
  let finalAmount = monthlyFee.amount;
  let lateFeApplied = 0;
  let discountApplied = 0;

  // Aplicar multa se em atraso
  if (currentDate > dueDate && monthlyFee.monthly_fee_settings?.late_fee_percentage > 0) {
    lateFeApplied = (monthlyFee.amount * monthlyFee.monthly_fee_settings.late_fee_percentage) / 100;
    finalAmount += lateFeApplied;
  }

  // Aplicar desconto se pagamento antecipado
  const discountDaysBefore = monthlyFee.monthly_fee_settings?.discount_days_before || 0;
  const discountPercentage = monthlyFee.monthly_fee_settings?.discount_percentage || 0;

  if (discountDaysBefore > 0 && discountPercentage > 0) {
    const discountDeadline = new Date(dueDate);
    discountDeadline.setDate(discountDeadline.getDate() - discountDaysBefore);

    if (currentDate <= discountDeadline) {
      discountApplied = (monthlyFee.amount * discountPercentage) / 100;
      finalAmount -= discountApplied;
    }
  }

  // Gerar string PIX
  const pixData = {
    pixKey,
    amount: finalAmount,
    description: `${monthlyFee.monthly_fee_settings?.name} - ${monthlyFee.reference_month}/${monthlyFee.reference_year}`,
    merchantName: 'CLUBE DE FUTEBOL',
    merchantCity: 'SAO PAULO'
  };

  const qrCodeData = generatePixString(pixData);

  // Criar transação de billing
  const billingTransaction = await createBillingTransaction(clubId, {
    type: 'recebe',
    entity_type: 'player',
    entity_id: parseInt(monthlyFee.player_id),
    entity_name: monthlyFee.players?.name || 'Jogador',
    pix_key: pixKey,
    amount: finalAmount,
    description: pixData.description,
    due_date: monthlyFee.due_date,
    qr_code_data: qrCodeData
  });

  // Atualizar mensalidade com os valores calculados e link para billing
  await supabaseWithClubId
    .from('player_monthly_fees')
    .update({
      billing_transaction_id: billingTransaction.id,
      late_fee_applied: lateFeApplied,
      discount_applied: discountApplied,
      final_amount: finalAmount,
      updated_at: new Date().toISOString()
    })
    .eq('id', monthlyFeeId);

  return {
    billing_transaction_id: billingTransaction.id,
    pix_code: qrCodeData,
    qr_code_data: qrCodeData
  };
};

// Comprovantes de Pagamento
export const getMonthlyFeeReceipts = async (
  clubId: number,
  filters?: {
    monthly_fee_id?: number;
    player_id?: string;
    status?: string;
  }
): Promise<MonthlyFeeReceipt[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  let query = supabaseWithClubId
    .from('monthly_fee_receipts')
    .select(`
      *,
      players:player_id (
        name
      ),
      player_monthly_fees:monthly_fee_id (
        reference_month,
        reference_year,
        monthly_fee_settings:fee_setting_id (
          name
        )
      )
    `)
    .eq('club_id', clubId);

  if (filters?.monthly_fee_id) {
    query = query.eq('monthly_fee_id', filters.monthly_fee_id);
  }

  if (filters?.player_id) {
    query = query.eq('player_id', filters.player_id);
  }

  if (filters?.status) {
    query = query.eq('status', filters.status);
  }

  const { data, error } = await query.order('uploaded_at', { ascending: false });

  if (error) throw error;

  return (data || []).map(item => ({
    ...item,
    player_name: item.players?.name || 'Jogador não encontrado',
    monthly_fee_reference: item.player_monthly_fees
      ? `${item.player_monthly_fees.monthly_fee_settings?.name} - ${item.player_monthly_fees.reference_month}/${item.player_monthly_fees.reference_year}`
      : 'Mensalidade não encontrada'
  }));
};

export const uploadMonthlyFeeReceipt = async (
  clubId: number,
  monthlyFeeId: number,
  playerId: string,
  file: File
): Promise<MonthlyFeeReceipt> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  // Upload do arquivo para o Supabase Storage
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = `monthly-fee-receipts/${clubId}/${playerId}/${fileName}`;

  const { error: uploadError } = await supabaseWithClubId.storage
    .from('profileimages')
    .upload(filePath, file);

  if (uploadError) throw uploadError;

  // Salvar registro no banco
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_receipts')
    .insert({
      club_id: clubId,
      monthly_fee_id: monthlyFeeId,
      player_id: playerId,
      file_path: filePath,
      file_name: file.name,
      file_size: file.size,
      mime_type: file.type
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const reviewMonthlyFeeReceipt = async (
  clubId: number,
  receiptId: number,
  status: 'approved' | 'rejected',
  reviewNotes?: string
): Promise<MonthlyFeeReceipt> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_receipts')
    .update({
      status,
      review_notes: reviewNotes,
      reviewed_at: new Date().toISOString()
    })
    .eq('id', receiptId)
    .eq('club_id', clubId)
    .select()
    .single();

  if (error) throw error;

  // Se aprovado, marcar mensalidade como paga
  if (status === 'approved') {
    const receipt = data;
    await markMonthlyFeeAsPaid(clubId, receipt.monthly_fee_id, 'comprovante');
  }

  return data;
};

// Log de Emails
export const logMonthlyFeeEmail = async (
  clubId: number,
  emailData: Omit<MonthlyFeeEmailLog, 'id' | 'club_id' | 'sent_at'>
): Promise<MonthlyFeeEmailLog> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('monthly_fee_email_log')
    .insert({
      club_id: clubId,
      ...emailData
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const getMonthlyFeeEmailLogs = async (
  clubId: number,
  filters?: {
    player_id?: string;
    email_type?: string;
    success?: boolean;
  }
): Promise<MonthlyFeeEmailLog[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  let query = supabaseWithClubId
    .from('monthly_fee_email_log')
    .select('*')
    .eq('club_id', clubId);

  if (filters?.player_id) {
    query = query.eq('player_id', filters.player_id);
  }

  if (filters?.email_type) {
    query = query.eq('email_type', filters.email_type);
  }

  if (filters?.success !== undefined) {
    query = query.eq('success', filters.success);
  }

  const { data, error } = await query.order('sent_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

// Estatísticas
export const getMonthlyFeeStats = async (
  clubId: number,
  year?: number,
  month?: number
) => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  let query = supabaseWithClubId
    .from('player_monthly_fees')
    .select('status, amount, final_amount')
    .eq('club_id', clubId);

  if (year) {
    query = query.eq('reference_year', year);
  }

  if (month) {
    query = query.eq('reference_month', month);
  }

  const { data, error } = await query;

  if (error) throw error;

  const stats = {
    total: data?.length || 0,
    paid: data?.filter(f => f.status === 'paid').length || 0,
    pending: data?.filter(f => f.status === 'pending').length || 0,
    overdue: data?.filter(f => f.status === 'overdue').length || 0,
    cancelled: data?.filter(f => f.status === 'cancelled').length || 0,
    total_amount: data?.reduce((sum, f) => sum + (f.final_amount || f.amount), 0) || 0,
    paid_amount: data?.filter(f => f.status === 'paid').reduce((sum, f) => sum + (f.final_amount || f.amount), 0) || 0,
    pending_amount: data?.filter(f => f.status !== 'paid').reduce((sum, f) => sum + (f.final_amount || f.amount), 0) || 0
  };

  return stats;
};