import { TrainingElement, DrawingElement } from '@/components/training/InteractiveTrainingBuilder';
import { Trajectory, TrajectoryPoint } from '@/components/training/TrajectorySystem';

export interface AnimationFrame {
  id: string;
  timestamp: number;
  elements: TrainingElement[];
  drawings: DrawingElement[];
  trajectories: Trajectory[];
  annotations?: any[];
  description?: string;
}

export interface PlaybackState {
  isPlaying: boolean;
  currentFrame: number;
  totalFrames: number;
  speed: number;
  loop: boolean;
  direction: 'forward' | 'backward';
}

export interface AnimationSettings {
  fps: number;
  duration: number;
  quality: 'low' | 'medium' | 'high' | 'ultra';
  smoothing: boolean;
  interpolation: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

export interface TimelineEvent {
  id: string;
  timestamp: number;
  type: 'keyframe' | 'action' | 'transition';
  data: any;
}

export class AnimationEngine {
  private frames: AnimationFrame[] = [];
  private playbackState: PlaybackState;
  private settings: AnimationSettings;
  private timeline: TimelineEvent[] = [];
  private animationId: number | null = null;
  private lastFrameTime: number = 0;
  private callbacks: {
    onFrameChange?: (frame: number) => void;
    onPlayStateChange?: (playing: boolean) => void;
    onComplete?: () => void;
    onProgress?: (progress: number) => void;
  } = {};

  constructor(settings: Partial<AnimationSettings> = {}) {
    this.settings = {
      fps: 30,
      duration: 10000, // 10 seconds default
      quality: 'high',
      smoothing: true,
      interpolation: 'ease',
      ...settings
    };

    this.playbackState = {
      isPlaying: false,
      currentFrame: 0,
      totalFrames: Math.floor(this.settings.duration / (1000 / this.settings.fps)),
      speed: 1,
      loop: false,
      direction: 'forward'
    };
  }

  // Playback Control Methods
  play(): void {
    if (this.playbackState.isPlaying) return;
    
    this.playbackState.isPlaying = true;
    this.lastFrameTime = performance.now();
    this.startAnimation();
    this.callbacks.onPlayStateChange?.(true);
  }

  pause(): void {
    if (!this.playbackState.isPlaying) return;
    
    this.playbackState.isPlaying = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    this.callbacks.onPlayStateChange?.(false);
  }

  stop(): void {
    this.pause();
    this.playbackState.currentFrame = 0;
    this.callbacks.onFrameChange?.(0);
  }

  seek(frame: number): void {
    const clampedFrame = Math.max(0, Math.min(this.playbackState.totalFrames - 1, frame));
    this.playbackState.currentFrame = clampedFrame;
    this.callbacks.onFrameChange?.(clampedFrame);
  }

  // Speed Control Methods
  setSpeed(speed: number): void {
    this.playbackState.speed = Math.max(0.1, Math.min(5, speed));
  }

  getSpeed(): number {
    return this.playbackState.speed;
  }

  // Timeline Management
  private startAnimation(): void {
    const animate = (currentTime: number) => {
      if (!this.playbackState.isPlaying) return;

      const deltaTime = currentTime - this.lastFrameTime;
      const frameInterval = (1000 / this.settings.fps) / this.playbackState.speed;

      if (deltaTime >= frameInterval) {
        this.updatePlaybackFrame();
        this.lastFrameTime = currentTime;
      }

      this.animationId = requestAnimationFrame(animate);
    };

    this.animationId = requestAnimationFrame(animate);
  }

  private updatePlaybackFrame(): void {
    const { currentFrame, totalFrames, direction, loop } = this.playbackState;
    
    let nextFrame = direction === 'forward' ? currentFrame + 1 : currentFrame - 1;

    if (nextFrame >= totalFrames) {
      if (loop) {
        nextFrame = 0;
      } else {
        this.pause();
        this.callbacks.onComplete?.();
        return;
      }
    } else if (nextFrame < 0) {
      if (loop) {
        nextFrame = totalFrames - 1;
      } else {
        this.pause();
        this.callbacks.onComplete?.();
        return;
      }
    }

    this.playbackState.currentFrame = nextFrame;
    this.callbacks.onFrameChange?.(nextFrame);
    
    const progress = (nextFrame / (totalFrames - 1)) * 100;
    this.callbacks.onProgress?.(progress);
  }

  // Frame Management
  addFrame(frame: Omit<AnimationFrame, 'id'>): string {
    const newFrame: AnimationFrame = {
      ...frame,
      id: `frame_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    };
    
    this.frames.push(newFrame);
    this.frames.sort((a, b) => a.timestamp - b.timestamp);
    
    // Update total frames based on duration
    this.playbackState.totalFrames = Math.max(
      this.playbackState.totalFrames,
      Math.floor(frame.timestamp / (1000 / this.settings.fps))
    );
    
    return newFrame.id;
  }

  removeFrame(frameId: string): boolean {
    const index = this.frames.findIndex(f => f.id === frameId);
    if (index === -1) return false;
    
    this.frames.splice(index, 1);
    return true;
  }

  getCurrentFrame(): AnimationFrame | null {
    const currentTime = (this.playbackState.currentFrame / this.settings.fps) * 1000;
    return this.getFrameAtTime(currentTime);
  }

  getFrameAtTime(timestamp: number): AnimationFrame | null {
    if (this.frames.length === 0) return null;
    
    // Find the closest frame at or before the timestamp
    let closestFrame = this.frames[0];
    for (const frame of this.frames) {
      if (frame.timestamp <= timestamp) {
        closestFrame = frame;
      } else {
        break;
      }
    }
    
    return closestFrame;
  }

  getTotalFrames(): number {
    return this.playbackState.totalFrames;
  }

  setFrame(frame: number): void {
    this.seek(frame);
  }

  updateFrame(frameId: string, updates: Partial<AnimationFrame>): boolean {
    const frame = this.frames.find(f => f.id === frameId);
    if (!frame) return false;
    
    Object.assign(frame, updates);
    
    if (updates.timestamp !== undefined) {
      this.frames.sort((a, b) => a.timestamp - b.timestamp);
    }
    
    return true;
  }

  // Trajectory Management
  addTrajectory(trajectory: Trajectory): void {
    // Add trajectory to current frame or create new frame
    const currentTime = (this.playbackState.currentFrame / this.settings.fps) * 1000;
    let frame = this.getFrameAtTime(currentTime);
    
    if (!frame) {
      frame = {
        id: `frame_${Date.now()}`,
        timestamp: currentTime,
        elements: [],
        drawings: [],
        trajectories: [trajectory]
      };
      this.frames.push(frame);
    } else {
      frame.trajectories.push(trajectory);
    }
    
    this.frames.sort((a, b) => a.timestamp - b.timestamp);
  }

  updateTrajectory(id: string, updates: Partial<Trajectory>): void {
    for (const frame of this.frames) {
      const trajectory = frame.trajectories.find(t => t.id === id);
      if (trajectory) {
        Object.assign(trajectory, updates);
      }
    }
  }

  removeTrajectory(id: string): void {
    for (const frame of this.frames) {
      frame.trajectories = frame.trajectories.filter(t => t.id !== id);
    }
  }

  // Interpolation Methods
  interpolateElements(fromFrame: AnimationFrame, toFrame: AnimationFrame, progress: number): TrainingElement[] {
    const interpolated: TrainingElement[] = [];
    
    // Match elements by ID and interpolate positions
    for (const fromElement of fromFrame.elements) {
      const toElement = toFrame.elements.find(e => e.id === fromElement.id);
      
      if (toElement) {
        const interpolatedElement: TrainingElement = {
          ...fromElement,
          position: {
            x: this.lerp(fromElement.position.x, toElement.position.x, progress),
            y: this.lerp(fromElement.position.y, toElement.position.y, progress)
          }
        };
        
        // Interpolate rotation if present
        if (fromElement.properties.rotation !== undefined && toElement.properties.rotation !== undefined) {
          interpolatedElement.properties.rotation = this.lerp(
            fromElement.properties.rotation,
            toElement.properties.rotation,
            progress
          );
        }
        
        interpolated.push(interpolatedElement);
      } else {
        // Element only exists in from frame
        interpolated.push(fromElement);
      }
    }
    
    // Add elements that only exist in to frame
    for (const toElement of toFrame.elements) {
      if (!fromFrame.elements.find(e => e.id === toElement.id)) {
        interpolated.push(toElement);
      }
    }
    
    return interpolated;
  }

  private lerp(start: number, end: number, progress: number): number {
    const easedProgress = this.applyEasing(progress);
    return start + (end - start) * easedProgress;
  }

  private applyEasing(progress: number): number {
    switch (this.settings.interpolation) {
      case 'linear':
        return progress;
      case 'ease':
        return progress * progress * (3 - 2 * progress);
      case 'ease-in':
        return progress * progress;
      case 'ease-out':
        return 1 - Math.pow(1 - progress, 2);
      case 'ease-in-out':
        return progress < 0.5 
          ? 2 * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      default:
        return progress;
    }
  }

  // Timeline Events
  addTimelineEvent(event: Omit<TimelineEvent, 'id'>): string {
    const newEvent: TimelineEvent = {
      ...event,
      id: `event_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    };
    
    this.timeline.push(newEvent);
    this.timeline.sort((a, b) => a.timestamp - b.timestamp);
    
    return newEvent.id;
  }

  removeTimelineEvent(eventId: string): boolean {
    const index = this.timeline.findIndex(e => e.id === eventId);
    if (index === -1) return false;
    
    this.timeline.splice(index, 1);
    return true;
  }

  getTimelineEvents(startTime: number, endTime: number): TimelineEvent[] {
    return this.timeline.filter(event => 
      event.timestamp >= startTime && event.timestamp <= endTime
    );
  }

  // Settings Management
  updateSettings(newSettings: Partial<AnimationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    // Recalculate total frames if FPS or duration changed
    if (newSettings.fps || newSettings.duration) {
      this.playbackState.totalFrames = Math.floor(this.settings.duration / (1000 / this.settings.fps));
    }
  }

  getSettings(): AnimationSettings {
    return { ...this.settings };
  }

  // State Management
  getPlaybackState(): PlaybackState {
    return { ...this.playbackState };
  }

  setLoop(loop: boolean): void {
    this.playbackState.loop = loop;
  }

  setDirection(direction: 'forward' | 'backward'): void {
    this.playbackState.direction = direction;
  }

  // Event Callbacks
  onFrameChange(callback: (frame: number) => void): void {
    this.callbacks.onFrameChange = callback;
  }

  onPlayStateChange(callback: (playing: boolean) => void): void {
    this.callbacks.onPlayStateChange = callback;
  }

  onComplete(callback: () => void): void {
    this.callbacks.onComplete = callback;
  }

  onProgress(callback: (progress: number) => void): void {
    this.callbacks.onProgress = callback;
  }

  // Export Methods
  exportFrames(): AnimationFrame[] {
    return [...this.frames];
  }

  exportTimeline(): TimelineEvent[] {
    return [...this.timeline];
  }

  importFrames(frames: AnimationFrame[]): void {
    this.frames = [...frames];
    this.frames.sort((a, b) => a.timestamp - b.timestamp);
    
    // Update total frames
    if (frames.length > 0) {
      const maxTimestamp = Math.max(...frames.map(f => f.timestamp));
      this.playbackState.totalFrames = Math.floor(maxTimestamp / (1000 / this.settings.fps));
    }
  }

  importTimeline(timeline: TimelineEvent[]): void {
    this.timeline = [...timeline];
    this.timeline.sort((a, b) => a.timestamp - b.timestamp);
  }

  // Cleanup
  destroy(): void {
    this.pause();
    this.frames = [];
    this.timeline = [];
    this.callbacks = {};
  }

  // Utility Methods
  getFrameRate(): number {
    return this.settings.fps;
  }

  getDuration(): number {
    return this.settings.duration;
  }

  getCurrentTime(): number {
    return (this.playbackState.currentFrame / this.settings.fps) * 1000;
  }

  getProgress(): number {
    return (this.playbackState.currentFrame / (this.playbackState.totalFrames - 1)) * 100;
  }

  isPlaying(): boolean {
    return this.playbackState.isPlaying;
  }

  canPlay(): boolean {
    return this.frames.length > 0 || this.playbackState.totalFrames > 0;
  }

  reset(): void {
    this.stop();
    this.frames = [];
    this.timeline = [];
    this.playbackState.totalFrames = Math.floor(this.settings.duration / (1000 / this.settings.fps));
  }
}

// Factory function for creating animation engine instances
export const createAnimationEngine = (settings?: Partial<AnimationSettings>): AnimationEngine => {
  return new AnimationEngine(settings);
};

// Default animation engine instance
export const defaultAnimationEngine = new AnimationEngine();