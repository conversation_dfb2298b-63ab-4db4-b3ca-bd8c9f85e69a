# Resumo do Projeto: Plataforma de Gestão Esportiva Integrada

## Visão Geral

A "Game Day Nexus Platform" é um sistema de gestão (ERP) completo e robusto, desenvolvido para administrar todas as operações de uma organização esportiva de forma centralizada e eficiente. A plataforma abrange desde a gestão de atletas e colaboradores até módulos complexos de finanças, saúde, logística e operações de jogo.

O sistema foi projetado com foco em escalabilidade, segurança e usabilidade, utilizando um sistema de permissões granular que permite controle total sobre o acesso a cada funcionalidade.

## Funcionalidades Principais

### Módulo de Gestão de Pessoal
- **Gestão de Jogadores e Colaboradores:** Cadastro completo, perfis individuais, histórico, contratos e informações de contato.
- **Sistema de Permissões Avançado:** Controle de acesso granular para cada ação e visualização dentro do sistema, permitindo a criação de papéis e departamentos com permissões específicas.
- **Gestão de Departamentos:** Criação e gerenciamento de departamentos internos, associando colaboradores e definindo hierarquias.

### Módulo Esportivo
- **Gestão de Treinamentos:** Agendamento, controle de presença, planejamento de atividades e locais de treino.
- **Gestão de Partidas e Competições:** Cadastro de campeonatos, agendamento de partidas, e registro de resultados.
- **Sistema de Escalação (Lineup):** Ferramenta para criar e gerenciar escalações para cada partida e categoria.
- **Convocatórias (Call-ups):** Módulo para convocar jogadores para partidas e eventos.
- **Avaliação de Atletas:** Sistema para registrar e acompanhar avaliações técnicas e físicas dos jogadores.

### Módulo Administrativo e Financeiro
- **Controle Financeiro:** Gestão de contas a pagar/receber, adiantamentos salariais e transações financeiras com upload de comprovantes.
- **Gestão de Estoque (Inventory):** Controle de entrada e saída de materiais, solicitações de inventário e gerenciamento de suprimentos.
- **Gerenciamento de Fornecedores:** Cadastro e gestão de fornecedores e ordens de compra.
- **Exportação de Relatórios:** Capacidade de gerar relatórios detalhados para praticamente todos os módulos do sistema.

### Módulo de Saúde e Bem-estar
- **Agenda Médica e Prontuários:** Agendamento de consultas, registro de prontuários médicos, controle de disponibilidade médica e gestão de profissionais de saúde.
- **Sistema de Alimentação:** Planejamento de refeições, controle de participantes e gerenciamento de locais de alimentação.
- **Gestão de Alojamentos:** Administração das acomodações para jogadores e colaboradores.

### Funcionalidades Técnicas e de Suporte
- **Sistema de Notificações:** Alertas em tempo real para atualizações importantes (novas partidas, treinos, convocações, etc.).
- **Gestão de Documentos:** Upload, verificação e armazenamento de documentos de jogadores e colaboradores.
- **Logs de Auditoria:** Rastreamento de todas as ações importantes realizadas no sistema para fins de segurança e conformidade.
- **Autenticação Segura:** Sistema de login e confirmação de e-mail para garantir a segurança das contas.

## Tecnologias Utilizadas

- **Frontend:** React, TypeScript, Vite, Tailwind CSS
- **Backend & Banco de Dados:** Supabase, PostgreSQL
- **Linguagem de Scripting/Automação:** Node.js, Bun
