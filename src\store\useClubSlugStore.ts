import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ClubInfo } from '@/api/clubs';

interface ClubSlugStore {
  // Cache de clubes por slug
  clubsCache: Record<string, ClubInfo>;
  // Slug atual
  currentSlug: string | null;
  // Clube atual
  currentClub: ClubInfo | null;
  // Loading state
  loading: boolean;
  
  // Actions
  setClub: (slug: string, club: ClubInfo) => void;
  getClubFromCache: (slug: string) => ClubInfo | null;
  setCurrentSlug: (slug: string) => void;
  setLoading: (loading: boolean) => void;
  clearCache: () => void;
}

export const useClubSlugStore = create<ClubSlugStore>()(
  persist(
    (set, get) => ({
      clubsCache: {},
      currentSlug: null,
      currentClub: null,
      loading: false,

      setClub: (slug: string, club: ClubInfo) => {
        set((state) => {
          const newCache = { ...state.clubsCache, [slug]: club };
          return {
            clubsCache: newCache,
            currentClub: state.currentSlug === slug ? club : state.currentClub
          };
        });
      },

      getClubFromCache: (slug: string) => {
        const state = get();
        return state.clubsCache[slug] || null;
      },

      setCurrentSlug: (slug: string) => {
        const state = get();
        const cachedClub = state.clubsCache[slug];
        
        set({
          currentSlug: slug,
          currentClub: cachedClub || null
        });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      clearCache: () => {
        set({
          clubsCache: {},
          currentSlug: null,
          currentClub: null,
          loading: false
        });
      }
    }),
    {
      name: 'club-slug-store',
      // Apenas persistir o cache, não o estado de loading
      partialize: (state) => ({
        clubsCache: state.clubsCache
      })
    }
  )
);