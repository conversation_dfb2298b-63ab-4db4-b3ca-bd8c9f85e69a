
import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { useClubTitlesStore } from "@/store/useClubTitlesStore";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useTheme } from "@/context/ThemeContext";
import { ThemeSelector } from "@/components/ThemeSelector";
import { Upload, Image as ImageIcon, Trash2, QrCode } from "lucide-react";
import { uploadClubLogo } from "@/api/storage-simple";
import { usePermission } from "@/hooks/usePermission";
import { SETTINGS_PERMISSIONS } from "@/constants/permissions";
import { PixQRCodeModal } from "@/components/financial/PixQRCodeModal";
import { ClubSlugManager } from "@/components/club/ClubSlugManager";

export default function ConfiguracoesClube() {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { setLogo, setClubName } = useTheme();

  const { clubInfo, loading, error, fetchClubInfo, updateClubInfo } = useClubInfoStore();
  const { categories, fetchCategories } = useCategoriesStore();
  const { titles, fetchTitles, addTitle, removeTitle } = useClubTitlesStore();

  const [name, setName] = useState("");
  const [stadium, setStadium] = useState("");
  const [foundedDate, setFoundedDate] = useState("");
  const [president, setPresident] = useState("");
  const [logoUrl, setLogoUrl] = useState("");
  const [address, setAddress] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [phone, setPhone] = useState("");
  const [website, setWebsite] = useState("");
  const [email, setEmail] = useState("");
  const [contractWarningDays, setContractWarningDays] = useState("60");
  const [pixKey, setPixKey] = useState("");
  const [newTitle, setNewTitle] = useState("");
  const [newTitleYear, setNewTitleYear] = useState("");
  const [uploading, setUploading] = useState(false);
  const [pixModalOpen, setPixModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { can, isLoaded } = usePermission();
  const canEditSettings = isLoaded && can(SETTINGS_PERMISSIONS.EDIT);

  useEffect(() => {
    fetchClubInfo(clubId);
    fetchCategories(clubId);
    fetchTitles(clubId);
  }, [fetchClubInfo, fetchCategories, fetchTitles, clubId]);

  useEffect(() => {
    if (clubInfo) {
      setName(clubInfo.name || "");
      setStadium(clubInfo.stadium || "");
      setFoundedDate(clubInfo.founded_year || "");
      setPresident(clubInfo.president || "");
      setLogoUrl(clubInfo.logo_url || "");
      setAddress(clubInfo.address || "");
      setZipCode(clubInfo.zip_code || "");
      setPhone(clubInfo.phone || "");
      setWebsite(clubInfo.website || "");
      setEmail(clubInfo.email || "");
      setContractWarningDays(
        clubInfo.contract_warning_days?.toString() || "60"
      );
      setPixKey(clubInfo.pix_key || "");
    }
  }, [clubInfo]);

  // Função para adicionar um parâmetro de timestamp à URL para evitar cache
  const addCacheBuster = (url: string) => {
    const timestamp = new Date().getTime();
    // Verificar se a URL já tem parâmetros
    const hasParams = url.includes('?');
    const separator = hasParams ? '&' : '?';
    return `${url}${separator}t=${timestamp}`;
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!canEditSettings) {
      toast({
        title: 'Permissão insuficiente',
        description: 'Você não pode editar as configurações do clube',
        variant: 'destructive'
      });
      return;
    }
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);

      // Fazer upload do arquivo
      const imageUrl = await uploadClubLogo(clubId.toString(), file);

      // Adicionar timestamp para evitar cache
      const urlWithTimestamp = addCacheBuster(imageUrl);

      // Atualizar a URL do logo
      setLogoUrl(urlWithTimestamp);

      // Limpar o cache da imagem
      const img = new Image();
      img.src = urlWithTimestamp;

      toast({
        title: "Logo enviado",
        description: "A imagem foi enviada com sucesso.",
      });
    } catch (err) {
      console.error("Erro ao fazer upload do logo:", err);
      toast({
        title: "Erro",
        description: err instanceof Error ? err.message : "Não foi possível enviar a imagem.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);

      // Limpar o valor do input para permitir selecionar o mesmo arquivo novamente
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleAddTitle = async () => {
    if (!canEditSettings) {
      toast({
        title: 'Permissão insuficiente',
        description: 'Você não pode editar as configurações do clube',
        variant: 'destructive'
      });
      return;
    }
    if (!newTitle) return;
    try {
      await addTitle(clubId, {
        title: newTitle,
        year: newTitleYear ? parseInt(newTitleYear) : undefined
      });
      setNewTitle('');
      setNewTitleYear('');
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível adicionar o título',
        variant: 'destructive'
      });
    }
  };

  const handleSaveClub = async () => {
    if (!canEditSettings) {
      toast({
        title: "Permissão insuficiente",
        description: "Você não pode editar as configurações do clube.",
        variant: "destructive",
      });
      return;
    }
    try {
      const updatedClub = await updateClubInfo(clubId, {
        name,
        stadium,
        founded_year: foundedDate || undefined,
        president,
        logo_url: logoUrl,
        address,
        zip_code: zipCode,
        phone,
        website,
        email,
        contract_warning_days: contractWarningDays
          ? parseInt(contractWarningDays)
          : undefined,
        pix_key: pixKey,
      });

      // Atualizar o ThemeContext com as novas informações
      if (updatedClub) {
        if (updatedClub.name) {
          setClubName(updatedClub.name);
        }
        if (updatedClub.logo_url) {
          setLogo(updatedClub.logo_url);
        }
      }

      toast({
        title: "Clube atualizado",
        description: "As configurações do clube foram salvas com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as alterações.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-team-blue mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
        <p className="text-muted-foreground">
          Gerencie as configurações do clube, usuários e preferências
        </p>
      </div>

      <Tabs defaultValue="club" className="w-full">
        <TabsList>
          <TabsTrigger value="club">Clube</TabsTrigger>
          <TabsTrigger value="url">URL Personalizada</TabsTrigger>
          <TabsTrigger value="user">Usuário</TabsTrigger>
          <TabsTrigger value="appearance">Interface</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
        </TabsList>
        <TabsContent value="club" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Informações do Clube</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="space-y-4">
                  <div className="flex flex-col items-center">
                    <Avatar className="h-24 w-24">
                      <AvatarImage
                        src={logoUrl}
                        alt={name}
                        key={logoUrl} // Forçar remontagem quando a URL mudar
                      />
                      <AvatarFallback className="bg-team-blue text-white text-lg">
                        {name?.slice(0, 2).toUpperCase() || "FC"}
                      </AvatarFallback>
                    </Avatar>

                    <div className="mt-2 space-y-2">
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileUpload}
                        accept="image/*"
                        className="hidden"
                        disabled={uploading || !canEditSettings}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading || !canEditSettings}
                        className="w-full"
                      >
                        {uploading ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                            <span>Enviando...</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Enviar logo</span>
                          </div>
                        )}
                      </Button>

                      {logoUrl && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // Forçar atualização da imagem adicionando um novo timestamp
                            setLogoUrl(addCacheBuster(logoUrl.split('?')[0]));
                          }}
                          className="w-full text-xs"
                          disabled={!canEditSettings}
                        >
                          <div className="flex items-center">
                            <ImageIcon className="h-3 w-3 mr-1" />
                            <span>Atualizar visualização</span>
                          </div>
                        </Button>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">URL do Logo</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="URL do Logo"
                        value={logoUrl}
                        onChange={(e) => setLogoUrl(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex-1 space-y-4">
                  <div>
                    <label className="text-sm font-medium">Nome do Clube</label>
                    <Input
                      placeholder="Nome do clube"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      disabled={!canEditSettings}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Estádio</label>
                    <Input
                      placeholder="Estádio"
                      value={stadium}
                      onChange={(e) => setStadium(e.target.value)}
                      disabled={!canEditSettings}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                    <label className="text-sm font-medium">Data de Fundação</label>
                      <Input
                        placeholder="Data de fundação"
                        type="date"
                        value={foundedDate}
                        onChange={(e) => setFoundedDate(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Presidente</label>
                      <Input
                        placeholder="Presidente"
                        value={president}
                        onChange={(e) => setPresident(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Endereço</label>
                    <Input
                      placeholder="Endereço completo"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      disabled={!canEditSettings}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">CEP</label>
                      <Input
                        placeholder="CEP"
                        value={zipCode}
                        onChange={(e) => setZipCode(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Telefone</label>
                      <Input
                        placeholder="Telefone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Website</label>
                      <Input
                        placeholder="Site do clube"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Email</label>
                      <Input
                        placeholder="Email do clube"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Dias de aviso de contratos</label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Ex: 60"
                        value={contractWarningDays}
                        onChange={(e) => setContractWarningDays(e.target.value)}
                        disabled={!canEditSettings}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Chave PIX</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="CPF, CNPJ, email, telefone ou chave aleatória"
                        value={pixKey}
                        onChange={(e) => setPixKey(e.target.value)}
                        disabled={!canEditSettings}
                      />
                      <Button
                        variant="outline"
                        onClick={() => setPixModalOpen(true)}
                        disabled={!pixKey || !canEditSettings}
                        title="Gerar QR Code PIX"
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Configure a chave PIX para receber pagamentos de mensalidades
                    </p>
                  </div>
                </div>
              </div>
              {error && <div className="text-red-500 text-sm">{error}</div>}
              <div className="flex justify-end">
                <Button onClick={handleSaveClub} disabled={loading || !canEditSettings}>
                  Salvar alterações
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Categorias e Títulos</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Categorias Registradas</h4>
                {categories.length === 0 ? (
                  <p className="text-sm text-muted-foreground">Nenhuma categoria cadastrada.</p>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {categories.map(cat => (
                      <Badge key={cat.id} variant="secondary">{cat.name}</Badge>
                    ))}
                  </div>
                )}
              </div>
              <div>
                <h4 className="font-medium mb-2">Títulos do Clube</h4>
                {titles.length === 0 ? (
                  <p className="text-sm text-muted-foreground">Nenhum título cadastrado.</p>
                ) : (
                  <ul className="space-y-1">
                    {titles.map(t => (
                      <li key={t.id} className="flex items-center justify-between">
                        <span>{t.title}{t.year ? ` (${t.year})` : ''}</span>
                        <Button size="icon" variant="ghost" onClick={() => removeTitle(clubId, t.id)} disabled={!canEditSettings}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                )}
                <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
                  <Input
                    placeholder="Título"
                    value={newTitle}
                    onChange={(e) => setNewTitle(e.target.value)}
                    className="md:col-span-2"
                    disabled={!canEditSettings}
                  />
                  <Input
                    placeholder="Ano"
                    value={newTitleYear}
                    onChange={(e) => setNewTitleYear(e.target.value)}
                    disabled={!canEditSettings}
                  />
                  <Button onClick={handleAddTitle} disabled={!canEditSettings}>Adicionar</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Outras configurações do clube aqui */}

        </TabsContent>

        <TabsContent value="url" className="space-y-4">
          <ClubSlugManager />
        </TabsContent>

        <TabsContent value="user" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Perfil de Usuário</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Nome</label>
                  <Input value={user?.name || ""} readOnly />
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input value={user?.email || ""} readOnly />
                </div>
                <div>
                  <label className="text-sm font-medium">ID de Usuário</label>
                  <Input value={user?.id || ""} readOnly />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Outras abas aqui */}
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Interface</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Cores do Clube</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Personalize as cores do seu clube para refletir a identidade visual da sua equipe.
                  Estas cores serão aplicadas em todo o sistema.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ThemeSelector disabled={!canEditSettings} />
                  <div className="space-y-4">
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">Visualização</h4>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full" style={{ backgroundColor: 'var(--color-primary)' }}></div>
                          <span className="text-sm">Cor Primária</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full" style={{ backgroundColor: 'var(--color-secondary)' }}></div>
                          <span className="text-sm">Cor Secundária</span>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">Exemplo</h4>
                      <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--color-primary)' }}>
                        <div className="text-white font-medium">Cabeçalho</div>
                      </div>
                      <div className="mt-2 p-3 border rounded-md">
                        <div className="font-medium" style={{ color: 'var(--color-primary)' }}>Título</div>
                        <div className="text-sm mt-1">Conteúdo de exemplo</div>
                        <button
                          className="mt-2 px-3 py-1 text-sm rounded-md text-white"
                          style={{ backgroundColor: 'var(--color-primary)' }}
                        >
                          Botão
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notificações</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Configurações de notificações estarão disponíveis em breve.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal PIX QR Code */}
      <PixQRCodeModal
        open={pixModalOpen}
        onOpenChange={setPixModalOpen}
        pixKey={pixKey}
        clubName={name}
        clubLogoUrl={logoUrl}
      />
    </div>
  );
}
