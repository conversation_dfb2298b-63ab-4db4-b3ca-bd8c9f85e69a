import React from 'react';
import { Badge } from '@/components/ui/badge';
import { ArrowRightLeft, FileText } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface PlayerTransferBadgeProps {
  isTransfer?: boolean;
  transferId?: number;
  hasGlobalDocuments?: boolean;
}

export function PlayerTransferBadge({ isTransfer, transferId, hasGlobalDocuments }: PlayerTransferBadgeProps) {
  if (!isTransfer) return null;

  return (
    <TooltipProvider>
      <div className="flex gap-1">
        <Tooltip>
          <TooltipTrigger>
            <Badge variant="secondary" className="flex items-center gap-1 text-xs">
              <ArrowRightLeft className="h-3 w-3" />
              Transferido
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Jogador transferido via sistema global</p>
            {transferId && <p className="text-xs text-muted-foreground">ID: {transferId}</p>}
          </TooltipContent>
        </Tooltip>

        {hasGlobalDocuments && (
          <Tooltip>
            <TooltipTrigger>
              <Badge variant="outline" className="flex items-center gap-1 text-xs">
                <FileText className="h-3 w-3" />
                Docs
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>Documentos copiados automaticamente</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}