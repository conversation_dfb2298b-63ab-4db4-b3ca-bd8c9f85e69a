import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { sendReleaseEmail } from '../dist/services/brevoEmailService.js';
import { getClubInfo } from '../dist/api/api.js';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  const { data, error } = await supabase
    .from('player_evaluations')
    .select(`
      id,
      club_id,
      player_id,
      players:player_id (
        name,
        user_id,
        users:user_id (
          email
        )
      ),
      club_info:club_id (
        name
      )
    `)
    .eq('status', 'released');

  if (error) {
    console.error('Error fetching released evaluations:', error);
    process.exit(1);
  }

  for (const evaluation of data || []) {
    const email = evaluation.players?.users?.email;
    const playerName = evaluation.players?.name || 'Atleta';
    const clubName = evaluation.club_info?.name || (await getClubInfo(evaluation.club_id)).name;

    if (!email) {
      console.warn(`No email found for player ${playerName}`);
      continue;
    }

    try {
      await sendReleaseEmail(email, playerName, clubName);
      console.log(`Release email sent to ${playerName} <${email}>`);
    } catch (e) {
      console.error('Failed to send release email:', e);
    }
  }
}

main();