import { supabase } from '@/integrations/supabase/client';

async function ensureAuth() {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) {
    throw new Error('Sessão não autenticada');
  }
}

export interface PlayerCardHistory {
  player_id: string;
  match_id: string;
  match_date: string;
  card_type: 'yellow' | 'red' | 'second_yellow';
  minute: string;
  competition_id?: string;
  category_id?: number;
}

export interface SuspensionCheck {
  shouldSuspend: boolean;
  reason: string;
  matches_to_skip: number;
}

export interface PlayerSuspension {
  id: string;
  club_id: number;
  player_id: string;
  competition_id?: string;
  category_id?: number;
  reason: string;
  matches_remaining: number;
  original_matches: number;
  suspended_at: string;
  created_at: string;
  updated_at: string;
}

/**
 * Busca o histórico de cartões de um jogador nos últimos jogos de uma competição/categoria específica
 */
export async function getPlayerCardHistory(
  clubId: number, 
  playerId: string,
  competitionId?: string,
  categoryId?: number,
  limit: number = 10
): Promise<PlayerCardHistory[]> {
  await ensureAuth();
  let query = supabase
    .from('match_events')
    .select(`
      player_id,
      match_id,
      minute,
      event_data,
      matches!inner(date, result, competition_id, category_id)
    `)
    .eq('club_id', clubId)
    .eq('player_id', playerId)
    .eq('event_type', 'card')
    .not('matches.result', 'is', null)
    .order('matches(date)', { ascending: false })
    .limit(limit);

  // Filtrar por competição se especificada
  if (competitionId) {
    query = query.eq('matches.competition_id', competitionId);
  }

  // Filtrar por categoria se especificada
  if (categoryId) {
    query = query.eq('matches.category_id', categoryId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Erro ao buscar histórico de cartões:', error);
    return [];
  }

  return (data || []).map(event => ({
    player_id: event.player_id,
    match_id: event.match_id,
    match_date: event.matches.date,
    card_type: event.event_data.card_type,
    minute: event.minute || '0',
    competition_id: event.matches.competition_id,
    category_id: event.matches.category_id
  }));
}

/**
 * Verifica se um jogador deve ser suspenso baseado nas regras de cartões para uma competição/categoria específica
 */
export async function checkPlayerSuspension(
  clubId: number,
  playerId: string,
  newCardType: 'yellow' | 'red' | 'second_yellow',
  currentMatchId: string,
  competitionId?: string,
  categoryId?: number
): Promise<SuspensionCheck> {
  // Regra 1: Cartão vermelho direto = suspensão imediata
  if (newCardType === 'red') {
    return {
      shouldSuspend: true,
      reason: 'Cartão vermelho direto',
      matches_to_skip: 1
    };
  }

  // Regra 2: Segundo cartão amarelo na mesma partida = suspensão
  if (newCardType === 'second_yellow') {
    return {
      shouldSuspend: true,
      reason: 'Segundo cartão amarelo na mesma partida',
      matches_to_skip: 1
    };
  }

  // Regra 3: 3 cartões amarelos em jogos diferentes = suspensão
  if (newCardType === 'yellow') {
    const cardHistory = await getPlayerCardHistory(clubId, playerId, competitionId, categoryId, 20);
    
    console.log('🟡 [CARTÃO AMARELO] Histórico completo de cartões:', cardHistory);
    
    // Filtrar apenas cartões amarelos em jogos diferentes (excluindo o atual)
    const yellowCardsInDifferentMatches = cardHistory.filter(card => 
      card.card_type === 'yellow' && card.match_id !== currentMatchId
    );

    console.log('🟡 [CARTÃO AMARELO] Cartões amarelos em jogos diferentes:', yellowCardsInDifferentMatches);

    // Lógica de reset: A cada 3 cartões amarelos, há uma suspensão
    // Então contamos quantos "ciclos" de 3 cartões já foram completados
    const totalYellowCards = yellowCardsInDifferentMatches.length + 1; // +1 pelo cartão atual
    const completedCycles = Math.floor((totalYellowCards - 1) / 3); // Quantos ciclos de 3 já foram completados
    const cardsInCurrentCycle = (totalYellowCards - 1) % 3; // Cartões no ciclo atual (0, 1 ou 2)

    console.log('� [CARTÃO A MARELO] Total de cartões amarelos (incluindo atual):', totalYellowCards);
    console.log('🟡 [CARTÃO AMARELO] Ciclos de suspensão completados:', completedCycles);
    console.log('🟡 [CARTÃO AMARELO] Cartões no ciclo atual:', cardsInCurrentCycle);

    // Se já tem 2 cartões no ciclo atual, o terceiro (atual) causa suspensão
    if (cardsInCurrentCycle >= 2) {
      return {
        shouldSuspend: true,
        reason: 'Terceiro cartão amarelo em jogos diferentes',
        matches_to_skip: 1
      };
    }
  }

  return {
    shouldSuspend: false,
    reason: '',
    matches_to_skip: 0
  };
}

/**
 * Aplica suspensão automática a um jogador para uma competição/categoria específica
 */
export async function applySuspension(
  clubId: number,
  playerId: string,
  reason: string,
  matchesToSkip: number = 1,
  competitionId?: string,
  categoryId?: number
): Promise<void> {
  try {
    // Verificar se já existe uma suspensão ativa para este contexto
    let suspensionQuery = supabase
      .from('player_suspensions')
      .select('*')
      .eq('club_id', clubId)
      .eq('player_id', playerId)
      .gt('matches_remaining', 0);

    // Filtrar por competição
    if (competitionId) {
      suspensionQuery = suspensionQuery.eq('competition_id', competitionId);
    } else {
      suspensionQuery = suspensionQuery.is('competition_id', null);
    }

    // Filtrar por categoria
    if (categoryId) {
      suspensionQuery = suspensionQuery.eq('category_id', categoryId);
    } else {
      suspensionQuery = suspensionQuery.is('category_id', null);
    }

    const { data: existingSuspension, error: suspensionError } = await suspensionQuery.single();
    
    if (suspensionError && suspensionError.code !== 'PGRST116') {
      console.error('❌ [SUSPENSÃO] Erro ao buscar suspensão existente:', suspensionError);
      // Continua mesmo com erro, pois pode ser que não existe suspensão (o que é normal)
    }

    if (existingSuspension) {
      // Atualizar suspensão existente (somar jogos)
      await supabase
        .from('player_suspensions')
        .update({
          matches_remaining: existingSuspension.matches_remaining + matchesToSkip,
          original_matches: existingSuspension.original_matches + matchesToSkip,
          reason: `${existingSuspension.reason}; ${reason}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSuspension.id);
    } else {
      // Criar nova suspensão
      const suspensionData = {
        club_id: clubId,
        player_id: playerId,
        competition_id: competitionId || null,
        category_id: categoryId || null,
        reason,
        matches_remaining: matchesToSkip + 1, // +1 para compensar o processamento da partida atual
        original_matches: matchesToSkip // Manter o valor original correto
      };
      
      console.log('🟡 [SUSPENSÃO] Dados para inserção:', suspensionData);
      
      const { data: insertResult, error: insertError } = await supabase
        .from('player_suspensions')
        .insert(suspensionData)
        .select();
      
      if (insertError) {
        console.error('❌ [SUSPENSÃO] Erro ao inserir suspensão:', insertError);
        throw insertError;
      }
      
      console.log('✅ [SUSPENSÃO] Suspensão inserida com sucesso:', insertResult);
    }

    console.log(`Jogador ${playerId} suspenso: ${reason} (Competição: ${competitionId}, Categoria: ${categoryId})`);
  } catch (error) {
    console.error('Erro ao aplicar suspensão:', error);
    throw error;
  }
}

/**
 * Verifica e aplica suspensão se necessário após um cartão
 */
export async function processCardSuspension(
  clubId: number,
  playerId: string,
  cardType: 'yellow' | 'red' | 'second_yellow',
  matchId: string,
  competitionId?: string,
  categoryId?: number
): Promise<boolean> {
  console.log('🟡 [CARTÃO] Processando suspensão por cartão:', {
    clubId,
    playerId,
    cardType,
    matchId,
    competitionId,
    categoryId
  });

  try {
    const suspensionCheck = await checkPlayerSuspension(clubId, playerId, cardType, matchId, competitionId, categoryId);
    
    console.log('🟡 [CARTÃO] Resultado da verificação:', suspensionCheck);
    
    if (suspensionCheck.shouldSuspend) {
      console.log('🟡 [CARTÃO] Aplicando suspensão...');
      await applySuspension(clubId, playerId, suspensionCheck.reason, suspensionCheck.matches_to_skip, competitionId, categoryId);
      console.log('✅ [CARTÃO] Suspensão aplicada com sucesso');
      return true;
    }
    
    console.log('ℹ️ [CARTÃO] Nenhuma suspensão necessária');
    return false;
  } catch (error) {
    console.error('❌ [CARTÃO] Erro ao processar suspensão por cartão:', error);
    return false;
  }
}

/**
 * Reduz o contador de jogos de suspensão após uma partida para uma competição/categoria específica
 */
export async function processSuspensionAfterMatch(
  clubId: number,
  competitionId?: string,
  categoryId?: number
): Promise<void> {
  try {
    // Buscar todas as suspensões ativas do clube
    const { data: allSuspensions, error } = await supabase
      .from('player_suspensions')
      .select('*')
      .eq('club_id', clubId)
      .gt('matches_remaining', 0);

    if (error) {
      console.error('Erro ao buscar suspensões ativas:', error);
      return;
    }

    if (!allSuspensions || allSuspensions.length === 0) {
      return;
    }

    // Filtrar suspensões relevantes para o contexto da partida
    const relevantSuspensions = allSuspensions.filter(suspension => {
      // Suspensão geral (sem competição/categoria específica) se aplica a tudo
      if (!suspension.competition_id && !suspension.category_id) {
        return true;
      }
      
      // Suspensão específica: deve coincidir exatamente com o contexto
      // Se a suspensão tem competição definida, deve coincidir
      if (suspension.competition_id && suspension.competition_id !== competitionId) {
        return false;
      }
      
      // Se a suspensão tem categoria definida, deve coincidir  
      if (suspension.category_id && suspension.category_id !== categoryId) {
        return false;
      }
      
      // Se chegou até aqui, a suspensão se aplica ao contexto
      return true;
    });

    // Reduzir contador e remover suspensões se necessário
    for (const suspension of relevantSuspensions) {
      const remainingMatches = suspension.matches_remaining - 1;
      
      if (remainingMatches <= 0) {
        // Remover suspensão (jogador liberado)
        await supabase
          .from('player_suspensions')
          .delete()
          .eq('id', suspension.id);
        
        console.log(`Jogador ${suspension.player_id} liberado da suspensão (Competição: ${competitionId}, Categoria: ${categoryId})`);
      } else {
        // Reduzir contador
        await supabase
          .from('player_suspensions')
          .update({
            matches_remaining: remainingMatches,
            updated_at: new Date().toISOString()
          })
          .eq('id', suspension.id);
        
        console.log(`Jogador ${suspension.player_id} ainda tem ${remainingMatches} jogo(s) de suspensão (Competição: ${competitionId}, Categoria: ${categoryId})`);
      }
    }
  } catch (error) {
    console.error('Erro ao processar suspensões após partida:', error);
  }
}
/**
 * Verifica se um jogador está suspenso para uma partida específica (competição/categoria)
 */
export async function isPlayerSuspendedForMatch(
  clubId: number,
  playerId: string,
  competitionId?: string,
  categoryId?: number
): Promise<{
  isSuspended: boolean;
  reason?: string;
  matchesRemaining?: number;
  suspensions?: PlayerSuspension[];
}> {
  try {
    // Buscar suspensões ativas para o jogador
    const { data: allSuspensions, error } = await supabase
      .from('player_suspensions')
      .select('*')
      .eq('club_id', clubId)
      .eq('player_id', playerId)
      .gt('matches_remaining', 0);

    if (error) {
      console.error('Erro ao verificar suspensão do jogador:', error);
      return { isSuspended: false };
    }

    if (!allSuspensions || allSuspensions.length === 0) {
      return { isSuspended: false };
    }

    // Filtrar suspensões relevantes para o contexto da partida
    const relevantSuspensions = allSuspensions.filter(suspension => {
      // Suspensão geral (sem competição/categoria específica) se aplica a tudo
      if (!suspension.competition_id && !suspension.category_id) {
        return true;
      }
      
      // Suspensão específica: deve coincidir exatamente com o contexto
      // Se a suspensão tem competição definida, deve coincidir
      if (suspension.competition_id && suspension.competition_id !== competitionId) {
        return false;
      }
      
      // Se a suspensão tem categoria definida, deve coincidir  
      if (suspension.category_id && suspension.category_id !== categoryId) {
        return false;
      }
      
      // Se chegou até aqui, a suspensão se aplica ao contexto
      return true;
    });

    if (relevantSuspensions.length === 0) {
      return { isSuspended: false };
    }

    // Pegar a suspensão com mais jogos restantes
    const mainSuspension = relevantSuspensions.reduce((prev, current) => 
      (current.matches_remaining > prev.matches_remaining) ? current : prev
    );

    return {
      isSuspended: true,
      reason: mainSuspension.reason,
      matchesRemaining: mainSuspension.matches_remaining,
      suspensions: relevantSuspensions
    };
  } catch (error) {
    console.error('Erro ao verificar suspensão do jogador:', error);
    return { isSuspended: false };
  }
}

/**
 * Busca todos os jogadores suspensos de um clube para uma competição/categoria específica
 */
export async function getSuspendedPlayers(
  clubId: number,
  competitionId?: string,
  categoryId?: number
): Promise<Array<{
  id: string;
  name: string;
  reason: string;
  matchesRemaining: number;
  competitionId?: string;
  categoryId?: number;
}>> {
  try {
    // Buscar suspensões ativas
    let suspensionQuery = supabase
      .from('player_suspensions')
      .select(`
        *,
        players!inner(id, name)
      `)
      .eq('club_id', clubId)
      .gt('matches_remaining', 0);

    const { data: suspensions, error } = await suspensionQuery;

    if (error) {
      console.error('Erro ao buscar jogadores suspensos:', error);
      return [];
    }

    if (!suspensions || suspensions.length === 0) {
      return [];
    }

    // Filtrar suspensões relevantes para o contexto
    const relevantSuspensions = suspensions.filter(suspension => {
      // Se não especificou contexto, mostrar todas
      if (!competitionId && !categoryId) {
        return true;
      }
      
      // Suspensão geral se aplica a tudo
      if (!suspension.competition_id && !suspension.category_id) {
        return true;
      }
      
      // Suspensão específica da competição
      if (suspension.competition_id && suspension.competition_id === competitionId) {
        return true;
      }
      
      // Suspensão específica da categoria
      if (suspension.category_id && suspension.category_id === categoryId) {
        return true;
      }
      
      return false;
    });

    return relevantSuspensions.map(suspension => ({
      id: suspension.player_id,
      name: suspension.players.name,
      reason: suspension.reason,
      matchesRemaining: suspension.matches_remaining,
      competitionId: suspension.competition_id,
      categoryId: suspension.category_id
    }));
  } catch (error) {
    console.error('Erro ao buscar jogadores suspensos:', error);
    return [];
  }
}

/**
 * Busca todas as suspensões ativas de um jogador
 */
export async function getPlayerSuspensions(
  clubId: number,
  playerId: string
): Promise<PlayerSuspension[]> {
  try {
    const { data: suspensions, error } = await supabase
      .from('player_suspensions')
      .select('*')
      .eq('club_id', clubId)
      .eq('player_id', playerId)
      .gt('matches_remaining', 0)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar suspensões do jogador:', error);
      return [];
    }

    return suspensions || [];
  } catch (error) {
    console.error('Erro ao buscar suspensões do jogador:', error);
    return [];
  }
}