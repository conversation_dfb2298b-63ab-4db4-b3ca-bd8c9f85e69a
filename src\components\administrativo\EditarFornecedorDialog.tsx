import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { Supplier, updateSupplier } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";
import { fetchAddressByCEP } from "@/lib/cep";
import { SupplierOrdersTab } from "./SupplierOrdersTab";

interface EditarFornecedorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  supplier: Supplier;
  onSuccess?: () => void;
}

export function EditarFornecedorDialog({
  open,
  onOpenChange,
  clubId,
  supplier,
  onSuccess
}: EditarFornecedorDialogProps) {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Campos básicos
  const [companyName, setCompanyName] = useState(supplier.company_name || "");
  const [phone1, setPhone1] = useState(supplier.phone1 || "");
  const [phone2, setPhone2] = useState(supplier.phone2 || "");
  const [email, setEmail] = useState(supplier.email || "");
  const [expirationDate, setExpirationDate] = useState(supplier.expiration_date ?
    new Date(supplier.expiration_date).toISOString().split('T')[0] : "");

  const [observation, setObservation] = useState(supplier.observation || "");

  // Campos de endereço
  const [zipCode, setZipCode] = useState(supplier.zip_code || "");
  const [state, setState] = useState(supplier.state || "");
  const [city, setCity] = useState(supplier.city || "");
  const [address, setAddress] = useState(supplier.address || "");
  const [addressNumber, setAddressNumber] = useState(supplier.address_number || "");

  // Campos bancários
  const [bankName, setBankName] = useState(supplier.bank_name || "");
  const [bankAgency, setBankAgency] = useState(supplier.bank_agency || "");
  const [bankAccount, setBankAccount] = useState(supplier.bank_account || "");
  const [bankPix, setBankPix] = useState(supplier.bank_pix || "");

  // Atualizar campos quando o fornecedor mudar
  useEffect(() => {
    setCompanyName(supplier.company_name || "");
    setPhone1(supplier.phone1 || "");
    setPhone2(supplier.phone2 || "");
    setEmail(supplier.email || "");
    setExpirationDate(supplier.expiration_date ?
      new Date(supplier.expiration_date).toISOString().split('T')[0] : "");
    setZipCode(supplier.zip_code || "");
    setState(supplier.state || "");
    setCity(supplier.city || "");
    setAddress(supplier.address || "");
    setAddressNumber(supplier.address_number || "");
    setBankName(supplier.bank_name || "");
    setBankAgency(supplier.bank_agency || "");
    setBankAccount(supplier.bank_account || "");
    setBankPix(supplier.bank_pix || "");
    setObservation(supplier.observation || "");
  }, [supplier]);

  // Função para buscar endereço pelo CEP
  const handleZipCodeBlur = async () => {
    if (zipCode.length === 8 || zipCode.length === 9) {
      try {
        const addressData = await fetchAddressByCEP(zipCode);
        if (addressData) {
          setState(addressData.state);
          setCity(addressData.city);
          setAddress(addressData.street);
        }
      } catch (err) {
        console.error("Erro ao buscar CEP:", err);
      }
    }
  };

  // Função para salvar o fornecedor
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!companyName) {
        throw new Error("O nome da empresa é obrigatório");
      }

      // Atualizar o fornecedor
      await updateSupplier(
        clubId,
        user?.id || "",
        supplier.id,
        {
          company_name: companyName,
          phone1: phone1 || undefined,
          phone2: phone2 || undefined,
          email: email || undefined,
          expiration_date: expirationDate || undefined,
          zip_code: zipCode || undefined,
          state: state || undefined,
          city: city || undefined,
          address: address || undefined,
          address_number: addressNumber || undefined,
          bank_name: bankName || undefined,
          bank_agency: bankAgency || undefined,
          bank_account: bankAccount || undefined,
          bank_pix: bankPix || undefined,
          observation: observation || undefined,
        }
      );

      toast({
        title: "Sucesso",
        description: "Fornecedor atualizado com sucesso",
      });

      // Fechar o diálogo
      onOpenChange(false);

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao atualizar fornecedor:", err);
      setError(err.message || "Erro ao atualizar fornecedor");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar fornecedor",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Editar Fornecedor</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="address">Endereço</TabsTrigger>
            <TabsTrigger value="bank">Dados Bancários</TabsTrigger>
            <TabsTrigger value="orders">Pedidos</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companyName">Nome da Empresa *</Label>
                <Input
                  id="companyName"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  placeholder="Nome da empresa"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone1">Telefone Principal</Label>
                  <Input
                    id="phone1"
                    value={phone1}
                    onChange={(e) => setPhone1(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone2">Telefone Secundário</Label>
                  <Input
                    id="phone2"
                    value={phone2}
                    onChange={(e) => setPhone2(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">E-mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expirationDate">Data de Vencimento</Label>
                  <Input
                    id="expirationDate"
                    type="date"
                    value={expirationDate}
                    onChange={(e) => setExpirationDate(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="observation">Observação</Label>
                <Textarea
                  id="observation"
                  value={observation}
                  onChange={(e) => setObservation(e.target.value)}
                  placeholder="Observações sobre o fornecedor"
                  rows={3}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="address" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zipCode">CEP</Label>
                  <Input
                    id="zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    onBlur={handleZipCodeBlur}
                    placeholder="00000-000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">Estado</Label>
                  <Input
                    id="state"
                    value={state}
                    onChange={(e) => setState(e.target.value)}
                    placeholder="UF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="Cidade"
                />
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Rua, Avenida, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="addressNumber">Número</Label>
                  <Input
                    id="addressNumber"
                    value={addressNumber}
                    onChange={(e) => setAddressNumber(e.target.value)}
                    placeholder="Nº"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankName">Nome do Banco</Label>
                <Input
                  id="bankName"
                  value={bankName}
                  onChange={(e) => setBankName(e.target.value)}
                  placeholder="Nome do banco"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bankAgency">Agência</Label>
                  <Input
                    id="bankAgency"
                    value={bankAgency}
                    onChange={(e) => setBankAgency(e.target.value)}
                    placeholder="Número da agência"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bankAccount">Conta</Label>
                  <Input
                    id="bankAccount"
                    value={bankAccount}
                    onChange={(e) => setBankAccount(e.target.value)}
                    placeholder="Número da conta"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankPix">Chave PIX</Label>
                <Input
                  id="bankPix"
                  type="text"
                  value={bankPix}
                  onChange={(e) => setBankPix(e.target.value)}
                  placeholder="Chave PIX"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="orders">
            <SupplierOrdersTab
              supplierId={supplier.id}
              supplierName={supplier.company_name}
              clubId={clubId}
              onRefresh={onSuccess}
            />
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
