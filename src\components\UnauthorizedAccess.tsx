import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getUserAuthorizedClubs } from '@/api/clubAccess';
import { Shield, AlertTriangle, Home, LogOut } from 'lucide-react';

interface UnauthorizedAccessProps {
  attemptedClub?: string;
  message?: string;
}

export function UnauthorizedAccess({ attemptedClub, message }: UnauthorizedAccessProps) {
  const navigate = useNavigate();
  const [authorizedClubs, setAuthorizedClubs] = useState<{ id: number; slug: string | null; name: string }[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadAuthorizedClubs() {
      try {
        const clubs = await getUserAuthorizedClubs();
        setAuthorizedClubs(clubs);
      } catch (error) {
        console.error('Erro ao carregar clubes autorizados:', error);
      } finally {
        setLoading(false);
      }
    }

    loadAuthorizedClubs();
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    navigate('/login', { replace: true });
  };

  const handleGoToAuthorizedClub = (club: { id: number; slug: string | null; name: string }) => {
    if (club.slug) {
      navigate(`/${club.slug}/dashboard`, { replace: true });
    } else {
      // Fallback se não há slug
      localStorage.setItem('clubId', String(club.id));
      navigate('/redirect-to-club', { replace: true });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-red-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl border-red-200 shadow-2xl">
        <CardHeader className="text-center pb-6 bg-gradient-to-r from-red-50 to-red-100">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-red-100 rounded-full">
              <Shield className="w-12 h-12 text-red-600" />
            </div>
          </div>
          <CardTitle className="text-3xl font-bold text-red-800 mb-2">
            Acesso Não Autorizado
          </CardTitle>
          <p className="text-red-600 text-lg">
            Você não tem permissão para acessar este clube
          </p>
        </CardHeader>

        <CardContent className="p-8 space-y-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <AlertDescription className="text-red-800 font-medium">
              <strong>ATENÇÃO:</strong> Tentativa de acesso não autorizado foi registrada.
              {attemptedClub && (
                <span className="block mt-2">
                  Clube tentado: <code className="bg-red-100 px-2 py-1 rounded text-sm">{attemptedClub}</code>
                </span>
              )}
            </AlertDescription>
          </Alert>

          {message && (
            <div className="bg-gray-50 p-4 rounded-lg border">
              <p className="text-gray-700">{message}</p>
            </div>
          )}

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">O que você pode fazer:</h3>
            
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Carregando seus clubes...</p>
              </div>
            ) : authorizedClubs.length > 0 ? (
              <div className="space-y-3">
                <p className="text-gray-600">Acesse um dos seus clubes autorizados:</p>
                <div className="grid gap-2">
                  {authorizedClubs.map((club) => (
                    <Button
                      key={club.id}
                      variant="outline"
                      className="justify-start h-auto p-4 border-green-200 hover:bg-green-50"
                      onClick={() => handleGoToAuthorizedClub(club)}
                    >
                      <Home className="w-4 h-4 mr-3 text-green-600" />
                      <div className="text-left">
                        <div className="font-medium text-green-800">{club.name}</div>
                        {club.slug && (
                          <div className="text-sm text-green-600">/{club.slug}</div>
                        )}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            ) : (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  Você não possui acesso a nenhum clube. Entre em contato com o administrador.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="flex-1 border-red-200 text-red-700 hover:bg-red-50"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Fazer Logout
            </Button>
            
            <Button
              onClick={() => navigate('/', { replace: true })}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              <Home className="w-4 h-4 mr-2" />
              Ir para Home
            </Button>
          </div>

          <div className="text-center text-sm text-gray-500 pt-4 border-t">
            <p>
              Se você acredita que isso é um erro, entre em contato com o administrador do sistema.
            </p>
            <p className="mt-1">
              Tentativas de acesso não autorizado são monitoradas e registradas.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}