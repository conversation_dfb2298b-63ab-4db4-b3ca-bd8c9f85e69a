import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, User, ArrowRight, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BlogSEO } from '@/components/blog/BlogSEO';
import { BlogStats } from '@/components/blog/BlogStats';

interface BlogPost {
  id: string;
  title: string;
  description: string;
  content: string;
  slug: string;
  category: string;
  tags: string[];
  author: string;
  publishedAt: string;
  readTime: number;
  featured: boolean;
  image?: string;
}

import { blogPosts } from '@/data/blog-posts';

const categories = ['Todos', 'Gestão Esportiva', 'Financeiro', 'Médico', 'Treinos', 'Logística'];

export default function BlogHome() {
  const [posts, setPosts] = useState<BlogPost[]>(blogPosts);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(blogPosts);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Todos');

  useEffect(() => {
    let filtered = posts;

    // Filtrar por categoria
    if (selectedCategory !== 'Todos') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPosts(filtered);
  }, [posts, searchTerm, selectedCategory]);

  const featuredPost = posts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <>
      <BlogSEO
        title="Blog - Game Day Nexus | Gestão Esportiva Profissional"
        description="Guias, tutoriais e dicas para gestão profissional de clubes de futebol. Templates gratuitos, cases de sucesso e melhores práticas."
        canonical="/blog"
        type="website"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Simple Header */}
        <header className="bg-white shadow-sm border-b sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/" className="flex items-center space-x-2">
                <img src="/logo-branca.png" alt="Game Day Nexus Logo" className="h-10 w-auto" />
                <div className="flex flex-col hidden sm:flex">
                  <span className="font-bold text-xl text-gray-900">Game Day Nexus</span>
                  <span className="text-xs text-blue-600 font-medium">Gestão Esportiva</span>
                </div>
              </Link>
              
              <div className="flex items-center space-x-4">
                <Link to="/" className="text-gray-600 hover:text-blue-600 font-medium">
                  Home
                </Link>
                <Link to="/login">
                  <Button variant="outline" size="sm">Entrar</Button>
                </Link>
                <Link to="/trial">
                  <Button size="sm">Teste Grátis</Button>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Blog Game Day Nexus
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Guias, tutoriais e dicas para gestão profissional de clubes de futebol. 
                Templates gratuitos, cases de sucesso e melhores práticas.
              </p>
            </div>

            {/* Search and Filter */}
            <div className="mt-8 flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Featured Post */}
          {featuredPost && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Post em Destaque</h2>
              <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group border-2 border-blue-100">
                <div className="md:flex">
                  <div className="md:w-1/2 relative">
                    {featuredPost.image && (
                      <img
                        src={featuredPost.image}
                        alt={featuredPost.title}
                        className="w-full h-64 md:h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    )}
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                        ⭐ Post em Destaque
                      </Badge>
                    </div>
                  </div>
                  <div className="md:w-1/2 p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary">{featuredPost.category}</Badge>
                      <span className="text-sm text-gray-500">•</span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {featuredPost.readTime} min de leitura
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      <Link 
                        to={`/blog/${featuredPost.slug}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {featuredPost.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {featuredPost.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-gray-500">
                        <User className="h-4 w-4 mr-1" />
                        {featuredPost.author}
                        <span className="mx-2">•</span>
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(featuredPost.publishedAt).toLocaleDateString('pt-BR')}
                      </div>
                      <Link to={`/blog/${featuredPost.slug}`}>
                        <Button variant="outline" size="sm">
                          Ler mais
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {/* Regular Posts Grid */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {selectedCategory === 'Todos' ? 'Todos os Posts' : `Posts de ${selectedCategory}`}
            </h2>
            
            {regularPosts.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">
                  {searchTerm || selectedCategory !== 'Todos' 
                    ? 'Nenhum post encontrado com os filtros aplicados.' 
                    : 'Nenhum post disponível no momento.'
                  }
                </p>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {regularPosts.map((post) => (
                  <Card key={post.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                    <div className="relative">
                      {post.image && (
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      )}
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
                          {post.category}
                        </Badge>
                      </div>
                      <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-xs flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {post.readTime} min
                      </div>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-lg leading-tight">
                        <Link 
                          to={`/blog/${post.slug}`}
                          className="hover:text-blue-600 transition-colors group-hover:text-blue-600"
                        >
                          {post.title}
                        </Link>
                      </CardTitle>
                      <CardDescription className="line-clamp-2 text-gray-600">
                        {post.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {post.author}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(post.publishedAt).toLocaleDateString('pt-BR')}
                        </div>
                      </div>
                      <Link to={`/blog/${post.slug}`} className="block">
                        <Button variant="outline" size="sm" className="w-full group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-600 transition-colors">
                          Ler mais
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Blog Stats */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Resultados Comprovados
            </h2>
            <BlogStats />
          </div>

          {/* Newsletter CTA */}
          <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">
              🚀 Receba Conteúdo Exclusivo
            </h3>
            <p className="mb-6 max-w-2xl mx-auto opacity-90">
              Templates gratuitos, guias práticos e dicas de gestão esportiva 
              direto no seu email. Sem spam, apenas conteúdo de valor.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <Input 
                placeholder="Seu melhor email"
                className="flex-1 bg-white text-gray-900"
              />
              <Button variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                Inscrever-se Grátis
              </Button>
            </div>
            <p className="text-xs mt-3 opacity-75">
              ✅ Sem spam • ✅ Cancele quando quiser • ✅ 100% gratuito
            </p>
          </div>
        </div>
      </div>
    </>
  );
}