# Estrutura da Planilha de Fluxo de Caixa para Clubes

## Abas da Planilha

### 1. **Dashboard** (Visão Geral)
```
A1: FLUXO DE CAIXA - [NOME DO CLUBE]
A3: Período: Janeiro a Dezembro 2025

Indicadores Principais:
- Receita Total: =SOMA(Receitas!B2:M2)
- Despesa Total: =SOMA(Despesas!B2:M2)  
- Saldo Atual: =Receita Total - Despesa Total
- Inadimplência: =Inadimplencia!B15

Gráficos:
- Receitas x Despesas (mensal)
- Evolução do saldo
- Receitas por categoria
- <PERSON><PERSON> despesas
```

### 2. **Receitas** (Detalhamento)
```
     A          B      C      D      E      F      G      H      I      J      K      L      M      N
1    RECEITAS   JAN    FEV    MAR    ABR    MAI    JUN    JUL    AGO    SET    OUT    NOV    DEZ    TOTAL
2    Mensalidades
3    Infantil   1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   =SOMA(B3:M3)
4    Juvenil    2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   =SOMA(B4:M4)
5    Adulto     2500   2500   2500   2500   2500   2500   2500   2500   2500   2500   2500   2500   =SOMA(B5:M5)
6    
7    Outras Receitas
8    Patrocínios 5000   5000   5000   5000   5000   5000   5000   5000   5000   5000   5000   5000   =SOMA(B8:M8)
9    Eventos     1000    500   2000    800   1500   3000   1000    500   1200   2000   1500   2500   =SOMA(B9:M9)
10   Vendas       200    150    300    250    180    400    220    180    280    350    200    290   =SOMA(B10:M10)
11   
12   TOTAL      =SOMA(B3:B10) [aplicar para todas as colunas]
```

### 3. **Despesas** (Detalhamento)
```
     A              B      C      D      E      F      G      H      I      J      K      L      M      N
1    DESPESAS       JAN    FEV    MAR    ABR    MAI    JUN    JUL    AGO    SET    OUT    NOV    DEZ    TOTAL
2    
3    Pessoal
4    Técnicos       3000   3000   3000   3000   3000   3000   3000   3000   3000   3000   3000   3000   =SOMA(B4:M4)
5    Funcionários   2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   2000   =SOMA(B5:M5)
6    Encargos        800    800    800    800    800    800    800    800    800    800    800    800   =SOMA(B6:M6)
7    
8    Operacionais
9    Aluguel Campo  1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   1500   =SOMA(B9:M9)
10   Energia         300    280    320    290    310    350    380    360    340    320    300    280   =SOMA(B10:M10)
11   Água            150    140    160    145    155    170    180    175    165    160    150    140   =SOMA(B11:M11)
12   Material Esp.   500    300    800    400    600   1000    500    300    700    900    500    400   =SOMA(B12:M12)
13   Transporte      800    600   1200    800   1000   1500    800    600   1100   1400    800    700   =SOMA(B13:M13)
14   Alimentação     400    300    600    400    500    750    400    300    550    700    400    350   =SOMA(B14:M14)
15   
16   TOTAL          =SOMA(B4:B14) [aplicar para todas as colunas]
```

### 4. **Inadimplência** (Controle)
```
A1: CONTROLE DE INADIMPLÊNCIA

     A              B           C           D           E           F
1    CATEGORIA      ATLETAS     MENSALIDADE INADIMPL.   %          VALOR PERDIDO
2    Infantil       30          50.00       3           10%        150.00
3    Juvenil        25          80.00       2           8%         160.00  
4    Adulto         20          125.00      1           5%         125.00
5    
6    TOTAL          75          -           6           8%         435.00
7    
8    META INADIMPLÊNCIA: 5%
9    INADIMPLÊNCIA ATUAL: =E6
10   STATUS: =SE(E6<=5%;"✅ DENTRO DA META";"⚠️ ACIMA DA META")
```

### 5. **Projeções** (Cenários)
```
A1: PROJEÇÕES E CENÁRIOS

CENÁRIO CONSERVADOR (Crescimento 5%):
- Receita Anual: =Dashboard!Receita Total * 1.05
- Despesa Anual: =Dashboard!Despesa Total * 1.03
- Saldo Projetado: =Receita - Despesa

CENÁRIO OTIMISTA (Crescimento 15%):
- Receita Anual: =Dashboard!Receita Total * 1.15
- Despesa Anual: =Dashboard!Despesa Total * 1.08
- Saldo Projetado: =Receita - Despesa

CENÁRIO PESSIMISTA (Queda 10%):
- Receita Anual: =Dashboard!Receita Total * 0.90
- Despesa Anual: =Dashboard!Despesa Total * 1.05
- Saldo Projetado: =Receita - Despesa
```

## Fórmulas Importantes

### Indicadores Automáticos
```excel
// Margem de Lucro
=SE(Receitas!N12>0;(Receitas!N12-Despesas!N16)/Receitas!N12*100;0)

// Ponto de Equilíbrio
=Despesas!N16/12

// Crescimento Mensal
=SE(B12>0;(C12-B12)/B12*100;0)

// Alerta de Fluxo Negativo
=SE(B12-B16<0;"⚠️ ATENÇÃO: Fluxo Negativo";"✅ Fluxo Positivo")
```

### Formatação Condicional
```
Valores Negativos: Vermelho, Negrito
Valores Positivos: Verde
Metas Atingidas: Verde com ✅
Metas Não Atingidas: Vermelho com ⚠️
```

## Instruções de Uso (Incluir na planilha)

### Aba "Instruções"
```
COMO USAR ESTA PLANILHA:

1. CONFIGURAÇÃO INICIAL:
   - Substitua "[NOME DO CLUBE]" pelo nome do seu clube
   - Ajuste as categorias conforme sua realidade
   - Defina valores base nas abas Receitas e Despesas

2. ATUALIZAÇÃO MENSAL:
   - Insira valores reais nas colunas mensais
   - Acompanhe os indicadores no Dashboard
   - Verifique alertas de inadimplência

3. ANÁLISE:
   - Use os gráficos para identificar tendências
   - Compare cenários na aba Projeções
   - Monitore o ponto de equilíbrio

4. DICAS:
   - Mantenha backup mensal da planilha
   - Revise categorias trimestralmente
   - Use os alertas para ações preventivas

SUPORTE: <EMAIL>
```

## Validações e Proteções
```excel
// Validação de dados (valores positivos apenas)
=E(ÉNÚMERO(B3);B3>=0)

// Proteção de fórmulas
Proteger células com fórmulas, deixar apenas células de entrada editáveis

// Alertas automáticos
=SE(Inadimplencia!E6>10%;"CRÍTICO: Inadimplência muito alta!";"")
```

Esta estrutura cria uma planilha profissional e funcional que realmente ajuda clubes a controlarem suas finanças. Vou continuar com os outros lead magnets...