/**
 * Componente de loading otimizado para transições rápidas
 */
export function FastLoadingSpinner() {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-team-blue"></div>
    </div>
  );
}

/**
 * Loading para páginas completas (mais elaborado)
 */
export function PageLoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-team-blue to-team-blue/60">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue mx-auto mb-4"></div>
        <h2 className="text-2xl font-bold mb-4 text-team-blue">Carregando...</h2>
        <p className="text-gray-600">Aguarde enquanto carregamos os dados.</p>
      </div>
    </div>
  );
}