import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useCurrentClubId } from '@/context/ClubContext';
import { 
  FileText, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  Download,
  Search,
  Filter
} from 'lucide-react';
import {
  getMonthlyFeeReceipts,
  reviewMonthlyFeeReceipt,
  MonthlyFeeReceipt
} from '@/api/monthlyFees';
import { sendReceiptConfirmation } from '@/services/monthlyFeeEmailService';
import { getClubInfo } from '@/api/api';

export function MonthlyFeeReceiptsManager() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  
  const [receipts, setReceipts] = useState<MonthlyFeeReceipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedReceipt, setSelectedReceipt] = useState<MonthlyFeeReceipt | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [reviewStatus, setReviewStatus] = useState<'approved' | 'rejected'>('approved');
  const [reviewNotes, setReviewNotes] = useState('');
  const [clubInfo, setClubInfo] = useState<any>(null);

  useEffect(() => {
    loadData();
    loadClubInfo();
  }, [clubId, statusFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      const data = await getMonthlyFeeReceipts(clubId, {
        status: statusFilter && statusFilter !== 'all' ? statusFilter : undefined
      });
      setReceipts(data);
    } catch (error: any) {
      console.error('Erro ao carregar comprovantes:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os comprovantes',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadClubInfo = async () => {
    try {
      const info = await getClubInfo(clubId);
      setClubInfo(info);
    } catch (error) {
      console.error('Erro ao carregar informações do clube:', error);
    }
  };

  const handleReview = async () => {
    if (!selectedReceipt) return;

    try {
      await reviewMonthlyFeeReceipt(clubId, selectedReceipt.id, reviewStatus, reviewNotes);
      
      // Enviar email de confirmação
      if (selectedReceipt.player_name) {
        // Buscar email do jogador (seria necessário expandir a query ou fazer uma busca adicional)
        // Por enquanto, vamos assumir que temos o email
        const playerEmail = '<EMAIL>'; // TODO: Buscar email real
        
        await sendReceiptConfirmation({
          clubId,
          playerId: selectedReceipt.player_id,
          playerName: selectedReceipt.player_name,
          playerEmail,
          clubName: clubInfo?.name || 'Clube',
          monthlyFeeId: selectedReceipt.monthly_fee_id,
          feeName: selectedReceipt.monthly_fee_reference?.split(' - ')[0] || 'Mensalidade',
          referenceMonth: parseInt(selectedReceipt.monthly_fee_reference?.split('/')[0]?.split(' - ')[1] || '1'),
          referenceYear: parseInt(selectedReceipt.monthly_fee_reference?.split('/')[1] || new Date().getFullYear().toString()),
          receiptFileName: selectedReceipt.file_name,
          status: reviewStatus,
          reviewNotes: reviewNotes || undefined
        });
      }

      toast({
        title: 'Sucesso',
        description: `Comprovante ${reviewStatus === 'approved' ? 'aprovado' : 'rejeitado'} com sucesso`
      });

      setIsReviewDialogOpen(false);
      setSelectedReceipt(null);
      setReviewNotes('');
      loadData();
    } catch (error: any) {
      console.error('Erro ao revisar comprovante:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao revisar comprovante',
        variant: 'destructive'
      });
    }
  };

  const handleViewReceipt = (receipt: MonthlyFeeReceipt) => {
    // Abrir arquivo em nova aba
    const fileUrl = `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/profileimages/${receipt.file_path}`;
    window.open(fileUrl, '_blank');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Aprovado</Badge>;
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Rejeitado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredReceipts = receipts.filter(receipt => {
    const matchesSearch = !searchTerm || 
      receipt.player_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      receipt.file_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      receipt.monthly_fee_reference?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Comprovantes de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="space-y-2">
              <Label>Buscar</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Buscar por jogador, arquivo ou mensalidade..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendente</SelectItem>
                  <SelectItem value="approved">Aprovado</SelectItem>
                  <SelectItem value="rejected">Rejeitado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {filteredReceipts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum comprovante encontrado</p>
              <p className="text-sm">
                {searchTerm || statusFilter 
                  ? 'Tente ajustar os filtros de busca'
                  : 'Os comprovantes enviados pelos jogadores aparecerão aqui'
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Jogador</TableHead>
                    <TableHead>Mensalidade</TableHead>
                    <TableHead>Arquivo</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Enviado em</TableHead>
                    <TableHead>Revisado por</TableHead>
                    <TableHead className="w-[120px]">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReceipts.map((receipt) => (
                    <TableRow key={receipt.id}>
                      <TableCell>
                        <div className="font-medium">{receipt.player_name}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{receipt.monthly_fee_reference}</div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-sm">{receipt.file_name}</div>
                          <div className="text-xs text-muted-foreground">
                            {receipt.file_size && formatFileSize(receipt.file_size)}
                            {receipt.mime_type && ` • ${receipt.mime_type}`}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(receipt.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(receipt.uploaded_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {receipt.reviewed_at ? (
                          <div className="text-sm">
                            <div>{formatDate(receipt.reviewed_at)}</div>
                            {receipt.review_notes && (
                              <div className="text-xs text-muted-foreground truncate max-w-32">
                                {receipt.review_notes}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewReceipt(receipt)}
                            title="Visualizar arquivo"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          
                          {receipt.status === 'pending' && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedReceipt(receipt);
                                  setReviewStatus('approved');
                                  setIsReviewDialogOpen(true);
                                }}
                                title="Aprovar"
                                className="text-green-600 hover:text-green-700"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedReceipt(receipt);
                                  setReviewStatus('rejected');
                                  setIsReviewDialogOpen(true);
                                }}
                                title="Rejeitar"
                                className="text-red-600 hover:text-red-700"
                              >
                                <XCircle className="w-4 h-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de revisão */}
      <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {reviewStatus === 'approved' ? 'Aprovar' : 'Rejeitar'} Comprovante
            </DialogTitle>
          </DialogHeader>
          
          {selectedReceipt && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <h3 className="font-semibold">{selectedReceipt.player_name}</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedReceipt.monthly_fee_reference}
                </p>
                <p className="text-sm">
                  <strong>Arquivo:</strong> {selectedReceipt.file_name}
                </p>
                <p className="text-sm">
                  <strong>Enviado em:</strong> {formatDate(selectedReceipt.uploaded_at)}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="review_notes">
                  Observações {reviewStatus === 'rejected' ? '(obrigatório para rejeição)' : '(opcional)'}
                </Label>
                <Textarea
                  id="review_notes"
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder={
                    reviewStatus === 'approved' 
                      ? 'Observações sobre a aprovação...'
                      : 'Motivo da rejeição (ex: comprovante ilegível, valor incorreto, etc.)'
                  }
                  rows={3}
                />
              </div>

              {reviewStatus === 'approved' && (
                <div className="text-sm text-muted-foreground">
                  <p>✅ Ao aprovar este comprovante:</p>
                  <p>• A mensalidade será marcada como paga</p>
                  <p>• O jogador receberá um email de confirmação</p>
                </div>
              )}

              {reviewStatus === 'rejected' && (
                <div className="text-sm text-muted-foreground">
                  <p>❌ Ao rejeitar este comprovante:</p>
                  <p>• A mensalidade permanecerá pendente</p>
                  <p>• O jogador receberá um email explicando a rejeição</p>
                  <p>• O jogador poderá enviar um novo comprovante</p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReviewDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleReview}
              disabled={reviewStatus === 'rejected' && !reviewNotes.trim()}
              variant={reviewStatus === 'approved' ? 'default' : 'destructive'}
            >
              {reviewStatus === 'approved' ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Aprovar
                </>
              ) : (
                <>
                  <XCircle className="w-4 h-4 mr-2" />
                  Rejeitar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}