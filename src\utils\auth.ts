export function isAuthenticated() {
  // Verificar se existe um token no localStorage
  const token = localStorage.getItem("token");

  // Se não existir token, o usuário não está autenticado
  if (!token) {
    return false;
  }

  try {
    // Verificar se o token não expirou (tokens JWT têm uma data de expiração)
    // Decodificar o token (formato: header.payload.signature)
    const tokenPart = token.split('.')[1];
    const base64 = tokenPart.replace(/-/g, '+').replace(/_/g, '/')
      .padEnd(tokenPart.length + (4 - (tokenPart.length % 4)) % 4, '=');
    const payload = JSON.parse(atob(base64));

    // Verificar se o token expirou
    const expirationTime = payload.exp * 1000; // Converter para milissegundos
    const currentTime = Date.now();

    if (currentTime > expirationTime) {
      // Token expirou, limpar localStorage
      localStorage.removeItem("token");
      localStorage.removeItem("userId");
      localStorage.removeItem("clubId");
      return false;
    }

    // Token válido
    return true;
  } catch (error) {
    // Se houver algum erro ao decodificar o token, consideramos inválido
    console.error("Erro ao verificar token:", error);
    localStorage.removeItem("token");
    localStorage.removeItem("userId");
    localStorage.removeItem("clubId");
    return false;
  }
}
