import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

let socket: Socket | null = null;

export function useSocketChatTest() {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);

  useEffect(() => {
    const socketUrl = 'https://chat-server-iota-inky.vercel.app';
    const socketPath = '/api/socket-simple';

    console.log('🔌 Conectando Socket.IO de teste...', socketUrl + socketPath);

    socket = io(socketUrl, {
      path: socketPath,
      transports: ['polling'],
      timeout: 20000
    });

    socket.on('connect', () => {
      console.log('✅ Conectado ao teste!');
      setIsConnected(true);
      setMessages(prev => [...prev, 'Conectado com sucesso!']);
    });

    socket.on('disconnect', () => {
      console.log('❌ Desconectado do teste');
      setIsConnected(false);
      setMessages(prev => [...prev, 'Desconectado']);
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Erro de conexão:', error.message);
      setMessages(prev => [...prev, `Erro: ${error.message}`]);
    });

    socket.on('welcome', (data) => {
      console.log('🎉 Mensagem de boas-vindas:', data);
      setMessages(prev => [...prev, `Servidor: ${data.message}`]);
    });

    socket.on('test-response', (data) => {
      console.log('📨 Resposta do teste:', data);
      setMessages(prev => [...prev, `Resposta: ${JSON.stringify(data)}`]);
    });

    return () => {
      socket?.disconnect();
      socket = null;
      setIsConnected(false);
    };
  }, []);

  const sendTestMessage = (message: string) => {
    if (socket?.connected) {
      console.log('📤 Enviando mensagem de teste:', message);
      socket.emit('test-message', { text: message, timestamp: new Date().toISOString() });
      setMessages(prev => [...prev, `Enviado: ${message}`]);
    } else {
      setMessages(prev => [...prev, 'Erro: Não conectado']);
    }
  };

  return {
    isConnected,
    messages,
    sendTestMessage
  };
}