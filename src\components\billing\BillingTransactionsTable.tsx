import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { MoreHorizontal, Eye, Trash2, CheckCircle, Download, Copy, Package } from 'lucide-react';
import { BillingTransaction, markBillingTransactionAsPaid, deleteBillingTransaction } from '@/api/billing';
import { generatePixQRCode, generatePixString } from '@/utils/pixGenerator';
import { BillingTransactionItemsView } from './BillingTransactionItemsView';
import jsPDF from 'jspdf';

interface BillingTransactionsTableProps {
  transactions: BillingTransaction[];
  onUpdate: () => void;
  clubName: string;
  clubId: number;
}

export function BillingTransactionsTable({ transactions, onUpdate, clubName, clubId }: BillingTransactionsTableProps) {
  const [selectedTransaction, setSelectedTransaction] = useState<BillingTransaction | null>(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const getStatusBadge = (status: string) => {
    if (status === 'pago') {
      return <Badge variant="default" className="bg-green-500">Pago</Badge>;
    }
    return <Badge variant="secondary">Pendente</Badge>;
  };

  const getTypeBadge = (type: string) => {
    if (type === 'recebe') {
      return <Badge variant="default" className="bg-green-500">Clube Recebe</Badge>;
    }
    return <Badge variant="destructive">Clube Paga</Badge>;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const handleMarkAsPaid = async (transaction: BillingTransaction) => {
    try {
      await markBillingTransactionAsPaid(clubId, transaction.id);
      toast({
        title: 'Transação atualizada',
        description: 'A transação foi marcada como paga'
      });
      onUpdate();
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar a transação',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (transaction: BillingTransaction) => {
    if (!confirm('Tem certeza que deseja excluir esta transação?')) return;

    try {
      await deleteBillingTransaction(clubId, transaction.id);
      toast({
        title: 'Transação excluída',
        description: 'A transação foi excluída com sucesso'
      });
      onUpdate();
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir a transação',
        variant: 'destructive'
      });
    }
  };

  const handleView = async (transaction: BillingTransaction) => {
    setSelectedTransaction(transaction);
    setViewModalOpen(true);
    
    if (transaction.qr_code_data) {
      setLoading(true);
      try {
        const qrCode = await generatePixQRCode({
          pixKey: transaction.pix_key,
          amount: transaction.amount,
          description: transaction.description,
          merchantName: transaction.type === 'recebe' ? clubName : transaction.entity_name,
          merchantCity: 'SAO PAULO'
        });
        setQrCodeDataURL(qrCode);
      } catch (error) {
        console.error('Erro ao gerar QR Code:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDownloadPDF = (transaction: BillingTransaction) => {
    if (!qrCodeDataURL) return;

    const pdf = new jsPDF();
    
    // Título
    pdf.setFontSize(20);
    pdf.text(transaction.type === 'cobranca' ? 'Cobrança PIX' : 'Recebimento PIX', 20, 30);
    
    // Informações
    pdf.setFontSize(12);
    pdf.text(`${transaction.type === 'cobranca' ? 'Pagador' : 'Recebedor'}: ${transaction.entity_name}`, 20, 50);
    pdf.text(`Valor: ${formatCurrency(transaction.amount)}`, 20, 60);
    pdf.text(`Descrição: ${transaction.description}`, 20, 70);
    pdf.text(`Status: ${transaction.status}`, 20, 80);
    if (transaction.due_date) {
      pdf.text(`Vencimento: ${formatDate(transaction.due_date)}`, 20, 90);
    }
    
    // QR Code
    if (qrCodeDataURL) {
      pdf.addImage(qrCodeDataURL, 'PNG', 20, 100, 80, 80);
    }
    
    // Instruções
    pdf.setFontSize(10);
    pdf.text('Instruções:', 20, 200);
    pdf.text('1. Abra o app do seu banco', 20, 210);
    pdf.text('2. Escolha a opção PIX', 20, 220);
    pdf.text('3. Escaneie o QR Code acima', 20, 230);
    pdf.text('4. Confirme o pagamento', 20, 240);
    
    // Chave PIX
    pdf.text(`Chave PIX: ${transaction.pix_key}`, 20, 260);
    
    const filename = `${transaction.type}-${transaction.entity_name.replace(/\s+/g, '-').toLowerCase()}-${transaction.id}.pdf`;
    pdf.save(filename);
    
    toast({
      title: 'PDF baixado',
      description: 'O arquivo foi salvo com sucesso!'
    });
  };

  const handleCopyPixString = (transaction: BillingTransaction) => {
    if (!transaction.qr_code_data) return;
    
    navigator.clipboard.writeText(transaction.qr_code_data);
    toast({
      title: 'Copiado',
      description: 'Código PIX copiado para a área de transferência'
    });
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tipo</TableHead>
              <TableHead>Entidade</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Descrição</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Vencimento</TableHead>
              <TableHead>Criado em</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  Nenhuma transação encontrada
                </TableCell>
              </TableRow>
            ) : (
              transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>{getTypeBadge(transaction.type)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{transaction.entity_name}</div>
                      <div className="text-sm text-muted-foreground capitalize">
                        {transaction.entity_type === 'player' ? 'Jogador' : 
                         transaction.entity_type === 'collaborator' ? 'Colaborador' : 'Cliente'}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(transaction.amount)}
                  </TableCell>
                  <TableCell className="max-w-[200px] truncate">
                    {transaction.description}
                  </TableCell>
                  <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                  <TableCell>
                    {transaction.due_date ? formatDate(transaction.due_date) : '-'}
                  </TableCell>
                  <TableCell>{formatDate(transaction.created_at)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(transaction)}>
                          <Eye className="mr-2 h-4 w-4" />
                          Visualizar
                        </DropdownMenuItem>
                        {transaction.status === 'pendente' && (
                          <DropdownMenuItem onClick={() => handleMarkAsPaid(transaction)}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Marcar como pago
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDelete(transaction)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Modal de visualização */}
      <Dialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalhes da Transação #{selectedTransaction?.id}</DialogTitle>
          </DialogHeader>
          
          {selectedTransaction && (
            <div className="space-y-6">
              {/* Informações básicas da transação */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Tipo:</span>
                  <div>{getTypeBadge(selectedTransaction.type)}</div>
                </div>
                <div>
                  <span className="font-medium">Status:</span>
                  <div>{getStatusBadge(selectedTransaction.status)}</div>
                </div>
                <div>
                  <span className="font-medium">Entidade:</span>
                  <div>{selectedTransaction.entity_name}</div>
                </div>
                <div>
                  <span className="font-medium">Valor:</span>
                  <div className="font-medium">{formatCurrency(selectedTransaction.amount)}</div>
                </div>
              </div>
              
              <div>
                <span className="font-medium text-sm">Descrição:</span>
                <p className="text-sm text-muted-foreground mt-1">
                  {selectedTransaction.description}
                </p>
              </div>
              
              {selectedTransaction.due_date && (
                <div>
                  <span className="font-medium text-sm">Vencimento:</span>
                  <p className="text-sm text-muted-foreground mt-1">
                    {formatDate(selectedTransaction.due_date)}
                  </p>
                </div>
              )}

              {/* Itens do estoque */}
              <BillingTransactionItemsView 
                clubId={clubId} 
                transactionId={selectedTransaction.id} 
              />
              
              {/* QR Code PIX (apenas para transações pendentes) */}
              {selectedTransaction.status === 'pendente' && (
                <>
                  {loading ? (
                    <div className="flex justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : qrCodeDataURL ? (
                    <div className="space-y-4 border-t pt-4">
                      <h3 className="font-medium">QR Code PIX</h3>
                      <div className="flex justify-center">
                        <img 
                          src={qrCodeDataURL} 
                          alt="QR Code PIX" 
                          className="border rounded-lg"
                        />
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          onClick={() => handleDownloadPDF(selectedTransaction)}
                          className="flex-1"
                          variant="outline"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Baixar PDF
                        </Button>
                        <Button 
                          onClick={() => handleCopyPixString(selectedTransaction)}
                          variant="outline"
                        >
                          <Copy className="w-4 h-4 mr-2" />
                          Copiar PIX
                        </Button>
                      </div>
                    </div>
                  ) : null}
                </>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}