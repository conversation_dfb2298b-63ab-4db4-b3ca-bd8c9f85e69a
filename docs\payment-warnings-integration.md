# Sistema de Avisos de Pagamento - Documentação

## Visão Geral

O sistema de avisos de pagamento foi implementado para notificar usuários administrativos (Presidente, CEO, Diretores, etc.) sobre vencimentos de mensalidade para clubes com planos ativos (não trial).

## Componentes Criados

### 1. `usePaymentWarnings` Hook
- **Localização**: `src/hooks/usePaymentWarnings.ts`
- **Função**: Calcula avisos baseados na data de próximo pagamento
- **Retorna**: Informações sobre status do pagamento e dias restantes

### 2. `useAdminRole` Hook
- **Localização**: `src/hooks/useAdminRole.ts`
- **Função**: Verifica se o usuário tem role administrativo
- **Roles Administrativos**:
  - Presidente
  - Vice presidente
  - CEO
  - Diretor
  - Gerente administrativo
  - Gerente de operações
  - Coordenador

### 3. `PaymentWarningBanner` Component
- **Localização**: `src/components/layout/PaymentWarningBanner.tsx`
- **Função**: Banner de aviso no topo da aplicação
- **Visibilidade**: Apenas para usuários administrativos

### 4. `PaymentStatusCard` Component
- **Localização**: `src/components/dashboard/PaymentStatusCard.tsx`
- **Função**: Card detalhado para dashboard
- **Visibilidade**: Apenas para usuários administrativos

## Lógica de Avisos

### Tipos de Aviso:
1. **`payment_overdue`** - Pagamento em atraso
   - Severidade: `error`
   - Cor: Vermelho
   - Ação: Pagar imediatamente

2. **`payment_due_soon`** - Pagamento vence em breve (≤7 dias)
   - Severidade: `error` (≤2 dias) ou `warning` (3-7 dias)
   - Cor: Vermelho ou Amarelo
   - Ação: Preparar pagamento

3. **`payment_current`** - Pagamento em dia
   - Severidade: `info`
   - Cor: Verde
   - Não exibe aviso

## Integração no Dashboard

Para adicionar o card de pagamento no dashboard, adicione no arquivo de dashboard:

```tsx
import { PaymentStatusCard } from '@/components/dashboard/PaymentStatusCard';

// No componente do dashboard
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Outros cards */}
  <PaymentStatusCard />
</div>
```

## Banco de Dados

### Campo Adicionado:
- **Tabela**: `club_info`
- **Campo**: `next_payment_date` (DATE)
- **Função**: Armazena data do próximo vencimento

### Funções SQL:
1. **`calculate_next_payment_date()`** - Calcula próxima data de pagamento
2. **`update_next_payment_date_on_payment()`** - Atualiza data quando pagamento é feito

### Trigger:
- **`trigger_update_next_payment_date`** - Executa quando pagamento é marcado como pago

## Como Executar a Migração

1. Execute o script SQL:
```bash
psql -d sua_database -f sql/add_next_payment_date_to_club_info.sql
```

2. Ou execute via Supabase Dashboard na aba SQL Editor

## Configuração de Roles

Os roles administrativos são definidos na tabela `collaborator_roles`. Para adicionar novos roles administrativos, edite o array em `useAdminRole.ts`:

```typescript
const adminRoles = [
  'Presidente',
  'Vice presidente', 
  'CEO',
  'Diretor',
  'Gerente administrativo',
  'Gerente de operações',
  'Coordenador',
  // Adicione novos roles aqui
];
```

## Personalização

### Alterar Período de Aviso:
No `usePaymentWarnings.ts`, altere a condição:
```typescript
// Atualmente: 7 dias
if (diffDays <= 7) {
  // Altere para o número de dias desejado
}
```

### Alterar Severidade:
```typescript
severity: diffDays <= 2 ? 'error' : 'warning'
// Altere os valores conforme necessário
```

## Testes

Para testar o sistema:

1. **Criar clube com plano ativo**
2. **Definir `next_payment_date` próxima**
3. **Atribuir role administrativo ao usuário**
4. **Verificar se avisos aparecem**

## Próximos Passos

1. **Notificações por Email**: Implementar envio automático de lembretes
2. **Dashboard Financeiro**: Criar página dedicada para gestão financeira
3. **Histórico de Pagamentos**: Mostrar histórico completo
4. **Integração com Gateway**: Conectar com sistema de pagamento