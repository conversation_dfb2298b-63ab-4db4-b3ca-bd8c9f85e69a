import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, X, FileText, Check, AlertCircle, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface DocumentUploadProps {
  value?: string;
  onChange: (value: string | null, file?: File) => void;
  className?: string;
  disabled?: boolean;
  maxSize?: number; // em MB
  status?: "pending" | "verified" | "rejected" | "missing";
  documentType: string;
  documentLabel: string;
  required?: boolean;
  onView?: () => void;
  actions?: React.ReactNode; // Botões de ação adicionais
  uploading?: boolean; // Indica se está fazendo upload
  errorMessage?: string | null; // Mensagem de erro externa
}

export function DocumentUpload(props: DocumentUploadProps) {
  const {
    value,
    onChange,
    className = "",
    disabled = false,
    maxSize = 5, // 5MB por padrão
    status = "missing",
    documentType,
    documentLabel,
    required = false,
    onView,
    actions,
    uploading = false,
    errorMessage,
  } = props;
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isClient, setIsClient] = useState(false);

  // Garantir que o componente esteja montado no cliente
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Atualizar erro quando a prop errorMessage mudar
  useEffect(() => {
    if (errorMessage !== undefined) {
      setError(errorMessage);
    }
  }, [errorMessage]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const validateFile = (file: File): boolean => {
    // Verificar tamanho
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      setError(`O documento deve ter no máximo ${maxSize}MB`);
      return false;
    }

    // Verificar tipo de arquivo
    const allowedTypes = [
      "application/pdf",
    ];

    if (!allowedTypes.includes(file.type)) {
      setError("Formato de arquivo não suportado");
      return false;
    }

    setError(null);
    return true;
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  // Função para lidar com o clique no botão de upload
  const handleUploadClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (inputRef.current && !disabled) {
      inputRef.current.click();
    }
  };

  // Limpar URL do objeto quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (value && value.startsWith('blob:')) {
        URL.revokeObjectURL(value);
      }
    };
  }, [value]);

  const handleFileChange = (file: File) => {
    try {
      // Criar URL para preview
      const fileUrl = URL.createObjectURL(file);
      // Limpar erros anteriores
      setError(null);
      onChange(fileUrl, file);
    } catch (err) {
      console.error("Erro ao processar arquivo:", err);
      setError("Erro ao processar o arquivo. Tente novamente.");
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        handleFileChange(file);
      }
    }
  };

  const handleRemove = () => {
    onChange(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "verified":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <Check className="h-3 w-3 mr-1" />
            Verificado
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <X className="h-3 w-3 mr-1" />
            Rejeitado
          </Badge>
        );
      case "missing":
        return required ? (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Obrigatório
          </Badge>
        ) : (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            Opcional
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="flex justify-between items-center mb-2">
        <span className="font-medium">{documentLabel}</span>
        {getStatusBadge()}
      </div>

      <div
        className={cn(
          "relative border-2 rounded-md overflow-hidden transition-colors",
          dragActive ? "border-primary bg-primary/5" : "border-dashed border-gray-300",
          disabled && "opacity-50 cursor-not-allowed",
          error && "border-red-500"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {uploading ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
            <p className="text-sm text-gray-600">Enviando documento...</p>
          </div>
        ) : value ? (
          <div
            className={`flex items-center justify-between p-3 ${status === "rejected" ? "bg-red-50 cursor-pointer" : ""}`}
            onClick={status === "rejected" && !disabled ? handleUploadClick : undefined}
          >
            <div className="flex items-center">
              <FileText className={`h-5 w-5 mr-2 ${status === "rejected" ? "text-red-500" : "text-gray-500"}`} />
              <div>
                <span className="text-sm truncate max-w-[150px]">Documento enviado</span>
                {status === "rejected" && (
                  <div>
                    <span className="text-xs text-red-600 block">Documento rejeitado</span>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUploadClick(e);
                      }}
                      className="text-xs text-red-600 block font-medium underline"
                    >
                      Clique para enviar novamente
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center">
              {onView && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Impedir propagação do evento
                    onView();
                  }}
                  className="h-8 px-2 relative z-20" // Aumentar o z-index
                >
                  Ver
                </Button>
              )}
              {actions}

              {!disabled && status !== "verified" && !actions && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Impedir propagação do evento
                    handleRemove();
                  }}
                  className="h-8 px-2 text-red-500 hover:text-red-700 hover:bg-red-50 relative z-20"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <FileText className="h-10 w-10 text-gray-400 mb-3" />
            <p className="text-sm text-gray-600 mb-1">
              {dragActive ? "Solte o arquivo aqui" : `Enviar ${documentLabel}`}
            </p>
            <div className="flex flex-col items-center">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={handleUploadClick}
                disabled={disabled}
              >
                <Upload className="h-4 w-4 mr-2" />
                Selecionar arquivo
              </Button>
              <p className="text-xs text-gray-400 mt-2">
                Arraste e solte o arquivo aqui, ou clique para selecionar
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Formatos suportados: .pdf
              </p>
              <p className="text-xs text-gray-400">
                Tamanho máximo: {maxSize}MB
              </p>
            </div>
          </div>
        )}

        {/* Input de arquivo oculto */}
        {isClient && (
          <input
            ref={inputRef}
            type="file"
            accept=".pdf"
            className="hidden"
            onChange={handleInputChange}
            disabled={disabled || status === "verified" || uploading}
          />
        )}
      </div>

      {error && (
        <p className="text-red-500 text-sm mt-1 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </p>
      )}
    </div>
  );
}
