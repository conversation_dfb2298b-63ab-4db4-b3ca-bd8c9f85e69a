# Guia do Sistema de Sequências de Drills - DrillSequencer

## Visão Geral

O DrillSequencer é um componente avançado que permite criar, gerenciar e reproduzir sequências de treinos de forma interativa. Ele implementa funcionalidades de drag & drop, timeline visual, controles de transição e persistência no banco de dados.

## Funcionalidades Implementadas

### 1. Interface Drag & Drop para Reordenação
- **O que faz**: Permite arrastar e soltar passos do drill para reordená-los
- **Como usar**: Clique e arraste o ícone de "grip" (≡) ao lado de cada passo
- **Feedback visual**: Durante o arraste, o item fica semi-transparente e com sombra

### 2. Timeline Visual
- **O que faz**: Mostra uma representação visual da sequência completa do drill
- **Elementos**:
  - Blocos coloridos representam cada passo
  - Blocos cinzas representam tempos de transição
  - Linha vermelha mostra a posição atual durante reprodução
  - Clique nos blocos para navegar diretamente para um passo

### 3. Controles de Duração e Transição
- **Duração do Passo**: Tempo que cada passo deve durar (em segundos)
- **Tempo de Transição**: Tempo entre passos para preparação/mudança (em segundos)
- **Edição**: Clique no ícone de edição (✏️) para modificar estes valores

### 4. Controles de Reprodução Avançados
- **Play/Pause**: Inicia/pausa a reprodução da sequência
- **Stop**: Para a reprodução e volta ao início
- **Skip**: Navega entre passos (anterior/próximo)
- **Velocidade**: Controla a velocidade de reprodução (0.5x a 2x)
- **Modo Loop**: Repete a sequência infinitamente
- **Transição Auto**: Avança automaticamente entre passos

### 5. Salvamento de Sequências
- **Botão "Salvar Sequência"**: Persiste a sequência atual no banco de dados
- **Inclui**: Ordem dos passos, durações, tempos de transição

## Como Testar o Sistema

### Pré-requisitos
1. Sistema rodando com banco de dados configurado
2. Usuário logado com permissões de treinador/admin
3. Acesso ao módulo de treinamentos

### Teste 1: Criação de Drill Básico
```bash
1. Acesse o sistema de treinamentos
2. Clique em "Novo Drill"
3. Preencha:
   - Nome: "Teste de Sequência"
   - Categoria: "Tático"
   - Dificuldade: "Iniciante"
4. Observe que um passo inicial é criado automaticamente
```

### Teste 2: Adição de Múltiplos Passos
```bash
1. No DrillSequencer (aba "Sequência"), clique "Adicionar"
2. Repita 2-3 vezes para ter 4 passos total
3. Observe que cada passo recebe um número sequencial
4. Verifique que o timeline visual mostra todos os passos
```

### Teste 3: Edição de Passos
```bash
1. Clique no ícone de edição (✏️) de um passo
2. Modifique:
   - Nome: "Aquecimento"
   - Descrição: "Exercícios de aquecimento"
   - Duração: 600 (10 minutos)
   - Transição: 30 (30 segundos)
3. Clique "Salvar"
4. Observe que o timeline visual se atualiza automaticamente
```

### Teste 4: Reordenação com Drag & Drop
```bash
1. Clique e segure o ícone de grip (≡) de um passo
2. Arraste para uma nova posição
3. Solte o mouse
4. Verifique que:
   - A ordem dos passos mudou
   - Os números dos passos se atualizaram
   - O timeline visual reflete a nova ordem
   - Uma notificação de sucesso aparece
```

### Teste 5: Reprodução de Sequência
```bash
1. Configure alguns passos com durações diferentes
2. Ative "Transição Auto" e "Modo Loop"
3. Clique no botão Play (▶️)
4. Observe:
   - Barra de progresso do passo atual
   - Linha vermelha no timeline movendo-se
   - Transição automática entre passos
   - Loop infinito quando chega ao final
```

### Teste 6: Controles de Velocidade
```bash
1. Durante a reprodução, altere a velocidade para 2x
2. Observe que a reprodução acelera
3. Teste com 0.5x para reprodução lenta
4. Verifique que a linha do timeline se move na velocidade correta
```

### Teste 7: Navegação Manual
```bash
1. Durante a reprodução, clique em diferentes blocos do timeline
2. Observe que pula diretamente para o passo selecionado
3. Use os botões de Skip (⏮️ ⏭️) para navegar
4. Verifique que o tempo de reprodução reseta ao mudar de passo
```

### Teste 8: Salvamento de Sequência
```bash
1. Crie uma sequência com 3-4 passos
2. Configure durações e transições diferentes
3. Clique "Salvar Sequência"
4. Verifique que aparece notificação de sucesso
5. Recarregue a página e verifique que a sequência persiste
```

## Estrutura de Dados

### DrillStep (Passo do Drill)
```typescript
interface DrillStep {
  id: string;
  name: string;
  description: string;
  duration: number; // em segundos
  elements: TrainingElement[];
  annotations: any[];
  drawings: DrawingElement[];
  transitionTime?: number; // tempo de transição
}
```

### Estados do Componente
```typescript
// Estados principais
const [editingStep, setEditingStep] = useState<string | null>(null);
const [playbackTime, setPlaybackTime] = useState(0);
const [loopMode, setLoopMode] = useState(false);
const [autoTransition, setAutoTransition] = useState(true);

// Formulário de edição
const [stepForm, setStepForm] = useState({
  name: '',
  description: '',
  duration: 300,
  transitionTime: 5
});
```

## Fluxo de Funcionamento

### 1. Inicialização
```
DrillSequencer recebe props → 
Configura sensores de drag & drop → 
Renderiza timeline e lista de passos
```

### 2. Reprodução
```
Usuário clica Play → 
Timer inicia → 
Atualiza playbackTime a cada frame → 
Verifica se deve avançar para próximo passo → 
Aplica transições e loop se configurado
```

### 3. Drag & Drop
```
Usuário arrasta passo → 
handleDragEnd é chamado → 
arrayMove reordena os passos → 
onDrillUpdate atualiza o drill → 
Timeline e lista se atualizam
```

### 4. Salvamento
```
Usuário clica "Salvar Sequência" → 
handleSaveSequence é chamado → 
Dados são preparados com transições → 
API é chamada (simulada) → 
Notificação de sucesso/erro
```

## Componentes Visuais

### Timeline Visual
- **Blocos de Passo**: Cor primária para passo ativo, secundária para outros
- **Blocos de Transição**: Cinza semi-transparente com bordas
- **Indicador de Reprodução**: Linha vermelha vertical
- **Interatividade**: Clique para navegar

### Lista de Passos
- **Drag Handle**: Ícone de grip para arrastar
- **Badge de Número**: Mostra a ordem do passo
- **Botões de Ação**: Selecionar, editar, deletar
- **Informações**: Duração, elementos, transição

### Controles de Reprodução
- **Botões de Navegação**: Skip anterior/próximo
- **Botão Play/Pause**: Alterna reprodução
- **Botão Stop**: Para e reseta
- **Seletor de Velocidade**: Dropdown com opções
- **Checkboxes**: Loop e transição automática

## Troubleshooting

### Problema: Drag & Drop não funciona
**Solução**: Verifique se as dependências @dnd-kit estão instaladas
```bash
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
```

### Problema: Timeline não atualiza
**Solução**: Verifique se o drill tem passos com durações válidas
```typescript
// Cada passo deve ter duration > 0
step.duration = Math.max(1, step.duration);
```

### Problema: Reprodução não avança
**Solução**: Verifique se autoTransition está ativado e se há próximo passo
```typescript
// Verificar se não é o último passo ou se loop está ativo
if (currentStepIndex < drill.steps.length - 1 || loopMode) {
  // Pode avançar
}
```

### Problema: Salvamento falha
**Solução**: Implementar a chamada real da API
```typescript
// Substituir simulação por chamada real
const result = await updateDrill(drill.id, drillToSave);
```

## Próximos Passos

1. **Integração com API Real**: Conectar com backend para persistência
2. **Animações de Transição**: Adicionar animações suaves entre passos
3. **Templates de Sequência**: Salvar sequências como templates reutilizáveis
4. **Exportação**: Permitir exportar sequências como PDF/vídeo
5. **Colaboração**: Permitir compartilhamento de sequências entre usuários

## Arquivos Relacionados

- `src/components/training/DrillSequencer.tsx` - Componente principal
- `src/components/training/InteractiveTrainingBuilder.tsx` - Container pai
- `src/lib/training-api.ts` - Funções de API
- `sql/training-drill-system.sql` - Schema do banco de dados