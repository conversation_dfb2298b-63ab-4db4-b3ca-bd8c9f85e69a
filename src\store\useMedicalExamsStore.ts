import { create } from "zustand";
import {
  MedicalExam,
  getMedicalExams,
  getPlayerMedicalExams,
  getClubMedicalExams,
  createMedicalExam,
  updateMedicalExam,
  deleteMedicalExam,
  uploadExam,
  ExamStatus
} from "@/api/api";

interface MedicalExamsState {
  exams: MedicalExam[];
  loading: boolean;
  error: string | null;
  fetchExams: (clubId: number, recordId: number) => Promise<void>;
  fetchPlayerExams: (clubId: number, playerId: string) => Promise<void>;
  fetchClubExams: (
    clubId: number,
    startDate?: string,
    endDate?: string
  ) => Promise<void>;
  addExam: (
    clubId: number,
    userId: string,
    exam: Omit<MedicalExam, "id" | "club_id" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateExam: (
    clubId: number,
    userId: string,
    id: number,
    exam: Partial<Omit<MedicalExam, "id" | "club_id" | "created_at" | "updated_at">>
  ) => Promise<void>;
  deleteExam: (clubId: number, userId: string, id: number) => Promise<void>;
  uploadExamFile: (clubId: number, userId: string, examId: number, file: File) => Promise<void>;
}

export const useMedicalExamsStore = create<MedicalExamsState>((set) => ({
  exams: [],
  loading: false,
  error: null,

  fetchExams: async (clubId, recordId) => {
    set({ loading: true, error: null });
    try {
      const exams = await getMedicalExams(clubId, recordId);
      set({ exams, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar exames médicos",
        loading: false,
      });
    }
  },

  fetchPlayerExams: async (clubId, playerId) => {
    set({ loading: true, error: null });
    try {
      const exams = await getPlayerMedicalExams(clubId, playerId);
      set({ exams, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar exames do jogador",
        loading: false,
      });
    }
  },

  fetchClubExams: async (clubId, startDate, endDate) => {
    set({ loading: true, error: null });
    try {
      const exams = await getClubMedicalExams(clubId, startDate, endDate);
      set({ exams, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar exames",
        loading: false,
      });
    }
  },

  addExam: async (clubId, userId, exam) => {
    set({ loading: true, error: null });
    try {
      const newExam = await createMedicalExam(clubId, userId, exam);
      set((state) => ({
        exams: [...state.exams, newExam],
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar exame médico",
        loading: false,
      });
    }
  },

  updateExam: async (clubId, userId, id, exam) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMedicalExam(clubId, userId, id, exam);
      set((state) => ({
        exams: state.exams.map((e) => (e.id === id ? updated : e)),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao atualizar exame médico",
        loading: false,
      });
    }
  },

  deleteExam: async (clubId, userId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteMedicalExam(clubId, userId, id);
      set((state) => ({
        exams: state.exams.filter((e) => e.id !== id),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao excluir exame médico",
        loading: false,
      });
    }
  },

  uploadExamFile: async (clubId, userId, examId, file) => {
    set({ loading: true, error: null });
    try {
      const fileUrl = await uploadExam(clubId, userId, examId, file);
      set((state) => ({
        exams: state.exams.map((e) =>
          e.id === examId ? { ...e, file_url: fileUrl } : e
        ),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao fazer upload do arquivo de exame",
        loading: false,
      });
    }
  },
}));
