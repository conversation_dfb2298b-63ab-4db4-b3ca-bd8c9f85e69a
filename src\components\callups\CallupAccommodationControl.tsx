import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { Plus, UserPlus, Pen, Trash2, X } from "lucide-react";
import { Collaborator } from "@/api/collaborators";
import type { Category, Player } from "@/api/api";
import { getCategoryPlayers, getPlayers } from "@/api/api";
import {
  getCallupAccommodations,
  getCallupHotelRooms,
  getCallupAccommodationGuests,
  createCallupAccommodation,
  updateCallupAccommodation,
  addGuestToCallupAccommodation,
  deleteCallupAccommodation,
  removeCallupAccommodationGuest,
  CallupAccommodation as DBAccommodation,
  CallupHotelRoom,
  CallupAccommodationGuest,
} from "@/api/callupAccommodations";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";

interface RoomForm {
  number: string;
  capacity: number;
}

interface CallupAccommodationControlProps {
  callupId: number;
  collaborators: Collaborator[];
  categories: Category[];
  clubId: number;
  defaultCategoryId?: number | null;
}

export function CallupAccommodationControl({ callupId, collaborators, categories, clubId, defaultCategoryId }: CallupAccommodationControlProps) {
  const [accommodations, setAccommodations] = useState<DBAccommodation[]>([]);
  const [guests, setGuests] = useState<Record<number, CallupAccommodationGuest[]>>({});
  const [rooms, setRooms] = useState<Record<number, CallupHotelRoom[]>>({});
  const [showDialog, setShowDialog] = useState(false);
  const [editingAcc, setEditingAcc] = useState<DBAccommodation | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: "hotel",
    address: "",
    roomsCount: "",
    capacity: "",
  });
  const [roomsForm, setRoomsForm] = useState<RoomForm[]>([]);

  const [categoryFilter, setCategoryFilter] = useState<string>(
    defaultCategoryId ? String(defaultCategoryId) : "all"
  );
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);

  useEffect(() => {
    setCategoryFilter(defaultCategoryId ? String(defaultCategoryId) : "all");
  }, [defaultCategoryId]);

  type RoomStatus = 'all' | 'empty' | 'available' | 'full'
  const [roomFilters, setRoomFilters] = useState<Record<number, RoomStatus>>({})

  const [guestDialogOpen, setGuestDialogOpen] = useState(false);
  const [currentAcc, setCurrentAcc] = useState<DBAccommodation | null>(null);
  const [guestForm, setGuestForm] = useState({
    personType: "player",
    selectedPlayerIds: [] as string[], // Changed to array
    selectedCollaboratorIds: [] as string[], // Changed to array
    roomId: "",
    checkIn: "",
    checkOut: "",
  });

  useEffect(() => {
    const fetchPlayers = async () => {
      if (!guestDialogOpen) return;
      try {
        if (categoryFilter === "all") {
          const all = await getPlayers(clubId);
          setAvailablePlayers(all);
        } else {
          const catPlayers = await getCategoryPlayers(clubId, parseInt(categoryFilter));
          setAvailablePlayers(catPlayers);
        }
      } catch (e) {
        console.error("Erro ao carregar jogadores", e);
        setAvailablePlayers([]);
      }
    };
    fetchPlayers();
  }, [guestDialogOpen, categoryFilter, clubId]);

  useEffect(() => {
    getCallupAccommodations(callupId)
      .then(async (accs) => {
        setAccommodations(accs);
        const roomsObj: Record<number, CallupHotelRoom[]> = {};
        const guestsObj: Record<number, CallupAccommodationGuest[]> = {};
        for (const acc of accs) {
          if (acc.type === 'hotel') {
            roomsObj[acc.id] = await getCallupHotelRooms(acc.id);
          }
          guestsObj[acc.id] = await getCallupAccommodationGuests(acc.id);
        }
        setRooms(roomsObj);
        setGuests(guestsObj);
      })
      .catch((e) => console.error('Erro ao carregar alojamentos', e));
  }, [callupId]);

  const saveAccommodation = async () => {
    const capacity =
      formData.type === 'hotel'
        ? roomsForm.reduce((sum, r) => sum + r.capacity, 0)
        : parseInt(formData.capacity) || 0;

    if (editingAcc) {
      const updated = await updateCallupAccommodation(editingAcc.id, {
        name: formData.name,
        type: formData.type as 'hotel' | 'apartment',
        address: formData.address,
        capacity,
      }, formData.type === 'hotel' ? roomsForm.map(r => ({ room_number: r.number, capacity: r.capacity })) : undefined);

      setAccommodations(accommodations.map(a => a.id === updated.id ? updated : a));
      if (updated.type === 'hotel') {
        const rms = await getCallupHotelRooms(updated.id);
        setRooms(prev => ({ ...prev, [updated.id]: rms }));
      }
    } else {
      const created = await createCallupAccommodation(callupId, {
        name: formData.name,
        type: formData.type as 'hotel' | 'apartment',
        address: formData.address,
        capacity,
      }, formData.type === 'hotel' ? roomsForm.map(r => ({ room_number: r.number, capacity: r.capacity })) : undefined);

      setAccommodations([...accommodations, created]);
      if (created.type === 'hotel') {
        const rms = await getCallupHotelRooms(created.id);
        setRooms(prev => ({ ...prev, [created.id]: rms }));
      }
    }

    setShowDialog(false);
    setEditingAcc(null);
    setFormData({ name: '', type: 'hotel', address: '', roomsCount: '', capacity: '' });
    setRoomsForm([]);
  };

  const openGuestDialog = async (acc: DBAccommodation) => {
    setCurrentAcc(acc);
    if (acc.type === 'hotel') {
      const rms = await getCallupHotelRooms(acc.id);
      setRooms(prev => ({ ...prev, [acc.id]: rms }));
    }
    const g = await getCallupAccommodationGuests(acc.id);
    setGuests(prev => ({ ...prev, [acc.id]: g }));
    setGuestDialogOpen(true);
    // Reset selected guests when opening the dialog
    setGuestForm(prev => ({ ...prev, selectedPlayerIds: [], selectedCollaboratorIds: [] }));
  };

  const addGuest = async () => {
    if (!currentAcc) return;

    const guestsToAdd: { playerId?: string; collaboratorId?: number; roomId?: number; checkIn?: string; checkOut?: string }[] = [];

    if (guestForm.personType === "player") {
      if (guestForm.selectedPlayerIds.length === 0) {
        toast({ title: "Erro", description: "Selecione pelo menos um jogador", variant: "destructive" });
        return;
      }
      for (const playerId of guestForm.selectedPlayerIds) {
        guestsToAdd.push({
          playerId,
          roomId: guestForm.roomId ? parseInt(guestForm.roomId) : undefined,
          checkIn: guestForm.checkIn || undefined,
          checkOut: guestForm.checkOut || undefined,
        });
      }
    } else { // collaborator
      if (guestForm.selectedCollaboratorIds.length === 0) {
        toast({ title: "Erro", description: "Selecione pelo menos um colaborador", variant: "destructive" });
        return;
      }
      for (const collaboratorId of guestForm.selectedCollaboratorIds) {
        guestsToAdd.push({
          collaboratorId: parseInt(collaboratorId),
          roomId: guestForm.roomId ? parseInt(guestForm.roomId) : undefined,
          checkIn: guestForm.checkIn || undefined,
          checkOut: guestForm.checkOut || undefined,
        });
      }
    }

    const currentGuestsInAcc = guests[currentAcc.id] || [];

    for (const guestData of guestsToAdd) {
      const { playerId, collaboratorId, roomId, checkIn, checkOut } = guestData;

      // Check for existing guest
      if (playerId && currentGuestsInAcc.some((g) => g.player_id === playerId)) {
        toast({ title: "Aviso", description: `Jogador ${availablePlayers.find(p => p.id === playerId)?.name || ''} já hospedado`, variant: "warning" });
        continue;
      }
      if (collaboratorId && currentGuestsInAcc.some((g) => g.collaborator_id === collaboratorId)) {
        toast({ title: "Aviso", description: `Colaborador ${collaborators.find(c => c.id === collaboratorId)?.full_name} já hospedado`, variant: "warning" });
        continue;
      }

      // Capacity check for apartment
      if (currentAcc.type === "apartment" && currentAcc.capacity !== null) {
        if (currentGuestsInAcc.length >= (currentAcc.capacity || 0)) {
          toast({ title: "Erro", description: "Alojamento lotado. Não foi possível adicionar todos os hóspedes.", variant: "destructive" });
          break; // Stop adding if apartment is full
        }
      }

      // Capacity check for hotel room
      if (currentAcc.type === "hotel" && roomId) {
        const room = rooms[currentAcc.id]?.find((r) => r.id === roomId);
        if (room) {
          const occupied = currentGuestsInAcc.filter((g) => g.hotel_room_id === room.id).length;
          if (occupied >= room.capacity) {
            const name = playerId
              ? availablePlayers.find(p => p.id === playerId)?.name
              : collaborators.find(c => c.id === collaboratorId)?.full_name;
            toast({ title: "Erro", description: `Quarto ${room.room_number} lotado. Não foi possível adicionar ${name}.`, variant: "destructive" });
            continue; // Skip this guest, try next
          }
        }
      }

      try {
        const created = await addGuestToCallupAccommodation(currentAcc.id, {
          player_id: playerId,
          collaborator_id: collaboratorId,
          hotel_room_id: roomId,
          check_in_date: checkIn,
          check_out_date: checkOut,
        });
        currentGuestsInAcc.push(created); // Add to current list for immediate UI update
      } catch (error) {
        console.error("Erro ao adicionar hóspede:", error);
        toast({ title: "Erro", description: "Não foi possível adicionar um dos hóspedes.", variant: "destructive" });
      }
    }

    setGuests(prev => ({
      ...prev,
      [currentAcc.id]: [...currentGuestsInAcc] // Update state with all added guests
    }));

    setGuestDialogOpen(false);
    setGuestForm({ personType: "player", selectedPlayerIds: [], selectedCollaboratorIds: [], roomId: "", checkIn: "", checkOut: "" });
  };

  const removeAccommodation = async (acc: DBAccommodation) => {
    if (!confirm(`Excluir o alojamento ${acc.name}?`)) return;
    await deleteCallupAccommodation(acc.id);
    setAccommodations(accommodations.filter(a => a.id !== acc.id));
    setRooms(prev => {
      const { [acc.id]: _, ...rest } = prev;
      return rest;
    });
    setGuests(prev => {
      const { [acc.id]: _, ...rest } = prev;
      return rest;
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button onClick={() => { setEditingAcc(null); setShowDialog(true); }} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Novo Alojamento
        </Button>
      </div>

      {accommodations.length === 0 ? (
        <p className="text-center text-muted-foreground">Nenhum alojamento criado</p>
      ) : (
        accommodations.map((acc) => (
          <Card key={acc.id} className="relative">
            <CardHeader className="flex flex-row items-start justify-between">
              <div>
                <CardTitle>{acc.name}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {acc.type === "hotel" ? "Hotel" : "Apartamento"} - {acc.address}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={async () => {
                  setEditingAcc(acc);
                  setFormData({
                    name: acc.name,
                    type: acc.type,
                    address: acc.address || '',
                    roomsCount: acc.type === 'hotel' ? String(rooms[acc.id]?.length || 0) : '',
                    capacity: acc.type === 'apartment' ? String(acc.capacity || '') : ''
                  });
                  if (acc.type === 'hotel') {
                    let rms = rooms[acc.id];
                    if (!rms) {
                      rms = await getCallupHotelRooms(acc.id);
                      setRooms(prev => ({ ...prev, [acc.id]: rms! }));
                    }
                    setRoomsForm(rms.map(r => ({ number: r.room_number, capacity: r.capacity })));
                  } else {
                    setRoomsForm([]);
                  }
                  setShowDialog(true);
                }}>
                  <Pen className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => openGuestDialog(acc)}>
                  <UserPlus className="h-4 w-4 mr-1" />
                  Adicionar Hóspede
                </Button>
                <Button variant="destructive" size="sm" onClick={() => removeAccommodation(acc)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="info">
                <TabsList>
                  <TabsTrigger value="info">Informações</TabsTrigger>
                  <TabsTrigger value="hospedes">Hóspedes</TabsTrigger>
                </TabsList>

                <TabsContent value="info">
                  <div className="space-y-4">
                    {acc.address && (
                      <div>
                        <h4 className="font-medium text-sm">Endereço</h4>
                        <p className="text-sm text-muted-foreground">{acc.address}</p>
                      </div>
                    )}
                    <div>
                      <h4 className="font-medium text-sm">Capacidade</h4>
                      <p className="text-sm text-muted-foreground">{acc.capacity || "-"}</p>
                    </div>
                    {acc.type === "hotel" && rooms[acc.id]?.length && (
                      <div>
                        <h4 className="font-medium text-sm mb-2">Quartos</h4>
                        <div className="mb-2 flex items-center gap-2">
                          <Label>Status</Label>
                          <Select
                            value={roomFilters[acc.id] ?? 'all'}
                            onValueChange={(v) =>
                              setRoomFilters((prev) => ({
                                ...prev,
                                [acc.id]: v as RoomStatus,
                              }))
                            }
                          >
                            <SelectTrigger className="w-40">
                              <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Todos</SelectItem>
                              <SelectItem value="empty">Vazio</SelectItem>
                              <SelectItem value="available">Com vaga</SelectItem>
                              <SelectItem value="full">Cheio</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Quarto</TableHead>
                              <TableHead>Capacidade</TableHead>
                              <TableHead>Ocupação</TableHead>
                              <TableHead>Hóspedes</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                          {rooms[acc.id]
                              .filter((room) => {
                                const occLen =
                                  guests[acc.id]?.filter(
                                    (g) => g.hotel_room_id === room.id,
                                  ).length || 0;
                                const filter = roomFilters[acc.id] ?? 'all';
                                if (filter === 'empty') return occLen === 0;
                                if (filter === 'available')
                                  return occLen > 0 && occLen < room.capacity;
                                if (filter === 'full') return occLen >= room.capacity;
                                return true;
                              })
                              .map((room) => {
                                const occ =
                                  guests[acc.id]?.filter(
                                    (g) => g.hotel_room_id === room.id,
                                  ) || [];
                                const status =
                                  occ.length === 0
                                    ? 'empty'
                                    : occ.length < room.capacity
                                    ? 'available'
                                    : 'full';
                                const textColor =
                                  status === 'empty'
                                    ? 'text-green-600'
                                    : status === 'available'
                                    ? 'text-blue-600'
                                    : 'text-red-600';
                                return (
                                  <TableRow key={room.id} className={textColor}>
                                    <TableCell>{room.room_number}</TableCell>
                                    <TableCell>{room.capacity}</TableCell>
                                    <TableCell>
                                      {occ.length}/{room.capacity}
                                    </TableCell>
                                    <TableCell>
                                      {occ
                                        .map((o) => o.player_name || o.collaborator_name)
                                        .join(', ') || '-'}
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                    {acc.type === "apartment" && (
                      <div>
                        <h4 className="font-medium text-sm mb-1">Hóspedes</h4>
                        <p className="text-sm text-muted-foreground">
                          {guests[acc.id]?.map(g => g.player_name || g.collaborator_name).join(', ') || 'Nenhum'}
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="hospedes">
                  {guests[acc.id]?.length ? (
                    <ul className="space-y-1 text-sm">
                      {guests[acc.id].map((g) => (
                        <li key={g.id} className="flex justify-between items-center">
                          <span>{g.player_name || g.collaborator_name}</span>
                          <div className="flex items-center gap-2">
                            <span>{g.room_number || '-'}</span>
                            <Button variant="ghost" size="icon" onClick={async () => {
                              await removeCallupAccommodationGuest(g.id);
                              setGuests(prev => ({
                                ...prev,
                                [acc.id]: prev[acc.id].filter(gg => gg.id !== g.id)
                              }));
                            }}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">Nenhum hóspede</p>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))
      )}

      <Dialog open={showDialog} onOpenChange={(v) => { setShowDialog(v); if (!v) setEditingAcc(null); }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingAcc ? 'Editar Alojamento' : 'Novo Alojamento'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome</Label>
              <Input id="name" value={formData.name} onChange={(e) => setFormData({ ...formData, name: e.target.value })} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo</Label>
              <Select
                value={formData.type}
                onValueChange={(v) => {
                  setFormData({ ...formData, type: v, roomsCount: "", capacity: "" });
                  setRoomsForm(v === "hotel" ? [{ number: "01", capacity: 1 }] : []);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hotel">Hotel</SelectItem>
                  <SelectItem value="apartment">Apartamento</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <Input id="address" value={formData.address} onChange={(e) => setFormData({ ...formData, address: e.target.value })} />
            </div>

            {formData.type === "hotel" ? (
              <div className="space-y-2">
                <Label htmlFor="roomsCount">Número de Quartos</Label>
                <Input
                  id="roomsCount"
                  type="number"
                  min="1"
                  value={formData.roomsCount}
                  onChange={(e) => {
                    const count = parseInt(e.target.value) || 0;
                    setFormData({ ...formData, roomsCount: e.target.value });
                    if (count > 0) {
                      const arr = Array.from({ length: count }, (_, idx) => roomsForm[idx] || { number: String(idx + 1).padStart(2, "0"), capacity: 1 });
                      setRoomsForm(arr);
                    } else {
                      setRoomsForm([]);
                    }
                  }}
                />

                {roomsForm.length > 0 && (
                  <div className="border rounded-md p-3 space-y-3 max-h-40 overflow-y-auto">
                    {roomsForm.map((room, index) => (
                      <div key={index} className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor={`room-number-${index}`} className="text-xs">Quarto #{index + 1}</Label>
                          <Input
                            id={`room-number-${index}`}
                            value={room.number}
                            onChange={(e) => {
                              const nf = [...roomsForm];
                              nf[index].number = e.target.value;
                              setRoomsForm(nf);
                            }}
                            className="h-8"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`room-capacity-${index}`} className="text-xs">Capacidade</Label>
                          <Input
                            id={`room-capacity-${index}`}
                            type="number"
                            min="1"
                            value={room.capacity}
                            onChange={(e) => {
                              const nf = [...roomsForm];
                              nf[index].capacity = parseInt(e.target.value) || 1;
                              setRoomsForm(nf);
                            }}
                            className="h-8"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="capacity">Capacidade</Label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  value={formData.capacity}
                  onChange={(e) => setFormData({ ...formData, capacity: e.target.value })}
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={saveAccommodation}>Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={guestDialogOpen} onOpenChange={setGuestDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Hóspede</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="personType">Tipo</Label>
              <Select
                value={guestForm.personType}
                onValueChange={(v) => setGuestForm({ ...guestForm, personType: v, selectedPlayerIds: [], selectedCollaboratorIds: [] })} // Clear selections on type change
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="player">Jogador</SelectItem>
                  <SelectItem value="collaborator">Colaborador</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {guestForm.personType === "player" ? (
              <div className="space-y-2">
                <div className="space-y-2">
                  <Label>Categoria</Label>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas</SelectItem>
                      {categories.map(c => (
                        <SelectItem key={c.id} value={String(c.id)}>{c.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Label>Jogadores</Label>
                <ScrollArea className="h-48 border rounded-md p-2">
                  {availablePlayers
                    .filter(p => !guests[currentAcc?.id || 0]?.some(g => g.player_id === p.id))
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((p) => (
                      <div key={p.id} className="flex items-center space-x-2 py-1">
                        <Checkbox
                          id={`player-${p.id}`}
                          checked={guestForm.selectedPlayerIds.includes(p.id)}
                          onCheckedChange={(checked) => {
                            setGuestForm(prev => ({
                              ...prev,
                              selectedPlayerIds: checked
                                ? [...prev.selectedPlayerIds, p.id]
                                : prev.selectedPlayerIds.filter(id => id !== p.id),
                            }));
                          }}
                        />
                        <Label htmlFor={`player-${p.id}`}>{p.name}</Label>
                      </div>
                    ))}
                </ScrollArea>
              </div>
            ) : (
              <div className="space-y-2">
                <Label>Colaboradores</Label>
                <ScrollArea className="h-48 border rounded-md p-2">
                  {collaborators
                    .filter(c => !guests[currentAcc?.id || 0]?.some(g => g.collaborator_id === c.id))
                    .map((c) => (
                      <div key={c.id} className="flex items-center space-x-2 py-1">
                        <Checkbox
                          id={`collaborator-${c.id}`}
                          checked={guestForm.selectedCollaboratorIds.includes(String(c.id))}
                          onCheckedChange={(checked) => {
                            setGuestForm(prev => ({
                              ...prev,
                              selectedCollaboratorIds: checked
                                ? [...prev.selectedCollaboratorIds, String(c.id)]
                                : prev.selectedCollaboratorIds.filter(id => id !== String(c.id)),
                            }));
                          }}
                        />
                        <Label htmlFor={`collaborator-${c.id}`}>{c.full_name}</Label>
                      </div>
                    ))}
                </ScrollArea>
              </div>
            )}

            {currentAcc?.type === 'hotel' && (
              <div className="space-y-2">
                <Label htmlFor="room">Quarto</Label>
                <Select
                  value={guestForm.roomId}
                  onValueChange={(v) => setGuestForm({ ...guestForm, roomId: v })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione" />
                  </SelectTrigger>
                  <SelectContent>
                    {rooms[currentAcc.id]
                       ?.map((r) => {
                        const occupied =
                          guests[currentAcc.id]?.filter(
                            (g) => g.hotel_room_id === r.id,
                          ).length || 0;
                        const available = r.capacity - occupied;
                        const status =
                          occupied === 0
                            ? 'empty'
                            : occupied < r.capacity
                            ? 'available'
                            : 'full';
                        const textColor =
                          status === 'empty'
                            ? 'text-green-600'
                            : status === 'available'
                            ? 'text-blue-600'
                            : 'text-red-600';
                        return { room: r, available, occupied, textColor };
                      })
                      .filter(({ available }) => available > 0)
                      .map(({ room, available, occupied, textColor }) => (
                        <SelectItem
                          key={room.id}
                          value={String(room.id)}
                          className={textColor}
                        >                          {room.room_number} ({available}/{room.capacity})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="checkin">Check-in</Label>
                <Input id="checkin" type="date" value={guestForm.checkIn} onChange={(e) => setGuestForm({ ...guestForm, checkIn: e.target.value })} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="checkout">Check-out</Label>
                <Input id="checkout" type="date" value={guestForm.checkOut} onChange={(e) => setGuestForm({ ...guestForm, checkOut: e.target.value })} />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={addGuest}>Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default CallupAccommodationControl;