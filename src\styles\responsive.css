/* Responsive utilities for Game Day Nexus Platform */

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {

  /* Tables */
  .responsive-table {
    font-size: 0.75rem;
  }

  .responsive-table th,
  .responsive-table td {
    padding: 0.5rem 0.25rem;
  }

  /* Cards */
  .responsive-card {
    padding: 1rem;
  }

  .responsive-card-header {
    padding-bottom: 0.5rem;
  }

  .responsive-card-title {
    font-size: 1.125rem;
  }

  /* Buttons */
  .responsive-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .responsive-button-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  /* Forms */
  .responsive-input {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .responsive-select {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  /* Modals */
  .responsive-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .responsive-modal-content {
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
  }

  /* Tabs */
  .responsive-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .responsive-tabs::-webkit-scrollbar {
    display: none;
  }

  /* Grid layouts */
  .responsive-grid-1 {
    grid-template-columns: 1fr;
  }

  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 641px) and (max-width: 768px) {

  /* Tablet styles */
  .responsive-grid-tablet {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 769px) {

  /* Desktop styles */
  .responsive-grid-desktop {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Utility classes for responsive text */
.text-responsive-xs {
  font-size: 0.75rem;
}

@media (min-width: 640px) {
  .text-responsive-xs {
    font-size: 0.875rem;
  }
}

.text-responsive-sm {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .text-responsive-sm {
    font-size: 1rem;
  }
}

.text-responsive-base {
  font-size: 1rem;
}

@media (min-width: 640px) {
  .text-responsive-base {
    font-size: 1.125rem;
  }
}

/* Responsive spacing */
.space-responsive {
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .space-responsive {
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .space-responsive {
    gap: 1.5rem;
  }
}

/* Responsive padding */
.p-responsive {
  padding: 0.5rem;
}

@media (min-width: 640px) {
  .p-responsive {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .p-responsive {
    padding: 1.5rem;
  }
}

/* Hide elements on mobile */
@media (max-width: 640px) {
  .hide-mobile {
    display: none !important;
  }
}

/* Hide elements on tablet */
@media (min-width: 641px) and (max-width: 768px) {
  .hide-tablet {
    display: none !important;
  }
}

/* Hide elements on desktop */
@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Show only on mobile */
@media (min-width: 641px) {
  .show-mobile-only {
    display: none !important;
  }
}

/* Responsive flex utilities */
.flex-responsive {
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .flex-responsive {
    flex-direction: row;
  }
}

/* Responsive overflow for tables */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 600px;
}

/* Responsive modal positioning */
@media (max-width: 640px) {
  .modal-responsive {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }
}

/* Responsive dropdown menus */
@media (max-width: 640px) {
  .dropdown-responsive {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    transform: none;
    border-radius: 0.5rem 0.5rem 0 0;
    max-height: 50vh;
    overflow-y: auto;
  }
}

/* Responsive avatar sizes */
.avatar-responsive {
  width: 1.5rem;
  height: 1.5rem;
}

@media (min-width: 640px) {
  .avatar-responsive {
    width: 2rem;
    height: 2rem;
  }
}

/* Responsive badge sizes */
.badge-responsive {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
}

@media (min-width: 640px) {
  .badge-responsive {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Responsive card grids */
.card-grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .card-grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .card-grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .card-grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive stats grid */
.stats-grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .stats-grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .stats-grid-responsive {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Responsive form layouts */
.form-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .form-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Responsive navigation */
@media (max-width: 768px) {
  .nav-responsive {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 0.5rem;
    z-index: 50;
  }

  .nav-responsive-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .nav-responsive-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-bottom: 0.25rem;
  }
}

/* Responsive sidebar */
@media (max-width: 768px) {
  .sidebar-responsive {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .sidebar-responsive.open {
    transform: translateX(0);
  }
}

/* Responsive header */
.header-responsive {
  padding: 0.5rem 1rem;
}

@media (min-width: 640px) {
  .header-responsive {
    padding: 1rem 1.5rem;
  }
}

/* Responsive content padding */
.content-responsive {
  padding: 0.5rem;
}

@media (min-width: 640px) {
  .content-responsive {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .content-responsive {
    padding: 1.5rem;
  }
}

/* Responsive text truncation */
.truncate-responsive {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

@media (min-width: 640px) {
  .truncate-responsive {
    max-width: 150px;
  }
}

@media (min-width: 768px) {
  .truncate-responsive {
    max-width: 200px;
  }
}

/* Responsive image sizing */
.img-responsive {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* Responsive video sizing */
.video-responsive {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.video-responsive iframe,
.video-responsive object,
.video-responsive embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Responsive print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-responsive {
    font-size: 12pt;
    line-height: 1.4;
  }

  .print-responsive table {
    font-size: 10pt;
  }

  .print-responsive .page-break {
    page-break-before: always;
  }
}/
* Scrollbar hide utility */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}