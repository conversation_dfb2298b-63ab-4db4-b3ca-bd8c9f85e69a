import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useMedicalRecordsStore } from "@/store/useMedicalRecordsStore";
import { useMedicalExamsStore } from "@/store/useMedicalExamsStore";
import { format } from "date-fns";
import {
  getMedicalProfessionalByUserId,
  getMedicalProfessionals,
  MedicalProfessional,
} from "@/api/api";
import { usePermission } from "@/hooks/usePermission";
import { generateDigitalSignatureText } from "@/utils/digitalSignature";

interface ExamRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function ExamRequestDialog({ open, onOpenChange, onSuccess }: ExamRequestDialogProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { players, fetchPlayers } = usePlayersStore();
  const { medicalRecords, fetchMedicalRecords } = useMedicalRecordsStore();
  const { addExam } = useMedicalExamsStore();
  const { role } = usePermission();
  const [medicalProfessional, setMedicalProfessional] = useState<any>(null);
  const [medicalProfessionals, setMedicalProfessionals] = useState<MedicalProfessional[]>([]);
  const [doctorId, setDoctorId] = useState("");

  const [playerId, setPlayerId] = useState("");
  const [recordId, setRecordId] = useState("");
  const [examType, setExamType] = useState("");
  const [notes, setNotes] = useState("");

  useEffect(() => {
    if (open && clubId) {
      if (players.length === 0) fetchPlayers(clubId);
      if (medicalRecords.length === 0) fetchMedicalRecords(clubId);
      loadMedicalProfessional();
      fetchAllMedicalProfessionals();
      setPlayerId("");
      setRecordId("");
      setExamType("");
      setNotes("");
    }
  }, [open, clubId]);

  const fetchAllMedicalProfessionals = async () => {
    try {
      const profs = await getMedicalProfessionals(clubId);
      setMedicalProfessionals(profs);
    } catch (err) {
      console.error("Erro ao carregar profissionais médicos:", err);
    }
  };

  const loadMedicalProfessional = async () => {
    if (!user?.id) return;
    try {
      const prof = await getMedicalProfessionalByUserId(clubId, user.id);
      if (prof) {
        setMedicalProfessional(prof);
        setDoctorId(String(prof.id));
      } else {
        setMedicalProfessional(null);
        setDoctorId("");
      }
    } catch (err) {
      console.error("Erro ao carregar profissional médico:", err);
    }
  };

  const handleSubmit = async () => {
    const isAdminOrPresident = role === "admin" || role === "president";
    if (!medicalProfessional && !isAdminOrPresident) {
      toast({
        title: "Profissional não encontrado",
        description:
          "Seu usuário precisa estar cadastrado como profissional médico para solicitar exames.",
        variant: "destructive",
      });
      return;
    }
    const finalDoctorId = doctorId || (medicalProfessional ? String(medicalProfessional.id) : "");
    if (!finalDoctorId) {
      toast({
        title: "Médico responsável",
        description: "Selecione o médico responsável pela solicitação",
        variant: "destructive",
      });
      return;
    }
    if (!playerId || !recordId || !examType) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha todos os campos para solicitar o exame",
        variant: "destructive"
      });
      return;
    }

    try {
      await addExam(clubId, user?.id || "", {
        record_id: Number(recordId),
        player_id: playerId,
        exam_type: examType,
        request_date: format(new Date(), "yyyy-MM-dd"),
        exam_date: undefined,
        requested_by: Number(finalDoctorId),
        status: "Solicitado",
        notes,
      });

      toast({
        title: "Solicitação criada",
        description: "Exame solicitado e assinado digitalmente."
      });
      onOpenChange(false);
      onSuccess();
    } catch (err) {
      console.error("Erro ao solicitar exame:", err);
      toast({
        title: "Erro",
        description: "Não foi possível solicitar o exame",
        variant: "destructive"
      });
    }
  };

  const signatureText = generateDigitalSignatureText(user?.name || "", role || "medical");

  const playerRecords = medicalRecords.filter(r => String(r.player_id) === playerId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Solicitar Exame</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Jogador*</Label>
            <Select value={playerId} onValueChange={setPlayerId}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o jogador" />
              </SelectTrigger>
              <SelectContent>
                {players
                  .filter(p => 
                    p.status !== "inativo" && 
                    p.status !== "em avaliacao" && 
                    p.status !== "aguardando documentacao" && 
                    p.status !== "aguardando agendamento" && 
                    p.status !== "jogador agendado"
                  )
                  .map(p => (
                    <SelectItem key={p.id} value={String(p.id)}>{p.name}</SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Prontuário*</Label>
            <Select value={recordId} onValueChange={setRecordId}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o prontuário" />
              </SelectTrigger>
              <SelectContent>
                {playerRecords.map(r => (
                  <SelectItem key={r.id} value={String(r.id)}>{format(new Date(r.date), "dd/MM/yyyy")}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {(!medicalProfessional || role === "admin" || role === "president") && (
            <div className="space-y-2">
              <Label>Médico Responsável*</Label>
              <Select value={doctorId} onValueChange={setDoctorId}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o médico" />
                </SelectTrigger>
                <SelectContent>
                  {medicalProfessionals.map(m => (
                    <SelectItem key={m.id} value={String(m.id)}>{m.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          <div className="space-y-2">
            <Label>Tipo de Exame*</Label>
            <Input value={examType} onChange={e => setExamType(e.target.value)} />
          </div>
          <div className="space-y-2">
            <Label>Solicitação</Label>
            <Textarea value={notes} onChange={e => setNotes(e.target.value)} rows={3} />
          </div>
          <p className="text-sm whitespace-pre-wrap border rounded p-2 bg-muted/50">
            {signatureText}
          </p>
        </div>
        <DialogFooter className="pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSubmit}>Assinar e Solicitar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
