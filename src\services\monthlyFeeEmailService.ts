import { sendEmailWithBrevo } from "./brevoEmailService";
import { logMonthlyFeeEmail } from "@/api/monthlyFees";

interface MonthlyFeeEmailData {
  clubId: number;
  playerId: string;
  playerName: string;
  playerEmail: string;
  clubName: string;
  monthlyFeeId?: number;
}

interface PaymentReminderData extends MonthlyFeeEmailData {
  feeName: string;
  amount: number;
  dueDate: string;
  referenceMonth: number;
  referenceYear: number;
  pixCode?: string;
  paymentLink?: string;
}

interface OverdueNotificationData extends MonthlyFeeEmailData {
  feeName: string;
  amount: number;
  dueDate: string;
  referenceMonth: number;
  referenceYear: number;
  daysPastDue: number;
  lateFeAmount?: number;
  pixCode?: string;
  paymentLink?: string;
}

interface PaymentLinkData extends MonthlyFeeEmailData {
  feeName: string;
  amount: number;
  referenceMonth: number;
  referenceYear: number;
  pixCode: string;
  paymentLink: string;
}

interface ReceiptConfirmationData extends MonthlyFeeEmailData {
  feeName: string;
  referenceMonth: number;
  referenceYear: number;
  receiptFileName: string;
  status: 'received' | 'approved' | 'rejected';
  reviewNotes?: string;
}

/**
 * Envia lembrete de pagamento de mensalidade
 */
export async function sendPaymentReminder(data: PaymentReminderData): Promise<boolean> {
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const paymentLink = data.paymentLink || `${SITE_URL}/player/monthly-fees/${data.monthlyFeeId}`;
  
  const emailBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Lembrete de Mensalidade - ${data.clubName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 0; }
        .header { background-color: #4F46E5; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f1f1f1; }
        .fee-details { background-color: #fff; padding: 15px; border-left: 4px solid #4F46E5; margin: 20px 0; }
        .pix-code { background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; font-family: monospace; word-break: break-all; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 15px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💰 Lembrete de Mensalidade</h1>
        </div>
        <div class="content">
          <p>Olá <strong>${data.playerName}</strong>,</p>
          <p>Este é um lembrete sobre sua mensalidade que vence em breve.</p>

          <div class="fee-details">
            <h3>Detalhes da Mensalidade</h3>
            <p><strong>Descrição:</strong> ${data.feeName}</p>
            <p><strong>Referência:</strong> ${data.referenceMonth}/${data.referenceYear}</p>
            <p><strong>Valor:</strong> R$ ${data.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
            <p><strong>Vencimento:</strong> ${new Date(data.dueDate).toLocaleDateString('pt-BR')}</p>
          </div>

          ${data.pixCode ? `
            <h3>💳 Pagamento via PIX</h3>
            <p>Você pode pagar usando o código PIX abaixo:</p>
            <div class="pix-code">${data.pixCode}</div>
            <p><small>Copie o código acima e cole no seu aplicativo bancário na opção "PIX Copia e Cola"</small></p>
          ` : ''}

          <div class="warning">
            <p><strong>⚠️ Importante:</strong> Após efetuar o pagamento, não se esqueça de enviar o comprovante através do link abaixo para que possamos confirmar o pagamento.</p>
          </div>

          <p style="text-align: center; margin: 30px 0;">
            <a href="${paymentLink}" class="button">Ver Detalhes e Enviar Comprovante</a>
          </p>

          <p>Ou acesse: <a href="${paymentLink}">${paymentLink}</a></p>

          <p>Em caso de dúvidas, entre em contato com a administração do clube.</p>
        </div>
        <div class="footer">
          <p>Atenciosamente,<br><strong>${data.clubName}</strong></p>
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const success = await sendEmailWithBrevo({
    to: data.playerEmail,
    subject: `💰 Lembrete: Mensalidade ${data.referenceMonth}/${data.referenceYear} - ${data.clubName}`,
    body: emailBody
  });

  // Log do email
  await logMonthlyFeeEmail(data.clubId, {
    monthly_fee_id: data.monthlyFeeId,
    player_id: data.playerId,
    email_type: 'reminder',
    recipient_email: data.playerEmail,
    subject: `Lembrete: Mensalidade ${data.referenceMonth}/${data.referenceYear}`,
    success,
    email_data: { amount: data.amount, due_date: data.dueDate }
  });

  return success;
}

/**
 * Envia notificação de mensalidade em atraso
 */
export async function sendOverdueNotification(data: OverdueNotificationData): Promise<boolean> {
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const paymentLink = data.paymentLink || `${SITE_URL}/player/monthly-fees/${data.monthlyFeeId}`;
  
  const totalAmount = data.amount + (data.lateFeAmount || 0);
  
  const emailBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Mensalidade em Atraso - ${data.clubName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 0; }
        .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f1f1f1; }
        .fee-details { background-color: #fff; padding: 15px; border-left: 4px solid #dc3545; margin: 20px 0; }
        .pix-code { background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; font-family: monospace; word-break: break-all; }
        .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 15px 0; color: #721c24; }
        .late-fee { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 15px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⚠️ Mensalidade em Atraso</h1>
        </div>
        <div class="content">
          <p>Olá <strong>${data.playerName}</strong>,</p>
          
          <div class="alert">
            <p><strong>Sua mensalidade está em atraso há ${data.daysPastDue} dia(s).</strong></p>
            <p>Para manter sua situação regularizada no clube, é importante efetuar o pagamento o quanto antes.</p>
          </div>

          <div class="fee-details">
            <h3>Detalhes da Mensalidade</h3>
            <p><strong>Descrição:</strong> ${data.feeName}</p>
            <p><strong>Referência:</strong> ${data.referenceMonth}/${data.referenceYear}</p>
            <p><strong>Valor Original:</strong> R$ ${data.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
            ${data.lateFeAmount ? `<p><strong>Multa por Atraso:</strong> R$ ${data.lateFeAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>` : ''}
            <p><strong>Valor Total:</strong> R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
            <p><strong>Vencimento:</strong> ${new Date(data.dueDate).toLocaleDateString('pt-BR')} (${data.daysPastDue} dia(s) atrás)</p>
          </div>

          ${data.lateFeAmount ? `
            <div class="late-fee">
              <p><strong>💰 Multa Aplicada:</strong> Foi aplicada uma multa de R$ ${data.lateFeAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} devido ao atraso no pagamento.</p>
            </div>
          ` : ''}

          ${data.pixCode ? `
            <h3>💳 Pagamento via PIX</h3>
            <p>Você pode pagar usando o código PIX abaixo (já com a multa incluída):</p>
            <div class="pix-code">${data.pixCode}</div>
            <p><small>Copie o código acima e cole no seu aplicativo bancário na opção "PIX Copia e Cola"</small></p>
          ` : ''}

          <p style="text-align: center; margin: 30px 0;">
            <a href="${paymentLink}" class="button">Pagar Agora e Enviar Comprovante</a>
          </p>

          <p>Ou acesse: <a href="${paymentLink}">${paymentLink}</a></p>

          <p><strong>Importante:</strong> Após efetuar o pagamento, envie o comprovante através do link acima para regularizar sua situação.</p>
          
          <p>Em caso de dúvidas ou dificuldades, entre em contato com a administração do clube.</p>
        </div>
        <div class="footer">
          <p>Atenciosamente,<br><strong>${data.clubName}</strong></p>
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const success = await sendEmailWithBrevo({
    to: data.playerEmail,
    subject: `⚠️ ATRASO: Mensalidade ${data.referenceMonth}/${data.referenceYear} - ${data.clubName}`,
    body: emailBody
  });

  // Log do email
  await logMonthlyFeeEmail(data.clubId, {
    monthly_fee_id: data.monthlyFeeId,
    player_id: data.playerId,
    email_type: 'overdue',
    recipient_email: data.playerEmail,
    subject: `ATRASO: Mensalidade ${data.referenceMonth}/${data.referenceYear}`,
    success,
    email_data: { 
      amount: data.amount, 
      due_date: data.dueDate, 
      days_past_due: data.daysPastDue,
      late_fee_amount: data.lateFeAmount,
      total_amount: totalAmount
    }
  });

  return success;
}

/**
 * Envia link de pagamento com PIX
 */
export async function sendPaymentLink(data: PaymentLinkData): Promise<boolean> {
  const emailBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Link de Pagamento - ${data.clubName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 0; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f1f1f1; }
        .fee-details { background-color: #fff; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0; }
        .pix-code { background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; font-family: monospace; word-break: break-all; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💳 Link de Pagamento</h1>
        </div>
        <div class="content">
          <p>Olá <strong>${data.playerName}</strong>,</p>
          <p>Aqui está o link para pagamento da sua mensalidade:</p>

          <div class="fee-details">
            <h3>Detalhes da Mensalidade</h3>
            <p><strong>Descrição:</strong> ${data.feeName}</p>
            <p><strong>Referência:</strong> ${data.referenceMonth}/${data.referenceYear}</p>
            <p><strong>Valor:</strong> R$ ${data.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
          </div>

          <h3>💳 Código PIX para Pagamento</h3>
          <div class="pix-code">${data.pixCode}</div>
          <p><small>Copie o código acima e cole no seu aplicativo bancário na opção "PIX Copia e Cola"</small></p>

          <p style="text-align: center; margin: 30px 0;">
            <a href="${data.paymentLink}" class="button">Acessar Página de Pagamento</a>
          </p>

          <p>Ou acesse: <a href="${data.paymentLink}">${data.paymentLink}</a></p>

          <p><strong>Importante:</strong> Após efetuar o pagamento, não se esqueça de enviar o comprovante através do link acima.</p>
        </div>
        <div class="footer">
          <p>Atenciosamente,<br><strong>${data.clubName}</strong></p>
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const success = await sendEmailWithBrevo({
    to: data.playerEmail,
    subject: `💳 Link de Pagamento: ${data.feeName} ${data.referenceMonth}/${data.referenceYear} - ${data.clubName}`,
    body: emailBody
  });

  // Log do email
  await logMonthlyFeeEmail(data.clubId, {
    monthly_fee_id: data.monthlyFeeId,
    player_id: data.playerId,
    email_type: 'payment_link',
    recipient_email: data.playerEmail,
    subject: `Link de Pagamento: ${data.feeName} ${data.referenceMonth}/${data.referenceYear}`,
    success,
    email_data: { amount: data.amount, pix_code: data.pixCode }
  });

  return success;
}

/**
 * Envia confirmação de recebimento/aprovação/rejeição de comprovante
 */
export async function sendReceiptConfirmation(data: ReceiptConfirmationData): Promise<boolean> {
  let emailBody = '';
  let subject = '';
  let headerColor = '#4F46E5';
  let statusText = '';
  let statusIcon = '';

  switch (data.status) {
    case 'received':
      headerColor = '#4F46E5';
      statusIcon = '📄';
      statusText = 'Comprovante Recebido';
      subject = `📄 Comprovante Recebido: ${data.feeName} ${data.referenceMonth}/${data.referenceYear}`;
      break;
    case 'approved':
      headerColor = '#28a745';
      statusIcon = '✅';
      statusText = 'Pagamento Confirmado';
      subject = `✅ Pagamento Confirmado: ${data.feeName} ${data.referenceMonth}/${data.referenceYear}`;
      break;
    case 'rejected':
      headerColor = '#dc3545';
      statusIcon = '❌';
      statusText = 'Comprovante Rejeitado';
      subject = `❌ Comprovante Rejeitado: ${data.feeName} ${data.referenceMonth}/${data.referenceYear}`;
      break;
  }

  emailBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${statusText} - ${data.clubName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 0; }
        .header { background-color: ${headerColor}; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; background-color: ${headerColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f1f1f1; }
        .fee-details { background-color: #fff; padding: 15px; border-left: 4px solid ${headerColor}; margin: 20px 0; }
        .status-box { background-color: #fff; padding: 15px; border: 2px solid ${headerColor}; border-radius: 8px; margin: 20px 0; text-align: center; }
        .review-notes { background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; margin: 15px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${statusIcon} ${statusText}</h1>
        </div>
        <div class="content">
          <p>Olá <strong>${data.playerName}</strong>,</p>

          <div class="status-box">
            <h2>${statusIcon} ${statusText}</h2>
            <p>Arquivo: <strong>${data.receiptFileName}</strong></p>
          </div>

          <div class="fee-details">
            <h3>Detalhes da Mensalidade</h3>
            <p><strong>Descrição:</strong> ${data.feeName}</p>
            <p><strong>Referência:</strong> ${data.referenceMonth}/${data.referenceYear}</p>
          </div>

          ${data.status === 'received' ? `
            <p>Recebemos seu comprovante de pagamento e ele está sendo analisado pela nossa equipe.</p>
            <p>Você receberá uma nova notificação assim que a análise for concluída.</p>
          ` : ''}

          ${data.status === 'approved' ? `
            <p><strong>🎉 Parabéns!</strong> Seu pagamento foi confirmado e sua mensalidade está quitada.</p>
            <p>Obrigado por manter sua situação em dia no clube!</p>
          ` : ''}

          ${data.status === 'rejected' ? `
            <p>Infelizmente, não foi possível aprovar o comprovante enviado.</p>
            <p><strong>Você precisará enviar um novo comprovante ou entrar em contato com a administração.</strong></p>
          ` : ''}

          ${data.reviewNotes ? `
            <div class="review-notes">
              <h4>Observações da Análise:</h4>
              <p>${data.reviewNotes}</p>
            </div>
          ` : ''}

          ${data.status === 'rejected' ? `
            <p style="text-align: center; margin: 30px 0;">
              <a href="${import.meta.env.VITE_SITE_URL || "http://localhost:3000"}/player/monthly-fees/${data.monthlyFeeId}" class="button">Enviar Novo Comprovante</a>
            </p>
          ` : ''}

          <p>Em caso de dúvidas, entre em contato com a administração do clube.</p>
        </div>
        <div class="footer">
          <p>Atenciosamente,<br><strong>${data.clubName}</strong></p>
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const success = await sendEmailWithBrevo({
    to: data.playerEmail,
    subject: `${subject} - ${data.clubName}`,
    body: emailBody
  });

  // Log do email
  await logMonthlyFeeEmail(data.clubId, {
    monthly_fee_id: data.monthlyFeeId,
    player_id: data.playerId,
    email_type: 'receipt_confirmation',
    recipient_email: data.playerEmail,
    subject,
    success,
    email_data: { 
      status: data.status, 
      receipt_file_name: data.receiptFileName,
      review_notes: data.reviewNotes
    }
  });

  return success;
}