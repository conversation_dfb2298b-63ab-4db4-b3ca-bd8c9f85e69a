import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlayerSalary } from "@/integrations/supabase/types";
import { getPlayers } from "@/api";
import { getCollaborators } from "@/api/collaborators";

interface SalaryData {
  player_id?: string;
  collaborator_id?: number;
  value: number;
  start_date: string;
  end_date?: string | null;
  status: string;
  details?: string;
  club_id: number;
}

interface SalaryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (salary: SalaryData) => void;
  initialData?: Partial<SalaryData>;
  clubId: number;
}

export function SalaryDialog({ open, onOpenChange, onSave, initialData = {}, clubId }: SalaryDialogProps) {
  // Defensive: ensure initialData is always an object, never null
  const safeInitialData = initialData || {};

  const [personType, setPersonType] = useState<'player' | 'collaborator'>(safeInitialData.collaborator_id ? 'collaborator' : 'player');
  const [personId, setPersonId] = useState<string | number>(safeInitialData.player_id || safeInitialData.collaborator_id || "");
  const [value, setValue] = useState<string | number>(safeInitialData.value || "");
  const [startDate, setStartDate] = useState(safeInitialData.start_date || "");
  const [endDate, setEndDate] = useState(safeInitialData.end_date || "");
  const [status, setStatus] = useState(safeInitialData.status || "Ativo");
  const [details, setDetails] = useState(safeInitialData.details || "");
  const [players, setPlayers] = useState<{ id: string; name: string }[]>([]);
  const [collaborators, setCollaborators] = useState<{ id: number; name: string }[]>([]);

  useEffect(() => {
    if (!clubId) return;

    getPlayers(clubId).then((data) => {
      const sorted = (data || [])
        .map((p: any) => ({ id: p.id, name: p.name }))
        .sort((a, b) => a.name.localeCompare(b.name));
      setPlayers(sorted);
    });

    getCollaborators(clubId).then((data) => {
      const sorted = (data || [])
        .map((c: any) => ({ id: c.id, name: c.full_name }))
        .sort((a, b) => a.name.localeCompare(b.name));
      setCollaborators(sorted);
    });
  }, [clubId]);

  function handleSave() {
    if (!personId || !value || !startDate) return;

    const payload: SalaryData = {
      value: Number(value),
      start_date: startDate,
      end_date: endDate || null,
      status,
      details,
      club_id: clubId,
    };

    if (personType === 'player') {
      payload.player_id = String(personId);
    } else {
      payload.collaborator_id = Number(personId);
    }

    onSave(payload);
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{safeInitialData ? "Editar Salário" : "Novo Salário"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium">Tipo</label>
            <select
              className="w-full border rounded px-2 py-1"
              value={personType}
              onChange={e => {
                setPersonType(e.target.value as 'player' | 'collaborator');
                setPersonId('');
              }}
            >
              <option value="player">Jogador</option>
              <option value="collaborator">Colaborador</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium">
              {personType === 'player' ? 'Jogador' : 'Colaborador'}
            </label>
            <select
              className="w-full border rounded px-2 py-1"
              value={personId}
              onChange={e => setPersonId(e.target.value)}
            >
              <option value="">
                {personType === 'player' ? 'Selecione um jogador' : 'Selecione um colaborador'}
              </option>
              {(personType === 'player' ? players : collaborators).map(p => (                <option key={p.id} value={p.id}>{p.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium">Valor (R$)</label>
            <Input
              type="number"
              value={value}
              onChange={e => setValue(e.target.value)}
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Data de Início</label>
            <Input
              type="date"
              value={startDate}
              onChange={e => setStartDate(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Data de Fim</label>
            <Input
              type="date"
              value={endDate || ""}
              onChange={e => setEndDate(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Status</label>
            <select
              className="w-full border rounded px-2 py-1"
              value={status}
              onChange={e => setStatus(e.target.value)}
            >
              <option value="Ativo">Ativo</option>
              <option value="Encerrado">Encerrado</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium">Detalhes</label>
            <Input
              value={details}
              onChange={e => setDetails(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!personId || !value || !startDate}>
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
