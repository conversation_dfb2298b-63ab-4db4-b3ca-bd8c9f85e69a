import { CustomRule, RuleCondition, RuleAction } from './CustomRulesEditor';

export interface RuleEngineContext {
  elements: Array<{
    id: string;
    type: string;
    position: { x: number; y: number };
    properties: Record<string, any>;
  }>;
  currentTime: number;
  fieldDimensions: { width: number; height: number };
  animationState: {
    isPlaying: boolean;
    speed: number;
    currentFrame: number;
  };
  userActions: Array<{
    type: string;
    timestamp: number;
    elementId?: string;
    data: Record<string, any>;
  }>;
}

export interface RuleExecutionResult {
  ruleId: string;
  executed: boolean;
  actions: Array<{
    type: string;
    parameters: Record<string, any>;
    timestamp: number;
  }>;
  errors: string[];
}

export class RuleEngine {
  private rules: CustomRule[] = [];
  private context: RuleEngineContext;
  private executionHistory: RuleExecutionResult[] = [];
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(context: RuleEngineContext) {
    this.context = context;
  }

  setRules(rules: CustomRule[]) {
    this.rules = rules.filter(rule => rule.enabled && rule.validation.isValid);
  }

  updateContext(context: Partial<RuleEngineContext>) {
    this.context = { ...this.context, ...context };
  }

  executeRules(): RuleExecutionResult[] {
    const results: RuleExecutionResult[] = [];

    // Sort rules by priority (higher priority first)
    const sortedRules = [...this.rules].sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      try {
        const result = this.executeRule(rule);
        results.push(result);
        
        if (result.executed) {
          this.executionHistory.push(result);
        }
      } catch (error) {
        console.error(`Error executing rule ${rule.id}:`, error);
        results.push({
          ruleId: rule.id,
          executed: false,
          actions: [],
          errors: [error instanceof Error ? error.message : 'Unknown error']
        });
      }
    }

    return results;
  }

  private executeRule(rule: CustomRule): RuleExecutionResult {
    const result: RuleExecutionResult = {
      ruleId: rule.id,
      executed: false,
      actions: [],
      errors: []
    };

    try {
      // Evaluate all conditions
      const conditionsResult = this.evaluateConditions(rule.conditions);
      
      if (conditionsResult.success) {
        // Execute all actions
        for (const action of rule.actions) {
          const actionResult = this.executeAction(action);
          if (actionResult.success) {
            result.actions.push({
              type: action.type,
              parameters: action.parameters,
              timestamp: this.context.currentTime
            });
          } else {
            result.errors.push(actionResult.error || 'Action execution failed');
          }
        }
        result.executed = true;
      }
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : 'Rule execution failed');
    }

    return result;
  }

  private evaluateConditions(conditions: RuleCondition[]): { success: boolean; error?: string } {
    if (conditions.length === 0) {
      return { success: false, error: 'No conditions to evaluate' };
    }

    let result = true;
    let currentOperator: 'AND' | 'OR' = 'AND';

    for (let i = 0; i < conditions.length; i++) {
      const condition = conditions[i];
      const conditionResult = this.evaluateCondition(condition);

      if (i === 0) {
        result = conditionResult.success;
      } else {
        if (currentOperator === 'AND') {
          result = result && conditionResult.success;
        } else {
          result = result || conditionResult.success;
        }
      }

      // Set operator for next iteration
      if (condition.logicalOperator) {
        currentOperator = condition.logicalOperator;
      }
    }

    return { success: result };
  }

  private evaluateCondition(condition: RuleCondition): { success: boolean; error?: string } {
    try {
      switch (condition.type) {
        case 'element_position':
          return this.evaluateElementPosition(condition);
        case 'element_distance':
          return this.evaluateElementDistance(condition);
        case 'time_elapsed':
          return this.evaluateTimeElapsed(condition);
        case 'player_action':
          return this.evaluatePlayerAction(condition);
        case 'ball_position':
          return this.evaluateBallPosition(condition);
        case 'custom':
          return this.evaluateCustomCondition(condition);
        default:
          return { success: false, error: `Unknown condition type: ${condition.type}` };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Condition evaluation failed' 
      };
    }
  }

  private evaluateElementPosition(condition: RuleCondition): { success: boolean; error?: string } {
    const element = this.context.elements.find(el => el.id === condition.target);
    if (!element) {
      return { success: false, error: 'Element not found' };
    }

    const { x, y } = element.position;
    const value = condition.value;

    switch (condition.operator) {
      case 'equals':
        return { success: x === value.x && y === value.y };
      case 'within_range':
        const distance = Math.sqrt(Math.pow(x - value.x, 2) + Math.pow(y - value.y, 2));
        return { success: distance <= value.range };
      default:
        return { success: false, error: `Unsupported operator for element_position: ${condition.operator}` };
    }
  }

  private evaluateElementDistance(condition: RuleCondition): { success: boolean; error?: string } {
    const elements = this.context.elements;
    if (elements.length < 2) {
      return { success: false, error: 'Need at least 2 elements for distance calculation' };
    }

    // If target is specified, calculate distance between target and first element
    // Otherwise, calculate minimum distance between any two elements
    let distance: number;

    if (condition.target) {
      const targetElement = elements.find(el => el.id === condition.target);
      const otherElement = elements.find(el => el.id !== condition.target);
      
      if (!targetElement || !otherElement) {
        return { success: false, error: 'Target elements not found' };
      }

      distance = Math.sqrt(
        Math.pow(targetElement.position.x - otherElement.position.x, 2) +
        Math.pow(targetElement.position.y - otherElement.position.y, 2)
      );
    } else {
      // Find minimum distance between any two elements
      distance = Infinity;
      for (let i = 0; i < elements.length; i++) {
        for (let j = i + 1; j < elements.length; j++) {
          const d = Math.sqrt(
            Math.pow(elements[i].position.x - elements[j].position.x, 2) +
            Math.pow(elements[i].position.y - elements[j].position.y, 2)
          );
          distance = Math.min(distance, d);
        }
      }
    }

    const value = parseFloat(condition.value);
    
    switch (condition.operator) {
      case 'less_than':
        return { success: distance < value };
      case 'greater_than':
        return { success: distance > value };
      case 'equals':
        return { success: Math.abs(distance - value) < 0.1 };
      default:
        return { success: false, error: `Unsupported operator for element_distance: ${condition.operator}` };
    }
  }

  private evaluateTimeElapsed(condition: RuleCondition): { success: boolean; error?: string } {
    const value = parseFloat(condition.value);
    const currentTime = this.context.currentTime / 1000; // Convert to seconds

    switch (condition.operator) {
      case 'greater_than':
        return { success: currentTime > value };
      case 'less_than':
        return { success: currentTime < value };
      case 'equals':
        return { success: Math.abs(currentTime - value) < 0.1 };
      default:
        return { success: false, error: `Unsupported operator for time_elapsed: ${condition.operator}` };
    }
  }

  private evaluatePlayerAction(condition: RuleCondition): { success: boolean; error?: string } {
    const recentActions = this.context.userActions.filter(
      action => this.context.currentTime - action.timestamp < 5000 // Last 5 seconds
    );

    const actionType = condition.value;
    const hasAction = recentActions.some(action => action.type === actionType);

    switch (condition.operator) {
      case 'equals':
        return { success: hasAction };
      case 'not_equals':
        return { success: !hasAction };
      default:
        return { success: false, error: `Unsupported operator for player_action: ${condition.operator}` };
    }
  }

  private evaluateBallPosition(condition: RuleCondition): { success: boolean; error?: string } {
    const ballElement = this.context.elements.find(el => el.type === 'ball');
    if (!ballElement) {
      return { success: false, error: 'Ball element not found' };
    }

    const { x, y } = ballElement.position;
    const { width, height } = this.context.fieldDimensions;

    switch (condition.value) {
      case 'within_field':
        const withinField = x >= 0 && x <= width && y >= 0 && y <= height;
        return { success: condition.operator === 'equals' ? withinField : !withinField };
      case 'center_field':
        const centerDistance = Math.sqrt(Math.pow(x - width/2, 2) + Math.pow(y - height/2, 2));
        const isCenter = centerDistance < width * 0.1; // Within 10% of field width from center
        return { success: condition.operator === 'equals' ? isCenter : !isCenter };
      default:
        return { success: false, error: `Unknown ball position value: ${condition.value}` };
    }
  }

  private evaluateCustomCondition(condition: RuleCondition): { success: boolean; error?: string } {
    // For custom conditions, we expect the value to be a JavaScript expression
    // This is a simplified implementation - in production, you'd want a safer evaluation method
    try {
      const expression = condition.value;
      const context = {
        elements: this.context.elements,
        time: this.context.currentTime,
        field: this.context.fieldDimensions,
        animation: this.context.animationState
      };

      // Simple expression evaluation (replace with safer method in production)
      const result = new Function('context', `with(context) { return ${expression}; }`)(context);
      return { success: Boolean(result) };
    } catch (error) {
      return { success: false, error: `Custom condition evaluation failed: ${error}` };
    }
  }

  private executeAction(action: RuleAction): { success: boolean; error?: string } {
    try {
      // Apply delay if specified
      if (action.delay && action.delay > 0) {
        setTimeout(() => this.performAction(action), action.delay);
      } else {
        this.performAction(action);
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Action execution failed' 
      };
    }
  }

  private performAction(action: RuleAction) {
    switch (action.type) {
      case 'move_element':
        this.emitEvent('moveElement', action.parameters);
        break;
      case 'highlight_element':
        this.emitEvent('highlightElement', action.parameters);
        break;
      case 'show_message':
        this.emitEvent('showMessage', action.parameters);
        break;
      case 'play_sound':
        this.emitEvent('playSound', action.parameters);
        break;
      case 'pause_animation':
        this.emitEvent('pauseAnimation', action.parameters);
        break;
      case 'trigger_event':
        this.emitEvent('triggerEvent', action.parameters);
        break;
      default:
        console.warn(`Unknown action type: ${action.type}`);
    }
  }

  // Event system for communicating with the UI
  addEventListener(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  removeEventListener(event: string, callback: Function) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emitEvent(event: string, data: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Utility methods
  getExecutionHistory(): RuleExecutionResult[] {
    return [...this.executionHistory];
  }

  clearExecutionHistory() {
    this.executionHistory = [];
  }

  getActiveRules(): CustomRule[] {
    return this.rules;
  }

  // Rule testing
  testRule(rule: CustomRule, testContext?: Partial<RuleEngineContext>): RuleExecutionResult {
    const originalContext = this.context;
    
    if (testContext) {
      this.context = { ...this.context, ...testContext };
    }

    const result = this.executeRule(rule);

    // Restore original context
    this.context = originalContext;

    return result;
  }

  // Performance monitoring
  getPerformanceMetrics() {
    const totalRules = this.rules.length;
    const executedRules = this.executionHistory.length;
    const failedRules = this.executionHistory.filter(r => r.errors.length > 0).length;
    
    return {
      totalRules,
      executedRules,
      failedRules,
      successRate: totalRules > 0 ? ((executedRules - failedRules) / totalRules) * 100 : 0,
      averageExecutionTime: 0 // Would need to implement timing
    };
  }
}

// Utility functions for rule creation and validation
export const RuleUtils = {
  createCondition: (
    type: RuleCondition['type'],
    operator: RuleCondition['operator'],
    value: any,
    target?: string
  ): RuleCondition => ({
    id: `cond_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    operator,
    value,
    target,
    logicalOperator: 'AND'
  }),

  createAction: (
    type: RuleAction['type'],
    parameters: Record<string, any>,
    delay?: number
  ): RuleAction => ({
    id: `act_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    parameters,
    delay
  }),

  validateRuleLogic: (rule: CustomRule): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check for circular dependencies
    // Check for conflicting actions
    // Check for impossible conditions
    
    // Basic validation
    if (rule.conditions.length === 0) {
      errors.push('Rule must have at least one condition');
    }

    if (rule.actions.length === 0) {
      errors.push('Rule must have at least one action');
    }

    // Check for logical consistency
    const hasTimeCondition = rule.conditions.some(c => c.type === 'time_elapsed');
    const hasPauseAction = rule.actions.some(a => a.type === 'pause_animation');
    
    if (hasTimeCondition && hasPauseAction) {
      // This could create a loop - warn about it
      errors.push('Time-based conditions with pause actions may create infinite loops');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};