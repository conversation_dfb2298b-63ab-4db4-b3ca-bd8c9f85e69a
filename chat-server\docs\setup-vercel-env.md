# Configurar Variáveis de Ambiente no Vercel

## 1. Acesse o painel do Vercel
https://vercel.com/brunobosso98s-projects/chat-server

## 2. Vá em Settings > Environment Variables

## 3. <PERSON><PERSON><PERSON> as seguintes variáveis:

### SUPABASE_URL
- **Name**: `SUPABASE_URL`
- **Value**: Sua URL do Supabase (ex: https://xxx.supabase.co)
- **Environment**: Production, Preview, Development

### SUPABASE_SERVICE_KEY
- **Name**: `SUPABASE_SERVICE_KEY`
- **Value**: Sua Service Role Key do Supabase (não a anon key!)
- **Environment**: Production, Preview, Development

## 4. Redeploy
Após adicionar as variáveis, faça um redeploy:
```bash
npx vercel --prod
```

## Como encontrar as chaves do Supabase:
1. Acesse seu projeto no Supabase
2. Vá em Settings > API
3. **URL**: Project URL
4. **Service Key**: service_role (secret) - NÃO a anon public!

⚠️ **IMPORTANTE**: Use a SERVICE_ROLE key, não a anon key, pois o servidor precisa de permissões administrativas.