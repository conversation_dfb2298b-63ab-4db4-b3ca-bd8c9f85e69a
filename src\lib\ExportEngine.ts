import { TrainingDrill } from '@/components/training/InteractiveTrainingBuilder';

// Export formats supported by the system
export type ExportFormat = 'pdf' | 'image' | 'video' | 'json';

// Base export options interface
export interface BaseExportOptions {
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  includeMetadata?: boolean;
  watermark?: {
    text: string;
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity?: number;
  };
}

// PDF-specific export options
export interface PDFExportOptions extends BaseExportOptions {
  format: 'pdf';
  pageSize?: 'A4' | 'A3' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
  includeInstructions?: boolean;
  includeTimeline?: boolean;
  template?: 'standard' | 'detailed' | 'minimal' | 'landscape';
}

// Image-specific export options
export interface ImageExportOptions extends BaseExportOptions {
  format: 'image';
  imageFormat?: 'png' | 'jpg' | 'webp';
  width?: number;
  height?: number;
  backgroundColor?: string;
  includeField?: boolean;
}

// Video-specific export options
export interface VideoExportOptions extends BaseExportOptions {
  format: 'video';
  videoFormat?: 'mp4' | 'webm';
  width?: number;
  height?: number;
  fps?: number;
  duration?: number;
  compression?: 'low' | 'medium' | 'high';
}

// JSON-specific export options
export interface JSONExportOptions extends BaseExportOptions {
  format: 'json';
  includeAnimations?: boolean;
  includeTrajectories?: boolean;
  minify?: boolean;
}

// Union type for all export options
export type ExportOptions = PDFExportOptions | ImageExportOptions | VideoExportOptions | JSONExportOptions;

// Progress tracking interface
export interface ExportProgress {
  stage: 'preparing' | 'processing' | 'rendering' | 'finalizing' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
  estimatedTimeRemaining?: number; // in milliseconds
}

// Export result interface
export interface ExportResult {
  success: boolean;
  data?: Blob | string;
  filename: string;
  size?: number;
  error?: string;
  metadata?: {
    format: ExportFormat;
    duration?: number;
    dimensions?: { width: number; height: number };
    fileSize: number;
    exportedAt: Date;
  };
}

// Export job interface for queue management
export interface ExportJob {
  id: string;
  drill: TrainingDrill;
  options: ExportOptions;
  priority: 'low' | 'normal' | 'high';
  createdAt: Date;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: ExportProgress;
  result?: ExportResult;
}

// Base exporter interface that all specific exporters must implement
export interface BaseExporter {
  export(drill: TrainingDrill, options: BaseExportOptions): Promise<ExportResult>;
  validateOptions(options: BaseExportOptions): boolean;
  getDefaultOptions(): BaseExportOptions;
  getSupportedFormats(): ExportFormat[];
}

// Main ExportEngine class
export class ExportEngine {
  private exporters: Map<ExportFormat, BaseExporter> = new Map();
  private jobQueue: ExportJob[] = [];
  private activeJobs: Map<string, ExportJob> = new Map();
  private maxConcurrentJobs: number = 2;
  private progressCallbacks: Map<string, (progress: ExportProgress) => void> = new Map();
  private worker: Worker | null = null;

  constructor() {
    this.initializeWorker();
  }

  // Initialize Web Worker for background processing
  private initializeWorker(): void {
    try {
      // Create worker from inline script to avoid external file dependency
      const workerScript = `
        self.onmessage = function(e) {
          const { type, data } = e.data;
          
          switch (type) {
            case 'EXPORT_DRILL':
              // Process export in worker
              self.postMessage({
                type: 'EXPORT_PROGRESS',
                jobId: data.jobId,
                progress: { stage: 'processing', progress: 50, message: 'Processing drill data...' }
              });
              
              // Simulate processing time
              setTimeout(() => {
                self.postMessage({
                  type: 'EXPORT_COMPLETE',
                  jobId: data.jobId,
                  result: { success: true, filename: 'drill_export.pdf' }
                });
              }, 1000);
              break;
          }
        };
      `;

      const blob = new Blob([workerScript], { type: 'application/javascript' });
      this.worker = new Worker(URL.createObjectURL(blob));
      
      this.worker.onmessage = (e) => {
        this.handleWorkerMessage(e.data);
      };
    } catch (error) {
      console.warn('Failed to initialize export worker:', error);
      // Fallback to main thread processing
    }
  }

  // Handle messages from worker
  private handleWorkerMessage(message: any): void {
    const { type, jobId, progress, result } = message;
    
    switch (type) {
      case 'EXPORT_PROGRESS':
        this.updateJobProgress(jobId, progress);
        break;
      case 'EXPORT_COMPLETE':
        this.completeJob(jobId, result);
        break;
      case 'EXPORT_ERROR':
        this.failJob(jobId, result.error);
        break;
    }
  }

  // Register an exporter for a specific format
  registerExporter(format: ExportFormat, exporter: BaseExporter): void {
    this.exporters.set(format, exporter);
  }

  // Get available export formats
  getAvailableFormats(): ExportFormat[] {
    return Array.from(this.exporters.keys());
  }

  // Export a single drill
  async exportDrill(drill: TrainingDrill, options: ExportOptions): Promise<ExportResult> {
    const jobId = this.generateJobId();
    
    try {
      // Validate options
      if (!this.validateExportOptions(options)) {
        throw new Error('Invalid export options');
      }

      // Get appropriate exporter
      const exporter = this.exporters.get(options.format);
      if (!exporter) {
        throw new Error(`No exporter available for format: ${options.format}`);
      }

      // Create export job
      const job: ExportJob = {
        id: jobId,
        drill,
        options,
        priority: 'normal',
        createdAt: new Date(),
        status: 'processing',
        progress: {
          stage: 'preparing',
          progress: 0,
          message: 'Preparing export...'
        }
      };

      this.activeJobs.set(jobId, job);
      this.updateProgress(jobId, { stage: 'preparing', progress: 10, message: 'Validating drill data...' });

      // Perform export
      const result = await exporter.export(drill, options);
      
      this.updateProgress(jobId, { stage: 'complete', progress: 100, message: 'Export completed successfully' });
      this.activeJobs.delete(jobId);
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown export error';
      this.updateProgress(jobId, { 
        stage: 'error', 
        progress: 0, 
        message: `Export failed: ${errorMessage}` 
      });
      
      this.activeJobs.delete(jobId);
      
      return {
        success: false,
        filename: '',
        error: errorMessage
      };
    }
  }

  // Export multiple drills in batch
  async exportMultiple(drills: TrainingDrill[], format: ExportFormat, options?: Partial<ExportOptions>): Promise<ExportResult[]> {
    const results: ExportResult[] = [];
    const batchId = this.generateJobId();
    
    this.updateProgress(batchId, {
      stage: 'preparing',
      progress: 0,
      message: `Preparing batch export of ${drills.length} drills...`
    });

    for (let i = 0; i < drills.length; i++) {
      const drill = drills[i];
      const drillOptions: ExportOptions = {
        format,
        quality: 'medium',
        includeMetadata: true,
        ...options
      } as ExportOptions;

      try {
        const result = await this.exportDrill(drill, drillOptions);
        results.push(result);
        
        const progress = ((i + 1) / drills.length) * 100;
        this.updateProgress(batchId, {
          stage: 'processing',
          progress,
          message: `Exported ${i + 1} of ${drills.length} drills`
        });
      } catch (error) {
        results.push({
          success: false,
          filename: `${drill.name}_export_failed`,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    this.updateProgress(batchId, {
      stage: 'complete',
      progress: 100,
      message: `Batch export completed: ${results.filter(r => r.success).length}/${drills.length} successful`
    });

    return results;
  }

  // Add job to queue for background processing
  queueExport(drill: TrainingDrill, options: ExportOptions, priority: 'low' | 'normal' | 'high' = 'normal'): string {
    const jobId = this.generateJobId();
    
    const job: ExportJob = {
      id: jobId,
      drill,
      options,
      priority,
      createdAt: new Date(),
      status: 'pending',
      progress: {
        stage: 'preparing',
        progress: 0,
        message: 'Queued for export...'
      }
    };

    this.jobQueue.push(job);
    this.sortJobQueue();
    this.processQueue();
    
    return jobId;
  }

  // Get job status
  getJobStatus(jobId: string): ExportJob | null {
    return this.activeJobs.get(jobId) || this.jobQueue.find(job => job.id === jobId) || null;
  }

  // Cancel a job
  cancelJob(jobId: string): boolean {
    // Remove from queue
    const queueIndex = this.jobQueue.findIndex(job => job.id === jobId);
    if (queueIndex !== -1) {
      this.jobQueue.splice(queueIndex, 1);
      return true;
    }

    // Cancel active job
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob) {
      activeJob.status = 'failed';
      activeJob.progress = {
        stage: 'error',
        progress: 0,
        message: 'Export cancelled by user'
      };
      this.activeJobs.delete(jobId);
      return true;
    }

    return false;
  }

  // Set progress callback for a job
  onProgress(jobId: string, callback: (progress: ExportProgress) => void): void {
    this.progressCallbacks.set(jobId, callback);
  }

  // Remove progress callback
  offProgress(jobId: string): void {
    this.progressCallbacks.delete(jobId);
  }

  // Private helper methods
  private generateJobId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private validateExportOptions(options: ExportOptions): boolean {
    if (!options.format) return false;
    
    const exporter = this.exporters.get(options.format);
    if (!exporter) return false;
    
    return exporter.validateOptions(options);
  }

  private sortJobQueue(): void {
    const priorityOrder = { high: 3, normal: 2, low: 1 };
    this.jobQueue.sort((a, b) => {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.activeJobs.size >= this.maxConcurrentJobs || this.jobQueue.length === 0) {
      return;
    }

    const job = this.jobQueue.shift();
    if (!job) return;

    job.status = 'processing';
    this.activeJobs.set(job.id, job);

    try {
      const result = await this.exportDrill(job.drill, job.options);
      job.result = result;
      job.status = result.success ? 'completed' : 'failed';
    } catch (error) {
      job.status = 'failed';
      job.result = {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    this.activeJobs.delete(job.id);
    
    // Process next job in queue
    setTimeout(() => this.processQueue(), 100);
  }

  private updateProgress(jobId: string, progress: ExportProgress): void {
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.progress = progress;
    }

    const callback = this.progressCallbacks.get(jobId);
    if (callback) {
      callback(progress);
    }
  }

  private updateJobProgress(jobId: string, progress: ExportProgress): void {
    this.updateProgress(jobId, progress);
  }

  private completeJob(jobId: string, result: ExportResult): void {
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.status = 'completed';
      job.result = result;
      job.progress = {
        stage: 'complete',
        progress: 100,
        message: 'Export completed successfully'
      };
    }
    this.activeJobs.delete(jobId);
  }

  private failJob(jobId: string, error: string): void {
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.status = 'failed';
      job.result = {
        success: false,
        filename: '',
        error
      };
      job.progress = {
        stage: 'error',
        progress: 0,
        message: `Export failed: ${error}`
      };
    }
    this.activeJobs.delete(jobId);
  }

  // Cleanup
  destroy(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    
    this.jobQueue = [];
    this.activeJobs.clear();
    this.progressCallbacks.clear();
    this.exporters.clear();
  }

  // Utility methods
  getQueueLength(): number {
    return this.jobQueue.length;
  }

  getActiveJobsCount(): number {
    return this.activeJobs.size;
  }

  getAllJobs(): ExportJob[] {
    return [...this.jobQueue, ...Array.from(this.activeJobs.values())];
  }

  clearQueue(): void {
    this.jobQueue = [];
  }

  setMaxConcurrentJobs(max: number): void {
    this.maxConcurrentJobs = Math.max(1, Math.min(10, max));
  }
}

// Default export engine instance
export const defaultExportEngine = new ExportEngine();

// Factory function for creating export engine instances
export const createExportEngine = (): ExportEngine => {
  return new ExportEngine();
};