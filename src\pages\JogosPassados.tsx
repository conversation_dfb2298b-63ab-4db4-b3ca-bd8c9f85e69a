import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DatePicker } from "@/components/ui/date-picker";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, BarChart2, Edit, Trash2 } from "lucide-react";
import { useMatchHistoryStore } from "@/store/useMatchHistoryStore";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useSeasonStore } from "@/store/useSeasonStore";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import type { MatchHistory } from "@/api";
import { MatchHistoryDetailsDialog } from "@/components/modals/MatchHistoryDetailsDialog";
import { EditMatchHistoryDialog } from "@/components/modals/EditMatchHistoryDialog";
import { migrateMatchResults } from "@/utils/migrateMatchResults";
import { subDays } from "date-fns";

import { ModuleGuard } from "@/components/guards/ModuleGuard";
import { PieChart, Pie, Cell, ResponsiveContainer } from "recharts";

// Componente para a página de jogos passados
export default function JogosPassados() {
  return (
    <ModuleGuard module="matches">
      <JogosPassadosContent />
    </ModuleGuard>
  );
}

function JogosPassadosContent() {
  const navigate = useNavigate();
  // Zustand store
  const { matchHistory: historicoPartidas, loading, fetchMatchHistory, deleteMatchHistory } = useMatchHistoryStore();
  const [selectedGame, setSelectedGame] = useState<MatchHistory | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [gameToEdit, setGameToEdit] = useState<MatchHistory | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState<Date | undefined>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  const clubId = useCurrentClubId();
  const { activeSeason } = useSeasonStore();
  const { clubInfo } = useClubInfoStore();

  // Aplicar filtros de data e busca
  const filteredPartidas = historicoPartidas.filter((partida) => {
    const data = new Date(partida.date);
    const afterStart = !startDate || data >= startDate;
    const beforeEnd = !endDate || data <= endDate;
    const query = searchQuery.toLowerCase();
    const translated = partida.result === "win" ? "vitoria" : partida.result === "draw" ? "empate" : "derrota";
    const searchMatch = partida.opponent.toLowerCase().includes(query) ||
      (partida.competition || "").toLowerCase().includes(query) ||
      partida.result.toLowerCase().includes(query) ||
      translated.includes(query);
    return afterStart && beforeEnd && (!searchQuery || searchMatch);
  });

  // Cálculos dinâmicos para resumo de desempenho
  const vitorias = filteredPartidas.filter(p => p.result === "win").length;
  const empates = filteredPartidas.filter(p => p.result === "draw").length;
  const derrotas = filteredPartidas.filter(p => p.result === "loss").length;
  const golsMarcados = filteredPartidas.reduce((acc, p) => acc + (p.score_home ?? 0), 0);
  const golsSofridos = filteredPartidas.reduce((acc, p) => acc + (p.score_away ?? 0), 0);
  const totalJogos = filteredPartidas.length;
  const avgShots = totalJogos ? filteredPartidas.reduce((acc, p) => acc + (p.shots ?? 0), 0) / totalJogos : 0;
  const avgShotsOnTarget = totalJogos ? filteredPartidas.reduce((acc, p) => acc + (p.shots_on_target ?? 0), 0) / totalJogos : 0;
  const avgCorners = totalJogos ? filteredPartidas.reduce((acc, p) => acc + (p.corners ?? 0), 0) / totalJogos : 0;
  const avgFouls = totalJogos ? filteredPartidas.reduce((acc, p) => acc + (p.fouls ?? 0), 0) / totalJogos : 0;
  const avgOffsides = totalJogos ? filteredPartidas.reduce((acc, p) => acc + (p.offsides ?? 0), 0) / totalJogos : 0;

  const resultadosData = [
    { name: "Vitórias", value: vitorias, color: "#10b981" },
    { name: "Empates", value: empates, color: "#9ca3af" },
    { name: "Derrotas", value: derrotas, color: "#ef4444" },
  ];

  useEffect(() => {
    const updateSelectedCategory = () => {
      const storedCategoryId = localStorage.getItem("selectedCategoryId");
      if (storedCategoryId) {
        setSelectedCategoryId(parseInt(storedCategoryId, 10));
      } else {
        setSelectedCategoryId(null);
      }
    };

    updateSelectedCategory();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedCategoryId") {
        updateSelectedCategory();
      }
    };

    const handleCategoryChanged = () => {
      updateSelectedCategory();
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("categoryChanged", handleCategoryChanged as EventListener);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("categoryChanged", handleCategoryChanged as EventListener);
    };
  }, []);

  useEffect(() => {
    setSelectedGame(historicoPartidas[0] || null);
  }, [historicoPartidas]);

  useEffect(() => {
    if (clubId && activeSeason) {
      // Migrar resultados antigos para o novo formato
      migrateMatchResults(clubId).then(() => {
        // Após a migração, buscar os dados atualizados
        fetchMatchHistory(clubId, activeSeason.id, selectedCategoryId ?? undefined);
      });
    }
  }, [fetchMatchHistory, clubId, activeSeason, selectedCategoryId]);

  // Função para classificar o resultado como vitória, empate ou derrota
  const getResultClass = (result: string) => {
    switch (result) {
      case "win":
        return "border-emerald-200 bg-emerald-50 text-emerald-700";
      case "draw":
        return "border-gray-200 bg-gray-50 text-gray-700";
      case "loss":
        return "border-rose-200 bg-rose-50 text-rose-700";
      default:
        return "";
    }
  };

  // Componente para exibir estatísticas em forma de barras
  const StatBar = ({ value, maxValue = 100, label }: { value: number, maxValue?: number, label: string }) => (
    <div className="mb-2">
      <div className="flex justify-between text-sm mb-1">
        <span>{label}</span>
        <span>{value}%</span>
      </div>
      <div className="h-2 bg-gray-100 rounded-full">
        <div
          className="h-full bg-team-blue rounded-full"
          style={{ width: `${(value / maxValue) * 100}%` }}
        />
      </div>
    </div>
  );

  // Utilitário para garantir array de escalação
  function getEscalacaoArray(escalacao: unknown): string[] {
    if (!escalacao) return [];
    if (Array.isArray(escalacao)) return escalacao as string[];
    if (typeof escalacao === 'object' && escalacao !== null && 'length' in escalacao) {
      return Array.from(escalacao as ArrayLike<unknown>).map((j) => String(j));
    }
    return [];
  }

  const handleDeleteGame = async (partida: MatchHistory) => {
    if (!clubId) return;
    if (!window.confirm(`Tem certeza que deseja excluir a partida contra ${partida.opponent}? Esta ação não pode ser desfeita.`)) {
      return;
    }
    try {
      await deleteMatchHistory(clubId, partida.id);
      toast({
        title: "Sucesso",
        description: "Partida excluída com sucesso.",
      });
      if (activeSeason) {
        await fetchMatchHistory(clubId, activeSeason.id, selectedCategoryId ?? undefined);
      }
      if (selectedGame?.id === partida.id) {
        setSelectedGame(null);
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erro ao excluir partida.";
      toast({
        title: "Erro",
        description: message,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Cabeçalho da página */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ChevronLeft className="h-4 w-4 mr-1" /> Voltar
            </Button>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Histórico de Jogos</h1>
          </div>
          <p className="text-muted-foreground mt-1">
            Análise e estatísticas de jogos anteriores
          </p>
        </div>
      </div>

      {/* Lista de jogos */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
          <div className="flex flex-col gap-2 lg:flex-row lg:items-end lg:justify-between">
              <div>
                <CardTitle>Histórico Recente</CardTitle>
                <CardDescription>
                  Resultados e estatísticas dos últimos jogos
                </CardDescription>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Input
                  placeholder="Buscar..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full sm:w-[180px]"
                />
                <DatePicker
                  date={startDate}
                  setDate={setStartDate}
                  placeholder="Início"
                  className="w-full sm:w-[140px]"
                />
                <DatePicker
                  date={endDate}
                  setDate={setEndDate}
                  placeholder="Fim"
                  className="w-full sm:w-[140px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Adversário</TableHead>
                  <TableHead>Competição</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Resultado</TableHead>
                  <TableHead>Local</TableHead>
                  <TableHead>Finalizações</TableHead>
                  <TableHead>Finalizações no Gol</TableHead>
                  <TableHead>Escanteios</TableHead>
                  <TableHead>Faltas</TableHead>
                  <TableHead>Impedimentos</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPartidas.map((partida) => (
                  <TableRow
                    key={partida.id}
                    className={partida.id === selectedGame?.id ? 'bg-muted/50' : 'cursor-pointer hover:bg-accent'}
                    onClick={() => {
                      setSelectedGame(partida);
                      setDetailsOpen(true);
                    }}
                  >
                    <TableCell className="font-medium">{partida.opponent}</TableCell>
                    <TableCell>{partida.competition}</TableCell>
                    <TableCell>{partida.date}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getResultClass(partida.result)}
                      >
                        {partida.result === "win" ? "Vitória" : partida.result === "draw" ? "Empate" : "Derrota"}
                      </Badge>
                    </TableCell>
                    <TableCell>{partida.location}</TableCell>
                    <TableCell>{partida.shots}</TableCell>
                    <TableCell>{partida.shots_on_target}</TableCell>
                    <TableCell>{partida.corners}</TableCell>
                    <TableCell>{partida.fouls}</TableCell>
                    <TableCell>{partida.offsides}</TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => setSelectedGame(partida)}
                          title="Ver detalhes"
                        >
                          <BarChart2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => {
                            setGameToEdit(partida);
                            setEditOpen(true);
                          }}
                          title="Editar partida"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteGame(partida);
                          }}
                          title="Excluir partida"
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Resumo de desempenho */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Resumo de Desempenho</CardTitle>
            <CardDescription>Estatísticas gerais da equipe</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-2">Resultados</h4>
                <div className="flex justify-center">
                  <ResponsiveContainer width={200} height={200}>
                    <PieChart>
                      <Pie data={resultadosData} dataKey="value" nameKey="name" outerRadius={80} label>
                        {resultadosData.map((entry) => (
                          <Cell key={entry.name} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-center gap-4 mt-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="w-3 h-3 bg-emerald-500 rounded-sm" />
                    <span>Vitórias: {vitorias}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="w-3 h-3 bg-gray-400 rounded-sm" />
                    <span>Empates: {empates}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="w-3 h-3 bg-red-500 rounded-sm" />
                    <span>Derrotas: {derrotas}</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-2">Gols</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold">{golsMarcados}</div>
                    <div className="text-xs text-muted-foreground">Marcados</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold">{golsSofridos}</div>
                    <div className="text-xs text-muted-foreground">Sofridos</div>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-2">Médias por Jogo</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Finalizações</span>
                    <span>{avgShots}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Finalizações no Gol</span>
                    <span>{avgShotsOnTarget}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Escanteios</span>
                    <span>{avgCorners}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Faltas</span>
                    <span>{avgFouls}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Impedimentos</span>
                    <span>{avgOffsides}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detalhes do jogo selecionado */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              {clubInfo?.name} {selectedGame?.type === "casa" ? "vs" : "@"} {selectedGame?.opponent} - {selectedGame?.date}
            </CardTitle>
            <Badge variant="outline" className={getResultClass(selectedGame?.result || "")}>
              {selectedGame?.result === "win" ? "Vitória" : selectedGame?.result === "draw" ? "Empate" : "Derrota"}
            </Badge>
          </div>
          <CardDescription>
            {selectedGame?.competition} • {selectedGame?.location}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="visao-geral">
            <TabsList className="mb-4">
              <TabsTrigger value="visao-geral">Visão Geral</TabsTrigger>
              <TabsTrigger value="estatisticas">Estatísticas</TabsTrigger>
              <TabsTrigger value="escalacao">Escalação</TabsTrigger>
              <TabsTrigger value="eventos">Gols & Cartões</TabsTrigger>
            </TabsList>

            <TabsContent value="visao-geral">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Resumo do Jogo</h3>
                  <p className="text-muted-foreground">
                    {selectedGame?.result === "win" && `${clubInfo?.name} conquistou uma importante vitória contra o ${selectedGame?.opponent} com o placar de ${selectedGame?.score_home} x ${selectedGame?.score_away}.`}
                    {selectedGame?.result === "draw" && `${clubInfo?.name} empatou com o ${selectedGame?.opponent} em um jogo difícil que terminou em ${selectedGame?.score_home} x ${selectedGame?.score_away}.`}
                    {selectedGame?.result === "loss" && `${clubInfo?.name} não conseguiu superar o ${selectedGame?.opponent} e foi derrotado por ${selectedGame?.score_home} x ${selectedGame?.score_away}.`}
                  </p>
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Estatísticas Principais</h4>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Finalizações</span>
                        <span>{selectedGame?.shots}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Finalizações no Gol</span>
                        <span>{selectedGame?.shots_on_target}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Escanteios</span>
                        <span>{selectedGame?.corners}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Faltas</span>
                        <span>{selectedGame?.fouls}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Impedimentos</span>
                        <span>{selectedGame?.offsides}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">Destaques</h3>
                  <div className="space-y-3">
                    {selectedGame?.gols.map((gol, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-green-50 border border-green-100 rounded-md">
                        <div className="bg-green-500 text-white h-8 w-8 rounded-full flex items-center justify-center text-sm">
                          {gol.minuto}'
                        </div>
                        <div>
                          <div className="font-medium">{gol.jogador}</div>
                        </div>
                      </div>
                    ))}
                    {selectedGame?.gols.length === 0 && (
                      <div className="p-3 bg-gray-50 rounded-md text-center text-muted-foreground">
                        Sem gols registrados neste jogo
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="estatisticas">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Estatísticas Detalhadas</h3>
                  <div className="space-y-3">
                    <StatBar value={(selectedGame?.shots_on_target / selectedGame?.shots) * 100} label="Precisão de Finalizações (%)" />
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Finalizações:</span>
                        <span>{selectedGame?.shots}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Finalizações no Gol:</span>
                        <span>{selectedGame?.shots_on_target}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Escanteios:</span>
                        <span>{selectedGame?.corners}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Faltas Cometidas:</span>
                        <span>{selectedGame?.fouls}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Impedimentos:</span>
                        <span>{selectedGame?.offsides}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">Análise Comparativa</h3>
                  <div className="p-4 border rounded-md bg-gray-50">
                    <div className="text-sm mb-4">
                      <p>
                        Comparação com a média da temporada:
                      </p>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Finalizações</span>
                        <Badge variant={selectedGame?.shots > avgShots ? "default" : "secondary"}>
                          {selectedGame?.shots > avgShots ? "Acima" : "Abaixo"} da média
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="escalacao">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Escalação Titular</h3>
                  <div className="p-4 bg-green-50 border border-green-100 rounded-md relative min-h-[300px]">
                    {/* Representação visual simplificada da escalação */}
                    {getEscalacaoArray(selectedGame?.escalacao).length === 0 ? (
                      <div className="text-center text-muted-foreground py-12">Nenhuma escalação cadastrada para este jogo.</div>
                    ) : (
                    <div className="space-y-6">
                      <div className="flex justify-center">
                        {getEscalacaoArray(selectedGame?.escalacao).slice(10, 11).map((jogador, index) => (
                          <div key={index} className="mx-2 bg-white rounded-full p-2 shadow text-center w-20">
                            <div className="text-xs font-medium">{jogador}</div>
                            <div className="text-[10px] text-muted-foreground">Atacante</div>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-center">
                        {getEscalacaoArray(selectedGame?.escalacao).slice(7, 10).map((jogador, index) => (
                          <div key={index} className="mx-2 bg-white rounded-full p-2 shadow text-center w-20">
                            <div className="text-xs font-medium">{jogador}</div>
                            <div className="text-[10px] text-muted-foreground">Meio-campo</div>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-center">
                        {getEscalacaoArray(selectedGame?.escalacao).slice(3, 7).map((jogador, index) => (
                          <div key={index} className="mx-2 bg-white rounded-full p-2 shadow text-center w-20">
                            <div className="text-xs font-medium">{jogador}</div>
                            <div className="text-[10px] text-muted-foreground">Defesa</div>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-center">
                        {getEscalacaoArray(selectedGame?.escalacao).slice(0, 1).map((jogador, index) => (
                          <div key={index} className="mx-2 bg-white rounded-full p-2 shadow text-center w-20">
                            <div className="text-xs font-medium">{jogador}</div>
                            <div className="text-[10px] text-muted-foreground">Goleiro</div>
                          </div>
                        ))}
                      </div>
                    </div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">Lista de Jogadores</h3>
                  <div className="border rounded-md overflow-hidden">
                    {getEscalacaoArray(selectedGame?.escalacao).length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">Nenhuma escalação cadastrada para este jogo.</div>
                    ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Jogador</TableHead>
                          <TableHead>Posição</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {getEscalacaoArray(selectedGame?.escalacao).map((jogador, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{jogador}</TableCell>
                            <TableCell>
                              {index === 0 && "Goleiro"}
                              {index >= 1 && index <= 4 && "Defesa"}
                              {index >= 5 && index <= 7 && "Meio-campo"}
                              {index >= 8 && "Atacante"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="eventos">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Gols</h3>
                  {(selectedGame?.gols && Array.isArray(selectedGame.gols) && selectedGame.gols.length > 0) ? (
                    <div className="space-y-2">
                      {selectedGame.gols.map((gol, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 border rounded-md bg-green-50"
                        >
                          <div className="bg-green-500 text-white h-9 w-9 rounded-full flex items-center justify-center font-medium">
                            {gol.minuto}'
                          </div>
                          <div>
                            <div className="font-medium">{gol.jogador}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 border rounded-md text-center text-muted-foreground">
                      Nenhum gol registrado neste jogo
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">Cartões</h3>
                  {(selectedGame?.cartoes && Array.isArray(selectedGame.cartoes) && selectedGame.cartoes.length > 0) ? (
                    <div className="space-y-2">
                      {selectedGame.cartoes.map((cartao, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 border rounded-md bg-amber-50"
                        >
                          <div className={`${
                            cartao.tipo === "amarelo" ? "bg-amber-400" : "bg-red-500"
                          } text-white h-9 w-6 rounded flex items-center justify-center`}>
                          </div>
                          <div>
                            <div className="font-medium">{cartao.jogador}</div>
                            <div className="text-xs text-muted-foreground">{cartao.tipo}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 border rounded-md text-center text-muted-foreground">
                      Nenhum cartão registrado neste jogo
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      {selectedGame?.cartoes && Array.isArray(selectedGame.cartoes) && selectedGame.cartoes.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold mb-1">Cartões</h4>
          <ul className="list-disc pl-5">
            {selectedGame.cartoes.map((cartao, idx) => (
              <li key={idx}>
                {cartao.jogador} ({cartao.tipo})
              </li>
            ))}
          </ul>
        </div>
      )}
      {selectedGame?.escalacao && Array.isArray(selectedGame.escalacao) && selectedGame.escalacao.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold mb-1">Escalação</h4>
          <ul className="list-disc pl-5">
            {getEscalacaoArray(selectedGame?.escalacao).map((jogador, idx) => (
              <li key={idx}>{jogador}</li>
            ))}
          </ul>
        </div>
      )}
      <MatchHistoryDetailsDialog open={detailsOpen} onOpenChange={setDetailsOpen} match={selectedGame} />
      <EditMatchHistoryDialog
        open={editOpen}
        onOpenChange={(open) => {
          setEditOpen(open);
          if (!open) {
            setGameToEdit(null);
            // Refresh match history after edit
            fetchMatchHistory(clubId, activeSeason?.id, selectedCategoryId ?? undefined);
          }
        }}
        match={gameToEdit}
        clubId={clubId}
      />
    </div>
  );
}