-- Function to restore player permissions when status changes from 'inativo' to active
CREATE OR REPLACE FUNCTION restore_player_permissions(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_default_permissions JSONB;
BEGIN
  -- Obtain the user associated with this player
  SELECT user_id INTO v_user_id
  FROM players
  WHERE id = p_player_id AND club_id = p_club_id;

  IF v_user_id IS NULL THEN
    RAISE NOTICE 'Jogador % não possui usuário associado', p_player_id;
    RETURN;
  END IF;

  -- Base permissions for players
  v_default_permissions := '{
    "dashboard.view": true,
    "players.view_own": true,
    "players.edit_own": true,
    "players.evaluation.view": true,
    "inventory.requests.create": true,
    "inventory.requests.edit": true
  }'::jsonb;

  -- Update or insert permissions in club_members
  IF EXISTS (
    SELECT 1 FROM club_members
    WHERE club_id = p_club_id AND user_id = v_user_id
  ) THEN
    UPDATE club_members
    SET permissions = v_default_permissions,
        status = 'ativo',
        updated_at = NOW()
    WHERE club_id = p_club_id AND user_id = v_user_id;
  ELSE
    INSERT INTO club_members (
      club_id,
      user_id,
      permissions,
      status,
      created_at,
      updated_at
    ) VALUES (
      p_club_id,
      v_user_id,
      v_default_permissions,
      'ativo',
      NOW(),
      NOW()
    );
  END IF;

  -- Reactivate player account if it exists
  UPDATE player_accounts
  SET expires_at = NULL
  WHERE club_id = p_club_id AND player_id = p_player_id;

  RAISE NOTICE 'Permissões do jogador % restauradas com sucesso', p_player_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;