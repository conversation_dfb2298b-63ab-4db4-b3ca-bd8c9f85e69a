import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getClubBySlug, ClubInfo } from '@/api/clubs';
import { validateUserClubAccessBySlug } from '@/api/clubAccess';
import { useClubSlugStore } from '@/store/useClubSlugStore';
import { supabase } from '@/integrations/supabase/client';

export function useClubSlug() {
  const { clubSlug } = useParams<{ clubSlug: string }>();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [accessDenied, setAccessDenied] = useState<{ denied: boolean; clubSlug?: string }>({ denied: false });
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    getClubFromCache,
    setClub,
    setCurrentSlug,
    currentClub,
    loading,
    setLoading
  } = useClubSlugStore();

  const loadClub = useCallback(async () => {
    if (!clubSlug) {
      setError("Slug do clube não encontrado na URL");
      setLoading(false);
      return;
    }

    // Evitar múltiplas execuções simultâneas
    if (isLoading) {
      return;
    }

    // Verificar se há sessão ativa antes de fazer validações
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      // Se não há sessão, definir erro e parar
      setError("Sessão não encontrada. Faça login novamente.");
      setLoading(false);
      return;
    }

    // Verificar cache primeiro
    const cachedClub = getClubFromCache(clubSlug);
    if (cachedClub) {
      setCurrentSlug(clubSlug);
      
      // Atualizar localStorage se necessário
      const currentClubId = localStorage.getItem("clubId");
      if (currentClubId !== String(cachedClub.id)) {
        localStorage.setItem("clubId", String(cachedClub.id));
      }
      
      // Não mostrar loading se já temos dados em cache
      if (loading) {
        setLoading(false);
      }
      setError(null);
      return;
    }

    // Se não está em cache, buscar do servidor e validar acesso
    if (!currentClub || currentClub.slug !== clubSlug) {
      try {
        setIsLoading(true);
        setLoading(true);
        setError(null);
        setAccessDenied({ denied: false });
        
        // PRIMEIRO: Validar se o usuário tem acesso ao clube (só se estiver logado)
        const accessValidation = await validateUserClubAccessBySlug(clubSlug, true);
        
        // Se não está logado, definir erro e parar
        if (accessValidation.notLoggedIn) {
          setError("Usuário não está logado. Faça login novamente.");
          setLoading(false);
          setIsLoading(false);
          return;
        }
        
        if (!accessValidation.hasAccess) {
          console.error(`ACESSO NEGADO: Usuário tentou acessar clube '${clubSlug}' sem autorização`);
          setAccessDenied({ denied: true, clubSlug });
          setLoading(false);
          setIsLoading(false);
          return;
        }
        
        // SEGUNDO: Se tem acesso, buscar dados do clube
        const clubData = await getClubBySlug(clubSlug);
        
        if (!clubData) {
          setError("Clube não encontrado");
          setLoading(false);
          setIsLoading(false);
          return;
        }

        // TERCEIRO: Verificar se o ID do clube bate com a validação
        if (clubData.id !== accessValidation.clubId) {
          console.error(`INCONSISTÊNCIA DE DADOS: IDs de clube não batem`);
          setAccessDenied({ denied: true, clubSlug });
          setLoading(false);
          setIsLoading(false);
          return;
        }

        // Salvar no cache e definir como atual
        setClub(clubSlug, clubData);
        setCurrentSlug(clubSlug);
        
        // Atualizar localStorage
        const currentClubId = localStorage.getItem("clubId");
        if (currentClubId !== String(clubData.id)) {
          localStorage.setItem("clubId", String(clubData.id));
        }
        
        setLoading(false);
        setIsLoading(false);
      } catch (err) {
        console.error("Erro ao carregar clube:", err);
        setError("Erro ao carregar dados do clube");
        setLoading(false);
        setIsLoading(false);
      }
    }
  }, [clubSlug, getClubFromCache, setClub, setCurrentSlug, setLoading, isLoading]);

  useEffect(() => {
    if (!clubSlug) {
      setError("Slug do clube não encontrado na URL");
      setLoading(false);
      return;
    }

    // Verificar cache primeiro
    const cachedClub = getClubFromCache(clubSlug);
    if (cachedClub) {
      setCurrentSlug(clubSlug);
      const currentClubId = localStorage.getItem("clubId");
      if (currentClubId !== String(cachedClub.id)) {
        localStorage.setItem("clubId", String(cachedClub.id));
      }
      setLoading(false);
      setError(null);
      return;
    }

    // Se não está em cache, carregar
    loadClub();
  }, [clubSlug, getClubFromCache, setCurrentSlug, setLoading]); // Dependências mínimas

  const redirectToClub = useCallback((newSlug: string, path: string = '/dashboard') => {
    navigate(`/${newSlug}${path}`);
  }, [navigate]);

  return {
    club: currentClub,
    clubSlug,
    loading,
    error,
    accessDenied,
    redirectToClub
  };
}