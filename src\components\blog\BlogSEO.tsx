import { Helmet } from 'react-helmet-async';

interface BlogSEOProps {
  title: string;
  description: string;
  canonical: string;
  type?: 'article' | 'website';
  image?: string;
  publishedAt?: string;
  modifiedAt?: string;
  author?: string;
  tags?: string[];
  readTime?: number;
  wordCount?: number;
}

export function BlogSEO({
  title,
  description,
  canonical,
  type = 'article',
  image = '/images/og-default.jpg',
  publishedAt,
  modifiedAt,
  author = 'Game Day Nexus',
  tags = [],
  readTime,
  wordCount
}: BlogSEOProps) {
  const fullUrl = `https://gamedaynexus.com${canonical}`;
  const fullImageUrl = image.startsWith('http') ? image : `https://gamedaynexus.com${image}`;

  // Schema markup para artigos
  const articleSchema = type === 'article' ? {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description,
    "image": fullImageUrl,
    "author": {
      "@type": "Organization",
      "name": author,
      "url": "https://gamedaynexus.com/sobre"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Game Day Nexus",
      "logo": {
        "@type": "ImageObject",
        "url": "https://gamedaynexus.com/logo.png"
      }
    },
    "datePublished": publishedAt,
    "dateModified": modifiedAt || publishedAt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": fullUrl
    },
    ...(wordCount && { "wordCount": wordCount }),
    "articleSection": "Gestão Esportiva",
    "keywords": tags.join(", ")
  } : null;

  // Breadcrumb schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://gamedaynexus.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": "https://gamedaynexus.com/blog"
      },
      ...(type === 'article' ? [{
        "@type": "ListItem",
        "position": 3,
        "name": title,
        "item": fullUrl
      }] : [])
    ]
  };

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{title}</title>
      <meta name="title" content={title} />
      <meta name="description" content={description} />
      <meta name="keywords" content={tags.join(", ")} />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Game Day Nexus" />
      
      {type === 'article' && (
        <>
          <meta property="article:author" content={author} />
          {publishedAt && <meta property="article:published_time" content={publishedAt} />}
          {modifiedAt && <meta property="article:modified_time" content={modifiedAt} />}
          <meta property="article:section" content="Gestão Esportiva" />
          {tags.map(tag => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={fullUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={fullImageUrl} />

      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="author" content={author} />
      <meta name="language" content="Portuguese" />
      <meta name="revisit-after" content="7 days" />

      {/* Reading time for articles */}
      {readTime && type === 'article' && (
        <meta name="twitter:label1" content="Tempo de leitura" />
      )}
      {readTime && type === 'article' && (
        <meta name="twitter:data1" content={`${readTime} minutos`} />
      )}

      {/* Schema.org structured data */}
      {articleSchema && (
        <script type="application/ld+json">
          {JSON.stringify(articleSchema)}
        </script>
      )}
      
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>

      {/* Organization schema (site-wide) */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Game Day Nexus",
          "alternateName": "GDN Platform",
          "url": "https://gamedaynexus.com",
          "logo": "https://gamedaynexus.com/logo.png",
          "description": "Plataforma completa de gestão esportiva para clubes de futebol",
          "foundingDate": "2024",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+55-11-99999-9999",
            "contactType": "customer service",
            "availableLanguage": "Portuguese"
          },
          "sameAs": [
            "https://linkedin.com/company/gamedaynexus",
            "https://youtube.com/@gamedaynexus",
            "https://instagram.com/gamedaynexus"
          ]
        })}
      </script>
    </Helmet>
  );
}