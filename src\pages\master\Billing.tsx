import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Plus,
  Filter,
  Download,
  Send,
  Eye,
  Edit,
  X,
  RefreshCw
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  getMasterPayments,
  getBillingStats,
  markPaymentAsPaid,
  cancelPayment,
  generateMonthlyPayments,
  sendPaymentReminder,
  suspendClubAccess,
  reactivateClubAccess,
  updatePaymentDueDate,
  type MasterPayment,
  type BillingStats,
  type PaymentFilters
} from '@/api/masterBilling';
import { toast } from 'sonner';
import { format, parseISO, differenceInDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const Billing: React.FC = () => {
  const [payments, setPayments] = useState<MasterPayment[]>([]);
  const [stats, setStats] = useState<BillingStats>({
    totalPending: 0,
    totalOverdue: 0,
    totalPaid: 0,
    monthlyRevenue: 0,
    overdueAmount: 0,
    defaultRate: 0,
    totalClubs: 0,
    activeClubs: 0
  });
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [selectedPayment, setSelectedPayment] = useState<MasterPayment | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuspendDialog, setShowSuspendDialog] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Estados para modais
  const [paymentMethod, setPaymentMethod] = useState('');
  const [transactionId, setTransactionId] = useState('');
  const [suspendReason, setSuspendReason] = useState('');
  const [newDueDate, setNewDueDate] = useState('');

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [paymentsData, statsData] = await Promise.all([
        getMasterPayments(filters),
        getBillingStats()
      ]);
      
      setPayments(paymentsData);
      setStats(statsData);
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      toast.error('Erro ao carregar dados de cobrança');
    } finally {
      setLoading(false);
    }
  };

  const handleGeneratePayments = async () => {
    try {
      setActionLoading(true);
      const result = await generateMonthlyPayments();
      
      if (result.success) {
        toast.success(result.message);
        loadData();
      } else {
        toast.error(result.message);
      }
    } catch (error: any) {
      console.error('Erro ao gerar cobranças:', error);
      toast.error('Erro ao gerar cobranças mensais');
    } finally {
      setActionLoading(false);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!selectedPayment) return;

    try {
      setActionLoading(true);
      await markPaymentAsPaid(selectedPayment.id, paymentMethod, transactionId);
      toast.success('Pagamento marcado como pago!');
      
      setShowPaymentModal(false);
      setSelectedPayment(null);
      setPaymentMethod('');
      setTransactionId('');
      loadData();
    } catch (error: any) {
      console.error('Erro ao marcar pagamento:', error);
      toast.error('Erro ao marcar pagamento como pago');
    } finally {
      setActionLoading(false);
    }
  };

  const handleSendReminder = async (payment: MasterPayment) => {
    try {
      setActionLoading(true);
      await sendPaymentReminder(payment.id);
      toast.success(`Lembrete enviado para ${payment.club?.name}`);
    } catch (error: any) {
      console.error('Erro ao enviar lembrete:', error);
      toast.error('Erro ao enviar lembrete');
    } finally {
      setActionLoading(false);
    }
  };

  const handleSuspendClub = async () => {
    if (!selectedPayment) return;

    try {
      setActionLoading(true);
      await suspendClubAccess(selectedPayment.club_id, suspendReason);
      toast.success(`Acesso do clube ${selectedPayment.club?.name} foi suspenso`);
      
      setShowSuspendDialog(false);
      setSelectedPayment(null);
      setSuspendReason('');
      loadData();
    } catch (error: any) {
      console.error('Erro ao suspender clube:', error);
      toast.error('Erro ao suspender acesso do clube');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReactivateClub = async (payment: MasterPayment) => {
    try {
      setActionLoading(true);
      await reactivateClubAccess(payment.club_id);
      toast.success(`Acesso do clube ${payment.club?.name} foi reativado`);
      loadData();
    } catch (error: any) {
      console.error('Erro ao reativar clube:', error);
      toast.error('Erro ao reativar acesso do clube');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: 'secondary' as const, label: 'Pendente', icon: Clock },
      paid: { variant: 'default' as const, label: 'Pago', icon: CheckCircle },
      overdue: { variant: 'destructive' as const, label: 'Em Atraso', icon: AlertTriangle },
      cancelled: { variant: 'outline' as const, label: 'Cancelado', icon: X }
    };

    const config = variants[status as keyof typeof variants] || variants.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getDaysOverdue = (dueDate: string) => {
    return differenceInDays(new Date(), parseISO(dueDate));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(parseISO(dateString), 'dd/MM/yyyy', { locale: ptBR });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando dados de cobrança...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestão de Cobrança</h1>
          <p className="text-gray-600 mt-1">
            Gerencie pagamentos e cobranças dos clubes
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleGeneratePayments}
            disabled={actionLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${actionLoading ? 'animate-spin' : ''}`} />
            Gerar Cobranças
          </Button>
          <Button className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pendentes</p>
                <p className="text-2xl font-bold">{stats.totalPending}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Em Atraso</p>
                <p className="text-2xl font-bold text-red-600">{stats.totalOverdue}</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(stats.overdueAmount)}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Receita Mensal</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.monthlyRevenue)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Taxa Inadimplência</p>
                <p className="text-2xl font-bold">{stats.defaultRate}%</p>
                <p className="text-xs text-gray-500">
                  {stats.activeClubs} de {stats.totalClubs} clubes
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Buscar por clube..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            
            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => setFilters(prev => ({ 
                ...prev, 
                status: value === 'all' ? undefined : value as any 
              }))}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="pending">Pendente</SelectItem>
                <SelectItem value="overdue">Em Atraso</SelectItem>
                <SelectItem value="paid">Pago</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              placeholder="Data inicial"
              value={filters.start_date || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              className="w-[150px]"
            />

            <Input
              type="date"
              placeholder="Data final"
              value={filters.end_date || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
              className="w-[150px]"
            />

            <Button
              variant="outline"
              onClick={() => setFilters({})}
              className="flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              Limpar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Pagamentos */}
      <Card>
        <CardHeader>
          <CardTitle>Pagamentos ({payments.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Clube</TableHead>
                  <TableHead>Plano</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Vencimento</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Dias em Atraso</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{payment.club?.name}</div>
                        <div className="text-sm text-gray-500">{payment.club?.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{payment.plan?.name}</div>
                        <div className="text-sm text-gray-500">
                          {payment.plan?.billing_cycle === 'monthly' ? 'Mensal' : 'Anual'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>
                      {formatDate(payment.due_date)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(payment.status)}
                    </TableCell>
                    <TableCell>
                      {payment.status === 'overdue' && (
                        <Badge variant="destructive">
                          {getDaysOverdue(payment.due_date)} dias
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {payment.status === 'pending' && (
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => {
                              setSelectedPayment(payment);
                              setShowPaymentModal(true);
                            }}
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                        )}
                        
                        {(payment.status === 'pending' || payment.status === 'overdue') && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSendReminder(payment)}
                            disabled={actionLoading}
                          >
                            <Send className="w-4 h-4" />
                          </Button>
                        )}

                        {payment.status === 'overdue' && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => {
                              setSelectedPayment(payment);
                              setShowSuspendDialog(true);
                            }}
                          >
                            <AlertTriangle className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Marcar como Pago */}
      <Dialog open={showPaymentModal} onOpenChange={setShowPaymentModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Marcar Pagamento como Pago</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600">Clube: {selectedPayment?.club?.name}</p>
              <p className="text-sm text-gray-600">Valor: {selectedPayment && formatCurrency(selectedPayment.amount)}</p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Método de Pagamento</label>
              <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o método" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pix">PIX</SelectItem>
                  <SelectItem value="boleto">Boleto</SelectItem>
                  <SelectItem value="cartao">Cartão de Crédito</SelectItem>
                  <SelectItem value="transferencia">Transferência</SelectItem>
                  <SelectItem value="dinheiro">Dinheiro</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">ID da Transação (opcional)</label>
              <Input
                value={transactionId}
                onChange={(e) => setTransactionId(e.target.value)}
                placeholder="Ex: TXN123456789"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentModal(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleMarkAsPaid}
              disabled={actionLoading || !paymentMethod}
            >
              {actionLoading ? 'Processando...' : 'Confirmar Pagamento'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de Suspender Clube */}
      <AlertDialog open={showSuspendDialog} onOpenChange={setShowSuspendDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Suspender Acesso do Clube</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja suspender o acesso do clube "{selectedPayment?.club?.name}"?
              Esta ação impedirá que os usuários do clube acessem o sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Motivo da Suspensão</label>
            <Textarea
              value={suspendReason}
              onChange={(e) => setSuspendReason(e.target.value)}
              placeholder="Descreva o motivo da suspensão..."
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleSuspendClub}
              className="bg-red-600 hover:bg-red-700"
              disabled={actionLoading}
            >
              {actionLoading ? 'Suspendendo...' : 'Suspender Clube'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};