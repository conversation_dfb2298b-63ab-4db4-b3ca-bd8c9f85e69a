import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Route,
  ArrowRight,
  Zap,
  RotateCcw,
  Play,
  Pause,
  Settings,
  Trash2,
  Copy,
  Edit,
  Target,
  Timer,
  TrendingUp,
  Activity,
  Save,
  Upload,
  Download,
  Eye,
  EyeOff,
  MousePointer,
  Pencil
} from 'lucide-react';

export interface TrajectoryPoint {
  x: number;
  y: number;
  timestamp: number;
  speed?: number;
  action?: 'run' | 'walk' | 'sprint' | 'stop' | 'pass' | 'shoot' | 'dribble';
}

export interface Trajectory {
  id: string;
  elementId: string;
  points: TrajectoryPoint[];
  type: 'player' | 'ball' | 'movement';
  style: {
    color: string;
    width: number;
    dashArray?: string;
    showArrows: boolean;
    showSpeed: boolean;
  };
  duration: number;
  name: string;
}

interface TrajectorySystemProps {
  trajectories: Trajectory[];
  onTrajectoriesChange: (trajectories: Trajectory[]) => void;
  selectedElementId?: string;
  isRecording: boolean;
  onRecordingChange: (recording: boolean) => void;
  onDrawingModeChange?: (mode: 'select' | 'draw' | 'edit') => void;
  drawingMode?: 'select' | 'draw' | 'edit';
  onPreviewTrajectory?: (trajectory: Trajectory | null) => void;
  onTrajectoryPointHandlerChange?: (handler: ((x: number, y: number) => void) | null) => void;
}

export function TrajectorySystem({
  trajectories,
  onTrajectoriesChange,
  selectedElementId,
  isRecording,
  onRecordingChange,
  onDrawingModeChange,
  drawingMode = 'select',
  onPreviewTrajectory,
  onTrajectoryPointHandlerChange
}: TrajectorySystemProps) {
  const [localTrajectories, setLocalTrajectories] = useState<Trajectory[]>(trajectories);
  const [selectedTrajectory, setSelectedTrajectory] = useState<string | null>(null);
  const [trajectoryStyle, setTrajectoryStyle] = useState({
    color: '#3b82f6',
    width: 2,
    showArrows: true,
    showSpeed: false,
    dashArray: ''
  });
  const [movementType, setMovementType] = useState<'run' | 'walk' | 'sprint'>('run');
  const [autoSpeed, setAutoSpeed] = useState(true);
  const [isDrawing, setIsDrawing] = useState(false);
  const [previewPoints, setPreviewPoints] = useState<TrajectoryPoint[]>([]);
  const [currentSpeed, setCurrentSpeed] = useState(5);
  const [showPreview, setShowPreview] = useState(true);
  const [savedTrajectories, setSavedTrajectories] = useState<Trajectory[]>([]);
  const drawingRef = useRef<boolean>(false);
  const lastPointTime = useRef<number>(0);
  const previewPointsRef = useRef<TrajectoryPoint[]>([]);
  const isDrawingRef = useRef<boolean>(false);

  useEffect(() => {
    isDrawingRef.current = isDrawing;
  }, [isDrawing]);

  const trajectory = selectedTrajectory
    ? localTrajectories.find(t => t.id === selectedTrajectory)
    : null;

  useEffect(() => {
    setLocalTrajectories(trajectories);
  }, [trajectories]);

  useEffect(() => {
    onTrajectoriesChange(localTrajectories);
  }, [localTrajectories, onTrajectoriesChange]);

  const addPointToTrajectory = useCallback((x: number, y: number) => {
    console.log('addPointToTrajectory called:', { x, y, isDrawing: isDrawingRef.current });

    if (!isDrawingRef.current) {
      console.log('Not drawing, ignoring point');
      return;
    }

    // Validate coordinates are numbers and within bounds
    if (isNaN(x) || isNaN(y)) {
      console.log('Invalid coordinates:', { x, y });
      return;
    }

    const validX = Math.max(0, Math.min(100, x));
    const validY = Math.max(0, Math.min(100, y));

    const now = Date.now();
    const timeDiff = now - lastPointTime.current;
    
    // Prevent adding points too quickly (debounce)
    if (timeDiff < 50) { // Reduced to 50ms for better responsiveness
      console.log('Point too quick, ignoring');
      return;
    }

    const speed = currentSpeed;

    const newPoint: TrajectoryPoint = {
      x: validX,
      y: validY,
      timestamp: now,
      speed: speed,
      action: movementType
    };

    console.log('Adding new point:', newPoint);

    // Update both preview points and ref for immediate access
    setPreviewPoints(prev => {
      const updated = [...prev, newPoint];
      previewPointsRef.current = updated;
      console.log('Updated preview points:', updated.length);
      return updated;
    });
    lastPointTime.current = now;
  }, [currentSpeed, movementType]);

  // Ensure the trajectory point handler is registered when drawing starts
  useEffect(() => {
    console.log('Registering trajectory handler via effect:', { isDrawing });
    if (isDrawing && onTrajectoryPointHandlerChange) {
      onTrajectoryPointHandlerChange(addPointToTrajectory);
    } else if (!isDrawing && onTrajectoryPointHandlerChange) {
      onTrajectoryPointHandlerChange(null);
    }
  }, [isDrawing, addPointToTrajectory, onTrajectoryPointHandlerChange]);

  // Update preview trajectory when points change
  useEffect(() => {
    if (isDrawing && showPreview && onPreviewTrajectory && previewPoints.length > 0) {
      const previewTrajectory: Trajectory = {
        id: 'preview',
        elementId: selectedElementId || '',
        points: previewPoints,
        type: 'player',
        style: { ...trajectoryStyle, color: '#ff6b6b' }, // Different color for preview
        duration: previewPoints.length > 1 ? previewPoints[previewPoints.length - 1].timestamp - previewPoints[0].timestamp : 0,
        name: 'Preview'
      };
      onPreviewTrajectory(previewTrajectory);
    } else if (!isDrawing && onPreviewTrajectory) {
      onPreviewTrajectory(null);
    }
  }, [isDrawing, showPreview, previewPoints, onPreviewTrajectory, selectedElementId, trajectoryStyle]);

  const handleCreateTrajectory = useCallback(() => {
    if (!selectedElementId) return;

    const newTrajectory: Trajectory = {
      id: `trajectory_${Date.now()}`,
      elementId: selectedElementId,
      points: [],
      type: 'player',
      style: { ...trajectoryStyle },
      duration: 0,
      name: `Trajetória ${localTrajectories.length + 1}`
    };

    setLocalTrajectories(prev => [...prev, newTrajectory]);
    setSelectedTrajectory(newTrajectory.id);
  }, [selectedElementId, localTrajectories, trajectoryStyle]);

  const handleDeleteTrajectory = useCallback((trajectoryId: string) => {
    setLocalTrajectories(prev => prev.filter(t => t.id !== trajectoryId));
    if (selectedTrajectory === trajectoryId) {
      setSelectedTrajectory(null);
    }
  }, [selectedTrajectory]);

  const handleDuplicateTrajectory = useCallback((trajectoryId: string) => {
    const original = localTrajectories.find(t => t.id === trajectoryId);
    if (!original) return;

    const duplicate: Trajectory = {
      ...original,
      id: `trajectory_${Date.now()}`,
      name: `${original.name} (Cópia)`,
      points: original.points.map(p => ({ ...p }))
    };

    setLocalTrajectories(prev => [...prev, duplicate]);
  }, [localTrajectories]);

  const handleUpdateTrajectory = useCallback((trajectoryId: string, updates: Partial<Trajectory>) => {
    setLocalTrajectories(prev => prev.map(t => (t.id === trajectoryId ? { ...t, ...updates } : t)));
  }, []);

  // Enhanced trajectory drawing functionality
  const startDrawing = useCallback(() => {
    console.log('Starting drawing for element:', selectedElementId);
    if (!selectedElementId) {
      console.log('No element selected');
      return;
    }
    
    setIsDrawing(true);
    isDrawingRef.current = true;
    drawingRef.current = true;
    setPreviewPoints([]);
    previewPointsRef.current = [];
    lastPointTime.current = Date.now();
    if (onTrajectoryPointHandlerChange) {
      onTrajectoryPointHandlerChange(addPointToTrajectory);
    }
    onDrawingModeChange?.('draw');
    console.log('Drawing started');
  }, [selectedElementId, onDrawingModeChange, onTrajectoryPointHandlerChange, addPointToTrajectory]);

  const stopDrawing = useCallback(() => {
    console.log('Stopping drawing, current points:', previewPointsRef.current.length);
    
    if (!isDrawing) {
      console.log('Not drawing, ignoring stop');
      return;
    }

    // Clear preview trajectory
    if (onPreviewTrajectory) {
      onPreviewTrajectory(null);
    }

    // Use ref to get the most current points
    const currentPoints = [...previewPointsRef.current]; // Create a copy
    
    console.log('Current points for trajectory:', currentPoints);
    
    if (currentPoints.length < 2) {
      console.log('Not enough points, canceling trajectory');
      setIsDrawing(false);
      isDrawingRef.current = false;
      drawingRef.current = false;
      setPreviewPoints([]);
      previewPointsRef.current = [];
      onDrawingModeChange?.('select');
      return;
    }

    // Create trajectory from current points
    const newTrajectory: Trajectory = {
      id: `trajectory_${Date.now()}`,
      elementId: selectedElementId!,
      points: currentPoints,
      type: 'player',
      style: { ...trajectoryStyle },
      duration: currentPoints[currentPoints.length - 1].timestamp - currentPoints[0].timestamp,
      name: `Trajetória ${localTrajectories.length + 1}`
    };

    console.log('Creating trajectory with points:', newTrajectory.points.length, newTrajectory);

    setLocalTrajectories(prev => {
      const updated = [...prev, newTrajectory];
      console.log('Updated trajectories:', updated.length);
      return updated;
    });
    setSelectedTrajectory(newTrajectory.id);
    setIsDrawing(false);
    isDrawingRef.current = false;
    drawingRef.current = false;
    setPreviewPoints([]);
    previewPointsRef.current = [];
    if (onTrajectoryPointHandlerChange) {
      onTrajectoryPointHandlerChange(null);
    }
    onDrawingModeChange?.('select');
  }, [isDrawing, selectedElementId, trajectoryStyle, localTrajectories, onDrawingModeChange, onPreviewTrajectory, onTrajectoryPointHandlerChange]);

  const calculateSpeedFromMovement = (points: TrajectoryPoint[], newX: number, newY: number, timeDiff: number): number => {
    if (points.length === 0) return currentSpeed;
    
    const lastPoint = points[points.length - 1];
    const distance = Math.sqrt(Math.pow(newX - lastPoint.x, 2) + Math.pow(newY - lastPoint.y, 2));
    const speed = timeDiff > 0 ? (distance / timeDiff) * 1000 : 0; // pixels per second
    
    return Math.max(0.1, Math.min(20, speed)); // Clamp between 0.1 and 20
  };

  const saveTrajectoryToLibrary = useCallback((trajectory: Trajectory) => {
    const savedTrajectory = {
      ...trajectory,
      id: `saved_${Date.now()}`,
      name: `${trajectory.name} (Salvo)`
    };
    setSavedTrajectories(prev => [...prev, savedTrajectory]);
  }, []);

  const loadTrajectoryFromLibrary = useCallback((savedTrajectory: Trajectory) => {
    if (!selectedElementId) return;

    const loadedTrajectory: Trajectory = {
      ...savedTrajectory,
      id: `trajectory_${Date.now()}`,
      elementId: selectedElementId,
      name: `${savedTrajectory.name} (Carregado)`
    };

    setLocalTrajectories(prev => [...prev, loadedTrajectory]);
    setSelectedTrajectory(loadedTrajectory.id);
  }, [selectedElementId]);

  const exportTrajectory = useCallback((trajectory: Trajectory) => {
    const dataStr = JSON.stringify(trajectory, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${trajectory.name.replace(/\s+/g, '_')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, []);

  const importTrajectory = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !selectedElementId) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedTrajectory = JSON.parse(e.target?.result as string) as Trajectory;
        const newTrajectory: Trajectory = {
          ...importedTrajectory,
          id: `trajectory_${Date.now()}`,
          elementId: selectedElementId,
          name: `${importedTrajectory.name} (Importado)`
        };
        setLocalTrajectories(prev => [...prev, newTrajectory]);
        setSelectedTrajectory(newTrajectory.id);
      } catch (error) {
        console.error('Error importing trajectory:', error);
      }
    };
    reader.readAsText(file);
  }, [selectedElementId]);

  const calculateTrajectoryStats = (trajectory: Trajectory) => {
    if (trajectory.points.length < 2) return null;

    const totalDistance = trajectory.points.reduce((acc, point, index) => {
      if (index === 0) return 0;
      const prev = trajectory.points[index - 1];
      const distance = Math.sqrt(
        Math.pow(point.x - prev.x, 2) + Math.pow(point.y - prev.y, 2)
      );
      return acc + distance;
    }, 0);

    const totalTime = trajectory.duration;
    const avgSpeed = totalTime > 0 ? totalDistance / totalTime : 0;
    const maxSpeed = Math.max(...trajectory.points.map(p => p.speed || 0));

    return {
      distance: totalDistance,
      duration: totalTime,
      avgSpeed,
      maxSpeed,
      points: trajectory.points.length
    };
  };

  const getMovementIcon = (action?: string) => {
    switch (action) {
      case 'sprint':
        return <Zap className="h-3 w-3" />;
      case 'run':
        return <TrendingUp className="h-3 w-3" />;
      case 'walk':
        return <Activity className="h-3 w-3" />;
      case 'pass':
        return <Target className="h-3 w-3" />;
      default:
        return <Route className="h-3 w-3" />;
    }
  };

  const getMovementColor = (action?: string) => {
    switch (action) {
      case 'sprint':
        return 'bg-red-100 text-red-800';
      case 'run':
        return 'bg-blue-100 text-blue-800';
      case 'walk':
        return 'bg-green-100 text-green-800';
      case 'pass':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Route className="h-4 w-4" />
          Sistema de Trajetórias
        </CardTitle>
        <CardDescription className="text-xs">
          Crie e gerencie movimentos e trajetórias dos elementos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Debug info */}
        {isDrawing && (
          <div className="p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-xs text-yellow-700">
              🐛 Debug: Pontos capturados: {previewPoints.length} | Ref: {previewPointsRef.current.length}
            </p>
          </div>
        )}

        {/* Modo de desenho */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Modo de Desenho</Label>
          <div className="grid grid-cols-3 gap-1">
            <Button
              variant={drawingMode === 'select' ? "default" : "outline"}
              size="sm"
              onClick={() => onDrawingModeChange?.('select')}
              className="text-xs"
            >
              <MousePointer className="h-3 w-3 mr-1" />
              Selecionar
            </Button>
            <Button
              variant={isDrawing ? "destructive" : "default"}
              size="sm"
              onClick={() => isDrawing ? stopDrawing() : startDrawing()}
              disabled={!selectedElementId}
              className="text-xs"
            >
              <Pencil className="h-3 w-3 mr-1" />
              {isDrawing ? 'Parar Desenho' : 'Desenhar Trajetória'}
            </Button>
            <Button
              variant={drawingMode === 'edit' ? "default" : "outline"}
              size="sm"
              onClick={() => onDrawingModeChange?.('edit')}
              className="text-xs"
            >
              <Edit className="h-3 w-3 mr-1" />
              Editar
            </Button>
          </div>
          
          {isDrawing && (
            <div className="p-2 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs text-blue-700">
                ✏️ Clique no campo para adicionar pontos à trajetória. Clique em "Parar" para finalizar.
              </p>
            </div>
          )}
        </div>

        {/* Controles de gravação */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Gravação Automática</Label>
            <Button
              variant={isRecording ? "destructive" : "default"}
              size="sm"
              onClick={() => onRecordingChange(!isRecording)}
              disabled={!selectedElementId}
            >
              {isRecording ? (
                <>
                  <Pause className="h-3 w-3 mr-1" />
                  Parar
                </>
              ) : (
                <>
                  <Play className="h-3 w-3 mr-1" />
                  Gravar
                </>
              )}
            </Button>
          </div>
          
          {isRecording && (
            <div className="p-2 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-xs text-red-700">
                🔴 Gravando movimento do elemento selecionado automaticamente...
              </p>
            </div>
          )}
        </div>

        {/* Configurações de desenho */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Configurações de Desenho</Label>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs">Tipo de Movimento</Label>
              <Select value={movementType} onValueChange={(value: any) => setMovementType(value)}>
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="walk">Caminhada</SelectItem>
                  <SelectItem value="run">Corrida</SelectItem>
                  <SelectItem value="sprint">Sprint</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label className="text-xs">Velocidade Manual</Label>
              <Slider
                value={[currentSpeed]}
                onValueChange={([speed]) => setCurrentSpeed(speed)}
                max={20}
                min={0.1}
                step={0.1}
                disabled={autoSpeed}
                className="mt-1"
              />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <Label className="text-xs">Velocidade Automática</Label>
            <Switch
              checked={autoSpeed}
              onCheckedChange={setAutoSpeed}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label className="text-xs">Mostrar Preview</Label>
            <Switch
              checked={showPreview}
              onCheckedChange={setShowPreview}
            />
          </div>
        </div>

        {/* Criar nova trajetória */}
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCreateTrajectory}
            disabled={!selectedElementId}
            className="w-full"
          >
            <Route className="h-3 w-3 mr-1" />
            Nova Trajetória
          </Button>
          
          {!selectedElementId && (
            <p className="text-xs text-muted-foreground">
              Selecione um elemento no campo para criar trajetórias
            </p>
          )}
        </div>

        {/* Lista de trajetórias */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">
            Trajetórias ({localTrajectories.length})
          </Label>
          
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {localTrajectories.map(traj => {
              const stats = calculateTrajectoryStats(traj);
              return (
                <div
                  key={traj.id}
                  className={`p-2 rounded border cursor-pointer ${
                    selectedTrajectory === traj.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-muted hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedTrajectory(traj.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium">{traj.name}</span>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateTrajectory(traj.id);
                        }}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTrajectory(traj.id);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <div
                      className="w-3 h-3 rounded"
                      style={{ backgroundColor: traj.style.color }}
                    />
                    <span>{traj.points.length} pontos</span>
                    {stats && (
                      <>
                        <span>•</span>
                        <span>{stats.duration.toFixed(1)}s</span>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Configurações da trajetória selecionada */}
        {trajectory && (
          <div className="space-y-3 border-t pt-3">
            <Label className="text-xs font-medium">Configurações</Label>
            
            <div>
              <Label className="text-xs">Nome</Label>
              <Input
                value={trajectory.name}
                onChange={(e) => handleUpdateTrajectory(trajectory.id, { name: e.target.value })}
                className="text-xs"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Cor</Label>
                <div className="grid grid-cols-4 gap-1 mt-1">
                  {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'].map(color => (
                    <button
                      key={color}
                      className={`w-6 h-6 rounded border-2 ${
                        trajectory.style.color === color ? 'border-primary' : 'border-muted'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleUpdateTrajectory(trajectory.id, {
                        style: { ...trajectory.style, color }
                      })}
                    />
                  ))}
                </div>
              </div>
              
              <div>
                <Label className="text-xs">Espessura</Label>
                <Slider
                  value={[trajectory.style.width]}
                  onValueChange={([width]) => handleUpdateTrajectory(trajectory.id, {
                    style: { ...trajectory.style, width }
                  })}
                  max={8}
                  min={1}
                  step={1}
                  className="mt-1"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Estilo</Label>
                <Select
                  value={trajectory.style.dashArray || 'solid'}
                  onValueChange={(value) => handleUpdateTrajectory(trajectory.id, {
                    style: { 
                      ...trajectory.style, 
                      dashArray: value === 'solid' ? '' : value 
                    }
                  })}
                >
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solid">Sólida</SelectItem>
                    <SelectItem value="5,5">Tracejada</SelectItem>
                    <SelectItem value="2,2">Pontilhada</SelectItem>
                    <SelectItem value="10,5,2,5">Traço-Ponto</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label className="text-xs">Tipo</Label>
                <Select
                  value={trajectory.type}
                  onValueChange={(value: any) => handleUpdateTrajectory(trajectory.id, { type: value })}
                >
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="player">Jogador</SelectItem>
                    <SelectItem value="ball">Bola</SelectItem>
                    <SelectItem value="movement">Movimento</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Estatísticas da trajetória */}
            {(() => {
              const stats = calculateTrajectoryStats(trajectory);
              return stats ? (
                <div className="p-3 bg-muted/50 rounded-lg">
                  <Label className="text-xs font-medium mb-2 block">Estatísticas</Label>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="font-medium">Pontos:</span>
                      <span className="ml-1">{stats.points}</span>
                    </div>
                    <div>
                      <span className="font-medium">Duração:</span>
                      <span className="ml-1">{stats.duration.toFixed(1)}s</span>
                    </div>
                    <div>
                      <span className="font-medium">Distância:</span>
                      <span className="ml-1">{stats.distance.toFixed(1)}m</span>
                    </div>
                    <div>
                      <span className="font-medium">Vel. Média:</span>
                      <span className="ml-1">{stats.avgSpeed.toFixed(1)}m/s</span>
                    </div>
                  </div>
                </div>
              ) : null;
            })()}
          </div>
        )}

        {/* Salvar e carregar trajetórias */}
        {trajectory && (
          <div className="space-y-2 border-t pt-3">
            <Label className="text-xs font-medium">Salvar/Carregar</Label>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => saveTrajectoryToLibrary(trajectory)}
                className="text-xs"
              >
                <Save className="h-3 w-3 mr-1" />
                Salvar
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportTrajectory(trajectory)}
                className="text-xs"
              >
                <Download className="h-3 w-3 mr-1" />
                Exportar
              </Button>
            </div>
            
            <div className="grid grid-cols-1 gap-2">
              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={importTrajectory}
                  style={{ display: 'none' }}
                  id="trajectory-import"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('trajectory-import')?.click()}
                  className="text-xs w-full"
                >
                  <Upload className="h-3 w-3 mr-1" />
                  Importar Trajetória
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Biblioteca de trajetórias salvas */}
        {savedTrajectories.length > 0 && (
          <div className="space-y-2 border-t pt-3">
            <Label className="text-xs font-medium">
              Biblioteca ({savedTrajectories.length})
            </Label>
            
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {savedTrajectories.map(savedTraj => (
                <div
                  key={savedTraj.id}
                  className="flex items-center justify-between p-2 rounded border border-muted hover:border-primary/50 cursor-pointer"
                  onClick={() => loadTrajectoryFromLibrary(savedTraj)}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded"
                      style={{ backgroundColor: savedTraj.style.color }}
                    />
                    <span className="text-xs font-medium">{savedTraj.name}</span>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4"
                      onClick={(e) => {
                        e.stopPropagation();
                        exportTrajectory(savedTraj);
                      }}
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSavedTrajectories(prev => prev.filter(t => t.id !== savedTraj.id));
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Configurações globais */}
        <div className="space-y-2 border-t pt-3">
          <Label className="text-xs font-medium">Configurações Globais</Label>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={trajectoryStyle.showArrows ? "default" : "outline"}
              size="sm"
              onClick={() => setTrajectoryStyle(prev => ({ 
                ...prev, 
                showArrows: !prev.showArrows 
              }))}
              className="text-xs"
            >
              <ArrowRight className="h-3 w-3 mr-1" />
              Setas
            </Button>
            
            <Button
              variant={trajectoryStyle.showSpeed ? "default" : "outline"}
              size="sm"
              onClick={() => setTrajectoryStyle(prev => ({ 
                ...prev, 
                showSpeed: !prev.showSpeed 
              }))}
              className="text-xs"
            >
              <Zap className="h-3 w-3 mr-1" />
              Velocidade
            </Button>
          </div>
        </div>

        {/* Dicas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dicas:</strong> Selecione um elemento e clique em "Gravar" para capturar movimentos em tempo real. 
            Use diferentes cores para distinguir tipos de movimento.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}             