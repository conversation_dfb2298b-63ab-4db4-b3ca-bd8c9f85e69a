import { TrainingDrill, TrainingElement, DrillStep } from '@/components/training/InteractiveTrainingBuilder';
import { AnimationEngine, AnimationFrame } from '../AnimationEngine';
import { 
  BaseExporter, 
  VideoExportOptions, 
  ExportResult, 
  ExportFormat 
} from '../ExportEngine';

// Video recording configuration
interface VideoConfig {
  width: number;
  height: number;
  fps: number;
  duration: number;
  quality: number;
  format: 'mp4' | 'webm';
  compression: 'low' | 'medium' | 'high';
}

// Frame data for animation
interface AnimationFrameData {
  timestamp: number;
  elements: TrainingElement[];
  trajectories: any[];
  drawings: any[];
}

// Video recorder wrapper
class VideoRecorder {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private mediaRecorder: MediaRecorder | null = null;
  private stream: MediaStream | null = null;
  private chunks: Blob[] = [];
  private isRecording: boolean = false;

  constructor(width: number, height: number) {
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d')!;
  }

  async startRecording(options: {
    mimeType: string;
    videoBitsPerSecond?: number;
  }): Promise<void> {
    try {
      // Get canvas stream
      this.stream = this.canvas.captureStream(30); // 30 FPS
      
      // Create media recorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: options.mimeType,
        videoBitsPerSecond: options.videoBitsPerSecond
      });

      // Set up event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.chunks.push(event.data);
        }
      };

      // Start recording
      this.mediaRecorder.start(100); // Collect data every 100ms
      this.isRecording = true;
    } catch (error) {
      throw new Error(`Failed to start recording: ${error}`);
    }
  }

  stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.isRecording) {
        reject(new Error('No active recording'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        const mimeType = this.mediaRecorder?.mimeType || 'video/webm';
        const videoBlob = new Blob(this.chunks, { type: mimeType });
        this.cleanup();
        resolve(videoBlob);
      };

      this.mediaRecorder.onerror = (event) => {
        this.cleanup();
        reject(new Error('Recording failed'));
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  getCanvas(): HTMLCanvasElement {
    return this.canvas;
  }

  getContext(): CanvasRenderingContext2D {
    return this.ctx;
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  private cleanup(): void {
    this.chunks = [];
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.mediaRecorder = null;
  }
}

// Canvas animation renderer for video
class VideoCanvasRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
  }

  clear(backgroundColor: string = '#4ade80'): void {
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }

  drawField(): void {
    const { width, height } = this.canvas;
    
    // Field lines
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 3;
    
    // Border
    this.ctx.strokeRect(0, 0, width, height);
    
    // Center line
    this.ctx.beginPath();
    this.ctx.moveTo(width / 2, 0);
    this.ctx.lineTo(width / 2, height);
    this.ctx.stroke();
    
    // Center circle
    this.ctx.beginPath();
    this.ctx.arc(width / 2, height / 2, Math.min(width, height) * 0.1, 0, 2 * Math.PI);
    this.ctx.stroke();
    
    // Goal areas
    const goalWidth = width * 0.15;
    const goalHeight = height * 0.4;
    const goalY = (height - goalHeight) / 2;
    
    this.ctx.strokeRect(0, goalY, goalWidth, goalHeight);
    this.ctx.strokeRect(width - goalWidth, goalY, goalWidth, goalHeight);
  }

  drawElements(elements: TrainingElement[]): void {
    elements.forEach(element => {
      this.drawElement(element);
    });
  }

  private drawElement(element: TrainingElement): void {
    const x = (element.position.x / 100) * this.canvas.width;
    const y = (element.position.y / 100) * this.canvas.height;
    const size = this.resolveSize(element, 15);
    
    this.ctx.save();
    
    // Apply rotation if present
    if (element.properties.rotation) {
      this.ctx.translate(x, y);
      this.ctx.rotate((element.properties.rotation * Math.PI) / 180);
      this.ctx.translate(-x, -y);
    }
    
    const color = this.getElementColor(element.type);
    this.ctx.fillStyle = color;
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 2;
    
    switch (element.type) {
      case 'player':
        this.drawPlayer(x, y, size, element);
        break;
      case 'ball':
        this.drawBall(x, y, size);
        break;
      case 'cone':
        this.drawCone(x, y, size);
        break;
      case 'goal':
        this.drawGoal(x, y, size);
        break;
      default:
        this.drawDefault(x, y, size);
    }
    
    this.ctx.restore();
  }

  private drawPlayer(x: number, y: number, size: number, element: TrainingElement): void {
    // Player circle
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
    
    // Player number
    if (element.properties.number) {
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = `bold ${size * 0.8}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(element.properties.number.toString(), x, y);
    }
  }

  private drawBall(x: number, y: number, size: number): void {
    this.ctx.fillStyle = '#ffffff';
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
  }

  private drawCone(x: number, y: number, size: number): void {
    this.ctx.beginPath();
    this.ctx.moveTo(x, y - size);
    this.ctx.lineTo(x - size * 0.7, y + size * 0.5);
    this.ctx.lineTo(x + size * 0.7, y + size * 0.5);
    this.ctx.closePath();
    this.ctx.fill();
    this.ctx.stroke();
  }

  private drawGoal(x: number, y: number, size: number): void {
    this.ctx.strokeRect(x - size, y - size * 0.5, size * 2, size);
  }

  private drawDefault(x: number, y: number, size: number): void {
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
  }

  drawTrajectories(trajectories: any[]): void {
    trajectories.forEach(trajectory => {
      if (trajectory.points && trajectory.points.length > 1) {
        this.drawTrajectory(trajectory);
      }
    });
  }

  private drawTrajectory(trajectory: any): void {
    const points = trajectory.points;
    
    this.ctx.strokeStyle = trajectory.color || '#ff0000';
    this.ctx.lineWidth = trajectory.width || 3;
    this.ctx.setLineDash(trajectory.dashed ? [10, 5] : []);
    
    this.ctx.beginPath();
    this.ctx.moveTo(
      (points[0].x / 100) * this.canvas.width,
      (points[0].y / 100) * this.canvas.height
    );
    
    for (let i = 1; i < points.length; i++) {
      this.ctx.lineTo(
        (points[i].x / 100) * this.canvas.width,
        (points[i].y / 100) * this.canvas.height
      );
    }
    this.ctx.stroke();
    this.ctx.setLineDash([]);
  }

  drawTimestamp(timestamp: number, duration: number): void {
    const timeText = this.formatTime(timestamp / 1000);
    const totalText = this.formatTime(duration / 1000);
    
    // Background
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(this.canvas.width - 120, 10, 110, 30);
    
    // Time text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`${timeText} / ${totalText}`, this.canvas.width - 15, 30);
  }

  drawTitle(title: string): void {
    // Background
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.canvas.width, 50);
    
    // Title text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(title, 20, 32);
  }

  private formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  private resolveSize(element: TrainingElement, fallback: number): number {
    const size = (element as any).properties.size;
    if (typeof size === 'number') return size;
    switch (size) {
      case 'small':
        return fallback * 0.5;
      case 'large':
        return fallback * 1.5;
      default:
        return fallback;
    }
  }

  private getElementColor(type: string): string {
    const colors: Record<string, string> = {
      player: '#3b82f6',
      ball: '#ffffff',
      cone: '#f59e0b',
      goal: '#000000',
      obstacle: '#ef4444'
    };
    return colors[type] || '#6b7280';
  }
}

export class VideoExporter implements BaseExporter {
  private defaultOptions: VideoExportOptions = {
    format: 'video',
    quality: 'high',
    videoFormat: 'webm',
    width: 1920,
    height: 1080,
    fps: 30,
    duration: 10000, // 10 seconds
    compression: 'medium',
    includeMetadata: true
  };

  async export(drill: TrainingDrill, options: VideoExportOptions): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const config = this.getVideoConfig(mergedOptions);
      
      // Create video recorder
      const recorder = new VideoRecorder(config.width, config.height);
      const renderer = new VideoCanvasRenderer(recorder.getCanvas());
      
      // Generate animation frames
      const animationFrames = this.generateAnimationFrames(drill, config);
      
      // Start recording
      const mimeType = this.getMimeType(config.format);
      const videoBitsPerSecond = this.getBitrate(config.quality, config.compression);
      
      await recorder.startRecording({
        mimeType,
        videoBitsPerSecond
      });
      
      // Render animation
      await this.renderAnimation(renderer, drill, animationFrames, config);
      
      // Stop recording and get video blob
      const videoBlob = await recorder.stopRecording();
      const filename = this.generateFilename(drill, config.format);
      
      return {
        success: true,
        data: videoBlob,
        filename,
        size: videoBlob.size,
        metadata: {
          format: 'video',
          duration: config.duration,
          fileSize: videoBlob.size,
          exportedAt: new Date(),
          dimensions: { width: config.width, height: config.height }
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: this.generateFilename(drill, 'webm'),
        error: error instanceof Error ? error.message : 'Video export failed'
      };
    }
  }

  private getVideoConfig(options: VideoExportOptions): VideoConfig {
    const qualitySettings = {
      low: { quality: 0.5, fps: 24 },
      medium: { quality: 0.7, fps: 30 },
      high: { quality: 0.9, fps: 30 },
      ultra: { quality: 1.0, fps: 60 }
    };
    
    const settings = qualitySettings[options.quality || 'high'];
    
    return {
      width: options.width || 1920,
      height: options.height || 1080,
      fps: options.fps || settings.fps,
      duration: options.duration || 10000,
      quality: settings.quality,
      format: options.videoFormat || 'webm',
      compression: options.compression || 'medium'
    };
  }

  private generateAnimationFrames(drill: TrainingDrill, config: VideoConfig): AnimationFrameData[] {
    const frames: AnimationFrameData[] = [];
    const frameInterval = 1000 / config.fps;
    const totalFrames = Math.floor(config.duration / frameInterval);
    
    // If drill has multiple steps, distribute them across the duration
    if (drill.steps.length > 0) {
      const stepDuration = config.duration / drill.steps.length;
      
      drill.steps.forEach((step, stepIndex) => {
        const stepStartTime = stepIndex * stepDuration;
        const stepFrames = Math.floor(stepDuration / frameInterval);
        
        for (let i = 0; i < stepFrames; i++) {
          const timestamp = stepStartTime + (i * frameInterval);
          
          // Create frame data with interpolated positions if needed
          frames.push({
            timestamp,
            elements: this.interpolateElements(step.elements, i / stepFrames),
            trajectories: step.annotations?.filter(a => a.type === 'trajectory') || [],
            drawings: step.drawings || []
          });
        }
      });
    } else {
      // Single frame repeated
      for (let i = 0; i < totalFrames; i++) {
        frames.push({
          timestamp: i * frameInterval,
          elements: [],
          trajectories: [],
          drawings: []
        });
      }
    }
    
    return frames;
  }

  private interpolateElements(elements: TrainingElement[], progress: number): TrainingElement[] {
    // Simple interpolation - in a real implementation, this would use trajectory data
    return elements.map(element => {
      const interpolatedElement = { ...element };
      
      // Apply simple movement if element has movement properties
      if (element.properties.movement) {
        interpolatedElement.position = {
          x: element.position.x + (element.properties.movement.x || 0) * progress * 20,
          y: element.position.y + (element.properties.movement.y || 0) * progress * 20
        };
      }
      
      return interpolatedElement;
    });
  }

  private async renderAnimation(
    renderer: VideoCanvasRenderer,
    drill: TrainingDrill,
    frames: AnimationFrameData[],
    config: VideoConfig
  ): Promise<void> {
    const frameInterval = 1000 / config.fps;
    
    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i];
      
      // Clear canvas
      renderer.clear('#4ade80');
      
      // Draw field
      renderer.drawField();
      
      // Draw title
      renderer.drawTitle(drill.name);
      
      // Draw elements
      renderer.drawElements(frame.elements);
      
      // Draw trajectories
      renderer.drawTrajectories(frame.trajectories);
      
      // Draw timestamp
      renderer.drawTimestamp(frame.timestamp, config.duration);
      
      // Wait for next frame
      await this.waitForFrame(frameInterval);
    }
  }

  private waitForFrame(interval: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, interval);
    });
  }

  private getMimeType(format: 'mp4' | 'webm'): string {
    const mimeTypes = {
      mp4: 'video/mp4',
      webm: 'video/webm;codecs=vp9'
    };
    
    // Check browser support
    const mimeType = mimeTypes[format];
    if (MediaRecorder.isTypeSupported(mimeType)) {
      return mimeType;
    }
    
    // Fallback to webm
    return 'video/webm';
  }

  private getBitrate(quality: string, compression: string): number {
    const baseRates = {
      low: 1000000,    // 1 Mbps
      medium: 2500000, // 2.5 Mbps
      high: 5000000,   // 5 Mbps
      ultra: 8000000   // 8 Mbps
    };
    
    const compressionMultipliers = {
      low: 0.5,
      medium: 0.75,
      high: 1.0
    };
    
    const baseRate = baseRates[quality as keyof typeof baseRates] || baseRates.medium;
    const multiplier = compressionMultipliers[compression as keyof typeof compressionMultipliers] || 0.75;
    
    return Math.floor(baseRate * multiplier);
  }

  // Alternative method: Export using animation engine
  async exportWithAnimationEngine(
    drill: TrainingDrill,
    animationEngine: AnimationEngine,
    options: VideoExportOptions
  ): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const config = this.getVideoConfig(mergedOptions);
      
      // Create recorder
      const recorder = new VideoRecorder(config.width, config.height);
      const renderer = new VideoCanvasRenderer(recorder.getCanvas());
      
      // Start recording
      const mimeType = this.getMimeType(config.format);
      const videoBitsPerSecond = this.getBitrate(config.quality, config.compression);
      
      await recorder.startRecording({
        mimeType,
        videoBitsPerSecond
      });
      
      // Play animation and record
      animationEngine.onFrameChange((frameNumber) => {
        const currentFrame = animationEngine.getCurrentFrame();
        if (currentFrame) {
          // Clear and render frame
          renderer.clear('#4ade80');
          renderer.drawField();
          renderer.drawTitle(drill.name);
          renderer.drawElements(currentFrame.elements);
          renderer.drawTrajectories(currentFrame.trajectories);
          renderer.drawTimestamp(
            animationEngine.getCurrentTime(),
            animationEngine.getDuration()
          );
        }
      });
      
      // Start animation
      animationEngine.play();
      
      // Wait for animation to complete
      await new Promise<void>((resolve) => {
        animationEngine.onComplete(() => {
          resolve();
        });
      });
      
      // Stop recording
      const videoBlob = await recorder.stopRecording();
      const filename = this.generateFilename(drill, config.format);
      
      return {
        success: true,
        data: videoBlob,
        filename,
        size: videoBlob.size,
        metadata: {
          format: 'video',
          duration: config.duration,
          fileSize: videoBlob.size,
          exportedAt: new Date(),
          dimensions: { width: config.width, height: config.height }
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: this.generateFilename(drill, 'webm'),
        error: error instanceof Error ? error.message : 'Animation video export failed'
      };
    }
  }

  private generateFilename(drill: TrainingDrill, format: string): string {
    const timestamp = new Date().toISOString().slice(0, 10);
    const safeName = drill.name.replace(/[^a-zA-Z0-9]/g, '_');
    return `${safeName}_${timestamp}.${format}`;
  }

  validateOptions(options: VideoExportOptions): boolean {
    if (options.format !== 'video') return false;
    
    const validFormats = ['mp4', 'webm'];
    if (options.videoFormat && !validFormats.includes(options.videoFormat)) return false;
    
    if (options.width && (options.width < 480 || options.width > 4096)) return false;
    if (options.height && (options.height < 360 || options.height > 2160)) return false;
    if (options.fps && (options.fps < 15 || options.fps > 120)) return false;
    if (options.duration && (options.duration < 1000 || options.duration > 300000)) return false;
    
    return true;
  }

  getDefaultOptions(): VideoExportOptions {
    return { ...this.defaultOptions };
  }

  getSupportedFormats(): ExportFormat[] {
    return ['video'];
  }
}