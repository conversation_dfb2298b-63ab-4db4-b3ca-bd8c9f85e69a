import { useEffect } from 'react';
import { useClubSlugStore } from '@/store/useClubSlugStore';
import { getUserClubs } from '@/api/api';
import { isAuthenticated } from '@/utils/auth';

/**
 * Componente que pré-carrega dados do clube para melhorar a performance
 * Deve ser usado no nível mais alto da aplicação
 */
export function ClubPreloader() {
  const { setClub } = useClubSlugStore();

  useEffect(() => {
    async function preloadClubData() {
      if (!isAuthenticated()) return;

      const userId = localStorage.getItem('userId');
      if (!userId) return;

      try {
        const clubs = await getUserClubs(userId);
        
        // Pré-carregar todos os clubes do usuário no cache
        clubs.forEach(club => {
          if (club.slug) {
            setClub(club.slug, club);
          }
        });
      } catch (error) {
        console.error('Erro ao pré-carregar dados dos clubes:', error);
      }
    }

    preloadClubData();
  }, [setClub]);

  return null; // Este componente não renderiza nada
}