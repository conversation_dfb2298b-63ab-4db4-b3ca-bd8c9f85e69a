import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowLeft, Send, Smile, Paperclip } from 'lucide-react';
import { useChatStore } from '@/store/useChatStore';
import { useChat } from '@/hooks/useChat';
import { useUser } from '@/context/UserContext';
import { ChatMessage } from './ChatMessage';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { getRoomDisplayName } from '@/utils/chat';

export function ChatRoom() {
  const [message, setMessage] = useState('');
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { 
    currentRoom, 
    messages, 
    setCurrentRoom,
    loadMessages
  } = useChatStore();

  const { sendMessage: socketSendMessage, markAsRead } = useChat();
  
  const { user } = useUser();

  const roomMessages = currentRoom ? messages[currentRoom.id] || [] : [];

  // Auto scroll para última mensagem
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [roomMessages]);

  // Carregar mensagens e marcar como lido
  useEffect(() => {
    if (!currentRoom) return;

    // Carregar mensagens da sala
    loadMessages(currentRoom.id, currentRoom.club_id);

    // Marcar como lido quando entrar na sala
    markAsRead(currentRoom.id);
  }, [currentRoom, loadMessages, markAsRead]);

  // Focar no input quando entrar na sala
  useEffect(() => {
    if (currentRoom && inputRef.current) {
      inputRef.current.focus();
    }
  }, [currentRoom]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || !currentRoom) return;

    try {
      socketSendMessage(currentRoom.id, message.trim(), replyTo || undefined);
      
      setMessage('');
      setReplyTo(null);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  if (!currentRoom) return null;

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-3 p-3 border-b">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => setCurrentRoom(null)}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        
        <div className="flex-1">
          <h3 className="font-medium text-sm">
            {getRoomDisplayName(currentRoom, user?.name || user?.email)}
          </h3>
          {currentRoom.description && (
            <p className="text-xs text-muted-foreground">
              {currentRoom.description}
            </p>
          )}
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-3">
        <div className="space-y-4">
          {roomMessages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p className="text-sm">Seja o primeiro a enviar uma mensagem!</p>
            </div>
          ) : (
            roomMessages.map((msg, index) => {
              const prevMessage = index > 0 ? roomMessages[index - 1] : null;
              const showAvatar = !prevMessage || 
                prevMessage.user_id !== msg.user_id ||
                new Date(msg.created_at).getTime() - new Date(prevMessage.created_at).getTime() > 300000; // 5 min

              return (
                <ChatMessage
                  key={msg.id}
                  message={msg}
                  showAvatar={showAvatar}
                  isOwn={msg.user_id === user?.id}
                  onReply={() => setReplyTo(msg.id)}
                />
              );
            })
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Reply Preview */}
      {replyTo && (
        <div className="px-3 py-2 border-t bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              {(() => {
                const replyMessage = roomMessages.find(m => m.id === replyTo);
                return replyMessage ? (
                  <div className="text-xs">
                    <span className="font-medium text-primary">
                      Respondendo a {replyMessage.user?.name}
                    </span>
                    <p className="text-muted-foreground truncate">
                      {replyMessage.content}
                    </p>
                  </div>
                ) : null;
              })()}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyTo(null)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </div>
      )}

      {/* Input */}
      <form onSubmit={handleSendMessage} className="p-3 border-t">
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="h-8 w-8 flex-shrink-0"
          >
            <Paperclip className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Digite sua mensagem..."
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            type="submit"
            size="icon"
            className="h-8 w-8 flex-shrink-0"
            disabled={!message.trim()}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}