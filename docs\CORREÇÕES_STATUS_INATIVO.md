# Correções Aplicadas - Status Inativo

## Problema Identificado
Erro `operator does not exist: uuid = text` ao tentar atualizar status de jogador para 'inativo'.

## Correções Implementadas

### 1. Atualização das Funções SQL (`sql/update-inactive-status-meal-removal.sql`)
- ✅ Adicionada remoção de participantes de alimentação quando jogador fica inativo
- ✅ Adicionada remoção de participantes de alimentação quando colaborador fica inativo
- ✅ Funções `remove_player_associations` e `remove_collaborator_associations` atualizadas

### 2. Correção de Políticas RLS (`sql/fix-player-update-error.sql`)
- ✅ Função `get_current_club_id()` melhorada com tratamento de erros
- ✅ Políticas RLS da tabela `players` recriadas com melhor tratamento de tipos
- ✅ Função de debug `debug_player_query()` criada para troubleshooting

### 3. Correção da API (`src/api/players.ts`)
- ✅ Uso consistente do `supabaseWithClubId` em todas as consultas
- ✅ Remoção de casting desnecessário `as any` que causava conflitos de tipo
- ✅ Consultas de verificação usando o mesmo cliente com headers

## Scripts de Teste Criados

### 1. `sql/test-inactive-meal-removal.sql`
Testa se jogadores e colaboradores são removidos das alimentações quando ficam inativos.

### 2. `sql/test-player-status-update.sql`
Testa se a atualização de status de jogador está funcionando corretamente.

### 3. `sql/test-player-inactive-functionality.sql` (existente)
Testa toda a funcionalidade de inativação de jogadores.

## Como Aplicar as Correções

1. **Execute os scripts SQL na ordem:**
   ```sql
   -- 1. Aplicar correções de RLS e tipos
   \i sql/fix-player-update-error.sql
   
   -- 2. Atualizar funções de remoção de associações
   \i sql/update-inactive-status-meal-removal.sql
   
   -- 3. Testar a funcionalidade (opcional)
   \i sql/test-player-status-update.sql
   \i sql/test-inactive-meal-removal.sql
   ```

2. **As correções na API já foram aplicadas** no arquivo `src/api/players.ts`

## Funcionalidades Implementadas

### ✅ Remoção Automática de Alimentação
Quando um jogador ou colaborador é marcado como 'inativo':
- São removidos automaticamente de todas as sessões de alimentação
- O histórico é mantido através dos triggers SQL
- A remoção acontece instantaneamente via trigger

### ✅ Correção de Tipos de Dados
- Consultas SQL agora usam tipos corretos (UUID vs TEXT)
- Políticas RLS melhoradas para evitar conflitos
- Cliente Supabase com headers corretos para contornar RLS

### ✅ Logs e Debug
- Logs detalhados para troubleshooting
- Função de debug para verificar consultas
- Tratamento de erros melhorado

## Verificação

Para verificar se tudo está funcionando:

1. **Teste a atualização de status:**
   - Tente marcar um jogador como 'inativo' na interface
   - Verifique se não há mais erros 406 ou 404

2. **Teste a remoção de alimentação:**
   - Adicione um jogador a uma sessão de alimentação
   - Marque o jogador como 'inativo'
   - Verifique se ele foi removido da alimentação

3. **Verifique os logs:**
   - Console do navegador deve mostrar logs de sucesso
   - Banco de dados deve mostrar NOTICE messages dos triggers