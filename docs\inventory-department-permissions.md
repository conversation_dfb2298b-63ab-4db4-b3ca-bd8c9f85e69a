# Permissões de Departamento de Estoque - Material Esportivo

## Resumo das Alterações

Este documento descreve as alterações implementadas para adicionar o departamento 'Treino' e criar um sistema de permissões para o departamento 'Material Esportivo' no sistema de estoque.

## Alterações Implementadas

### 1. Novo Departamento 'Treino'

- Adicionado o departamento 'Treino' à lista `INVENTORY_DEPARTMENTS` em `src/api/inventory.ts`
- O departamento agora aparece em todos os selects de departamento do sistema

### 2. Sistema de Permissões para 'Material Esportivo'

#### Nova Permissão
- **Permissão**: `inventory.departments.material_esportivo`
- **Descrição**: "Visualizar departamento Material Esportivo"
- **Módulo**: inventory

#### Comportamento das Permissões
- **Admin e President**: Podem visualizar todos os departamentos, incluindo 'Material Esportivo'
- **Outros usuários**: Só podem visualizar o departamento 'Material Esportivo' se tiverem a permissão específica

### 3. Funções de Filtro Implementadas

#### `filterProductsByPermissions(products, userRole, userPermissions)`
- Filtra produtos baseado nas permissões do usuário
- Remove produtos do departamento 'Material Esportivo' se o usuário não tiver permissão
- Admin e president sempre veem todos os produtos

#### `filterDepartmentsByPermissions(departments, userRole, userPermissions)`
- Filtra departamentos baseado nas permissões do usuário
- Remove o departamento 'Material Esportivo' da lista se o usuário não tiver permissão
- Admin e president sempre veem todos os departamentos

### 4. Arquivos Modificados

#### Backend/API
- `src/api/inventory.ts`: Adicionado departamento 'Treino' e funções de filtro
- `src/api/api.ts`: Exportação das novas funções de filtro
- `src/constants/permissions.ts`: Nova permissão e configuração

#### Frontend/Componentes
- `src/pages/Estoque.tsx`: Aplicação dos filtros de permissão
- `src/components/estoque/CadastrarProdutoDialog.tsx`: Filtro de departamentos
- `src/components/estoque/SolicitacaoEstoqueDialog.tsx`: Filtro de departamentos

#### Banco de Dados
- `sql/add-material-esportivo-permission.sql`: Script para adicionar a permissão

## Como Usar

### Para Administradores
1. Acesse a página de gerenciamento de usuários
2. Edite as permissões de um colaborador
3. Na seção "Estoque", marque ou desmarque "Visualizar departamento Material Esportivo"

### Para Usuários
- Se você não tiver a permissão, não verá produtos do departamento 'Material Esportivo'
- O departamento não aparecerá nos selects de filtro e cadastro
- Relatórios não incluirão dados do departamento restrito

## Execução do Script SQL

Para aplicar as alterações no banco de dados, execute:

```sql
-- Execute o script SQL
\i sql/add-material-esportivo-permission.sql
```

## Testes Recomendados

1. **Teste com usuário admin**: Deve ver todos os departamentos
2. **Teste com usuário president**: Deve ver todos os departamentos  
3. **Teste com colaborador sem permissão**: Não deve ver 'Material Esportivo'
4. **Teste com colaborador com permissão**: Deve ver 'Material Esportivo'
5. **Teste de cadastro**: Departamentos filtrados corretamente
6. **Teste de relatórios**: Dados filtrados conforme permissões

## Notas Técnicas

- As funções de filtro são aplicadas no frontend para melhor performance
- Admin e president sempre têm acesso total (hardcoded)
- A permissão é verificada em tempo real conforme o usuário navega
- Relatórios respeitam as mesmas regras de permissão