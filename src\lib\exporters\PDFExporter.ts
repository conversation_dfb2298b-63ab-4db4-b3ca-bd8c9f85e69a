import jsPD<PERSON> from 'jspdf';
import html2canvas from 'html2canvas';
import { TrainingDrill, TrainingElement, DrillStep } from '@/components/training/InteractiveTrainingBuilder';
import {
  BaseExporter,
  PDFExportOptions,
  ExportResult,
  ExportFormat
} from '../ExportEngine';

// Extend jsPDF with triangle method
declare module 'jspdf' {
  interface jsPDF {
    triangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, style?: string): jsPDF;
  }
}

// PDF layout templates
interface PDFTemplate {
  name: string;
  pageSize: [number, number];
  margins: { top: number; right: number; bottom: number; left: number };
  fieldArea: { x: number; y: number; width: number; height: number };
  titleArea: { x: number; y: number; width: number; height: number };
  instructionsArea: { x: number; y: number; width: number; height: number };
  timelineArea?: { x: number; y: number; width: number; height: number };
}

const PDF_TEMPLATES: Record<string, PDFTemplate> = {
  standard: {
    name: 'Standard',
    pageSize: [210, 297], // A4
    margins: { top: 20, right: 20, bottom: 20, left: 20 },
    fieldArea: { x: 20, y: 40, width: 170, height: 120 },
    titleArea: { x: 20, y: 10, width: 170, height: 25 },
    instructionsArea: { x: 20, y: 170, width: 170, height: 100 }
  },
  detailed: {
    name: 'Detailed',
    pageSize: [297, 210], // A4 Landscape
    margins: { top: 15, right: 15, bottom: 15, left: 15 },
    fieldArea: { x: 15, y: 30, width: 180, height: 120 },
    titleArea: { x: 15, y: 10, width: 267, height: 15 },
    instructionsArea: { x: 200, y: 30, width: 82, height: 120 },
    timelineArea: { x: 15, y: 155, width: 267, height: 40 }
  },
  landscape: {
    name: 'Landscape Full',
    pageSize: [297, 210], // A4 Landscape
    margins: { top: 10, right: 5, bottom: 10, left: 5 },
    fieldArea: { x: 5, y: 22, width: 287, height: 175 },
    titleArea: { x: 5, y: 10, width: 287, height: 10 },
    instructionsArea: { x: 5, y: 195, width: 287, height: 5 }
  },
  minimal: {
    name: 'Minimal',
    pageSize: [210, 297], // A4
    margins: { top: 15, right: 15, bottom: 15, left: 15 },
    fieldArea: { x: 15, y: 25, width: 180, height: 200 },
    titleArea: { x: 15, y: 10, width: 180, height: 12 },
    instructionsArea: { x: 15, y: 230, width: 180, height: 50 }
  }
};

// Field rendering configuration
interface FieldConfig {
  width: number;
  height: number;
  backgroundColor: string;
  lineColor: string;
  lineWidth: number;
  showGrid: boolean;
  gridSize: number;
  fieldType: 'full' | 'half' | 'quarter' | 'custom';
}

export class PDFExporter implements BaseExporter {
  private defaultOptions: PDFExportOptions = {
    format: 'pdf',
    quality: 'high',
    pageSize: 'A4',
    orientation: 'landscape',
    includeInstructions: true,
    includeTimeline: false,
    includeMetadata: true,
    template: 'landscape'
  };

  async export(drill: TrainingDrill, options: PDFExportOptions): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const template = PDF_TEMPLATES[mergedOptions.template || 'standard'];

      // Create PDF document
      const pdf = new jsPDF({
        orientation: mergedOptions.orientation === 'landscape' ? 'l' : 'p',
        unit: 'mm',
        format: mergedOptions.pageSize?.toLowerCase() || 'a4'
      });

      // Add drill steps on separate pages
      for (let i = 0; i < drill.steps.length; i++) {
        // Only add a new page if it's not the first step (jsPDF already creates the first page)
        if (i > 0) {
          pdf.addPage();
        }
        await this.addStepPage(pdf, drill, drill.steps[i], i, template, mergedOptions);
      }

      // Add timeline if requested
      if (mergedOptions.includeTimeline && template.timelineArea) {
        pdf.addPage();
        await this.addTimelinePage(pdf, drill, template, mergedOptions);
      }

      // Add metadata
      if (mergedOptions.includeMetadata) {
        this.addMetadata(pdf, drill);
      }

      // Add watermark if specified
      if (mergedOptions.watermark) {
        this.addWatermark(pdf, mergedOptions.watermark);
      }

      // Generate PDF blob
      const pdfBlob = pdf.output('blob');
      const filename = this.generateFilename(drill, 'pdf');

      return {
        success: true,
        data: pdfBlob,
        filename,
        size: pdfBlob.size,
        metadata: {
          format: 'pdf',
          fileSize: pdfBlob.size,
          exportedAt: new Date(),
          dimensions: {
            width: template.pageSize[0],
            height: template.pageSize[1]
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: this.generateFilename(drill, 'pdf'),
        error: error instanceof Error ? error.message : 'PDF export failed'
      };
    }
  }

  private setupPDFStyles(pdf: jsPDF): void {
    // Set default font
    pdf.setFont('helvetica');
    pdf.setFontSize(12);
  }

  private async addTitlePage(
    pdf: jsPDF,
    drill: TrainingDrill,
    template: PDFTemplate,
    options: PDFExportOptions
  ): Promise<void> {
    const { titleArea } = template;

    // Title
    pdf.setFontSize(18);
    pdf.setFont('helvetica', 'bold');
    pdf.text(drill.name, titleArea.x, titleArea.y + 8);

    // Subtitle with drill info
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    const subtitle = `${drill.category} • ${drill.difficulty} • ${drill.playersRequired} jogadores`;
    pdf.text(subtitle, titleArea.x, titleArea.y + 16);

    // Description
    if (drill.description) {
      pdf.setFontSize(10);
      const descriptionLines = pdf.splitTextToSize(drill.description, titleArea.width);
      pdf.text(descriptionLines, titleArea.x, titleArea.y + 24);
    }

    // Drill overview field
    await this.renderField(pdf, drill, drill.steps[0] || null, template.fieldArea, options);

    // Basic drill information
    this.addDrillInfo(pdf, drill, template);
  }

  private async addStepPage(
    pdf: jsPDF,
    drill: TrainingDrill,
    step: DrillStep,
    stepIndex: number,
    template: PDFTemplate,
    options: PDFExportOptions
  ): Promise<void> {
    const { titleArea, fieldArea, instructionsArea } = template;

    // Step title with drill name
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    const stepTitle = `${drill.name} - Passo ${stepIndex + 1}: ${step.name}`;
    pdf.text(stepTitle, titleArea.x, titleArea.y + 8);
    // Step duration
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    const duration = this.formatDuration(step.duration);
    pdf.text(`Duração: ${duration}`, titleArea.x, titleArea.y + 16);

    // Render field with step elements
    await this.renderField(pdf, drill, step, fieldArea, options);

    // Add instructions
    if (options.includeInstructions && step.description) {
      this.addStepInstructions(pdf, step, instructionsArea);
    }

    // Add step annotations
    if (step.annotations && step.annotations.length > 0) {
      this.addStepAnnotations(pdf, step, instructionsArea);
    }
  }

  private async addTimelinePage(
    pdf: jsPDF,
    drill: TrainingDrill,
    template: PDFTemplate,
    options: PDFExportOptions
  ): Promise<void> {
    if (!template.timelineArea) return;

    const { titleArea, timelineArea } = template;

    // Timeline title
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Timeline do Drill', titleArea.x, titleArea.y + 8);

    // Draw timeline
    this.drawTimeline(pdf, drill, timelineArea);
  }

  private async renderField(
    pdf: jsPDF,
    drill: TrainingDrill,
    step: DrillStep | null,
    area: { x: number; y: number; width: number; height: number },
    options: PDFExportOptions
  ): Promise<void> {
    const fieldConfig: FieldConfig = {
      width: area.width,
      height: area.height,
      backgroundColor: '#4ade80', // Green field
      lineColor: '#ffffff',
      lineWidth: 0.5,
      showGrid: false,
      gridSize: 10,
      fieldType: 'full'
    };

    // Draw field background
    pdf.setFillColor(74, 222, 128); // Green field
    pdf.rect(area.x, area.y, area.width, area.height, 'F');

    // Draw field lines
    this.drawFieldLines(pdf, area, fieldConfig);

    // Draw elements
    if (step) {
      this.drawElements(pdf, step.elements, area, fieldConfig);
      this.drawTrajectories(pdf, step, area, fieldConfig);
      this.drawDrawings(pdf, step.drawings, area, fieldConfig);
    }
  }

  private drawFieldLines(
    pdf: jsPDF,
    area: { x: number; y: number; width: number; height: number },
    config: FieldConfig
  ): void {
    pdf.setDrawColor(255, 255, 255); // White lines
    pdf.setLineWidth(config.lineWidth);

    // Field border
    pdf.rect(area.x, area.y, area.width, area.height);

    // Center line
    pdf.line(
      area.x + area.width / 2, area.y,
      area.x + area.width / 2, area.y + area.height
    );

    // Center circle
    const centerX = area.x + area.width / 2;
    const centerY = area.y + area.height / 2;
    const radius = Math.min(area.width, area.height) * 0.1;
    pdf.ellipse(centerX, centerY, radius, radius);

    // Goal areas (simplified)
    const goalWidth = area.width * 0.15;
    const goalHeight = area.height * 0.4;
    const goalY = area.y + (area.height - goalHeight) / 2;

    // Left goal area
    pdf.rect(area.x, goalY, goalWidth, goalHeight);

    // Right goal area
    pdf.rect(area.x + area.width - goalWidth, goalY, goalWidth, goalHeight);
  }

  private drawElements(
    pdf: jsPDF,
    elements: TrainingElement[],
    area: { x: number; y: number; width: number; height: number },
    config: FieldConfig
  ): void {
    elements.forEach(element => {
      const x = area.x + (element.position.x / 100) * area.width;
      const y = area.y + (element.position.y / 100) * area.height;

      // Set element color, respecting custom colors
      const color = this.getElementColor(element);
      pdf.setFillColor(color[0], color[1], color[2]);
      pdf.setDrawColor(0, 0, 0);
      pdf.setLineWidth(0.3);

      // Draw element based on type
      switch (element.type) {
        case 'player':
          // Draw player as ellipse (circle)
          pdf.ellipse(x, y, 2, 2, 'FD');
          // Add player number if available
          if (element.properties.playerNumber) {
            pdf.setFontSize(6);
            pdf.setTextColor(255, 255, 255);
            pdf.text(element.properties.playerNumber.toString(), x - 1, y + 1);
            pdf.setTextColor(0, 0, 0);
          }
          break;
        case 'ball':
          pdf.setFillColor(255, 255, 255);
          pdf.ellipse(x, y, 1.5, 1.5, 'FD');
          break;
        case 'cone':
          // Draw triangle for cone using lines
          pdf.setDrawColor(color[0], color[1], color[2]);
          pdf.setLineWidth(0.3);
          pdf.setFillColor(color[0], color[1], color[2]);

          // Draw triangle manually with lines
          pdf.line(x, y - 2, x - 1.5, y + 1);
          pdf.line(x - 1.5, y + 1, x + 1.5, y + 1);
          pdf.line(x + 1.5, y + 1, x, y - 2);
          break;
        case 'goal':
          pdf.rect(x - 3, y - 1.5, 6, 3, 'D');
          break;
        default:
          pdf.ellipse(x, y, 1.5, 1.5, 'FD');
      }

      // Add label or player name if present
      const label =
        element.properties.label ||
        (element.type === 'player'
          ? element.properties.playerName?.split(' ')[0]
          : undefined);
      if (label) {
        pdf.setFontSize(6);
        pdf.setTextColor(0, 0, 0);
        pdf.text(label, x, y + 3, { align: 'center' });
      }
    });
  }

  private drawTrajectories(
    pdf: jsPDF,
    step: DrillStep,
    area: { x: number; y: number; width: number; height: number },
    config: FieldConfig
  ): void {
    // Note: This would require trajectory data from the step
    // For now, we'll draw simple arrows between elements if they have movement
    step.elements.forEach(element => {
      if (element.properties.movement) {
        const startX = area.x + (element.position.x / 100) * area.width;
        const startY = area.y + (element.position.y / 100) * area.height;
        const endX = startX + (element.properties.movement.x || 0) * 10;
        const endY = startY + (element.properties.movement.y || 0) * 10;

        // Draw arrow
        pdf.setDrawColor(255, 0, 0); // Red
        pdf.setLineWidth(1);
        pdf.line(startX, startY, endX, endY);

        // Draw arrowhead
        const angle = Math.atan2(endY - startY, endX - startX);
        const arrowLength = 3;
        const arrowAngle = Math.PI / 6;

        pdf.line(
          endX,
          endY,
          endX - arrowLength * Math.cos(angle - arrowAngle),
          endY - arrowLength * Math.sin(angle - arrowAngle)
        );

        pdf.line(
          endX,
          endY,
          endX - arrowLength * Math.cos(angle + arrowAngle),
          endY - arrowLength * Math.sin(angle + arrowAngle)
        );
      }
    });
  }

  private drawDrawings(
    pdf: jsPDF,
    drawings: any[],
    area: { x: number; y: number; width: number; height: number },
    config: FieldConfig
  ): void {
    // Draw any custom drawings/annotations on the field
    drawings.forEach(drawing => {
      if (drawing.type === 'line') {
        pdf.setDrawColor(0, 0, 0); // Black
        pdf.setLineWidth(drawing.width || 1);

        const startX = area.x + (drawing.start.x / 100) * area.width;
        const startY = area.y + (drawing.start.y / 100) * area.height;
        const endX = area.x + (drawing.end.x / 100) * area.width;
        const endY = area.y + (drawing.end.y / 100) * area.height;

        pdf.line(startX, startY, endX, endY);
      }
    });
  }

  private addDrillInfo(pdf: jsPDF, drill: TrainingDrill, template: PDFTemplate): void {
    const infoY = template.fieldArea.y + template.fieldArea.height + 10;

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Informações do Drill:', template.margins.left, infoY);

    pdf.setFont('helvetica', 'normal');
    const info = [
      `Categoria: ${drill.category}`,
      `Dificuldade: ${drill.difficulty}`,
      `Jogadores: ${drill.playersRequired}`,
      `Duração Total: ${this.formatDuration(drill.totalDuration)}`,
      `Passos: ${drill.steps.length}`
    ];

    info.forEach((line, index) => {
      pdf.text(line, template.margins.left, infoY + 8 + (index * 6));
    });
  }

  private addStepInstructions(
    pdf: jsPDF,
    step: DrillStep,
    area: { x: number; y: number; width: number; height: number }
  ): void {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Instruções:', area.x, area.y);

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    const instructionLines = pdf.splitTextToSize(step.description, area.width);
    pdf.text(instructionLines, area.x, area.y + 8);
  }

  private addStepAnnotations(
    pdf: jsPDF,
    step: DrillStep,
    area: { x: number; y: number; width: number; height: number }
  ): void {
    const annotationsY = area.y + 60; // Offset from instructions

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Observações:', area.x, annotationsY);

    pdf.setFont('helvetica', 'normal');
    step.annotations.forEach((annotation, index) => {
      const text = `• ${annotation.text || annotation}`;
      pdf.text(text, area.x, annotationsY + 8 + (index * 6));
    });
  }

  private drawTimeline(
    pdf: jsPDF,
    drill: TrainingDrill,
    area: { x: number; y: number; width: number; height: number }
  ): void {
    const totalDuration = drill.totalDuration;
    let currentTime = 0;

    // Timeline background
    pdf.setFillColor('#f3f4f6');
    pdf.rect(area.x, area.y, area.width, area.height, 'F');

    // Timeline border
    pdf.setDrawColor('#d1d5db');
    pdf.rect(area.x, area.y, area.width, area.height);

    // Draw steps
    drill.steps.forEach((step, index) => {
      const stepWidth = (step.duration / totalDuration) * area.width;
      const stepX = area.x + (currentTime / totalDuration) * area.width;

      // Step block
      const stepColor = this.getStepColor(index);
      pdf.setFillColor(stepColor);
      pdf.rect(stepX, area.y + 5, stepWidth, area.height - 10, 'F');

      // Step label
      pdf.setFontSize(8);
      pdf.setTextColor('#000000');
      if (stepWidth > 20) { // Only show label if there's enough space
        pdf.text(`${index + 1}`, stepX + 2, area.y + area.height / 2);
      }

      currentTime += step.duration;
    });

    // Time markers
    pdf.setFontSize(8);
    pdf.setTextColor('#6b7280');
    pdf.text('0:00', area.x, area.y - 2);
    pdf.text(this.formatDuration(totalDuration), area.x + area.width - 15, area.y - 2);
  }

  private addMetadata(pdf: jsPDF, drill: TrainingDrill): void {
    pdf.setProperties({
      title: drill.name,
      subject: `Drill de Treinamento - ${drill.category}`,
      author: 'Game Day Nexus Platform',
      keywords: `treinamento, futebol, ${drill.category}, ${drill.difficulty}`,
      creator: 'Game Day Nexus Platform',
      producer: 'Game Day Nexus Platform'
    });
  }

  private addWatermark(pdf: jsPDF, watermark: { text: string; position: string; opacity?: number }): void {
    const pageCount = pdf.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);

      // Set watermark style
      pdf.setTextColor(200, 200, 200);
      pdf.setFontSize(40);
      pdf.setFont('helvetica', 'bold');

      // Calculate position
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      let x = pageWidth / 2;
      let y = pageHeight / 2;

      switch (watermark.position) {
        case 'top-left':
          x = 20; y = 30;
          break;
        case 'top-right':
          x = pageWidth - 20; y = 30;
          break;
        case 'bottom-left':
          x = 20; y = pageHeight - 20;
          break;
        case 'bottom-right':
          x = pageWidth - 20; y = pageHeight - 20;
          break;
        default: // center
          break;
      }

      // Add watermark text
      pdf.text(watermark.text, x, y, {
        align: 'center',
        angle: 45
      });
    }
  }

  private getElementColor(element: TrainingElement): [number, number, number] {
    if (element.properties.color) {
      const hex = element.properties.color.replace('#', '');
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      return [r, g, b];
    }
    const colors: Record<string, [number, number, number]> = {
      player: [59, 130, 246], // Blue
      ball: [255, 255, 255],   // White
      cone: [245, 158, 11],   // Orange
      goal: [0, 0, 0],   // Black
      obstacle: [239, 68, 68] // Red
    };
    return colors[element.type] || [107, 114, 128]; // Default gray
  }

  private getStepColor(index: number): string {
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
    return colors[index % colors.length];
  }

  private formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  private generateFilename(drill: TrainingDrill, format: string): string {
    const timestamp = new Date().toISOString().slice(0, 10);
    const safeName = drill.name.replace(/[^a-zA-Z0-9]/g, '_');
    return `${safeName}_${timestamp}.${format}`;
  }

  validateOptions(options: PDFExportOptions): boolean {
    if (options.format !== 'pdf') return false;

    const validPageSizes = ['A4', 'A3', 'Letter', 'Legal'];
    if (options.pageSize && !validPageSizes.includes(options.pageSize)) return false;

    const validOrientations = ['portrait', 'landscape'];
    if (options.orientation && !validOrientations.includes(options.orientation)) return false;

    const validTemplates = ['standard', 'detailed', 'minimal', 'landscape'];
    if (options.template && !validTemplates.includes(options.template)) return false;

    return true;
  }

  getDefaultOptions(): PDFExportOptions {
    return { ...this.defaultOptions };
  }

  getSupportedFormats(): ExportFormat[] {
    return ['pdf'];
  }
}