import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/context/UserContext";
import { Collaborator, ClubUser, getUsersWithoutCollaborator, linkCollaboratorToUser } from "@/api/api";

interface LinkUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function LinkUserDialog({ open, onOpenChange, clubId, collaborator, onSuccess }: LinkUserDialogProps) {
  const { user } = useUser();
  const { toast } = useToast();
  const [users, setUsers] = useState<ClubUser[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const load = async () => {
      if (open) {
        const data = await getUsersWithoutCollaborator(clubId, ["player", "medical"]);
        setUsers(data);
      }
    };
    load();
  }, [open, clubId]);

  const handleLink = async () => {
    if (!selectedUser || !user) return;
    try {
      setLoading(true);
      await linkCollaboratorToUser(clubId, user.id, collaborator.id, selectedUser);
      toast({ title: "Sucesso", description: "Usuário vinculado com sucesso" });
      onOpenChange(false);
      if (onSuccess) onSuccess();
    } catch (err: any) {
      console.error("Erro ao vincular usuário:", err);
      toast({ title: "Erro", description: err.message || "Não foi possível vincular o usuário", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Vincular Usuário</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-2">
          <Select value={selectedUser} onValueChange={setSelectedUser}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione um usuário" />
            </SelectTrigger>
            <SelectContent>
              {users.map(u => (
                <SelectItem key={u.id} value={u.id}>{u.name} - {u.email}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleLink} disabled={loading || !selectedUser}>{loading ? "Salvando..." : "Vincular"}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}