import { useState, useCallback } from 'react';
import { 
  searchPlayerByCPF, 
  getGlobalPlayerDocuments, 
  initiatePlayerTransfer,
  getPlayerTransferHistory,
  migratePlayerToGlobal,
  validateCPF,
  type PlayerSearchResult,
  type GlobalPlayerDocument,
  type PlayerTransfer,
  type TransferResult
} from '@/api/playerTransfers';
import { useUser } from '@/context/UserContext';
import { toast } from 'sonner';

export function usePlayerTransfers() {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [transferring, setTransferring] = useState(false);
  const [searchResult, setSearchResult] = useState<PlayerSearchResult | null>(null);
  const [documents, setDocuments] = useState<GlobalPlayerDocument[]>([]);
  const [transferHistory, setTransferHistory] = useState<PlayerTransfer[]>([]);

  /**
   * Busca um jogador por CPF
   */
  const searchPlayer = useCallback(async (cpf: string): Promise<PlayerSearchResult | null> => {
    if (!cpf.trim()) {
      toast.error('Digite um CPF para buscar');
      return null;
    }

    if (!validateCPF(cpf)) {
      toast.error('CPF inválido');
      return null;
    }

    setLoading(true);
    try {
      const result = await searchPlayerByCPF(cpf);
      setSearchResult(result);

      if (result.found && result.global_player_id) {
        // Buscar documentos do jogador
        const playerDocuments = await getGlobalPlayerDocuments(result.global_player_id);
        setDocuments(playerDocuments);
        
        if (result.is_active_elsewhere) {
          if (result.active_club_name === user?.club_info?.name) {
            toast.error('Jogador já está cadastrado em seu clube.');
          } else {
            toast.error(`Jogador já está ativo no clube: ${result.active_club_name}. O clube atual deve marcar o jogador como inativo antes da transferência.`);
          }
        } else if (result.documents_count > 0) {
          toast.success(`Jogador encontrado! ${result.documents_count} documento(s) disponível(is)`);
        } else {
          toast.success('Jogador encontrado!');
        }
      } else {
        toast.info('Jogador não encontrado no sistema. Será criado um novo registro.');
        setDocuments([]);
      }

      return result;
    } catch (error) {
      console.error('Erro na busca:', error);
      toast.error('Erro ao buscar jogador');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Inicia uma transferência de jogador
   */
  const transferPlayer = useCallback(async (
    cpf: string,
    playerData: any
  ): Promise<TransferResult | null> => {
    if (!user?.club_id) {
      toast.error('Clube não identificado');
      return null;
    }

    setTransferring(true);
    try {
      const result = await initiatePlayerTransfer(
        cpf,
        user.club_id,
        playerData,
        user.id
      );

      if (result.success) {
        toast.success(result.message);
        
        // Se há documentos, informar que serão copiados
        if (documents.length > 0 && result.player_id) {
          toast.info('Documentos copiados automaticamente');
        }

        // Limpar estado
        setSearchResult(null);
        setDocuments([]);
      } else {
        toast.error(result.message);
      }

      return result;
    } catch (error) {
      console.error('Erro na transferência:', error);
      toast.error('Erro ao transferir jogador');
      return null;
    } finally {
      setTransferring(false);
    }
  }, [user, documents.length]);

  /**
   * Obtém o histórico de transferências de um jogador
   */
  const getTransferHistory = useCallback(async (globalPlayerId: string): Promise<PlayerTransfer[]> => {
    setLoading(true);
    try {
      const history = await getPlayerTransferHistory(globalPlayerId);
      setTransferHistory(history);
      return history;
    } catch (error) {
      console.error('Erro ao buscar histórico:', error);
      toast.error('Erro ao buscar histórico de transferências');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Migra um jogador existente para o sistema global
   */
  const migratePlayer = useCallback(async (playerId: string): Promise<string | null> => {
    setLoading(true);
    try {
      const globalPlayerId = await migratePlayerToGlobal(playerId);
      toast.success('Jogador migrado para o sistema global com sucesso');
      return globalPlayerId;
    } catch (error) {
      console.error('Erro na migração:', error);
      toast.error('Erro ao migrar jogador');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Limpa o estado atual
   */
  const clearState = useCallback(() => {
    setSearchResult(null);
    setDocuments([]);
    setTransferHistory([]);
  }, []);

  /**
   * Valida um CPF
   */
  const isValidCPF = useCallback((cpf: string): boolean => {
    return validateCPF(cpf);
  }, []);

  return {
    // Estado
    loading,
    transferring,
    searchResult,
    documents,
    transferHistory,
    
    // Ações
    searchPlayer,
    transferPlayer,
    getTransferHistory,
    migratePlayer,
    clearState,
    isValidCPF,
    
    // Dados do usuário
    clubId: user?.club_id,
    userId: user?.id
  };
}