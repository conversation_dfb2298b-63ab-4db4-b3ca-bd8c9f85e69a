import React, { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  getCurrentMasterUser,
  masterSignOut,
  MasterUser,
  MasterOrganization,
} from '../api/masterAuth.ts';
import { supabase } from '../integrations/supabase/client';

interface MasterAuthContextType {
  masterUser: MasterUser | null;
  organization: MasterOrganization | null;
  loading: boolean;
  signOut: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  checkRole: (roles: string[]) => boolean;
  refreshAuth: () => Promise<void>;
}

const MasterAuthContext = createContext<MasterAuthContextType | undefined>(undefined);

interface MasterAuthProviderProps {
  children: ReactNode;
}

export const MasterAuthProvider = ({ children }: MasterAuthProviderProps) => {
  const [masterUser, setMasterUser] = useState<MasterUser | null>(null);
  const [organization, setOrganization] = useState<MasterOrganization | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const checkMasterAuth = async () => {
    console.log('checkMasterAuth: Iniciando verificação de autenticação master...');
    try {
      setLoading(true);
      const authData = await getCurrentMasterUser();
      console.log('checkMasterAuth: Resultado de getCurrentMasterUser:', authData ? 'Usuário encontrado' : 'Nenhum usuário encontrado');

      if (authData) {
        setMasterUser(authData.masterUser);
        setOrganization(authData.organization);
        console.log('checkMasterAuth: Usuário master e organização definidos.');
      } else {
        setMasterUser(null);
        setOrganization(null);
        console.log('checkMasterAuth: Usuário master e organização limpos.');
      }
    } catch (error) {
      console.error('checkMasterAuth: Erro ao verificar autenticação master:', error);
      setMasterUser(null);
      setOrganization(null);
    } finally {
      setLoading(false);
      console.log('checkMasterAuth: Verificação de autenticação master finalizada. Loading set to false.');
    }
  };

  const signOut = async () => {
    try {
      await masterSignOut();
      setMasterUser(null);
      setOrganization(null);
      navigate('/master/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const refreshAuth = async () => {
    await checkMasterAuth();
  };

  const checkPermission = (permission: string): boolean => {
    if (!masterUser) return false;

    if (masterUser.role === 'super_admin') {
      return true;
    }

    const permissions = masterUser.permissions || {};
    const permissionParts = permission.split('.');
    let currentLevel = permissions;

    for (const part of permissionParts) {
      if (typeof currentLevel !== 'object' || currentLevel === null) {
        return false;
      }
      currentLevel = currentLevel[part];
    }

    return Boolean(currentLevel);
  };

  const checkRole = (roles: string[]): boolean => {
    if (!masterUser) return false;
    return roles.includes(masterUser.role);
  };

  useEffect(() => {
    checkMasterAuth();

    // Escutar mudanças na autenticação do Supabase
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session ? 'session exists' : 'no session');
        if (event === 'SIGNED_OUT' || !session) {
          setMasterUser(null);
          setOrganization(null);
        } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await checkMasterAuth();
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []); // Removido masterUser da dependência para evitar loops

  const value: MasterAuthContextType = {
    masterUser,
    organization,
    loading,
    signOut,
    checkPermission,
    checkRole,
    refreshAuth
  };

  return (
    <MasterAuthContext.Provider value={value}>
      {children}
    </MasterAuthContext.Provider>
  );
};

export const useMasterAuth = (): MasterAuthContextType => {
  const context = useContext(MasterAuthContext);
  if (context === undefined) {
    throw new Error('useMasterAuth deve ser usado dentro de um MasterAuthProvider');
  }
  return context;
};

/**
 * Hook para proteger rotas master
 * Redireciona para login se não estiver autenticado
 */
export const useRequireMasterAuth = (requiredPermissions?: string[], requiredRoles?: string[]) => {
  const { masterUser, loading, checkPermission, checkRole } = useMasterAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Só executa verificações quando não está carregando
    if (!loading) {
      // Verifica se está na página de login para evitar redirecionamento circular
      const currentPath = window.location.pathname;
      if (currentPath === '/master/login') {
        return;
      }

      if (!masterUser) {
        console.log('useRequireMasterAuth: Usuário não autenticado, redirecionando para login');
        navigate('/master/login', { replace: true });
        return;
      }

      // Verificar permissões se especificadas
      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasAllPermissions = requiredPermissions.every(permission =>
          checkPermission(permission)
        );

        if (!hasAllPermissions) {
          console.log('useRequireMasterAuth: Permissões insuficientes, redirecionando para unauthorized');
          navigate('/master/unauthorized', { replace: true });
          return;
        }
      }

      // Verificar roles se especificados
      if (requiredRoles && requiredRoles.length > 0) {
        if (!checkRole(requiredRoles)) {
          console.log('useRequireMasterAuth: Role insuficiente, redirecionando para unauthorized');
          navigate('/master/unauthorized', { replace: true });
          return;
        }
      }
    }
  }, [masterUser, loading, navigate, requiredPermissions, requiredRoles, checkPermission, checkRole]);

  return { masterUser, loading };
};

/**
 * Hook para verificar se usuário tem acesso a uma funcionalidade
 */
export const useMasterPermissions = () => {
  const { checkPermission, checkRole, masterUser } = useMasterAuth();

  return {
    checkPermission,
    checkRole,
    masterUser,
    isSuperAdmin: masterUser?.role === 'super_admin',
    isAdmin: masterUser?.role === 'admin' || masterUser?.role === 'super_admin',
    canManageClubs: checkPermission('clubs.manage') || checkRole(['super_admin', 'admin']),
    canManagePlans: checkPermission('plans.manage') || checkRole(['super_admin', 'admin']),
    canManageUsers: checkPermission('users.manage') || checkRole(['super_admin']),
    canViewAnalytics: checkPermission('analytics.view') || checkRole(['super_admin', 'admin']),
    canManagePayments: checkPermission('payments.manage') || checkRole(['super_admin', 'admin'])
  };
};