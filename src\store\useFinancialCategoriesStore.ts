import { create } from "zustand";
import {
  FinancialCategory,
  getFinancialCategories,
  createFinancialCategory,
  updateFinancialCategory,
  deleteFinancialCategory,
} from "@/api/financialCategories";

interface FinancialCategoriesState {
  categories: FinancialCategory[];
  loading: boolean;
  error: string | null;
  fetchCategories: (clubId: number) => Promise<void>;
  addCategory: (clubId: number, name: string) => Promise<void>;
  updateCategory: (clubId: number, id: number, name: string) => Promise<void>;
  deleteCategory: (clubId: number, id: number) => Promise<void>;
}

export const useFinancialCategoriesStore = create<FinancialCategoriesState>((set) => ({
  categories: [],
  loading: false,
  error: null,

  fetchCategories: async (clubId: number) => {
    set({ loading: true, error: null });
    try {
      const categories = await getFinancialCategories(clubId);
      set({ categories, loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar categorias", loading: false });
    }
  },

  addCategory: async (clubId: number, name: string) => {
    set({ loading: true, error: null });
    try {
      const newCat = await createFinancialCategory(clubId, name);
      set((state) => ({ categories: [...state.categories, newCat], loading: false }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao adicionar categoria", loading: false });
    }
  },

  updateCategory: async (clubId: number, id: number, name: string) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateFinancialCategory(clubId, id, name);
      set((state) => ({
        categories: state.categories.map((c) => (c.id === id ? updated : c)),
        loading: false,
      }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao atualizar categoria", loading: false });
    }
  },

  deleteCategory: async (clubId: number, id: number) => {
    set({ loading: true, error: null });
    try {
      await deleteFinancialCategory(clubId, id);
      set((state) => ({ categories: state.categories.filter((c) => c.id !== id), loading: false }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao excluir categoria", loading: false });
    }
  },
}));