CREATE TABLE IF NOT EXISTS match_categories (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  match_id UUID REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(match_id, category_id)
);

-- Index to speed up lookups
CREATE INDEX IF NOT EXISTS idx_match_categories_match ON match_categories(match_id);
CREATE INDEX IF NOT EXISTS idx_match_categories_category ON match_categories(category_id);