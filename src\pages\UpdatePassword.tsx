import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { resetPasswordWithToken } from "@/api/auth";

export default function UpdatePassword() {
  const [password, setPassword] = useState("");
  const [confirm, setConfirm] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token") || "";
  const tokenError = !token;

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (tokenError) {
      setError("Token inv\u00e1lido ou ausente");
      return;
    }
    if (password.length < 6) {
      setError("A senha deve ter pelo menos 6 caracteres");
      return;
    }
    if (password.length > 64) {
      setError("A senha deve ter no m\u00e1ximo 64 caracteres");
      return;
    }
    if (password !== confirm) {
      setError("As senhas n\u00e3o coincidem");
      return;
    }
    setLoading(true);
    setError("");
    try {
      await resetPasswordWithToken(token, password);
      setSuccess(true);
      setTimeout(() => navigate("/login"), 2000);
    } catch (err) {
      console.error("Erro ao atualizar senha:", err);
      setError("N\u00e3o foi poss\u00edvel atualizar a senha.");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-muted">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Definir Nova Senha</CardTitle>
          <CardDescription>Digite sua nova senha abaixo.</CardDescription>
        </CardHeader>
        <CardContent>
          {success ? (
            <p className="text-sm text-green-600">Senha atualizada com sucesso.</p>
          ) : (
            <form className="space-y-4" onSubmit={handleSubmit}>
              {tokenError && (
                <p className="text-sm text-red-600">Token inv\u00e1lido ou expirado.</p>
              )}
              <Input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
                minLength={6}
                maxLength={64}
                placeholder="Nova senha"
              />
              <Input
                type="password"
                value={confirm}
                onChange={e => setConfirm(e.target.value)}
                required
                minLength={6}
                maxLength={64}
                placeholder="Confirme a senha"
              />
              {error && <p className="text-sm text-red-600">{error}</p>}
              <Button type="submit" className="w-full" disabled={loading || tokenError}>
                {loading ? "Salvando..." : "Salvar"}
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}