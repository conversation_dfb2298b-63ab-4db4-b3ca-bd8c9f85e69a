import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Heart,
  Moon,
  Zap,
  Smile,
  AlertCircle,
  CheckCircle,
  Save,
  Calendar
} from 'lucide-react';
import { useUser } from '@/context/UserContext';
import { createWellnessData, WellnessData } from '@/api/injuryPrevention';
import { getPlayers } from '@/api/players';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Player {
  id: number;
  name: string;
}

interface WellnessDataFormProps {
  clubId: number;
  playerId?: number;
  onSuccess?: () => void;
}

export function WellnessDataForm({ clubId, playerId, onSuccess }: WellnessDataFormProps) {
  const { user } = useUser();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(playerId || null);
  
  // Estados do formulário
  const [formData, setFormData] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    sleep_hours: 8,
    sleep_quality: 7,
    fatigue_level: 3,
    muscle_soreness: 2,
    stress_level: 3,
    mood: 7,
    weight: 0,
    resting_heart_rate: 60,
    notes: ''
  });

  useEffect(() => {
    loadPlayers();
  }, [clubId]);

  const loadPlayers = async () => {
    try {
      const playersData = await getPlayers(clubId);
      setPlayers(playersData);
    } catch (error) {
      console.error('Erro ao carregar jogadores:', error);
      toast.error('Erro ao carregar lista de jogadores');
    }
  };

  const handleSliderChange = (field: string, value: number[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value[0]
    }));
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPlayerId) {
      toast.error('Selecione um jogador');
      return;
    }

    if (!user?.id) {
      toast.error('Usuário não autenticado');
      return;
    }

    try {
      setLoading(true);

      const wellnessData: Omit<WellnessData, 'id' | 'club_id'> = {
        player_id: selectedPlayerId,
        date: formData.date,
        sleep_hours: formData.sleep_hours,
        sleep_quality: formData.sleep_quality,
        fatigue_level: formData.fatigue_level,
        muscle_soreness: formData.muscle_soreness,
        stress_level: formData.stress_level,
        mood: formData.mood,
        weight: formData.weight > 0 ? formData.weight : undefined,
        resting_heart_rate: formData.resting_heart_rate > 0 ? formData.resting_heart_rate : undefined,
        notes: formData.notes || undefined
      };

      await createWellnessData(clubId, user.id, wellnessData);
      
      toast.success('Dados de wellness registrados com sucesso!');
      
      // Reset form
      setFormData({
        date: format(new Date(), 'yyyy-MM-dd'),
        sleep_hours: 8,
        sleep_quality: 7,
        fatigue_level: 3,
        muscle_soreness: 2,
        stress_level: 3,
        mood: 7,
        weight: 0,
        resting_heart_rate: 60,
        notes: ''
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao salvar dados:', error);
      toast.error('Erro ao salvar dados de wellness');
    } finally {
      setLoading(false);
    }
  };

  // Função para determinar cor baseada no valor
  const getScoreColor = (value: number, reverse: boolean = false) => {
    if (reverse) {
      // Para fadiga, dor, stress (valores baixos são melhores)
      if (value <= 3) return 'text-green-600';
      if (value <= 6) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      // Para qualidade do sono, humor (valores altos são melhores)
      if (value >= 7) return 'text-green-600';
      if (value >= 4) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getScoreBadge = (value: number, reverse: boolean = false) => {
    const color = getScoreColor(value, reverse);
    let label = '';
    
    if (reverse) {
      if (value <= 3) label = 'Baixo';
      else if (value <= 6) label = 'Moderado';
      else label = 'Alto';
    } else {
      if (value >= 7) label = 'Bom';
      else if (value >= 4) label = 'Regular';
      else label = 'Ruim';
    }

    return (
      <Badge variant="outline" className={color}>
        {label}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-red-500" />
          Questionário de Wellness Diário
        </CardTitle>
        <CardDescription>
          Registre os dados de bem-estar para análise de risco de lesões
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Seleção de Jogador e Data */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="player">Jogador</Label>
              <Select 
                value={selectedPlayerId?.toString() || ''} 
                onValueChange={(value) => setSelectedPlayerId(Number(value))}
                disabled={!!playerId} // Desabilita se playerId foi passado como prop
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.id.toString()}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Data</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Dados de Sono */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Moon className="h-4 w-4" />
              Qualidade do Sono
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Horas de Sono</Label>
                <Input
                  type="number"
                  step="0.5"
                  min="0"
                  max="12"
                  value={formData.sleep_hours}
                  onChange={(e) => handleInputChange('sleep_hours', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Qualidade do Sono</Label>
                  {getScoreBadge(formData.sleep_quality)}
                </div>
                <Slider
                  value={[formData.sleep_quality]}
                  onValueChange={(value) => handleSliderChange('sleep_quality', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Péssima (1)</span>
                  <span className="font-medium">{formData.sleep_quality}</span>
                  <span>Excelente (10)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Dados Físicos */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Estado Físico
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nível de Fadiga</Label>
                  {getScoreBadge(formData.fatigue_level, true)}
                </div>
                <Slider
                  value={[formData.fatigue_level]}
                  onValueChange={(value) => handleSliderChange('fatigue_level', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Nenhuma (1)</span>
                  <span className="font-medium">{formData.fatigue_level}</span>
                  <span>Extrema (10)</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Dor Muscular</Label>
                  {getScoreBadge(formData.muscle_soreness, true)}
                </div>
                <Slider
                  value={[formData.muscle_soreness]}
                  onValueChange={(value) => handleSliderChange('muscle_soreness', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Nenhuma (1)</span>
                  <span className="font-medium">{formData.muscle_soreness}</span>
                  <span>Intensa (10)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Dados Psicológicos */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Smile className="h-4 w-4" />
              Estado Mental
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Nível de Stress</Label>
                  {getScoreBadge(formData.stress_level, true)}
                </div>
                <Slider
                  value={[formData.stress_level]}
                  onValueChange={(value) => handleSliderChange('stress_level', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Relaxado (1)</span>
                  <span className="font-medium">{formData.stress_level}</span>
                  <span>Muito Estressado (10)</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Humor</Label>
                  {getScoreBadge(formData.mood)}
                </div>
                <Slider
                  value={[formData.mood]}
                  onValueChange={(value) => handleSliderChange('mood', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Péssimo (1)</span>
                  <span className="font-medium">{formData.mood}</span>
                  <span>Excelente (10)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Dados Opcionais */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Dados Adicionais (Opcional)</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Peso (kg)</Label>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  placeholder="Ex: 75.5"
                  value={formData.weight || ''}
                  onChange={(e) => handleInputChange('weight', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label>Frequência Cardíaca em Repouso (bpm)</Label>
                <Input
                  type="number"
                  min="30"
                  max="120"
                  placeholder="Ex: 60"
                  value={formData.resting_heart_rate || ''}
                  onChange={(e) => handleInputChange('resting_heart_rate', Number(e.target.value))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Observações</Label>
              <Textarea
                placeholder="Alguma observação adicional sobre como se sente hoje..."
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </div>
          </div>

          {/* Botão de Envio */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading || !selectedPlayerId}
          >
            {loading ? (
              <>
                <Save className="h-4 w-4 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Registrar Dados de Wellness
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
