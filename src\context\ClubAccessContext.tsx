import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useUser } from "./UserContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, CreditCard, Clock, Mail } from "lucide-react";
import { PaymentWarningBanner } from "@/components/layout/PaymentWarningBanner";

export interface ClubAccessInfo {
  hasAccess: boolean;
  subscriptionStatus: 'active' | 'suspended' | 'cancelled' | 'trial';
  paymentStatus: 'current' | 'overdue' | 'cancelled';
  planName?: string;
  isTrialExpired?: boolean;
  trialDaysLeft?: number;
  errorMessage?: string;
  warningMessage?: string;
}

interface ClubAccessContextType {
  accessInfo: ClubAccessInfo;
  loading: boolean;
  refreshAccess: () => Promise<void>;
}

const ClubAccessContext = createContext<ClubAccessContextType>({
  accessInfo: {
    hasAccess: true,
    subscriptionStatus: 'active',
    paymentStatus: 'current'
  },
  loading: true,
  refreshAccess: async () => {},
});

// Componente de tela de acesso bloqueado
const AccessBlockedScreen = ({ accessInfo }: { accessInfo: ClubAccessInfo }) => {
  const getBlockedContent = () => {
    if (accessInfo.subscriptionStatus === 'suspended') {
      return {
        icon: <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />,
        title: "Acesso Suspenso",
        message: "O acesso do seu clube foi suspenso por falta de pagamento.",
        description: "Entre em contato com o suporte para regularizar a situação.",
        action: (
          <div className="space-y-2">
            <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
              <Mail className="w-4 h-4 mr-2" />
              Entrar em Contato
            </Button>
          </div>
        )
      };
    }

    if (accessInfo.subscriptionStatus === 'cancelled') {
      return {
        icon: <CreditCard className="w-16 h-16 text-gray-500 mx-auto mb-4" />,
        title: "Assinatura Cancelada",
        message: "A assinatura do seu clube foi cancelada.",
        description: "Para reativar o acesso, entre em contato com o suporte.",
        action: (
          <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
            <Mail className="w-4 h-4 mr-2" />
            Reativar Assinatura
          </Button>
        )
      };
    }

    if (accessInfo.isTrialExpired) {
      return {
        icon: <Clock className="w-16 h-16 text-orange-500 mx-auto mb-4" />,
        title: "Período de Teste Expirado",
        message: "Seu período de teste gratuito expirou.",
        description: "Escolha um plano para continuar usando o Game Day Nexus.",
        action: (
          <div className="space-y-2">
            <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
              <CreditCard className="w-4 h-4 mr-2" />
              Escolher Plano
            </Button>
            <Button variant="outline" className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
              <Mail className="w-4 h-4 mr-2" />
              Falar com Suporte
            </Button>
          </div>
        )
      };
    }

    return {
      icon: <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />,
      title: "Acesso Negado",
      message: accessInfo.errorMessage || "Não foi possível verificar o acesso.",
      description: "Entre em contato com o suporte para resolver este problema.",
      action: (
        <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
          <Mail className="w-4 h-4 mr-2" />
          Entrar em Contato
        </Button>
      )
    };
  };

  const content = getBlockedContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          {content.icon}
          <CardTitle className="text-2xl font-bold text-gray-900">
            {content.title}
          </CardTitle>
          <CardDescription className="text-lg">
            {content.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            {content.description}
          </p>
          {content.action}
          <div className="pt-4 border-t">
            <p className="text-sm text-gray-500">
              Plano atual: <span className="font-medium">{accessInfo.planName || 'Não definido'}</span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Componente de aviso (para trials expirando, etc.)
const AccessWarningBanner = ({ accessInfo }: { accessInfo: ClubAccessInfo }) => {
  if (!accessInfo.warningMessage) return null;

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            {accessInfo.warningMessage}
          </p>
          {accessInfo.trialDaysLeft !== undefined && (
            <p className="text-xs text-yellow-600 mt-1">
              {accessInfo.trialDaysLeft > 0 
                ? `${accessInfo.trialDaysLeft} dias restantes`
                : 'Expira hoje'
              }
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export function ClubAccessProvider({ children }: { children: ReactNode }) {
  const { user, loading: userLoading } = useUser();
  const [accessInfo, setAccessInfo] = useState<ClubAccessInfo>({
    hasAccess: true,
    subscriptionStatus: 'active',
    paymentStatus: 'current'
  });
  const [loading, setLoading] = useState(true);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  const checkAccess = async () => {
    // Se UserContext ainda está carregando, aguardar
    if (userLoading) {
      return;
    }
    
    // Se não há usuário, aguardar um pouco mais (pode estar carregando ainda)
    if (!user) {
      // Aguardar para os dados chegarem
      if (!initialCheckDone) {
        setTimeout(() => {
          checkAccess();
        }, 500);
        return;
      }
      
      // Se já aguardou e ainda não há usuário, mostrar erro
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: 'cancelled',
        paymentStatus: 'cancelled',
        errorMessage: 'Usuário não encontrado'
      });
      setLoading(false);
      setInitialCheckDone(true);
      return;
    }
    
    // Se não há dados do clube
    if (!user.club_info) {
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: 'cancelled',
        paymentStatus: 'cancelled',
        errorMessage: 'Dados do clube não encontrados'
      });
      setLoading(false);
      setInitialCheckDone(true);
      return;
    }

    const clubInfo = user.club_info;
    
    // Verificar se clube está suspenso
    if (clubInfo.subscription_status === 'suspended') {
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: clubInfo.subscription_status,
        paymentStatus: clubInfo.payment_status,
        planName: clubInfo.master_plans?.name,
        errorMessage: 'Acesso suspenso por falta de pagamento'
      });
      setLoading(false);
      setInitialCheckDone(true);
      return;
    }

    // Verificar se trial expirou PRIMEIRO (prioridade sobre status)
    if (clubInfo.is_trial && clubInfo.trial_end_date) {
      const trialEnd = new Date(clubInfo.trial_end_date);
      const now = new Date();
      const daysLeft = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      if (trialEnd < now) {
        setAccessInfo({
          hasAccess: false,
          subscriptionStatus: 'trial',
          paymentStatus: clubInfo.payment_status,
          planName: clubInfo.master_plans?.name,
          isTrialExpired: true,
          errorMessage: 'Período de teste expirado'
        });
        setLoading(false);
        setInitialCheckDone(true);
        return;
      }

      // Trial ainda válido, mas próximo do fim
      if (daysLeft <= 3) {
        setAccessInfo({
          hasAccess: true,
          subscriptionStatus: 'trial',
          paymentStatus: clubInfo.payment_status,
          planName: clubInfo.master_plans?.name,
          trialDaysLeft: daysLeft,
          warningMessage: `Seu período de teste expira em ${daysLeft} dia${daysLeft !== 1 ? 's' : ''}. Entre em contato para escolher um plano.`
        });
        setLoading(false);
        setInitialCheckDone(true);
        return;
      }
    }

    // Verificar se assinatura foi cancelada
    if (clubInfo.subscription_status === 'cancelled') {
      setAccessInfo({
        hasAccess: false,
        subscriptionStatus: clubInfo.subscription_status,
        paymentStatus: clubInfo.payment_status,
        planName: clubInfo.master_plans?.name,
        errorMessage: 'Assinatura cancelada'
      });
      setLoading(false);
      setInitialCheckDone(true);
      return;
    }



    // Verificar se pagamento está em atraso (mas ainda no período de tolerância)
    if (clubInfo.payment_status === 'overdue') {
      setAccessInfo({
        hasAccess: true,
        subscriptionStatus: clubInfo.subscription_status,
        paymentStatus: clubInfo.payment_status,
        planName: clubInfo.master_plans?.name,
        warningMessage: 'Pagamento em atraso. Regularize para evitar suspensão do acesso.'
      });
      setLoading(false);
      setInitialCheckDone(true);
      return;
    }

    // Se chegou até aqui, tem acesso liberado
    setAccessInfo({
      hasAccess: true,
      subscriptionStatus: clubInfo.subscription_status,
      paymentStatus: clubInfo.payment_status,
      planName: clubInfo.master_plans?.name
    });
    setLoading(false);
    setInitialCheckDone(true);
  };

  useEffect(() => {
    // Só executar quando UserContext terminar de carregar E houver dados do usuário
    if (!userLoading && (user || initialCheckDone)) {
      checkAccess();
    }
  }, [userLoading, user?.id, user?.club_info?.subscription_status, user?.club_info?.is_trial, user?.club_info?.trial_end_date]);

  // Recheck a cada 5 minutos
  useEffect(() => {
    if (!user?.id || !initialCheckDone) return;
    
    const interval = setInterval(() => {
      if (!userLoading && user) {
        checkAccess();
      }
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, [user?.id, initialCheckDone]);

  // Mostrar loading enquanto:
  // 1. UserContext está carregando OU
  // 2. UserContext terminou mas ainda não há dados do usuário E ainda não fez verificação inicial
  if (userLoading || (!user && !initialCheckDone)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-team-blue to-team-blue/60 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">
            {userLoading ? 'Carregando dados do usuário...' : 'Verificando acesso...'}
          </h2>
          <p className="text-gray-600">Aguarde um momento</p>
        </div>
      </div>
    );
  }

  // Só mostrar tela de bloqueio se já fez a verificação inicial
  if (!accessInfo.hasAccess && initialCheckDone) {
    return <AccessBlockedScreen accessInfo={accessInfo} />;
  }

  return (
    <ClubAccessContext.Provider value={{ accessInfo, loading, refreshAccess: checkAccess }}>
      <AccessWarningBanner accessInfo={accessInfo} />
      <PaymentWarningBanner />
      {children}
    </ClubAccessContext.Provider>
  );
}

export function useClubAccess() {
  return useContext(ClubAccessContext);
}