// API para processar lead magnets e adicionar à lista de email

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { email, leadMagnet, source = 'blog' } = req.body;

  if (!email || !leadMagnet) {
    return res.status(400).json({ message: 'Email e lead magnet são obrigatórios' });
  }

  try {
    // 1. Adicionar à lista de email (exemplo com ConvertKit/Mailchimp)
    await addToEmailList(email, leadMagnet, source);

    // 2. Salvar no banco de dados para tracking
    await saveLeadMagnetDownload({
      email,
      leadMagnet,
      source,
      timestamp: new Date().toISOString(),
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
    });

    // 3. Enviar email com o download
    await sendDownloadEmail(email, leadMagnet);

    res.status(200).json({ 
      success: true, 
      message: 'Lead magnet processado com sucesso' 
    });

  } catch (error) {
    console.error('Erro ao processar lead magnet:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erro interno do servidor' 
    });
  }
}

// Adicionar à lista de email
async function addToEmailList(email, leadMagnet, source) {
  // Exemplo com ConvertKit
  const CONVERTKIT_API_KEY = process.env.CONVERTKIT_API_KEY;
  const CONVERTKIT_FORM_ID = process.env.CONVERTKIT_FORM_ID;

  if (!CONVERTKIT_API_KEY || !CONVERTKIT_FORM_ID) {
    console.warn('ConvertKit não configurado');
    return;
  }

  const response = await fetch(`https://api.convertkit.com/v3/forms/${CONVERTKIT_FORM_ID}/subscribe`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      api_key: CONVERTKIT_API_KEY,
      email,
      tags: [leadMagnet, source],
      fields: {
        lead_magnet: leadMagnet,
        source: source,
        signup_date: new Date().toISOString()
      }
    })
  });

  if (!response.ok) {
    throw new Error('Erro ao adicionar à lista de email');
  }
}

// Salvar download no banco
async function saveLeadMagnetDownload(data) {
  // Implementar com Supabase ou outro banco
  const { createClient } = require('@supabase/supabase-js');
  
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  const { error } = await supabase
    .from('lead_magnet_downloads')
    .insert([data]);

  if (error) {
    console.error('Erro ao salvar no banco:', error);
    throw error;
  }
}

// Enviar email com download
async function sendDownloadEmail(email, leadMagnet) {
  const emailTemplates = {
    'planilha-fluxo-caixa': {
      subject: '📊 Sua Planilha de Fluxo de Caixa está pronta!',
      downloadUrl: '/downloads/planilha-fluxo-caixa.xlsx',
      template: 'fluxo-caixa'
    },
    'template-prontuario-medico': {
      subject: '🏥 Template de Prontuário Médico - Download',
      downloadUrl: '/downloads/template-prontuario-medico.pdf',
      template: 'prontuario-medico'
    },
    'checklist-convocacao': {
      subject: '✅ Checklist de Convocação Completo',
      downloadUrl: '/downloads/checklist-convocacao.pdf',
      template: 'convocacao'
    }
  };

  const emailData = emailTemplates[leadMagnet];
  if (!emailData) {
    console.warn(`Template não encontrado para: ${leadMagnet}`);
    return;
  }

  // Implementar envio de email (exemplo com SendGrid, Resend, etc.)
  // Por enquanto apenas log
  console.log(`Enviando email para ${email}:`, emailData);
}

// Função para criar tabela no Supabase (executar uma vez)
export async function createLeadMagnetTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS lead_magnet_downloads (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      email VARCHAR(255) NOT NULL,
      lead_magnet VARCHAR(100) NOT NULL,
      source VARCHAR(50) DEFAULT 'blog',
      timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      ip VARCHAR(45),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_lead_magnet_email ON lead_magnet_downloads(email);
    CREATE INDEX IF NOT EXISTS idx_lead_magnet_type ON lead_magnet_downloads(lead_magnet);
    CREATE INDEX IF NOT EXISTS idx_lead_magnet_date ON lead_magnet_downloads(timestamp);
  `;
  
  return sql;
}