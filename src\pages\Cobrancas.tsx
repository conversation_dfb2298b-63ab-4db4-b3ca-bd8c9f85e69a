import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useCurrentClubId } from '@/context/ClubContext';
import { useClubInfoStore } from '@/store/useClubInfoStore';
import { Plus, Filter, Download, CreditCard, Users, Building } from 'lucide-react';
import { 
  getBillingTransactions, 
  getClients, 
  BillingTransaction, 
  Client 
} from '@/api/billing';
import { CreateBillingModal } from '@/components/billing/CreateBillingModal';
import { BillingTransactionsTable } from '@/components/billing/BillingTransactionsTable';
import { ClientsManager } from '@/components/billing/ClientsManager';
import { ModuleGuard } from "@/components/guards/ModuleGuard";

export default function Cobrancas() {
  return (
    <ModuleGuard module="billing">
      <CobrancasContent />
    </ModuleGuard>
  );
}

function CobrancasContent() {
  const [activeTab, setActiveTab] = useState('players');
  const [transactions, setTransactions] = useState<BillingTransaction[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<BillingTransaction[]>([]);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  
  // Filtros
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const clubId = useCurrentClubId();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const { toast } = useToast();

  useEffect(() => {
    fetchClubInfo(clubId);
    loadData();
  }, [clubId]);

  useEffect(() => {
    applyFilters();
  }, [transactions, statusFilter, typeFilter, startDate, endDate, searchTerm, activeTab]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [transactionsData, clientsData] = await Promise.all([
        getBillingTransactions(clubId),
        getClients(clubId)
      ]);
      
      setTransactions(transactionsData);
      setClients(clientsData);
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os dados',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = transactions;

    // Filtrar por aba ativa
    if (activeTab === 'players') {
      filtered = filtered.filter(t => t.entity_type === 'player' || t.entity_type === 'collaborator');
    } else {
      filtered = filtered.filter(t => t.entity_type === 'client');
    }

    // Filtrar por status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(t => t.status === statusFilter);
    }

    // Filtrar por tipo
    if (typeFilter !== 'all') {
      filtered = filtered.filter(t => t.type === typeFilter);
    }

    // Filtrar por data
    if (startDate) {
      filtered = filtered.filter(t => new Date(t.created_at) >= new Date(startDate));
    }
    if (endDate) {
      filtered = filtered.filter(t => new Date(t.created_at) <= new Date(endDate));
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(t => 
        t.entity_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        t.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredTransactions(filtered);
  };

  const clearFilters = () => {
    setStatusFilter('all');
    setTypeFilter('all');
    setStartDate('');
    setEndDate('');
    setSearchTerm('');
  };

  const getStats = () => {
    const currentTabTransactions = activeTab === 'players' 
      ? transactions.filter(t => t.entity_type === 'player' || t.entity_type === 'collaborator')
      : transactions.filter(t => t.entity_type === 'client');

    const pending = currentTabTransactions.filter(t => t.status === 'pendente');
    const paid = currentTabTransactions.filter(t => t.status === 'pago');
    const cobrancas = currentTabTransactions.filter(t => t.type === 'recebe');
    const recebimentos = currentTabTransactions.filter(t => t.type === 'paga');

    const pendingAmount = pending.reduce((sum, t) => sum + t.amount, 0);
    const paidAmount = paid.reduce((sum, t) => sum + t.amount, 0);

    return {
      total: currentTabTransactions.length,
      pending: pending.length,
      paid: paid.length,
      cobrancas: cobrancas.length,
      recebimentos: recebimentos.length,
      pendingAmount,
      paidAmount
    };
  };

  const stats = getStats();

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando cobranças...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Cobranças</h1>
        <p className="text-muted-foreground">
          Gerencie cobranças e recebimentos via PIX
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="players" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Jogadores & Colaboradores
          </TabsTrigger>
          <TabsTrigger value="clients" className="flex items-center gap-2">
            <Building className="w-4 h-4" />
            Clientes
          </TabsTrigger>
        </TabsList>

        <TabsContent value="players" className="space-y-6">
          {/* Cards de estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">
                  transações
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                <div className="h-4 w-4 rounded-full bg-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.pending}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats.pendingAmount)}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pagas</CardTitle>
                <div className="h-4 w-4 rounded-full bg-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.paid}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats.paidAmount)}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recebe/Paga</CardTitle>
                <div className="flex gap-1">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  <div className="h-2 w-2 rounded-full bg-red-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.cobrancas}/{stats.recebimentos}</div>
                <p className="text-xs text-muted-foreground">
                  recebe/paga
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Filtros e ações */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <CardTitle>Transações</CardTitle>
                <Button onClick={() => setCreateModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Nova Transação
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Filtros */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <Input
                  placeholder="Buscar por nome ou descrição..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="md:col-span-2"
                />
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os status</SelectItem>
                    <SelectItem value="pendente">Pendente</SelectItem>
                    <SelectItem value="pago">Pago</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os tipos</SelectItem>
                    <SelectItem value="recebe">Clube Recebe</SelectItem>
                    <SelectItem value="paga">Clube Paga</SelectItem>
                  </SelectContent>
                </Select>
                
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  placeholder="Data inicial"
                />
                
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  placeholder="Data final"
                />
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={clearFilters}>
                  Limpar filtros
                </Button>
              </div>

              {/* Tabela */}
              <BillingTransactionsTable
                transactions={filteredTransactions}
                onUpdate={loadData}
                clubName={clubInfo?.name || 'Clube'}
                clubId={clubId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clients" className="space-y-6">
          {/* Cards de estatísticas para clientes */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Clientes</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clients.length}</div>
                <p className="text-xs text-muted-foreground">
                  cadastrados
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">
                  transações
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                <div className="h-4 w-4 rounded-full bg-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.pending}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats.pendingAmount)}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pagas</CardTitle>
                <div className="h-4 w-4 rounded-full bg-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.paid}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats.paidAmount)}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Gerenciamento de clientes */}
          <Card>
            <CardContent className="pt-6">
              <ClientsManager
                clients={clients}
                onUpdate={loadData}
                clubId={clubId}
              />
            </CardContent>
          </Card>

          {/* Transações de clientes */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <CardTitle>Transações de Clientes</CardTitle>
                <Button onClick={() => setCreateModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Nova Transação
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Filtros */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <Input
                  placeholder="Buscar por nome ou descrição..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="md:col-span-2"
                />
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os status</SelectItem>
                    <SelectItem value="pendente">Pendente</SelectItem>
                    <SelectItem value="pago">Pago</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os tipos</SelectItem>
                    <SelectItem value="recebe">Clube Recebe</SelectItem>
                    <SelectItem value="paga">Clube Paga</SelectItem>
                  </SelectContent>
                </Select>
                
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  placeholder="Data inicial"
                />
                
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  placeholder="Data final"
                />
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={clearFilters}>
                  Limpar filtros
                </Button>
              </div>

              {/* Tabela */}
              <BillingTransactionsTable
                transactions={filteredTransactions}
                onUpdate={loadData}
                clubName={clubInfo?.name || 'Clube'}
                clubId={clubId}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de criação */}
      <CreateBillingModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={loadData}
        clubName={clubInfo?.name || 'Clube'}
        clubPixKey={clubInfo?.pix_key}
        clients={clients}
        tabType={activeTab as 'players' | 'clients'}
      />
    </div>
  );
}