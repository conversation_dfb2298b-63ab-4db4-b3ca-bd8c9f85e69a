import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { validateUserClubAccessBySlug } from '@/api/clubAccess';

/**
 * Hook para monitoramento contínuo de segurança
 * Detecta tentativas de acesso não autorizado
 */
export function useSecurityMonitor() {
  const { clubSlug } = useParams<{ clubSlug: string }>();
  const navigate = useNavigate();

  useEffect(() => {
    if (!clubSlug) return;

    let isMounted = true;
    let violationCount = 0;
    const MAX_VIOLATIONS = 3;

    async function monitorAccess() {
      try {
        const { hasAccess, notLoggedIn } = await validateUserClubAccessBySlug(clubSlug, true);
        
        // Se não está logado, não fazer nada
        if (notLoggedIn) {
          return;
        }
        
        if (!hasAccess && isMounted) {
          violationCount++;
          
          console.error(`VIOLAÇÃO DE SEGURANÇA #${violationCount}: Acesso não autorizado ao clube '${clubSlug}'`);
          
          // Após múltiplas violações, bloquear completamente
          if (violationCount >= MAX_VIOLATIONS) {
            console.error(`BLOQUEIO DE SEGURANÇA: ${MAX_VIOLATIONS} violações detectadas. Redirecionando para login.`);
            localStorage.clear();
            sessionStorage.clear();
            navigate('/login', { replace: true });
            return;
          }
          
          // Primeira violação: mostrar aviso
          navigate('/unauthorized', { 
            replace: true, 
            state: { attemptedClub: clubSlug, violations: violationCount }
          });
        } else {
          // Reset contador se o acesso for válido
          violationCount = 0;
        }
      } catch (error) {
        console.error("Erro no monitoramento de segurança:", error);
      }
    }

    // Aguardar 5 segundos antes do primeiro monitoramento (para permitir login)
    const initialTimeout = setTimeout(monitorAccess, 5000);
    
    // Monitorar a cada 60 segundos (reduzido a frequência)
    const interval = setInterval(monitorAccess, 60 * 1000);

    return () => {
      isMounted = false;
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [clubSlug, navigate]);
}

/**
 * Hook para detectar mudanças suspeitas na URL
 */
export function useURLTamperingDetection() {
  const { clubSlug } = useParams<{ clubSlug: string }>();
  const navigate = useNavigate();

  useEffect(() => {
    // Detectar mudanças manuais na URL
    const handlePopState = async () => {
      const currentSlug = window.location.pathname.split('/')[1];
      
      if (currentSlug && currentSlug !== clubSlug) {
        console.warn(`POSSÍVEL MANIPULAÇÃO DE URL: Mudança de '${clubSlug}' para '${currentSlug}'`);
        
        // Validar acesso ao novo slug
        try {
          const { hasAccess, notLoggedIn } = await validateUserClubAccessBySlug(currentSlug, true);
          
          // Se não está logado, não fazer nada
          if (notLoggedIn) {
            return;
          }
          
          if (!hasAccess) {
            console.error(`TENTATIVA DE ACESSO BLOQUEADA: Usuário não tem acesso ao clube '${currentSlug}'`);
            navigate('/unauthorized', { 
              replace: true, 
              state: { attemptedClub: currentSlug, reason: 'URL manipulation detected' }
            });
          }
        } catch (error) {
          console.error("Erro na validação de URL:", error);
          navigate('/login', { replace: true });
        }
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [clubSlug, navigate]);
}