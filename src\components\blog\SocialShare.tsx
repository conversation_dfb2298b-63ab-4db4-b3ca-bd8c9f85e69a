import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Share2, Facebook, Twitter, Linkedin, Link, MessageCircle, Check } from 'lucide-react';
import { useBlogAnalytics } from '@/utils/analytics/blogTracking';

interface SocialShareProps {
  title: string;
  url: string;
  description?: string;
  hashtags?: string[];
  className?: string;
}

export function SocialShare({
  title,
  url,
  description = '',
  hashtags = [],
  className = ''
}: SocialShareProps) {
  const [copied, setCopied] = useState(false);
  const { trackSocialShare } = useBlogAnalytics();
  
  const fullUrl = url.startsWith('http') ? url : `https://gamedaynexus.com${url}`;
  const encodedTitle = encodeURIComponent(title);
  const encodedUrl = encodeURIComponent(fullUrl);
  const encodedDescription = encodeURIComponent(description);
  const hashtagString = hashtags.map(tag => `#${tag}`).join(' ');

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}&hashtags=${hashtags.join(',')}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDescription}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`
  };

  const handleShare = (platform: keyof typeof shareLinks) => {
    // Track social share
    trackSocialShare(platform, title, fullUrl);
    
    // Open share window
    const shareUrl = shareLinks[platform];
    window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      trackSocialShare('copy_link', title, fullUrl);
      
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Erro ao copiar link:', error);
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url: fullUrl
        });
        trackSocialShare('native_share', title, fullUrl);
      } catch (error) {
        console.error('Erro no compartilhamento nativo:', error);
      }
    }
  };

  return (
    <Card className={`${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Share2 className="h-5 w-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Compartilhar este post</h3>
        </div>
        
        <div className="flex flex-wrap gap-3">
          {/* Facebook */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleShare('facebook')}
            className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
          >
            <Facebook className="h-4 w-4 text-blue-600" />
            <span className="hidden sm:inline">Facebook</span>
          </Button>

          {/* Twitter */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleShare('twitter')}
            className="flex items-center gap-2 hover:bg-sky-50 hover:border-sky-300"
          >
            <Twitter className="h-4 w-4 text-sky-500" />
            <span className="hidden sm:inline">Twitter</span>
          </Button>

          {/* LinkedIn */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleShare('linkedin')}
            className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
          >
            <Linkedin className="h-4 w-4 text-blue-700" />
            <span className="hidden sm:inline">LinkedIn</span>
          </Button>

          {/* WhatsApp */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleShare('whatsapp')}
            className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300"
          >
            <MessageCircle className="h-4 w-4 text-green-600" />
            <span className="hidden sm:inline">WhatsApp</span>
          </Button>

          {/* Copy Link */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyLink}
            className={`flex items-center gap-2 transition-colors ${
              copied 
                ? 'bg-green-50 border-green-300 text-green-700' 
                : 'hover:bg-gray-50'
            }`}
          >
            {copied ? (
              <Check className="h-4 w-4" />
            ) : (
              <Link className="h-4 w-4" />
            )}
            <span className="hidden sm:inline">
              {copied ? 'Copiado!' : 'Copiar Link'}
            </span>
          </Button>

          {/* Native Share (mobile) */}
          {navigator.share && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleNativeShare}
              className="flex items-center gap-2 hover:bg-gray-50 sm:hidden"
            >
              <Share2 className="h-4 w-4" />
              Compartilhar
            </Button>
          )}
        </div>

        {/* Hashtags */}
        {hashtags.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-2">Hashtags sugeridas:</p>
            <p className="text-sm text-blue-600 font-mono">
              {hashtagString}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Componente compacto para usar inline
export function InlineSocialShare({
  title,
  url,
  size = 'sm'
}: {
  title: string;
  url: string;
  size?: 'sm' | 'md';
}) {
  const { trackSocialShare } = useBlogAnalytics();
  const fullUrl = url.startsWith('http') ? url : `https://gamedaynexus.com${url}`;
  
  const iconSize = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5';
  const buttonSize = size === 'sm' ? 'sm' : 'default';

  const handleShare = (platform: string, shareUrl: string) => {
    trackSocialShare(platform, title, fullUrl);
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">Compartilhar:</span>
      
      <Button
        variant="ghost"
        size={buttonSize}
        onClick={() => handleShare('facebook', `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}`)}
        className="p-2"
      >
        <Facebook className={`${iconSize} text-blue-600`} />
      </Button>
      
      <Button
        variant="ghost"
        size={buttonSize}
        onClick={() => handleShare('twitter', `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(fullUrl)}`)}
        className="p-2"
      >
        <Twitter className={`${iconSize} text-sky-500`} />
      </Button>
      
      <Button
        variant="ghost"
        size={buttonSize}
        onClick={() => handleShare('linkedin', `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(title)}`)}
        className="p-2"
      >
        <Linkedin className={`${iconSize} text-blue-700`} />
      </Button>
    </div>
  );
}

// Hook para gerar URLs de compartilhamento
export function useSocialShare(title: string, url: string, description?: string) {
  const fullUrl = url.startsWith('http') ? url : `https://gamedaynexus.com${url}`;
  
  return {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}&quote=${encodeURIComponent(title)}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(fullUrl)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(description || '')}`,
    whatsapp: `https://wa.me/?text=${encodeURIComponent(title)}%20${encodeURIComponent(fullUrl)}`,
    email: `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(description || '')}%0A%0A${encodeURIComponent(fullUrl)}`
  };
}