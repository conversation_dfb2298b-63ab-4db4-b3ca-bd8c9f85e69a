import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getCashFlowByDateRange, CashFlowEntry } from "@/api/financialReports";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface CashFlowReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any; // Adicionado para resolver o erro de tipagem
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
      getWidth: () => number;
      getHeight: () => number;
    };
    scaleFactor: number;
    pages: number[];
    events: any;
    getEncryptor: (objectId: number) => (data: string) => string;
  };
}

export function CashFlowReportDialog({ 
  open, 
  onOpenChange
}: CashFlowReportDialogProps) {
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Erro",
        description: "Por favor, selecione as datas de início e fim.",
        variant: "destructive",
      });
      return;
    }

    if (startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);
      
      // Get cash flow data
      // Ajustar as datas para evitar problemas de fuso horário. Utilizar
      // strings ISO completas para preservar o offset local ao consultar o
      // Supabase, evitando que registros do dia anterior apareçam no relatório
      // devido a conversões de fuso horário.
      const normalizedStart = new Date(startDate);
      normalizedStart.setHours(0, 0, 0, 0);
      const normalizedEnd = new Date(endDate);
      normalizedEnd.setHours(23, 59, 59, 999);

      const startDateStr = normalizedStart.toISOString();
      const endDateStr = normalizedEnd.toISOString();
      const cashFlowEntries = await getCashFlowByDateRange(clubId, startDateStr, endDateStr);

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;
      
      // Configurações de estilo
      const pageWidth = doc.internal.pageSize.width;
      const margin = 20;
      const headerY = 25; // Aumentado para dar espaço para a logo
      
      // Cores do tema do clube
      const primaryColor = clubInfo.primary_color || '#3b82f6'; // Azul padrão se não houver cor definida
      const secondaryColor = clubInfo.secondary_color || '#1d4ed8';
      
      // Adicionar logo
      let currentY = 20; // Aumentado para dar mais espaço no topo
      if (clubInfo.logo_url) {
        try {
          const logoResponse = await fetch(clubInfo.logo_url);
          const logoBlob = await logoResponse.blob();
          const logoUrl = URL.createObjectURL(logoBlob);
          
          // Adicionar logo no canto superior esquerdo
          const img = new Image();
          img.src = logoUrl;
          
          // Esperar a imagem carregar
          await new Promise((resolve) => {
            img.onload = resolve;
          });
          
          // Definir tamanho máximo para a logo (25x25mm)
          const maxLogoSize = 25; // Reduzido para caber melhor no cabeçalho
          let logoWidth = img.width;
          let logoHeight = img.height;
          
          // Redimensionar mantendo a proporção
          if (logoWidth > maxLogoSize || logoHeight > maxLogoSize) {
            const ratio = Math.min(maxLogoSize / logoWidth, maxLogoSize / logoHeight);
            logoWidth *= ratio;
            logoHeight *= ratio;
          }
          
          // Adicionar a logo
          doc.addImage(img, 'PNG', margin, currentY, logoWidth, logoHeight);
          
          // Adicionar título do relatório ao lado da logo
          doc.setFontSize(18);
          doc.setTextColor(primaryColor);
          doc.text("Relatório de Fluxo de Caixa", margin + logoWidth + 10, currentY + (logoHeight / 2) + 5);
          
          // Atualizar a posição Y atual para o próximo conteúdo
          currentY += logoHeight + 10; // Aumentado o espaçamento após a logo
          
        } catch (error) {
          console.error("Erro ao carregar o logo:", error);
        }
      }
      
      // Se não houver logo, adicionar o título centralizado
      if (!clubInfo.logo_url) {
        doc.setFontSize(18);
        doc.setTextColor(primaryColor);
        doc.text("Relatório de Fluxo de Caixa", pageWidth / 2, currentY, { align: "center" });
        currentY += 15; // Espaço após o título
      }
      
      // Adicionar informações do clube e período
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0); // Preto para o texto normal
      
      // Linha horizontal separadora
      doc.setDrawColor(200, 200, 200); // Cinza claro
      doc.setLineWidth(0.2);
      doc.line(margin, currentY - 5, pageWidth - margin, currentY - 5);
      
      // Informações do clube e período lado a lado
      const infoY = currentY + 5;
      doc.text(`Clube: ${clubInfo.name || 'Não informado'}`, margin, infoY);
      
      // Informações de período alinhadas à direita
      const periodText = `Período: ${format(startDate, 'dd/MM/yyyy')} a ${format(endDate, 'dd/MM/yyyy')}`;
      const periodTextWidth = doc.getTextWidth(periodText);
      doc.text(periodText, pageWidth - margin - periodTextWidth, infoY);
      
      
      // Linha horizontal inferior
      doc.line(margin, infoY + 10, pageWidth - margin, infoY + 10);
      
      currentY = infoY + 20; // Aumentado o espaçamento antes do conteúdo principal
      
      // Cor de fundo para cabeçalhos
      const headerFillColor = primaryColor;
      const headerTextColor = '#ffffff'; // Texto branco para melhor contraste
      
      // Configurar estilos da tabela
      const tableConfig = {
        headStyles: {
          fillColor: headerFillColor,
          textColor: headerTextColor,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fillColor: [255, 255, 255],
          textColor: [0, 0, 0],
          lineColor: [0, 0, 0],
          lineWidth: 0.1
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245]
        },
        margin: { top: currentY + 5 },
        startY: currentY + 5
      };
      
      // Se não houver logo, adicionar informações do clube como texto
      if (!clubInfo.logo_url) {
        doc.setFontSize(12);
        doc.text(clubInfo.name, pageWidth / 2, 25, { align: "center" });
        if (clubInfo.address) {
          doc.setFontSize(10);
          doc.text(clubInfo.address, pageWidth / 2, 30, { align: "center" });
        }
        if (clubInfo.phone) {
          doc.text(`Telefone: ${clubInfo.phone}`, pageWidth / 2, 35, { align: "center" });
        }
      }

      // Add period and generation date
      doc.setFontSize(10);
      const startY = clubInfo.logo_url ? 50 : 45; // Ajusta a posição Y baseado na presença do logo
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, startY + 5);

      // Define a posição Y atual para o conteúdo principal
      currentY = startY + 15;

      // Calcular resumo
      const totalReceitas = cashFlowEntries
        .filter(entry => entry.type === 'receita')
        .reduce((sum, entry) => sum + entry.amount, 0);
      
      const totalDespesas = cashFlowEntries
        .filter(entry => entry.type === 'despesa')
        .reduce((sum, entry) => sum + Math.abs(entry.amount), 0);

      const saldoFinal = cashFlowEntries.length > 0 ? cashFlowEntries[cashFlowEntries.length - 1].running_balance : 0;
      const saldoPeriodo = totalReceitas - totalDespesas;

      // Adicionar resumo em um box estilizado
      const summaryY = currentY;
      const summaryHeight = 40;
      
      // Fundo do resumo
      doc.setFillColor(245, 245, 245);
      doc.rect(margin, summaryY, pageWidth - (2 * margin), summaryHeight, 'F');
      
      // Borda do resumo
      doc.setDrawColor(200, 200, 200);
      doc.rect(margin, summaryY, pageWidth - (2 * margin), summaryHeight);
      
      // Título do resumo
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text("Resumo do Período", margin + 10, summaryY + 8);
      
      // Conteúdo do resumo
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Total de Receitas:`, margin + 15, summaryY + 18);
      doc.text(`R$ ${totalReceitas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, pageWidth - margin - 15, summaryY + 18, { align: 'right' });
      
      doc.text(`Total de Despesas:`, margin + 15, summaryY + 28);
      doc.text(`R$ ${totalDespesas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, pageWidth - margin - 15, summaryY + 28, { align: 'right' });
      
      doc.setFont('helvetica', 'bold');
      doc.text(`Saldo do Período:`, margin + 15, summaryY + 38);
      
      // Definir cor do texto baseada no saldo
      doc.setTextColor(saldoPeriodo >= 0 ? 0 : 255, saldoPeriodo >= 0 ? 128 : 0, 0);
      doc.text(`R$ ${saldoPeriodo.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 
               pageWidth - margin - 15, summaryY + 38, { align: 'right' });
      
      // Resetar cor do texto para preto
      doc.setTextColor(0, 0, 0);
      
      // Atualizar posição Y atual
      currentY += summaryHeight + 10;

      // Preparar dados da tabela
      const tableData = cashFlowEntries.map(entry => ({
        date: format(new Date(entry.date), "dd/MM/yyyy", { locale: ptBR }),
        type: entry.type === 'receita' ? 'Receita' : 'Despesa',
        description: entry.description,
        category: entry.category,
        income: entry.type === 'receita' ? entry.amount : 0,
        expense: entry.type === 'despesa' ? entry.amount : 0,
        balance: entry.running_balance
      }));
      
      // Converter para o formato esperado pela tabela
      const tableRows = tableData.map(entry => [
        entry.date,
        entry.type,
        entry.description,
        entry.category,
        entry.income ? `R$ ${entry.income.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : '',
        entry.expense ? `R$ ${entry.expense.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : '',
        `R$ ${entry.balance.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
      ]);

      // Adicionar tabela com estilos personalizados
      autoTable(doc, {
        head: [
          [
            { content: "Data", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold' } },
            { content: "Tipo", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold' } },
            { content: "Descrição", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold' } },
            { content: "Categoria", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold' } },
            { content: "Receita", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold', halign: 'right' } },
            { content: "Despesa", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold', halign: 'right' } },
            { content: "Saldo", styles: { fillColor: primaryColor, textColor: [255, 255, 255], fontStyle: 'bold', halign: 'right' } }
          ]
        ],
        body: tableRows,
        startY: currentY,
        styles: {
          fontSize: 8,
          cellPadding: 3,
          lineWidth: 0.1,
          lineColor: [200, 200, 200],
          font: 'helvetica',
          fontStyle: 'normal',
          overflow: 'linebreak',
          cellWidth: 'wrap',
          textColor: [0, 0, 0],
          fillColor: [255, 255, 255],
        },
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          lineWidth: 0.1,
          lineColor: [200, 200, 200],
          halign: 'left'
        },
        bodyStyles: {
          fillColor: [255, 255, 255],
          textColor: [0, 0, 0],
          lineWidth: 0.1,
          lineColor: [220, 220, 220],
          halign: 'left'
        },
        alternateRowStyles: {
          fillColor: [248, 248, 248]
        },
        columnStyles: {
          0: { cellWidth: 20, halign: 'left' }, // Data
          1: { cellWidth: 20, halign: 'left' }, // Tipo
          2: { cellWidth: 'auto', halign: 'left' }, // Descrição
          3: { cellWidth: 30, halign: 'left' }, // Categoria
          4: { cellWidth: 25, halign: 'right' }, // Receita
          5: { cellWidth: 25, halign: 'right' }, // Despesa
          6: { cellWidth: 30, halign: 'right', fontStyle: 'bold' } // Saldo
        },
        margin: { left: margin, right: margin },
        tableWidth: 'auto',
        tableLineColor: [220, 220, 220],
        tableLineWidth: 0.1,
        theme: 'grid',
        willDrawCell: (data: {
          section: 'head' | 'body' | 'foot';
          column: { index: number };
          cell: { raw: any; styles: { textColor?: string | number[] } };
          row: { index: number };
        }) => {
          // Aplicar cores condicionais para valores monetários
          if (data.section === 'body') {
            if (data.column.index === 4 && data.cell.raw) { // Coluna de Receita
              doc.setTextColor(0, 128, 0); // Verde
            } else if (data.column.index === 5 && data.cell.raw) { // Coluna de Despesa
              doc.setTextColor(255, 0, 0); // Vermelho
            } else if (data.column.index === 6) { // Coluna de Saldo
              const value = parseFloat(data.cell.raw.toString().replace(/[^0-9,-]/g, '').replace(',', '.'));
              doc.setTextColor(value >= 0 ? 0 : 255, value >= 0 ? 128 : 0, 0);
            } else {
              doc.setTextColor(0, 0, 0); // Preto para outras células
            }
          }
        },
        didDrawCell: (data: {
          section: 'head' | 'body' | 'foot';
          column: { index: number };
          cell: { raw: any; styles: { textColor?: string | number[] } };
          row: { index: number };
        }) => {
          // Resetar a cor do texto após desenhar cada célula
          doc.setTextColor(0, 0, 0);
        }
      });
      
      // Atualizar a posição Y atual após a tabela
      currentY = (doc as any).lastAutoTable.finalY + 10;

      // Adicionar rodapé
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setTextColor(150);
        
        // Linha de rodapé
        doc.setDrawColor(primaryColor);
        doc.setLineWidth(0.5);
        doc.line(
          margin,
          doc.internal.pageSize.height - 15,
          doc.internal.pageSize.width - margin,
          doc.internal.pageSize.height - 15
        );
        
        // Texto do rodapé
        doc.text(
          ` ${new Date().getFullYear()} ${clubInfo.name || 'Game Day Nexus'} - Página ${i} de ${pageCount}`,
          doc.internal.pageSize.width / 2,
          doc.internal.pageSize.height - 10,
          { align: "center" }
        );
      }
      // Save the PDF
      const reportTitle = `Fluxo_de_Caixa_${format(startDate, "dd-MM-yyyy")}_a_${format(endDate, "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);
      
      toast({
        title: "Relatório gerado",
        description: "O relatório de fluxo de caixa foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório de Fluxo de Caixa</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label htmlFor="endDate">Data de Fim</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
