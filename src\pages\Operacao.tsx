import { useState } from 'react';
import { OperationStaffList } from '@/components/operations/OperationStaffList';
import { OperationStaffForm } from '@/components/operations/OperationStaffForm';
import { GameOperationStaff } from '@/api/gameOperations';
import { ModuleGuard } from "@/components/guards/ModuleGuard";

export default function Operacao() {
  return (
    <ModuleGuard module="callups">
      <OperacaoContent />
    </ModuleGuard>
  );
}

function OperacaoContent() {
  const [formOpen, setFormOpen] = useState(false);
  const [selected, setSelected] = useState<GameOperationStaff | undefined>(undefined);
  const [refresh, setRefresh] = useState(false);

  const handleAdd = () => {
    setSelected(undefined);
    setFormOpen(true);
  };

  const handleEdit = (s: GameOperationStaff) => {
    setSelected(s);
    setFormOpen(true);
  };

  const handleSuccess = () => setRefresh(!refresh);

  return (
    <div className="space-y-4 sm:space-y-6">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Operação de Jogo</h1>
        <p className="text-sm sm:text-base text-muted-foreground">Gerencie membros e funções da operação de jogo</p>
      </div>

      <OperationStaffList onAdd={handleAdd} onEdit={handleEdit} refreshSignal={refresh} />

      <OperationStaffForm open={formOpen} onOpenChange={setFormOpen} staff={selected} onSuccess={handleSuccess} />
    </div>
  );
}
