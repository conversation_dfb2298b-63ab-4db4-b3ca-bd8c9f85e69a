// API route para gerar sitemap.xml dinamicamente

import { generateAllSitemaps } from '../src/utils/seo/sitemapGenerator.js';

// Mock data - substituir por dados reais do banco/CMS
const blogPosts = [
  {
    slug: 'gestao-clubes-futebol-guia-completo',
    title: 'Gestão de Clubes de Futebol: Guia Completo 2025',
    publishedAt: '2025-08-10T08:00:00-03:00',
    updatedAt: '2025-08-10T08:00:00-03:00',
    featuredImage: '/images/blog/gestao-clubes-featured.jpg',
    category: 'Gestão Esportiva'
  },
  {
    slug: 'fluxo-caixa-clubes-template-gratuito',
    title: 'Fluxo de Caixa para Clubes: Template Gratuito',
    publishedAt: '2025-08-11T08:00:00-03:00',
    updatedAt: '2025-08-11T08:00:00-03:00',
    featuredImage: '/images/blog/fluxo-caixa-featured.jpg',
    category: 'Financeiro'
  },
  {
    slug: 'prontuario-eletronico-atleta-guia-completo',
    title: 'Prontuário Eletrônico do Atleta: Guia Completo',
    publishedAt: '2025-08-12T08:00:00-03:00',
    updatedAt: '2025-08-12T08:00:00-03:00',
    featuredImage: '/images/blog/prontuario-featured.jpg',
    category: 'Médico'
  }
];

const videos = [
  {
    slug: 'como-criar-convocacao-completa',
    title: 'Como Criar uma Convocação Completa',
    publishedAt: '2025-08-10T10:00:00-03:00',
    thumbnail: '/images/videos/convocacao-thumb.jpg',
    category: 'Logística'
  },
  {
    slug: 'configurar-mensalidades-pix',
    title: 'Como Configurar Mensalidades com PIX',
    publishedAt: '2025-08-11T10:00:00-03:00',
    thumbnail: '/images/videos/pix-thumb.jpg',
    category: 'Financeiro'
  },
  {
    slug: 'escalacao-tatica-visual',
    title: 'Escalação Tática com Editor Visual',
    publishedAt: '2025-08-12T10:00:00-03:00',
    thumbnail: '/images/videos/escalacao-thumb.jpg',
    category: 'Técnico'
  },
  {
    slug: 'prontuario-eletronico-tutorial',
    title: 'Prontuário Eletrônico - Tutorial Completo',
    publishedAt: '2025-08-13T10:00:00-03:00',
    thumbnail: '/images/videos/prontuario-thumb.jpg',
    category: 'Médico'
  }
];

export default async function handler(req, res) {
  try {
    // Gerar todos os sitemaps
    const sitemaps = await generateAllSitemaps(blogPosts, videos);
    
    // Determinar qual sitemap retornar baseado na query
    const { type } = req.query;
    
    let xmlContent;
    let filename;
    
    switch (type) {
      case 'blog':
        xmlContent = sitemaps['sitemap-blog.xml'];
        filename = 'sitemap-blog.xml';
        break;
      case 'videos':
        xmlContent = sitemaps['sitemap-videos.xml'];
        filename = 'sitemap-videos.xml';
        break;
      case 'pages':
        xmlContent = sitemaps['sitemap-pages.xml'];
        filename = 'sitemap-pages.xml';
        break;
      case 'recursos':
        xmlContent = sitemaps['sitemap-recursos.xml'];
        filename = 'sitemap-recursos.xml';
        break;
      default:
        xmlContent = sitemaps['sitemap.xml'];
        filename = 'sitemap.xml';
    }

    // Set headers para XML
    res.setHeader('Content-Type', 'application/xml');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache por 1 hora
    res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
    
    res.status(200).send(xmlContent);
    
  } catch (error) {
    console.error('Erro ao gerar sitemap:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
}

// Função para buscar posts do banco de dados (implementar conforme necessário)
async function fetchBlogPosts() {
  // Implementar busca no Supabase ou outro banco
  // Por enquanto retorna mock data
  return blogPosts;
}

// Função para buscar vídeos do banco de dados
async function fetchVideos() {
  // Implementar busca no Supabase ou outro banco
  // Por enquanto retorna mock data
  return videos;
}

// Função para regenerar sitemaps (pode ser chamada via webhook ou cron)
export async function regenerateSitemaps() {
  try {
    const posts = await fetchBlogPosts();
    const videoList = await fetchVideos();
    
    const sitemaps = await generateAllSitemaps(posts, videoList);
    
    // Salvar sitemaps em arquivos estáticos (opcional)
    // ou manter apenas em memória/cache
    
    console.log('Sitemaps regenerados com sucesso');
    return sitemaps;
    
  } catch (error) {
    console.error('Erro ao regenerar sitemaps:', error);
    throw error;
  }
}