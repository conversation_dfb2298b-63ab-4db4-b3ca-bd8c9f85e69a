import { useState, useEffect } from 'react';
import { useUser } from '@/context/UserContext';

export interface PaymentStatus {
  type: 'payment_due_soon' | 'payment_overdue' | 'payment_current';
  message: string;
  daysUntilDue?: number;
  daysOverdue?: number;
  nextPaymentDate?: string;
  severity: 'info' | 'warning' | 'error';
  showCard: boolean; // Sempre true para o card
}

export function usePaymentStatus() {
  const { user } = useUser();
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [loading, setLoading] = useState(true);

  const calculatePaymentStatus = () => {
    if (!user?.club_info) {
      setPaymentStatus(null);
      setLoading(false);
      return;
    }

    const clubInfo = user.club_info;

    // Só mostrar para clubes com planos ativos (não trial)
    if (clubInfo.is_trial || clubInfo.subscription_status !== 'active') {
      setPaymentStatus(null);
      setLoading(false);
      return;
    }

    // Verificar se há data de próximo pagamento
    const nextPaymentDate = clubInfo.next_payment_date;
    if (!nextPaymentDate) {
      setPaymentStatus(null);
      setLoading(false);
      return;
    }

    const now = new Date();
    const paymentDate = new Date(nextPaymentDate);
    const diffTime = paymentDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Pagamento em atraso
    if (diffDays < 0) {
      const daysOverdue = Math.abs(diffDays);
      setPaymentStatus({
        type: 'payment_overdue',
        message: `Pagamento em atraso há ${daysOverdue} dia${daysOverdue !== 1 ? 's' : ''}`,
        daysOverdue,
        nextPaymentDate,
        severity: 'error',
        showCard: true
      });
      setLoading(false);
      return;
    }

    // Pagamento vence em breve
    if (diffDays <= 7) {
      setPaymentStatus({
        type: 'payment_due_soon',
        message: diffDays === 0 
          ? 'Pagamento vence hoje' 
          : `Pagamento vence em ${diffDays} dia${diffDays !== 1 ? 's' : ''}`,
        daysUntilDue: diffDays,
        nextPaymentDate,
        severity: diffDays <= 2 ? 'error' : 'warning',
        showCard: true
      });
      setLoading(false);
      return;
    }

    // Pagamento em dia - Card sempre aparece
    setPaymentStatus({
      type: 'payment_current',
      message: 'Pagamento em dia',
      daysUntilDue: diffDays,
      nextPaymentDate,
      severity: 'info',
      showCard: true // Card sempre aparece
    });
    setLoading(false);
  };

  useEffect(() => {
    calculatePaymentStatus();
  }, [user?.club_info?.next_payment_date, user?.club_info?.subscription_status]);

  return {
    paymentStatus,
    loading,
    refreshPaymentStatus: calculatePaymentStatus
  };
}