import { useState } from "react";
import { forcePlayerStatsSync, forcePlayerStatsSyncForPlayer } from "@/utils/forcePlayerStatsSync";
import { toast } from "@/components/ui/use-toast";

export function usePlayerStatsSync() {
  const [isLoading, setIsLoading] = useState(false);

  const syncAllPlayerStats = async (clubId: number) => {
    setIsLoading(true);
    try {
      await forcePlayerStatsSync(clubId);
      toast({
        title: "Sucesso",
        description: "Estatísticas dos jogadores sincronizadas com sucesso!",
      });
    } catch (error) {
      console.error("Erro ao sincronizar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Erro ao sincronizar estatísticas dos jogadores.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const syncPlayerStats = async (clubId: number, playerId: string) => {
    setIsLoading(true);
    try {
      await forcePlayerStatsSyncForPlayer(clubId, playerId);
      toast({
        title: "Sucesso",
        description: "Estatísticas do jogador sincronizadas com sucesso!",
      });
    } catch (error) {
      console.error("Erro ao sincronizar estatísticas do jogador:", error);
      toast({
        title: "Erro",
        description: "Erro ao sincronizar estatísticas do jogador.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    syncAllPlayerStats,
    syncPlayerStats,
    isLoading,
  };
}