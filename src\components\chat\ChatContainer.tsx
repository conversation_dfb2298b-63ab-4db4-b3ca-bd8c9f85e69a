import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageCircle, Users, X, Minimize2, Maximize2 } from 'lucide-react';
import { ChatRoomList } from './ChatRoomList';
import { ChatRoom } from './ChatRoom';
import { OnlineUsers } from './OnlineUsers';
import { ChatStatus } from './ChatStatus';
import { useChatStore } from '@/store/useChatStore';
import { useSocketChat } from '@/hooks/useSocketChat';
import { useUser } from '@/context/UserContext';
import { getRoomDisplayName } from '@/utils/chat';

interface ChatContainerProps {
  isOpen: boolean;
  onToggle: () => void;
}

export function ChatContainer({ isOpen, onToggle }: ChatContainerProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState<'rooms' | 'users'>('rooms');

  // IMPORTANTE: Inicializar Socket.IO
  useSocketChat();

  const {
    currentRoom,
    loadRooms,
    cleanup,
    error
  } = useChatStore();

  const { user } = useUser();
  const clubId = user?.club_id || localStorage.getItem('clubId');

  useEffect(() => {
    if (isOpen && clubId) {
      // Carregar dados iniciais apenas
      loadRooms(clubId.toString());
    }
  }, [isOpen, clubId, loadRooms]);

  // Limpar estado apenas ao desmontar o componente
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Socket.IO gerencia presença automaticamente

  if (!isOpen) {
    return (
      <Button
        onClick={onToggle}
        className="fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg z-50"
        size="icon"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>
    );
  }

  return (
    <Card className={`fixed bottom-4 right-4 z-50 shadow-xl transition-all duration-300 ${isMinimized
      ? 'w-80 h-12'
      : 'w-96 h-[600px]'
      }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-primary text-primary-foreground rounded-t-lg">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          <span className="font-medium">
            {currentRoom ? getRoomDisplayName(currentRoom, user?.name || user?.email) : 'Chat do Clube'}
          </span>
          <ChatStatus />
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? (
              <Maximize2 className="h-4 w-4" />
            ) : (
              <Minimize2 className="h-4 w-4" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20"
            onClick={onToggle}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {error && (
        <div className="px-3 py-2 text-sm text-destructive bg-destructive/10 border-b">
          {error}
        </div>
      )}

      {/* Content */}
      {!isMinimized && (
        <div className="flex flex-col h-[calc(600px-49px)]">
          {!currentRoom ? (
            <>
              {/* Tabs */}
              <div className="flex border-b">
                <button
                  className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${activeTab === 'rooms'
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-muted-foreground hover:text-foreground'
                    }`}
                  onClick={() => setActiveTab('rooms')}
                >
                  <MessageCircle className="h-4 w-4 inline mr-2" />
                  Salas
                </button>

                <button
                  className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${activeTab === 'users'
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-muted-foreground hover:text-foreground'
                    }`}
                  onClick={() => setActiveTab('users')}
                >
                  <Users className="h-4 w-4 inline mr-2" />
                  Online
                </button>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'rooms' ? (
                  <ChatRoomList />
                ) : (
                  <OnlineUsers />
                )}
              </div>
            </>
          ) : (
            <ChatRoom />
          )}
        </div>
      )}
    </Card>
  );
}