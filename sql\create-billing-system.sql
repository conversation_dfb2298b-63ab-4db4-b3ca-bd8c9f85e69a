-- <PERSON><PERSON><PERSON><PERSON> colunas PIX nas tabelas existentes se não existirem
ALTER TABLE players 
ADD COLUMN IF NOT EXISTS bank_pix_key VARCHAR(255);

ALTER TABLE collaborators 
ADD COLUMN IF NOT EXISTS bank_info JSONB DEFAULT '{}';

-- <PERSON><PERSON>r tabela de clientes
CREATE TABLE IF NOT EXISTS clients (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  pix_key VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>r tabela de cobranças/recebimentos
CREATE TABLE IF NOT EXISTS billing_transactions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('cobranca', 'recebimento')), -- cobrança ou recebimento
  entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('player', 'collaborator', 'client')), -- jogador, colaborador ou cliente
  entity_id INTEGER, -- ID do jogador, colaborador ou cliente
  entity_name VARCHAR(255) NOT NULL, -- Nome da entidade (para casos onde não há ID)
  pix_key VARCHAR(255) NOT NULL, -- Chave PIX a ser usada
  amount DECIMAL(10,2) NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'pago')),
  due_date DATE,
  paid_at TIMESTAMP WITH TIME ZONE,
  qr_code_data TEXT, -- Dados do QR Code gerado
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_clients_club_id ON clients(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_club_id ON billing_transactions(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_status ON billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_entity ON billing_transactions(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_due_date ON billing_transactions(due_date);

-- RLS (Row Level Security)
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_transactions ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para clients
DROP POLICY IF EXISTS "Users can view clients from their club" ON clients;
DROP POLICY IF EXISTS "Users can insert clients in their club" ON clients;
DROP POLICY IF EXISTS "Users can update clients from their club" ON clients;
DROP POLICY IF EXISTS "Users can delete clients from their club" ON clients;

CREATE POLICY "Users can view clients from their club" ON clients
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can insert clients in their club" ON clients
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can update clients from their club" ON clients
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can delete clients from their club" ON clients
  FOR DELETE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Políticas RLS para billing_transactions
DROP POLICY IF EXISTS "Users can view billing transactions from their club" ON billing_transactions;
DROP POLICY IF EXISTS "Users can insert billing transactions in their club" ON billing_transactions;
DROP POLICY IF EXISTS "Users can update billing transactions from their club" ON billing_transactions;
DROP POLICY IF EXISTS "Users can delete billing transactions from their club" ON billing_transactions;

CREATE POLICY "Users can view billing transactions from their club" ON billing_transactions
  FOR SELECT USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can insert billing transactions in their club" ON billing_transactions
  FOR INSERT WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can update billing transactions from their club" ON billing_transactions
  FOR UPDATE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can delete billing transactions from their club" ON billing_transactions
  FOR DELETE USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_transactions_updated_at BEFORE UPDATE ON billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comentários nas tabelas
COMMENT ON TABLE clients IS 'Tabela de clientes do clube para sistema de cobrança';
COMMENT ON TABLE billing_transactions IS 'Tabela de transações de cobrança e recebimento';

COMMENT ON COLUMN billing_transactions.type IS 'Tipo: cobranca (clube cobra) ou recebimento (clube recebe)';
COMMENT ON COLUMN billing_transactions.entity_type IS 'Tipo de entidade: player, collaborator ou client';
COMMENT ON COLUMN billing_transactions.entity_id IS 'ID da entidade (pode ser null para clientes sem cadastro)';
COMMENT ON COLUMN billing_transactions.entity_name IS 'Nome da entidade para exibição';
COMMENT ON COLUMN billing_transactions.pix_key IS 'Chave PIX a ser usada na transação';
COMMENT ON COLUMN billing_transactions.qr_code_data IS 'Dados do QR Code PIX gerado';