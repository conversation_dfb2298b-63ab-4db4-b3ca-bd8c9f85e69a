import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Save,
  Download,
  Upload,
  Share2,
  Copy,
  Edit,
  Trash2,
  Star,
  StarOff,
  Eye,
  EyeOff,
  Filter,
  Search,
  MoreVertical,
  FileText,
  Settings,
  Users,
  Globe,
  Lock,
  Heart,
  Tag,
  Calendar,
  User
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export interface ConfigurationTemplate {
  id: string;
  name: string;
  description: string;
  category: 'field' | 'timing' | 'intensity' | 'animation' | 'rules' | 'complete';
  tags: string[];
  configuration: Record<string, any>;
  metadata: {
    version: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
    usageCount: number;
    rating: number;
    isPublic: boolean;
    isFavorite: boolean;
    isOfficial: boolean;
  };
  sharing: {
    isShared: boolean;
    shareCode?: string;
    permissions: 'view' | 'edit' | 'admin';
    sharedWith: string[];
  };
}

interface TemplateManagerProps {
  currentSettings: Record<string, any>;
  onApplyTemplate: (template: ConfigurationTemplate) => void;
  onSettingsChange: (settings: Record<string, any>) => void;
  userId: string;
  clubId: number;
}

export function TemplateManager({
  currentSettings,
  onApplyTemplate,
  onSettingsChange,
  userId,
  clubId
}: TemplateManagerProps) {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<ConfigurationTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<ConfigurationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ConfigurationTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'usage' | 'rating'>('date');
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [showOnlyPublic, setShowOnlyPublic] = useState(false);

  // Template creation state
  const [newTemplate, setNewTemplate] = useState<Partial<ConfigurationTemplate>>({
    name: '',
    description: '',
    category: 'complete',
    tags: [],
    configuration: {},
    metadata: {
      version: '1.0.0',
      createdBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
      rating: 0,
      isPublic: false,
      isFavorite: false,
      isOfficial: false
    },
    sharing: {
      isShared: false,
      permissions: 'view',
      sharedWith: []
    }
  });

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, [userId, clubId]);

  // Filter and sort templates
  useEffect(() => {
    let filtered = [...templates];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(template => template.category === categoryFilter);
    }

    // Apply favorites filter
    if (showOnlyFavorites) {
      filtered = filtered.filter(template => template.metadata.isFavorite);
    }

    // Apply public filter
    if (showOnlyPublic) {
      filtered = filtered.filter(template => template.metadata.isPublic);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime();
        case 'usage':
          return b.metadata.usageCount - a.metadata.usageCount;
        case 'rating':
          return b.metadata.rating - a.metadata.rating;
        default:
          return 0;
      }
    });

    setFilteredTemplates(filtered);
  }, [templates, searchQuery, categoryFilter, sortBy, showOnlyFavorites, showOnlyPublic]);

  const loadTemplates = useCallback(async () => {
    try {
      // In a real implementation, this would load from an API
      const mockTemplates: ConfigurationTemplate[] = [
        {
          id: 'template_1',
          name: 'Configuração Padrão de Campo',
          description: 'Configurações básicas para visualização do campo de treinamento',
          category: 'field',
          tags: ['básico', 'campo', 'padrão'],
          configuration: {
            field: {
              showGrid: true,
              gridSize: 5,
              fieldColor: '#10b981',
              backgroundColor: '#f0fdf4',
              showCoordinates: false,
              showMeasurements: false,
              fieldScale: 1,
              showZones: true
            }
          },
          metadata: {
            version: '1.0.0',
            createdBy: 'system',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            usageCount: 45,
            rating: 4.2,
            isPublic: true,
            isFavorite: false,
            isOfficial: true
          },
          sharing: {
            isShared: true,
            permissions: 'view',
            sharedWith: []
          }
        },
        {
          id: 'template_2',
          name: 'Animações Suaves',
          description: 'Configurações otimizadas para animações fluidas e efeitos visuais',
          category: 'animation',
          tags: ['animação', 'performance', 'visual'],
          configuration: {
            animation: {
              enableAnimations: true,
              showTrajectories: true,
              trajectoryStyle: 'curved',
              showTimeline: true,
              particleEffects: true,
              motionBlur: true,
              easing: 'ease-in-out',
              highlightActiveElements: true
            },
            timing: {
              frameRate: 60,
              smoothTransitions: true,
              playbackSpeed: 1
            }
          },
          metadata: {
            version: '1.2.0',
            createdBy: userId,
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-01-20'),
            usageCount: 23,
            rating: 4.8,
            isPublic: false,
            isFavorite: true,
            isOfficial: false
          },
          sharing: {
            isShared: false,
            permissions: 'edit',
            sharedWith: []
          }
        }
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os templates",
        variant: "destructive"
      });
    }
  }, [userId, toast]);

  const saveTemplate = useCallback(async () => {
    if (!newTemplate.name?.trim()) {
      toast({
        title: "Erro",
        description: "Nome do template é obrigatório",
        variant: "destructive"
      });
      return;
    }

    try {
      const template: ConfigurationTemplate = {
        id: `template_${Date.now()}`,
        name: newTemplate.name!,
        description: newTemplate.description || '',
        category: newTemplate.category || 'complete',
        tags: newTemplate.tags || [],
        configuration: newTemplate.category === 'complete' ? currentSettings : 
          { [newTemplate.category!]: currentSettings[newTemplate.category!] },
        metadata: {
          ...newTemplate.metadata!,
          updatedAt: new Date()
        },
        sharing: newTemplate.sharing!
      };

      // In a real implementation, this would save to an API
      setTemplates(prev => [...prev, template]);
      setIsCreating(false);
      setNewTemplate({
        name: '',
        description: '',
        category: 'complete',
        tags: [],
        configuration: {},
        metadata: {
          version: '1.0.0',
          createdBy: userId,
          createdAt: new Date(),
          updatedAt: new Date(),
          usageCount: 0,
          rating: 0,
          isPublic: false,
          isFavorite: false,
          isOfficial: false
        },
        sharing: {
          isShared: false,
          permissions: 'view',
          sharedWith: []
        }
      });

      toast({
        title: "Sucesso",
        description: `Template "${template.name}" foi salvo com sucesso!`
      });
    } catch (error) {
      console.error('Error saving template:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o template",
        variant: "destructive"
      });
    }
  }, [newTemplate, currentSettings, userId, toast]);

  const deleteTemplate = useCallback(async (templateId: string) => {
    try {
      // In a real implementation, this would delete from an API
      setTemplates(prev => prev.filter(t => t.id !== templateId));
      
      toast({
        title: "Sucesso",
        description: "Template removido com sucesso!"
      });
    } catch (error) {
      console.error('Error deleting template:', error);
      toast({
        title: "Erro",
        description: "Não foi possível remover o template",
        variant: "destructive"
      });
    }
  }, [toast]);

  const toggleFavorite = useCallback(async (templateId: string) => {
    try {
      setTemplates(prev => prev.map(template =>
        template.id === templateId
          ? {
              ...template,
              metadata: {
                ...template.metadata,
                isFavorite: !template.metadata.isFavorite
              }
            }
          : template
      ));
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  }, []);

  const applyTemplate = useCallback(async (template: ConfigurationTemplate) => {
    try {
      // Update usage count
      setTemplates(prev => prev.map(t =>
        t.id === template.id
          ? {
              ...t,
              metadata: {
                ...t.metadata,
                usageCount: t.metadata.usageCount + 1
              }
            }
          : t
      ));

      onApplyTemplate(template);
      
      toast({
        title: "Template Aplicado",
        description: `Configurações do template "${template.name}" foram aplicadas!`
      });
    } catch (error) {
      console.error('Error applying template:', error);
      toast({
        title: "Erro",
        description: "Não foi possível aplicar o template",
        variant: "destructive"
      });
    }
  }, [onApplyTemplate, toast]);

  const exportTemplate = useCallback((template: ConfigurationTemplate) => {
    try {
      const exportData = {
        ...template,
        exportedAt: new Date().toISOString(),
        exportedBy: userId
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${template.name.replace(/\s+/g, '_')}_template.json`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Template Exportado",
        description: `Template "${template.name}" foi exportado com sucesso!`
      });
    } catch (error) {
      console.error('Error exporting template:', error);
      toast({
        title: "Erro",
        description: "Não foi possível exportar o template",
        variant: "destructive"
      });
    }
  }, [userId, toast]);

  const importTemplate = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string);
        
        // Validate imported data
        if (!importedData.name || !importedData.configuration) {
          throw new Error('Invalid template format');
        }

        const template: ConfigurationTemplate = {
          ...importedData,
          id: `template_${Date.now()}`,
          metadata: {
            ...importedData.metadata,
            createdBy: userId,
            createdAt: new Date(),
            updatedAt: new Date(),
            usageCount: 0,
            isOfficial: false
          }
        };

        setTemplates(prev => [...prev, template]);
        
        toast({
          title: "Template Importado",
          description: `Template "${template.name}" foi importado com sucesso!`
        });
      } catch (error) {
        console.error('Error importing template:', error);
        toast({
          title: "Erro",
          description: "Arquivo de template inválido",
          variant: "destructive"
        });
      }
    };
    reader.readAsText(file);
  }, [userId, toast]);

  const shareTemplate = useCallback(async (template: ConfigurationTemplate) => {
    try {
      const shareCode = `TMPL_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      
      setTemplates(prev => prev.map(t =>
        t.id === template.id
          ? {
              ...t,
              sharing: {
                ...t.sharing,
                isShared: true,
                shareCode
              }
            }
          : t
      ));

      // Copy share code to clipboard
      await navigator.clipboard.writeText(shareCode);
      
      toast({
        title: "Template Compartilhado",
        description: `Código de compartilhamento copiado: ${shareCode}`
      });
    } catch (error) {
      console.error('Error sharing template:', error);
      toast({
        title: "Erro",
        description: "Não foi possível compartilhar o template",
        variant: "destructive"
      });
    }
  }, [toast]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'field': return '🏟️';
      case 'timing': return '⏱️';
      case 'intensity': return '⚡';
      case 'animation': return '✨';
      case 'rules': return '📋';
      case 'complete': return '🎯';
      default: return '⚙️';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'field': return 'bg-green-100 text-green-800';
      case 'timing': return 'bg-blue-100 text-blue-800';
      case 'intensity': return 'bg-red-100 text-red-800';
      case 'animation': return 'bg-purple-100 text-purple-800';
      case 'rules': return 'bg-yellow-100 text-yellow-800';
      case 'complete': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Gerenciador de Templates</h3>
          <p className="text-sm text-muted-foreground">
            Salve, organize e compartilhe suas configurações personalizadas
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowImportDialog(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Importar
          </Button>
          <Button size="sm" onClick={() => setIsCreating(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Template
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="field">Campo</SelectItem>
                <SelectItem value="timing">Timing</SelectItem>
                <SelectItem value="intensity">Intensidade</SelectItem>
                <SelectItem value="animation">Animação</SelectItem>
                <SelectItem value="rules">Regras</SelectItem>
                <SelectItem value="complete">Completo</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Ordenar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Data</SelectItem>
                <SelectItem value="name">Nome</SelectItem>
                <SelectItem value="usage">Uso</SelectItem>
                <SelectItem value="rating">Avaliação</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Button
                variant={showOnlyFavorites ? "default" : "outline"}
                size="sm"
                onClick={() => setShowOnlyFavorites(!showOnlyFavorites)}
              >
                <Heart className="h-4 w-4" />
              </Button>
              <Button
                variant={showOnlyPublic ? "default" : "outline"}
                size="sm"
                onClick={() => setShowOnlyPublic(!showOnlyPublic)}
              >
                <Globe className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="relative group hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getCategoryIcon(template.category)}</span>
                  <div>
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    <CardDescription className="text-sm line-clamp-2">
                      {template.description}
                    </CardDescription>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => applyTemplate(template)}>
                      <Settings className="h-4 w-4 mr-2" />
                      Aplicar
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => toggleFavorite(template.id)}>
                      {template.metadata.isFavorite ? (
                        <StarOff className="h-4 w-4 mr-2" />
                      ) : (
                        <Star className="h-4 w-4 mr-2" />
                      )}
                      {template.metadata.isFavorite ? 'Remover dos Favoritos' : 'Adicionar aos Favoritos'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => exportTemplate(template)}>
                      <Download className="h-4 w-4 mr-2" />
                      Exportar
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => shareTemplate(template)}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Compartilhar
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => deleteTemplate(template.id)}
                      className="text-red-600"
                      disabled={template.metadata.isOfficial}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge className={getCategoryColor(template.category)}>
                    {template.category}
                  </Badge>
                  {template.metadata.isOfficial && (
                    <Badge variant="secondary">
                      <Star className="h-3 w-3 mr-1" />
                      Oficial
                    </Badge>
                  )}
                  {template.metadata.isFavorite && (
                    <Badge variant="outline">
                      <Heart className="h-3 w-3 mr-1" />
                      Favorito
                    </Badge>
                  )}
                  {template.sharing.isShared && (
                    <Badge variant="outline">
                      <Share2 className="h-3 w-3 mr-1" />
                      Compartilhado
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {template.metadata.usageCount} usos
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(template.metadata.updatedAt).toLocaleDateString()}
                  </div>
                  {template.metadata.rating > 0 && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current text-yellow-500" />
                      {template.metadata.rating.toFixed(1)}
                    </div>
                  )}
                </div>

                {template.tags.length > 0 && (
                  <div className="flex items-center gap-1 flex-wrap">
                    {template.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        <Tag className="h-2 w-2 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                    {template.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                <Button 
                  className="w-full" 
                  size="sm"
                  onClick={() => applyTemplate(template)}
                >
                  Aplicar Template
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredTemplates.length === 0 && (
          <div className="col-span-full">
            <Card className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <FileText className="h-12 w-12 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-medium">Nenhum template encontrado</h3>
                  <p className="text-sm text-muted-foreground">
                    {searchQuery || categoryFilter !== 'all' 
                      ? 'Tente ajustar os filtros de busca'
                      : 'Crie seu primeiro template personalizado'
                    }
                  </p>
                </div>
                {!searchQuery && categoryFilter === 'all' && (
                  <Button onClick={() => setIsCreating(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Primeiro Template
                  </Button>
                )}
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Create Template Dialog */}
      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Criar Novo Template</DialogTitle>
            <DialogDescription>
              Salve suas configurações atuais como um template reutilizável
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Nome do Template</Label>
                <Input
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Digite o nome do template"
                />
              </div>
              <div className="space-y-2">
                <Label>Categoria</Label>
                <Select
                  value={newTemplate.category}
                  onValueChange={(value: any) => setNewTemplate(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="complete">Configuração Completa</SelectItem>
                    <SelectItem value="field">Apenas Campo</SelectItem>
                    <SelectItem value="timing">Apenas Timing</SelectItem>
                    <SelectItem value="intensity">Apenas Intensidade</SelectItem>
                    <SelectItem value="animation">Apenas Animação</SelectItem>
                    <SelectItem value="rules">Apenas Regras</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Descrição</Label>
              <Textarea
                value={newTemplate.description}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Descreva o que este template faz"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags (separadas por vírgula)</Label>
              <Input
                value={newTemplate.tags?.join(', ')}
                onChange={(e) => setNewTemplate(prev => ({ 
                  ...prev, 
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                }))}
                placeholder="básico, campo, animação"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={newTemplate.metadata?.isPublic}
                onCheckedChange={(checked) => setNewTemplate(prev => ({
                  ...prev,
                  metadata: { ...prev.metadata!, isPublic: checked }
                }))}
              />
              <Label className="text-sm">
                Tornar público (outros usuários poderão ver e usar)
              </Label>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsCreating(false)}>
              Cancelar
            </Button>
            <Button onClick={saveTemplate}>
              <Save className="h-4 w-4 mr-2" />
              Salvar Template
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Import Template Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Importar Template</DialogTitle>
            <DialogDescription>
              Selecione um arquivo de template para importar
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground mb-2">
                Arraste um arquivo .json aqui ou clique para selecionar
              </p>
              <Input
                type="file"
                accept=".json"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    importTemplate(file);
                    setShowImportDialog(false);
                  }
                }}
                className="max-w-xs mx-auto"
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}