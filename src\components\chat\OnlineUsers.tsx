import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Users, Circle } from 'lucide-react';
import { useChatStore } from '@/store/useChatStore';
import { useSocketChat } from '@/hooks/useSocketChat';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const statusColors = {
  online: 'bg-green-500',
  away: 'bg-yellow-500',
  busy: 'bg-red-500',
  offline: 'bg-gray-400'
};

const statusLabels = {
  online: 'Online',
  away: 'Ausente',
  busy: 'Ocupado',
  offline: 'Offline'
};

export function OnlineUsers() {
  const { onlineUsers } = useChatStore();
  const { createDirectChat } = useSocketChat();

  const sortedUsers = [...onlineUsers].sort((a, b) => {
    // Ordenar por status (online primeiro) e depois por nome
    const statusOrder = { online: 0, away: 1, busy: 2, offline: 3 };
    const statusDiff = statusOrder[a.status] - statusOrder[b.status];
    
    if (statusDiff !== 0) return statusDiff;
    
    return (a.user?.name || '').localeCompare(b.user?.name || '');
  });

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-3 border-b">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">
            Usuários Online ({onlineUsers.length})
          </span>
        </div>
      </div>

      {/* Users List */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {sortedUsers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Nenhum usuário online</p>
            </div>
          ) : (
            sortedUsers.map((presence) => (
              <div
                key={presence.user_id}
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent transition-colors cursor-pointer"
                onClick={() => createDirectChat(presence.user_id)}
              >
                {/* Avatar with Status */}
                <div className="relative flex-shrink-0">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={presence.user?.avatar_url} />
                    <AvatarFallback className="text-xs">
                      {presence.user?.name?.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Status Indicator */}
                  <div className="absolute -bottom-0.5 -right-0.5">
                    <div className={`h-3 w-3 rounded-full border-2 border-background ${statusColors[presence.status]}`} />
                  </div>
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm truncate">
                      {presence.user?.name}
                    </h4>
                    
                    <Badge 
                      variant="secondary" 
                      className="text-xs"
                    >
                      {statusLabels[presence.status]}
                    </Badge>
                  </div>
                  
                  {presence.status !== 'online' && (
                    <p className="text-xs text-muted-foreground">
                      Visto {formatDistanceToNow(new Date(presence.last_seen), {
                        addSuffix: true,
                        locale: ptBR
                      })}
                    </p>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Status Legend */}
      <div className="p-3 border-t bg-muted/30">
        <div className="grid grid-cols-2 gap-2 text-xs">
          {Object.entries(statusLabels).map(([status, label]) => (
            <div key={status} className="flex items-center gap-2">
              <Circle className={`h-2 w-2 ${statusColors[status as keyof typeof statusColors]} rounded-full`} />
              <span className="text-muted-foreground">{label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}