import { supabase } from "@/integrations/supabase/client";

export interface CallupAccommodation {
  id: number;
  callup_id: number;
  name: string;
  type: 'hotel' | 'apartment';
  address?: string | null;
  capacity?: number | null;
  created_at?: string;
}

export interface CallupHotelRoom {
  id: number;
  callup_accommodation_id: number;
  room_number: string;
  capacity: number;
  created_at?: string;
}

export interface CallupAccommodationGuest {
  id: number;
  callup_accommodation_id: number;
  player_id?: string | null;
  collaborator_id?: number | null;
  hotel_room_id?: number | null;
  check_in_date?: string | null;
  check_out_date?: string | null;
  notes?: string | null;
  created_at?: string;
  player_name?: string;
  collaborator_name?: string;
  room_number?: string;
}

export async function getCallupAccommodations(callupId: number): Promise<CallupAccommodation[]> {
  const { data, error } = await supabase
    .from('callup_accommodations')
    .select('*')
    .eq('callup_id', callupId)
    .order('name');

  if (error) {
    throw new Error(`Erro ao buscar alojamentos: ${error.message}`);
  }
  return data || [];
}

export async function getCallupHotelRooms(accommodationId: number): Promise<CallupHotelRoom[]> {
  const { data, error } = await supabase
    .from('callup_hotel_rooms')
    .select('*')
    .eq('callup_accommodation_id', accommodationId)
    .order('room_number');

  if (error) {
    throw new Error(`Erro ao buscar quartos: ${error.message}`);
  }
  return data || [];
}

export async function createCallupAccommodation(callupId: number, accommodation: Omit<CallupAccommodation, 'id' | 'callup_id'>, rooms?: { room_number: string; capacity: number }[]): Promise<CallupAccommodation> {
  const { data, error } = await supabase
    .from('callup_accommodations')
    .insert({ ...accommodation, callup_id: callupId } as any)
    .select()
    .single();

  if (error) {
    throw new Error(`Erro ao criar alojamento: ${error.message}`);
  }

  if (data && accommodation.type === 'hotel' && rooms && rooms.length) {
    const roomsToInsert = rooms.map(r => ({
      callup_accommodation_id: data.id,
      room_number: r.room_number,
      capacity: r.capacity
    }));
    const { error: roomErr } = await supabase
      .from('callup_hotel_rooms')
      .insert(roomsToInsert as any);
    if (roomErr) {
      console.error('Erro ao criar quartos', roomErr);
    }
  }

  return data as CallupAccommodation;
}

export async function addGuestToCallupAccommodation(accommodationId: number, guest: { player_id?: string; collaborator_id?: number; hotel_room_id?: number; check_in_date?: string; check_out_date?: string; notes?: string; }): Promise<CallupAccommodationGuest> {
  const { data, error } = await supabase
    .from('callup_accommodation_guests')
    .insert({ ...guest, callup_accommodation_id: accommodationId } as any)
    .select(`*, players:player_id(name), collaborators:collaborator_id(full_name), rooms:hotel_room_id(room_number)`) 
    .single();

  if (error) {
    throw new Error(`Erro ao adicionar hóspede: ${error.message}`);
  }

  return {
    ...(data as any),
    player_name: (data as any).players?.name,
    collaborator_name: (data as any).collaborators?.full_name,
    room_number: (data as any).rooms?.room_number
  } as CallupAccommodationGuest;
}

export async function getCallupAccommodationGuests(accommodationId: number): Promise<CallupAccommodationGuest[]> {
  const { data, error } = await supabase
    .from('callup_accommodation_guests')
    .select(`*, players:player_id(name), collaborators:collaborator_id(full_name), rooms:hotel_room_id(room_number)`) 
    .eq('callup_accommodation_id', accommodationId);

  if (error) {
    throw new Error(`Erro ao buscar hóspedes: ${error.message}`);
  }

  return (data || []).map((item: any) => ({
    ...item,
    player_name: item.players?.name,
    collaborator_name: item.collaborators?.full_name,
    room_number: item.rooms?.room_number
  })) as CallupAccommodationGuest[];
}

export async function updateCallupAccommodation(
  id: number,
  updates: Partial<CallupAccommodation>,
  rooms?: { id?: number; room_number: string; capacity: number }[]
): Promise<CallupAccommodation> {
  const { data, error } = await supabase
    .from('callup_accommodations')
    .update(updates as any)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Erro ao atualizar alojamento: ${error.message}`);
  }

  if (updates.type === 'hotel' && rooms) {
    const { data: existingRooms, error: existingRoomsError } = await supabase
      .from('callup_hotel_rooms')
      .select('*, callup_accommodation_guests(id)') // Select guests to check if room is occupied
      .eq('callup_accommodation_id', id);

    if (existingRoomsError) {
      console.error('Erro ao buscar quartos existentes:', existingRoomsError);
      throw new Error('Erro ao buscar quartos existentes.');
    }

    const existingRoomMap = new Map<string, CallupHotelRoom & { callup_accommodation_guests: { id: number }[] }>();
    existingRooms?.forEach(room => existingRoomMap.set(room.room_number, room));

    const roomsToInsert: Omit<CallupHotelRoom, 'id' | 'created_at'>[] = [];
    const roomsToUpdate: { id: number; capacity: number }[] = [];
    const roomIdsToKeep: number[] = []; // To track rooms that should not be deleted

    for (const newRoom of rooms) {
      const existingRoom = existingRoomMap.get(newRoom.room_number);
      if (existingRoom) {
        // Room exists, update its capacity if changed
        if (existingRoom.capacity !== newRoom.capacity) {
          roomsToUpdate.push({ id: existingRoom.id, capacity: newRoom.capacity });
        }
        roomIdsToKeep.push(existingRoom.id);
        existingRoomMap.delete(newRoom.room_number); // Mark as processed
      } else {
        // New room, add it
        roomsToInsert.push({
          callup_accommodation_id: id,
          room_number: newRoom.room_number,
          capacity: newRoom.capacity,
        });
      }
    }

    // Process rooms that were in existingRooms but not in the new 'rooms' list
    for (const [roomNumber, roomToDelete] of existingRoomMap.entries()) {
      if (roomToDelete.callup_accommodation_guests && roomToDelete.callup_accommodation_guests.length > 0) {
        // Room has guests, do not delete it, but keep its ID
        roomIdsToKeep.push(roomToDelete.id);
        console.warn(`Quarto ${roomToDelete.room_number} não será excluído pois possui hóspedes.`);
      } else {
        // Room has no guests, delete it
        await supabase.from('callup_hotel_rooms').delete().eq('id', roomToDelete.id);
      }
    }

    // Perform updates
    if (roomsToUpdate.length > 0) {
      for (const roomUpdate of roomsToUpdate) {
        await supabase.from('callup_hotel_rooms').update({ capacity: roomUpdate.capacity }).eq('id', roomUpdate.id);
      }
    }

    // Perform inserts
    if (roomsToInsert.length > 0) {
      await supabase.from('callup_hotel_rooms').insert(roomsToInsert as any);
    }
  }

  return data as CallupAccommodation;
}

export async function deleteCallupAccommodation(id: number): Promise<void> {
  // Remover hóspedes vinculados ao alojamento
  await supabase
    .from('callup_accommodation_guests')
    .delete()
    .eq('callup_accommodation_id', id);

  // Remover quartos do hotel, se existirem
  await supabase
    .from('callup_hotel_rooms')
    .delete()
    .eq('callup_accommodation_id', id);

  const { error } = await supabase
    .from('callup_accommodations')
    .delete()
    .eq('id', id);
  if (error) {
    throw new Error(`Erro ao excluir alojamento: ${error.message}`);
  }
}

export async function removeCallupAccommodationGuest(guestId: number): Promise<void> {
  const { error } = await supabase
    .from('callup_accommodation_guests')
    .delete()
    .eq('id', guestId);
  if (error) {
    throw new Error(`Erro ao remover hóspede: ${error.message}`);
  }
}