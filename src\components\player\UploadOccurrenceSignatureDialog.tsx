import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Upload, Image as ImageIcon } from "lucide-react";
import { uploadSignature } from "@/api/storage";
import { updatePlayerOccurrence } from "@/api/api";
import { useCurrentClubId } from "@/context/ClubContext";

interface UploadOccurrenceSignatureDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  occurrenceId: number | null;
  occurrenceTitle: string;
  onSuccess: () => void;
}

export function UploadOccurrenceSignatureDialog({
  open,
  onOpenChange,
  occurrenceId,
  occurrenceTitle,
  onSuccess,
}: UploadOccurrenceSignatureDialogProps) {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }
  };

  const handleSelectFile = () => {
    fileInputRef.current?.click();
  };

  const handleUpload = async () => {
    if (!occurrenceId || !selectedFile) return;
    setIsLoading(true);
    try {
      const url = await uploadSignature(clubId, `occurrence_${occurrenceId}`, selectedFile);
      await updatePlayerOccurrence(occurrenceId, { signature_url: url });
      toast({ title: "Sucesso", description: "Imagem enviada com sucesso." });
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao enviar imagem:", error);
      toast({ title: "Erro", description: "Ocorreu um erro ao enviar a imagem.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Enviar Assinatura da Ocorrência</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Selecione uma imagem assinada para a ocorrência <strong>{occurrenceTitle}</strong>.
          </p>
          <div className="flex flex-col items-center justify-center">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />
            {previewUrl ? (
              <div className="mb-4 border rounded-md overflow-hidden">
                <img src={previewUrl} alt="Preview" className="max-h-[200px] object-contain" />
              </div>
            ) : (
              <div className="mb-4 border rounded-md p-8 flex flex-col items-center justify-center bg-gray-50">
                <ImageIcon className="h-16 w-16 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Nenhuma imagem selecionada</p>
              </div>
            )}
            <Button type="button" variant="outline" onClick={handleSelectFile} className="gap-2">
              <Upload className="h-4 w-4" /> Selecionar Imagem
            </Button>
          </div>
        </div>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleUpload} disabled={isLoading || !selectedFile} className="gap-2">
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Upload className="h-4 w-4" />}
            Enviar Imagem
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}