import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import {
  createEvaluationPayment,
  getPlayerEvaluationPayments,
  verifyPaymentReceipt,
  sendEvaluationPaymentEmail,
  EvaluationPayment
} from "@/api/evaluationPayments";
import { supabase } from "@/integrations/supabase/client";
import { 
  DollarSign, 
  Calendar, 
  CreditCard, 
  Mail, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  Eye,
  Download
} from "lucide-react";
import { format } from "date-fns";

interface Player {
  id: string;
  name: string;
  email?: string;
  player_evaluation_invitations?: Array<{
    id: number;
  }>;
}

interface EvaluationPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  player: Player | null;
}

export function EvaluationPaymentModal({
  open,
  onOpenChange,
  player
}: EvaluationPaymentModalProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();

  // Form state
  const [amount, setAmount] = useState("");
  const [periodDescription, setPeriodDescription] = useState("");
  const [pixKey, setPixKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [sendingEmail, setSendingEmail] = useState(false);

  // Payments list
  const [payments, setPayments] = useState<EvaluationPayment[]>([]);
  const [loadingPayments, setLoadingPayments] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<EvaluationPayment | null>(null);
  const [verifyingPayment, setVerifyingPayment] = useState<number | null>(null);

  // Load club PIX key
  useEffect(() => {
    const loadClubPixKey = async () => {
      try {
        const { data } = await supabase
          .from("club_info")
          .select("pix_key")
          .eq("id", clubId)
          .single();

        if (data?.pix_key) {
          setPixKey(data.pix_key);
        }
      } catch (err) {
        console.error("Erro ao carregar chave PIX:", err);
      }
    };

    if (open && clubId) {
      loadClubPixKey();
    }
  }, [open, clubId]);

  // Load existing payments when modal opens
  useEffect(() => {
    if (open && player) {
      loadPayments();
    }
  }, [open, player]);

  const loadPayments = async () => {
    if (!player) return;

    try {
      setLoadingPayments(true);
      const data = await getPlayerEvaluationPayments(clubId, player.id);
      setPayments(data);
    } catch (err: any) {
      console.error("Erro ao carregar cobranças:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao carregar cobranças",
        variant: "destructive",
      });
    } finally {
      setLoadingPayments(false);
    }
  };

  const handleCreatePayment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!player || !user?.id) return;

    if (!amount || !periodDescription || !pixKey) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    if (!player.email) {
      toast({
        title: "Erro",
        description: "Atleta não possui email cadastrado",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      const evaluationInvitationId = player.player_evaluation_invitations?.[0]?.id;

      // Create payment
      const payment = await createEvaluationPayment(
        clubId,
        player.id,
        parseFloat(amount),
        periodDescription,
        pixKey,
        user.id,
        evaluationInvitationId
      );

      // Get club name for email
      const { data: clubData } = await supabase
        .from("club_info")
        .select("name")
        .eq("id", clubId)
        .single();

      const clubName = clubData?.name || "Clube";

      // Send email
      setSendingEmail(true);
      await sendEvaluationPaymentEmail(
        player.email,
        player.name,
        parseFloat(amount),
        periodDescription,
        pixKey,
        payment.pix_code || "",
        payment.qr_code_data || "",
        payment.payment_token,
        clubName
      );

      toast({
        title: "Sucesso",
        description: "Cobrança criada e email enviado com sucesso",
      });

      // Reset form
      setAmount("");
      setPeriodDescription("");

      // Reload payments
      loadPayments();

    } catch (err: any) {
      console.error("Erro ao criar cobrança:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar cobrança",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setSendingEmail(false);
    }
  };

  const handleVerifyPayment = async (paymentId: number, status: 'paid' | 'cancelled', notes?: string) => {
    if (!user?.id) return;

    try {
      setVerifyingPayment(paymentId);

      await verifyPaymentReceipt(clubId, paymentId, status, user.id, notes);

      toast({
        title: "Sucesso",
        description: `Pagamento ${status === 'paid' ? 'aprovado' : 'cancelado'} com sucesso`,
      });

      // Reload payments
      loadPayments();

    } catch (err: any) {
      console.error("Erro ao verificar pagamento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao verificar pagamento",
        variant: "destructive",
      });
    } finally {
      setVerifyingPayment(null);
    }
  };

  const getStatusBadge = (payment: EvaluationPayment) => {
    switch (payment.status) {
      case 'pending':
        return payment.receipt_file_url ? (
          <Badge className="bg-yellow-500">Aguardando Verificação</Badge>
        ) : (
          <Badge className="bg-blue-500">Aguardando Pagamento</Badge>
        );
      case 'paid':
        return <Badge className="bg-green-500">Pago</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelado</Badge>;
      default:
        return <Badge>{payment.status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Finanças - {player?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Create New Payment Form */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">Nova Cobrança</h3>
            
            <form onSubmit={handleCreatePayment} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Valor (R$)*</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0,00"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pixKey">Chave PIX*</Label>
                  <Input
                    id="pixKey"
                    value={pixKey}
                    onChange={(e) => setPixKey(e.target.value)}
                    placeholder="Chave PIX do clube"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="periodDescription">Período*</Label>
                <Input
                  id="periodDescription"
                  value={periodDescription}
                  onChange={(e) => setPeriodDescription(e.target.value)}
                  placeholder="Ex: 15 a 30 de Janeiro de 2025"
                  required
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="submit"
                  disabled={loading || sendingEmail}
                  className="flex items-center gap-2"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : sendingEmail ? (
                    <Mail className="h-4 w-4" />
                  ) : (
                    <CreditCard className="h-4 w-4" />
                  )}
                  {loading ? "Criando..." : sendingEmail ? "Enviando Email..." : "Criar e Enviar Email"}
                </Button>
              </div>
            </form>
          </div>

          {/* Existing Payments */}
          <div className="border rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Cobranças Existentes</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={loadPayments}
                disabled={loadingPayments}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loadingPayments ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>

            {loadingPayments ? (
              <div className="flex justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin" />
              </div>
            ) : payments.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhuma cobrança encontrada
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div key={payment.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{formatCurrency(payment.amount)}</span>
                          {getStatusBadge(payment)}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Período: {payment.period_description}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Criado em: {format(new Date(payment.created_at), "dd/MM/yyyy 'às' HH:mm")}
                        </p>
                      </div>

                      <div className="flex gap-2">
                        {payment.receipt_file_url && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(payment.receipt_file_url, '_blank')}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Ver Comprovante
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = payment.receipt_file_url!;
                                link.download = `comprovante-${payment.id}.pdf`;
                                link.click();
                              }}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Baixar
                            </Button>
                          </>
                        )}

                        {payment.status === 'pending' && payment.receipt_file_url && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
                              onClick={() => handleVerifyPayment(payment.id, 'paid')}
                              disabled={verifyingPayment === payment.id}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Aprovar
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
                              onClick={() => handleVerifyPayment(payment.id, 'cancelled')}
                              disabled={verifyingPayment === payment.id}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Cancelar
                            </Button>
                          </>
                        )}
                      </div>
                    </div>

                    {payment.verification_notes && (
                      <div className="mt-2 p-2 bg-muted rounded text-sm">
                        <strong>Observações:</strong> {payment.verification_notes}
                      </div>
                    )}

                    {payment.verified_at && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Verificado em: {format(new Date(payment.verified_at), "dd/MM/yyyy 'às' HH:mm")}
                        {payment.created_by_user && ` por ${payment.created_by_user.name}`}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}