import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Search, 
  Filter,
  Download,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  RefreshCw,
  Clock
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { supabase } from "@/integrations/supabase/client";
import { toast } from 'sonner';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { DateRange } from 'react-day-picker';

interface AuditLog {
  id: number;
  user_id?: string;
  action: string;
  entity_type: string;
  entity_id?: number;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  user_name?: string;
  user_email?: string;
}

interface AuditStats {
  totalActions: number;
  todayActions: number;
  uniqueUsers: number;
  criticalActions: number;
  topActions: Array<{ action: string; count: number }>;
  topUsers: Array<{ user_name: string; count: number }>;
}

export const Audit: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [stats, setStats] = useState<AuditStats>({
    totalActions: 0,
    todayActions: 0,
    uniqueUsers: 0,
    criticalActions: 0,
    topActions: [],
    topUsers: []
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('all');
  const [entityFilter, setEntityFilter] = useState<string>('all');
  const [userFilter, setUserFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 7),
    to: new Date()
  });

  useEffect(() => {
    loadAuditLogs();
    loadAuditStats();
  }, [dateRange, actionFilter, entityFilter, userFilter]);

  const loadAuditLogs = async () => {
    try {
      setLoading(true);
      const {
        data: { session }
      } = await supabase.auth.getSession();
      if (!session) throw new Error('Usuário não autenticado');

      
      let query = supabase
        .from('master_audit_logs')
        .select(`
          *,
          master_users:user_id (name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      // Aplicar filtros de data
      if (dateRange?.from) {
        query = query.gte('created_at', startOfDay(dateRange.from).toISOString());
      }
      if (dateRange?.to) {
        query = query.lte('created_at', endOfDay(dateRange.to).toISOString());
      }

      // Aplicar filtros
      if (actionFilter !== 'all') {
        query = query.eq('action', actionFilter);
      }
      if (entityFilter !== 'all') {
        query = query.eq('entity_type', entityFilter);
      }
      if (userFilter !== 'all') {
        query = query.eq('user_id', userFilter);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Processar dados para incluir informações do usuário
      const processedLogs = data?.map(log => ({
        ...log,
        user_name: log.master_users?.name || 'Sistema',
        user_email: log.master_users?.email || ''
      })) || [];

      setLogs(processedLogs);
    } catch (error: any) {
      console.error('Erro ao carregar logs de auditoria:', error);
      toast.error('Erro ao carregar logs de auditoria');
    } finally {
      setLoading(false);
    }
  };

  const loadAuditStats = async () => {
    try {
      // Em produção, isso seria calculado no backend
      const today = startOfDay(new Date());
      const todayLogs = logs.filter(log => new Date(log.created_at) >= today);
      const uniqueUsers = new Set(logs.filter(log => log.user_id).map(log => log.user_id)).size;
      const criticalActions = logs.filter(log => 
        ['delete_club', 'suspend_club', 'delete_user', 'update_plan'].includes(log.action)
      ).length;

      // Top ações
      const actionCounts: Record<string, number> = {};
      logs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
      });
      const topActions = Object.entries(actionCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([action, count]) => ({ action, count }));

      // Top usuários
      const userCounts: Record<string, number> = {};
      logs.forEach(log => {
        if (log.user_name && log.user_name !== 'Sistema') {
          userCounts[log.user_name] = (userCounts[log.user_name] || 0) + 1;
        }
      });
      const topUsers = Object.entries(userCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([user_name, count]) => ({ user_name, count }));

      setStats({
        totalActions: logs.length,
        todayActions: todayLogs.length,
        uniqueUsers,
        criticalActions,
        topActions,
        topUsers
      });
    } catch (error: any) {
      console.error('Erro ao calcular estatísticas:', error);
    }
  };

  const getActionIcon = (action: string) => {
    const iconMap: Record<string, React.ElementType> = {
      create_club: Plus,
      update_club: Edit,
      delete_club: Trash2,
      suspend_club: XCircle,
      activate_club: CheckCircle,
      create_user: User,
      update_user: Edit,
      delete_user: Trash2,
      update_plan: Settings,
      system_backup: Shield,
      login: User,
      logout: User
    };

    const IconComponent = iconMap[action] || Activity;
    return <IconComponent className="w-4 h-4" />;
  };

  const getActionBadge = (action: string) => {
    const criticalActions = ['delete_club', 'suspend_club', 'delete_user'];
    const warningActions = ['update_plan', 'activate_club'];
    
    if (criticalActions.includes(action)) {
      return <Badge variant="destructive">Crítica</Badge>;
    } else if (warningActions.includes(action)) {
      return <Badge variant="secondary">Importante</Badge>;
    } else {
      return <Badge variant="outline">Normal</Badge>;
    }
  };

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      create_club: 'Criar Clube',
      update_club: 'Atualizar Clube',
      delete_club: 'Excluir Clube',
      suspend_club: 'Suspender Clube',
      activate_club: 'Ativar Clube',
      create_user: 'Criar Usuário',
      update_user: 'Atualizar Usuário',
      delete_user: 'Excluir Usuário',
      update_plan: 'Atualizar Plano',
      system_backup: 'Backup do Sistema',
      login: 'Login',
      logout: 'Logout'
    };
    
    return labels[action] || action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const exportAuditLogs = async () => {
    try {
      // Em produção, isso geraria um arquivo CSV ou Excel
      const csvContent = [
        ['Data/Hora', 'Usuário', 'Ação', 'Entidade', 'IP', 'Detalhes'].join(','),
        ...logs.map(log => [
          format(new Date(log.created_at), 'dd/MM/yyyy HH:mm:ss'),
          log.user_name || 'Sistema',
          getActionLabel(log.action),
          log.entity_type,
          log.ip_address || '',
          JSON.stringify(log.details || {})
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      
      toast.success('Logs exportados com sucesso!');
    } catch (error: any) {
      console.error('Erro ao exportar logs:', error);
      toast.error('Erro ao exportar logs');
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getActionLabel(log.action).toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.entity_type.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando logs de auditoria...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Auditoria</h1>
          <p className="text-gray-600 mt-1">
            Monitore todas as ações realizadas no sistema master
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadAuditLogs} className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Atualizar
          </Button>
          <Button onClick={exportAuditLogs} className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total de Ações</p>
                <p className="text-2xl font-bold">{stats.totalActions}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ações Hoje</p>
                <p className="text-2xl font-bold">{stats.todayActions}</p>
              </div>
              <Clock className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Usuários Ativos</p>
                <p className="text-2xl font-bold">{stats.uniqueUsers}</p>
              </div>
              <User className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ações Críticas</p>
                <p className="text-2xl font-bold text-red-600">{stats.criticalActions}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Actions e Top Users */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Ações Mais Frequentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.topActions.map((item, index) => (
                <div key={item.action} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                    </div>
                    <span className="font-medium">{getActionLabel(item.action)}</span>
                  </div>
                  <Badge variant="secondary">{item.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Usuários Mais Ativos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.topUsers.map((item, index) => (
                <div key={item.user_name} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${item.user_name}`} />
                      <AvatarFallback>
                        {item.user_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{item.user_name}</span>
                  </div>
                  <Badge variant="secondary">{item.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Ação" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as ações</SelectItem>
                <SelectItem value="create_club">Criar Clube</SelectItem>
                <SelectItem value="update_club">Atualizar Clube</SelectItem>
                <SelectItem value="suspend_club">Suspender Clube</SelectItem>
                <SelectItem value="delete_club">Excluir Clube</SelectItem>
                <SelectItem value="create_user">Criar Usuário</SelectItem>
                <SelectItem value="update_user">Atualizar Usuário</SelectItem>
                <SelectItem value="delete_user">Excluir Usuário</SelectItem>
              </SelectContent>
            </Select>
            <Select value={entityFilter} onValueChange={setEntityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Entidade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as entidades</SelectItem>
                <SelectItem value="club">Clube</SelectItem>
                <SelectItem value="user">Usuário</SelectItem>
                <SelectItem value="plan">Plano</SelectItem>
                <SelectItem value="payment">Pagamento</SelectItem>
                <SelectItem value="system">Sistema</SelectItem>
              </SelectContent>
            </Select>
            <DatePickerWithRange
              date={dateRange}
              onDateChange={setDateRange}
            />
          </div>
        </CardContent>
      </Card>

      {/* Lista de Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Logs de Auditoria ({filteredLogs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredLogs.map((log) => (
              <div key={log.id} className="flex items-start gap-4 p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center gap-2 mt-1">
                  {getActionIcon(log.action)}
                  {getActionBadge(log.action)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium">{getActionLabel(log.action)}</h3>
                    <Badge variant="outline" className="text-xs">
                      {log.entity_type}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    {log.details?.reason || 'Ação realizada no sistema'}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{log.user_name}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>{format(new Date(log.created_at), 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}</span>
                    </div>
                    {log.ip_address && (
                      <div className="flex items-center gap-1">
                        <Shield className="w-3 h-3" />
                        <span>{log.ip_address}</span>
                      </div>
                    )}
                  </div>
                  {(log.old_values || log.new_values) && (
                    <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                      {log.old_values && (
                        <div className="mb-1">
                          <span className="font-medium">Antes:</span> {JSON.stringify(log.old_values)}
                        </div>
                      )}
                      {log.new_values && (
                        <div>
                          <span className="font-medium">Depois:</span> {JSON.stringify(log.new_values)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}

            {filteredLogs.length === 0 && (
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Nenhum log encontrado</p>
                <p className="text-sm text-gray-500">Ajuste os filtros ou período de busca</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};