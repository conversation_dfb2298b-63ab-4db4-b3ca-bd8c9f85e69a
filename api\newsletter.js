// API para processar inscrições na newsletter

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { email, source = 'blog', variant = 'default', timestamp } = req.body;

  if (!email) {
    return res.status(400).json({ message: 'Email é obrigatório' });
  }

  // Validar formato do email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ message: 'Email inválido' });
  }

  try {
    // 1. Verificar se já está inscrito
    const existingSubscriber = await checkExistingSubscriber(email);
    if (existingSubscriber) {
      return res.status(200).json({ 
        success: true, 
        message: 'Email já cadastrado. Obrigado!',
        alreadySubscribed: true
      });
    }

    // 2. Adicionar à lista de email (ConvertKit/Mailchimp)
    await addToNewsletterList(email, source, variant);

    // 3. Salvar no banco de dados
    await saveNewsletterSubscription({
      email,
      source,
      variant,
      timestamp: timestamp || new Date().toISOString(),
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
      user_agent: req.headers['user-agent']
    });

    // 4. Enviar email de boas-vindas
    await sendWelcomeEmail(email, source);

    // 5. Enviar kit de planilhas (se aplicável)
    if (source.includes('blog') || variant === 'default') {
      await sendStarterKit(email);
    }

    res.status(200).json({ 
      success: true, 
      message: 'Inscrição realizada com sucesso!' 
    });

  } catch (error) {
    console.error('Erro ao processar newsletter:', error);
    
    // Diferentes tipos de erro
    if (error.message.includes('already subscribed')) {
      return res.status(200).json({ 
        success: true, 
        message: 'Email já cadastrado. Obrigado!',
        alreadySubscribed: true
      });
    }
    
    res.status(500).json({ 
      success: false, 
      message: 'Erro interno. Tente novamente em alguns minutos.' 
    });
  }
}

// Verificar se email já existe
async function checkExistingSubscriber(email) {
  const { createClient } = require('@supabase/supabase-js');
  
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  const { data, error } = await supabase
    .from('newsletter_subscribers')
    .select('email')
    .eq('email', email)
    .eq('status', 'active')
    .single();

  return data;
}

// Adicionar à lista de email
async function addToNewsletterList(email, source, variant) {
  const CONVERTKIT_API_KEY = process.env.CONVERTKIT_API_KEY;
  const CONVERTKIT_FORM_ID = process.env.CONVERTKIT_NEWSLETTER_FORM_ID;

  if (!CONVERTKIT_API_KEY || !CONVERTKIT_FORM_ID) {
    console.warn('ConvertKit não configurado para newsletter');
    return;
  }

  const tags = [
    'newsletter',
    source,
    variant,
    'blog_subscriber'
  ];

  const response = await fetch(`https://api.convertkit.com/v3/forms/${CONVERTKIT_FORM_ID}/subscribe`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      api_key: CONVERTKIT_API_KEY,
      email,
      tags,
      fields: {
        source: source,
        variant: variant,
        signup_date: new Date().toISOString(),
        lead_score: getInitialLeadScore(source, variant)
      }
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    if (errorData.message && errorData.message.includes('already subscribed')) {
      throw new Error('already subscribed');
    }
    throw new Error('Erro ao adicionar à lista de email');
  }

  return await response.json();
}

// Salvar no banco de dados
async function saveNewsletterSubscription(data) {
  const { createClient } = require('@supabase/supabase-js');
  
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  const { error } = await supabase
    .from('newsletter_subscribers')
    .insert([{
      ...data,
      status: 'active',
      lead_score: getInitialLeadScore(data.source, data.variant)
    }]);

  if (error) {
    console.error('Erro ao salvar newsletter no banco:', error);
    throw error;
  }
}

// Enviar email de boas-vindas
async function sendWelcomeEmail(email, source) {
  const emailTemplates = {
    blog: {
      subject: '🎉 Bem-vindo à comunidade Game Day Nexus!',
      template: 'welcome-blog'
    },
    exit_intent_popup: {
      subject: '📚 Obrigado por se juntar a nós!',
      template: 'welcome-popup'
    },
    sidebar: {
      subject: '⚽ Vamos revolucionar a gestão do seu clube!',
      template: 'welcome-sidebar'
    }
  };

  const emailData = emailTemplates[source] || emailTemplates.blog;

  // Implementar envio de email (SendGrid, Resend, etc.)
  console.log(`Enviando email de boas-vindas para ${email}:`, emailData);
  
  // Exemplo com SendGrid
  /*
  const sgMail = require('@sendgrid/mail');
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);

  const msg = {
    to: email,
    from: '<EMAIL>',
    subject: emailData.subject,
    templateId: emailData.template,
    dynamicTemplateData: {
      email: email,
      source: source
    }
  };

  await sgMail.send(msg);
  */
}

// Enviar kit inicial de planilhas
async function sendStarterKit(email) {
  const kitItems = [
    {
      name: 'Planilha de Fluxo de Caixa',
      url: '/downloads/planilha-fluxo-caixa.xlsx'
    },
    {
      name: 'Template de Prontuário Médico',
      url: '/downloads/template-prontuario-medico.pdf'
    },
    {
      name: 'Checklist de Convocação',
      url: '/downloads/checklist-convocacao.pdf'
    },
    {
      name: 'Calculadora de Minutagem',
      url: '/downloads/calculadora-minutagem.xlsx'
    },
    {
      name: 'Controle de Estoque',
      url: '/downloads/controle-estoque-materiais.xlsx'
    }
  ];

  // Implementar envio do kit
  console.log(`Enviando kit inicial para ${email}:`, kitItems);
}

// Calcular lead score inicial
function getInitialLeadScore(source, variant) {
  const scores = {
    blog: 10,
    exit_intent_popup: 5,
    sidebar: 7,
    blog_inline: 15,
    lead_magnet: 20
  };

  return scores[source] || 5;
}

// Função para criar tabela no Supabase
export async function createNewsletterTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS newsletter_subscribers (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      source VARCHAR(100) NOT NULL,
      variant VARCHAR(50),
      status VARCHAR(20) DEFAULT 'active',
      lead_score INTEGER DEFAULT 5,
      timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      ip VARCHAR(45),
      user_agent TEXT,
      unsubscribed_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscribers(email);
    CREATE INDEX IF NOT EXISTS idx_newsletter_status ON newsletter_subscribers(status);
    CREATE INDEX IF NOT EXISTS idx_newsletter_source ON newsletter_subscribers(source);
    CREATE INDEX IF NOT EXISTS idx_newsletter_date ON newsletter_subscribers(timestamp);
    CREATE INDEX IF NOT EXISTS idx_newsletter_lead_score ON newsletter_subscribers(lead_score);

    -- Trigger para atualizar updated_at
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    CREATE TRIGGER update_newsletter_subscribers_updated_at 
      BEFORE UPDATE ON newsletter_subscribers 
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  `;
  
  return sql;
}

// Endpoint para unsubscribe
export async function handleUnsubscribe(req, res) {
  const { email, token } = req.query;

  if (!email || !token) {
    return res.status(400).json({ message: 'Email e token são obrigatórios' });
  }

  try {
    // Verificar token (implementar validação de segurança)
    const isValidToken = await validateUnsubscribeToken(email, token);
    if (!isValidToken) {
      return res.status(400).json({ message: 'Token inválido' });
    }

    // Atualizar status no banco
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.VITE_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    const { error } = await supabase
      .from('newsletter_subscribers')
      .update({ 
        status: 'unsubscribed',
        unsubscribed_at: new Date().toISOString()
      })
      .eq('email', email);

    if (error) throw error;

    // Remover do ConvertKit também
    await removeFromConvertKit(email);

    res.status(200).json({ 
      success: true, 
      message: 'Unsubscribe realizado com sucesso' 
    });

  } catch (error) {
    console.error('Erro no unsubscribe:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erro interno' 
    });
  }
}

async function validateUnsubscribeToken(email, token) {
  // Implementar validação de token de segurança
  // Por exemplo, usando JWT ou hash baseado em email + secret
  return true; // Placeholder
}

async function removeFromConvertKit(email) {
  // Implementar remoção do ConvertKit
  console.log(`Removendo ${email} do ConvertKit`);
}