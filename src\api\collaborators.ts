import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { v4 as uuidv4 } from "uuid";
import { COLLABORATOR_PERMISSIONS } from "@/constants/permissions";
import { getDepartmentTypeById, getJobFunctionById } from "./departmentTypes";

// Types
export type Collaborator = {
  id: number;
  club_id: number;
  user_id?: string;
  registration_number: string;
  full_name: string;
  nickname?: string;
  birth_date?: string;
  phone?: string;
  role: string;
  role_type: 'technical' | 'assistant_technical';
  cpf?: string;
  zip_code?: string;
  state?: string;
  city?: string;
  address?: string;
  address_number?: string;
  credential_number?: string;
  email?: string;
  created_at: string;
  updated_at: string;
  // New fields
  image?: string;
  salary?: number;
  bonus?: number;
  bank_info?: {
    bank_name?: string;
    account_number?: string;
    agency?: string;
    pix?: string;
  };
  entry_date?: string;
  status?: string;
  document_id?: string;
  document_id_url?: string;
  certificate_url?: string;
  medical_certificate_url?: string;
  resume_url?: string;
  criminal_record_url?: string;
  // Department and function fields
  department_type_id?: number;
  job_function_id?: number;
  department_name?: string; // For joins
  job_function_name?: string; // For joins
};

export type CollaboratorDocument = {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: "pending" | "verified" | "rejected";
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string; // For joins
};

// Document types
export const COLLABORATOR_DOCUMENT_TYPES = [
  "identity",
  "certificate",
  "medical_certificate",
  "curriculum",
  "criminal_record",
];

export const COLLABORATOR_DOCUMENT_LABELS: Record<string, string> = {
  identity: "Documento de Registro",
  certificate: "Certificado",
  medical_certificate: "Atestado Médico",
  curriculum: "Currículo",
  criminal_record: "Antecedentes Criminais",
};

/**
 * Get all collaborators for a club
 * @param clubId Club ID
 * @returns List of collaborators
 */
export async function getCollaborators(clubId: number): Promise<Collaborator[]> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .select("*")
      .eq("club_id", clubId)
      .order("full_name");

    if (error) {
      throw new Error(`Error fetching collaborators: ${error.message}`);
    }

    const collaborators: Collaborator[] = data || [];

    await Promise.all(
      collaborators.map(async (c) => {
        if (c.department_type_id) {
          try {
            const dept = await getDepartmentTypeById(clubId, c.department_type_id);
            c.department_name = dept?.name || null;
          } catch {
            c.department_name = null;
          }
        }
        if (c.job_function_id) {
          try {
            const job = await getJobFunctionById(clubId, c.job_function_id);
            c.job_function_name = job?.name || null;
          } catch {
            c.job_function_name = null;
          }
        }
      })
    );

    return collaborators;
  } catch (error: any) {
    console.error("Error fetching collaborators:", error);
    throw new Error(`Error fetching collaborators: ${error.message}`);
  }
}

/**
 * Get a collaborator by ID
 * @param clubId Club ID
 * @param id Collaborator ID
 * @returns Collaborator
 */
export async function getCollaboratorById(clubId: number, id: number): Promise<Collaborator> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (error) {
      throw new Error(`Error fetching collaborator: ${error.message}`);
    }

    const collaborator: Collaborator = data as Collaborator;

    if (collaborator.department_type_id) {
      try {
        const dept = await getDepartmentTypeById(clubId, collaborator.department_type_id);
        collaborator.department_name = dept?.name || null;
      } catch {
        collaborator.department_name = null;
      }
    }

    if (collaborator.job_function_id) {
      try {
        const job = await getJobFunctionById(clubId, collaborator.job_function_id);
        collaborator.job_function_name = job?.name || null;
      } catch {
        collaborator.job_function_name = null;
      }
    }

    return collaborator;
  } catch (error: any) {
    console.error("Error fetching collaborator:", error);
    throw new Error(`Error fetching collaborator: ${error.message}`);
  }
}

export async function getCollaboratorsByIds(clubId: number, ids: Array<number | string>): Promise<Collaborator[]> {
  if (ids.length === 0) return [];
  try {
    const { data, error } = await supabase
      .from('collaborators')
      .select('*')
      .eq('club_id', clubId)
      .in('id', ids);

    if (error) {
      throw new Error(`Error fetching collaborators: ${error.message}`);
    }

    const collaborators: Collaborator[] = (data as Collaborator[]) || [];
    return collaborators;
  } catch (error: any) {
    console.error('Error fetching collaborators by ids:', error);
    throw new Error(error.message || 'Error fetching collaborators');
  }
}

/**
 * Get a collaborator record using the associated user ID
 * @param clubId Club ID
 * @param userId User ID
 * @returns Collaborator or null if not found
 */
export async function getCollaboratorByUserId(
  clubId: number,
  userId: string
): Promise<Collaborator | null> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .select("*")
      .eq("club_id", clubId)
      .eq("user_id", userId)
      .maybeSingle();

    if (error) {
      throw new Error(`Error fetching collaborator: ${error.message}`);
    }

    if (!data) return null;

    const collaborator: Collaborator = data as Collaborator;

    if (collaborator.department_type_id) {
      try {
        const dept = await getDepartmentTypeById(clubId, collaborator.department_type_id);
        collaborator.department_name = dept?.name || null;
      } catch {
        collaborator.department_name = null;
      }
    }

    if (collaborator.job_function_id) {
      try {
        const job = await getJobFunctionById(clubId, collaborator.job_function_id);
        collaborator.job_function_name = job?.name || null;
      } catch {
        collaborator.job_function_name = null;
      }
    }

    return collaborator;
  } catch (error: any) {
    console.error("Error fetching collaborator by user:", error);
    throw new Error(error.message || "Error fetching collaborator by user");
  }
}

/**
 * Create a new collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param collaborator Collaborator data
 * @returns Created collaborator
 */
export async function createCollaborator(
  clubId: number,
  userId: string,
  collaborator: Omit<Collaborator, "id" | "club_id" | "created_at" | "updated_at" | "registration_number">,
  collaboratorUserId?: string
): Promise<Collaborator> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.create",
        { name: collaborator.full_name, role: collaborator.role },
        async () => {
          try {
            const { data, error } = await supabase
              .from("collaborators")
              .insert({
                club_id: clubId,
                ...collaborator,
                user_id: collaborator.user_id ?? collaboratorUserId,
              })
              .select()
              .single();

            if (error) {
              console.error("Error creating collaborator:", error);
              throw new Error(`Error creating collaborator: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error creating collaborator:", error);
            throw new Error(error.message || "Error creating collaborator");
          }
        }
      );
    }
  );
}

/**
 * Update a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param id Collaborator ID
 * @param collaborator Collaborator data
 * @returns Updated collaborator
 */
export async function updateCollaborator(
  clubId: number,
  userId: string,
  id: number,
  collaborator: Partial<Omit<Collaborator, "id" | "club_id" | "created_at" | "updated_at" | "registration_number">>
): Promise<Collaborator> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.update",
        { id, ...collaborator },
        async () => {
          try {
            // Verificar se o status está sendo alterado
            const statusChanged = typeof collaborator.status !== 'undefined';
            let oldStatus = null;
            let statusChangedToInactive = false;
            let statusChangedFromInactive = false;

            // Buscar o colaborador atual para comparar o status
            if (statusChanged) {
              const { data: currentCollaborator, error: fetchError } = await supabase
                .from("collaborators")
                .select("status")
                .eq("club_id", clubId)
                .eq("id", id)
                .single();

              if (fetchError) {
                console.error("Erro ao buscar status atual do colaborador:", fetchError);
                throw new Error(`Erro ao buscar status atual do colaborador: ${fetchError.message}`);
              }
              
              oldStatus = currentCollaborator?.status;
              statusChangedToInactive = collaborator.status === 'inactive' && oldStatus !== 'inactive';
              statusChangedFromInactive = oldStatus === 'inactive' && collaborator.status !== 'inactive';
            }

            // Preparar os dados para atualização
            const updateData = {
              ...collaborator,
              updated_at: new Date().toISOString(),
            };

            // Garantir que o ID seja tratado como número
            const { data, error } = await supabase
              .from("collaborators")
              .update(updateData)
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              console.error("Erro ao atualizar colaborador:", error);
              throw new Error(`Erro ao atualizar colaborador: ${error.message}`);
            }

            if (!data) {
              throw new Error("Nenhum dado retornado ao atualizar colaborador");
            }

            // Se o status foi alterado para 'inactive', executar limpeza de vinculações
            if (statusChangedToInactive && oldStatus !== 'inactive') {
              try {
                console.log(`Colaborador ${id} alterado para status 'inactive'. Executando limpeza de vinculações...`);

                // Chamar a função SQL para remover vinculações
                const { error: cleanupError } = await supabase
                  .rpc('remove_collaborator_associations', {
                    p_club_id: clubId,
                    p_collaborator_id: id
                  });

                if (cleanupError) {
                  console.error("Erro ao executar limpeza de vinculações do colaborador:", cleanupError);
                  // Não lançamos erro aqui para não interromper o fluxo principal
                } else {
                  console.log(`Vinculações removidas com sucesso para o colaborador ${id}`);
                }
              } catch (cleanupError) {
                console.error("Erro ao executar limpeza de vinculações do colaborador:", cleanupError);
              }
            } 
            // Se o status foi alterado de 'inactive' para outro valor, o trigger no banco de dados
            // irá restaurar automaticamente as permissões e dados financeiros
            else if (statusChangedFromInactive) {
              console.log(`Colaborador ${id} alterado de 'inactive' para '${collaborator.status}'. O trigger do banco de dados irá restaurar as permissões.`);
            }

            return data as Collaborator;
          } catch (error: any) {
            console.error("Erro ao atualizar colaborador:", error);
            throw new Error(error.message || "Erro ao atualizar colaborador");
          }
        }
      );
    }
  );
}

/**
 * Link an existing user account to a collaborator
 * @param clubId Club ID
 * @param userId ID of the admin performing the action
 * @param collaboratorId Collaborator ID
 * @param targetUserId User ID to link
 * @returns Success status
 */
export async function linkCollaboratorToUser(
  clubId: number,
  userId: string,
  collaboratorId: number,
  targetUserId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.link_user",
        { collaboratorId, targetUserId },
        async () => {
          try {
            const { error } = await supabase
              .from("collaborators")
              .update({ user_id: targetUserId })
              .eq("club_id", clubId)
              .eq("id", collaboratorId);
            if (error) {
              throw new Error(`Erro ao vincular usuário: ${error.message}`);
            }
            return true;
          } catch (error: any) {
            console.error("Erro ao vincular usuário ao colaborador:", error);
            throw new Error(error.message || "Erro ao vincular usuário");
          }
        }
      );
    }
  );
}

/**
 * Delete a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param id Collaborator ID
 * @returns Success status
 */
export async function deleteCollaborator(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.delete",
        { id },
        async () => {
          try {
            // First, delete all documents associated with this collaborator
            await supabase
              .from("collaborator_documents")
              .delete()
              .eq("club_id", clubId)
              .eq("collaborator_id", id);

            // Then delete the collaborator
            const { error } = await supabase
              .from("collaborators")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              console.error("Error deleting collaborator:", error);
              throw new Error(`Error deleting collaborator: ${error.message}`);
            }

            return true;
          } catch (error: any) {
            console.error("Error deleting collaborator:", error);
            throw new Error(error.message || "Error deleting collaborator");
          }
        }
      );
    }
  );
}

/**
 * Upload a document for a collaborator
 * @param clubId Club ID
 * @param userId User ID
 * @param collaboratorId Collaborator ID
 * @param documentType Document type
 * @param file Document file
 * @returns Uploaded document
 */
export async function uploadCollaboratorDocument(
  clubId: number,
  userId: string,
  collaboratorId: number,
  documentType: string,
  file: File
): Promise<CollaboratorDocument> {
  return withPermission(
    clubId,
    userId,
    COLLABORATOR_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "collaborator.upload_document",
        { collaboratorId, documentType },
        async () => {
          try {
            // Upload the file to storage
            const fileUrl = await uploadCollaboratorDocumentToStorage(clubId, collaboratorId, documentType, file);

            // Register the document in the database
            const { data, error } = await supabase
              .from("collaborator_documents")
              .insert({
                club_id: clubId,
                collaborator_id: collaboratorId,
                document_type: documentType,
                file_url: fileUrl,
                status: "pending",
              })
              .select()
              .single();

            if (error) {
              console.error("Error registering collaborator document:", error);
              throw new Error(`Error registering collaborator document: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error uploading collaborator document:", error);
            throw new Error(error.message || "Error uploading collaborator document");
          }
        }
      );
    }
  );
}

/**
 * Upload a document file to storage
 * @param clubId Club ID
 * @param collaboratorId Collaborator ID
 * @param documentType Document type
 * @param file Document file
 * @returns File URL
 */
async function uploadCollaboratorDocumentToStorage(
  clubId: number,
  collaboratorId: number,
  documentType: string,
  file: File
): Promise<string> {
  try {
    // Limit file size (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_SIZE) {
      throw new Error("Document must be at most 5MB");
    }

    // Generate unique file name
    const fileExt = file.name.split(".").pop();
    const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
    const filePath = `${clubId}/collaborators/${collaboratorId}/${fileName}`;

    // Upload file with metadata
    const { error } = await supabase.storage
      .from("playerdocuments") // Using the same bucket as player documents
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
        metadata: {
          club_id: clubId.toString(),
          collaborator_id: collaboratorId.toString(),
          document_type: documentType,
        },
      });

    if (error) {
      throw new Error(`Error uploading document: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from("playerdocuments")
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error: any) {
    console.error("Error uploading document to storage:", error);
    throw new Error(error.message || "Error uploading document to storage");
  }
}

/**
 * Get collaborators by department type
 * @param clubId Club ID
 * @param departmentTypeId Department type ID
 * @returns List of collaborators in the department
 */
export async function getCollaboratorsByDepartment(
  clubId: number,
  departmentTypeId: number
): Promise<Collaborator[]> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .select("*")
      .eq("club_id", clubId)
      .eq("department_type_id", departmentTypeId)
      .order("full_name");

    if (error) {
      throw new Error(`Error fetching collaborators by department: ${error.message}`);
    }

    const collaborators: Collaborator[] = data || [];

    await Promise.all(
      collaborators.map(async (c) => {
        if (c.department_type_id) {
          try {
            const dept = await getDepartmentTypeById(clubId, c.department_type_id);
            c.department_name = dept?.name || null;
          } catch {
            c.department_name = null;
          }
        }
        if (c.job_function_id) {
          try {
            const job = await getJobFunctionById(clubId, c.job_function_id);
            c.job_function_name = job?.name || null;
          } catch {
            c.job_function_name = null;
          }
        }
      })
    );

    return collaborators;
  } catch (error: any) {
    console.error("Error fetching collaborators by department:", error);
    throw new Error(`Error fetching collaborators by department: ${error.message}`);
  }
}

/**
 * Get documents for a collaborator
 * @param clubId Club ID
 * @param collaboratorId Collaborator ID
 * @returns List of documents
 */
export async function getCollaboratorDocuments(
  clubId: number,
  collaboratorId: number
): Promise<CollaboratorDocument[]> {
  try {
    // Primeiro, buscar os documentos sem o join
    const { data, error } = await supabase
      .from("collaborator_documents")
      .select("*")
      .eq("club_id", clubId)
      .eq("collaborator_id", collaboratorId)
      .order("document_type");

    if (error) {
      throw new Error(`Error fetching documents: ${error.message}`);
    }

    // Se não houver documentos, retornar array vazio
    if (!data || data.length === 0) {
      return [];
    }

    // Para cada documento que tem um verificador, buscar o nome do verificador
    const documentsWithVerifierInfo = await Promise.all(
      data.map(async (doc) => {
        let verifierName = null;

        // Se o documento tiver sido verificado, buscar o nome do verificador
        if (doc.verified_by) {
          try {
            const { data: userData, error: userError } = await supabase
              .from("users")
              .select("name")
              .eq("id", doc.verified_by)
              .single();

            if (!userError && userData) {
              verifierName = userData.name;
            }
          } catch (err) {
            console.error("Error fetching verifier name:", err);
          }
        }

        return {
          ...doc,
          verifier_name: verifierName
        };
      })
    );

    return documentsWithVerifierInfo;
  } catch (error: any) {
    console.error("Error fetching collaborator documents:", error);
    throw new Error(error.message || "Error fetching collaborator documents");
  }
}