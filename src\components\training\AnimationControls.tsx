import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  RotateCcw,
  FastForward,
  Rewind,
  Camera,
  Video,
  Download,
  Settings,
  Layers,
  Eye,
  EyeOff,
  Zap,
  Route,
  Timer
} from 'lucide-react';
import { useAnimationEngine } from '@/hooks/useAnimationEngine';
import { TrainingElement, DrawingElement } from './InteractiveTrainingBuilder';
import { Trajectory } from './TrajectorySystem';

interface AnimationControlsProps {
  elements: TrainingElement[];
  drawings: DrawingElement[];
  trajectories: Trajectory[];
  onElementsUpdate?: (elements: TrainingElement[]) => void;
  onExportAnimation?: () => void;
  onCaptureFrame?: () => void;
}

export function AnimationControls({
  elements,
  drawings,
  trajectories,
  onElementsUpdate,
  onExportAnimation,
  onCaptureFrame
}: AnimationControlsProps) {
  const [showTrajectories, setShowTrajectories] = useState(true);
  const [showTimeline, setShowTimeline] = useState(true);
  const [animationQuality, setAnimationQuality] = useState<'low' | 'medium' | 'high' | 'ultra'>('high');
  const [recordingMode, setRecordingMode] = useState(false);
  const [keyframes, setKeyframes] = useState<number[]>([0, 30, 60, 90]);

  // Initialize animation engine
  const animationEngine = useAnimationEngine({
    settings: {
      fps: 30,
      duration: 10000,
      quality: animationQuality,
      smoothing: true,
      interpolation: 'ease'
    },
    onFrameChange: (frame) => {
      // Update elements based on current frame
      const currentFrame = animationEngine.getCurrentFrame();
      if (currentFrame && onElementsUpdate) {
        onElementsUpdate(currentFrame.elements);
      }
    },
    onPlayStateChange: (playing) => {
      // Handle play state changes if needed
    },
    onComplete: () => {
      // Handle animation completion
    }
  });

  const formatTime = (frame: number, fps = 30) => {
    const seconds = frame / fps;
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const frameNum = frame % fps;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${frameNum.toString().padStart(2, '0')}`;
  };

  const handleFrameSliderChange = (value: number[]) => {
    animationEngine.seek(value[0]);
  };

  const handleSpeedChange = (value: string) => {
    animationEngine.setSpeed(parseFloat(value));
  };

  const handleAddKeyframe = () => {
    const currentFrame = animationEngine.currentFrame;
    const frameId = animationEngine.addFrame(elements, drawings, trajectories);
    setKeyframes(prev => [...prev, currentFrame].sort((a, b) => a - b));
  };

  const handleCaptureFrameInternal = () => {
    const frameId = animationEngine.addFrame(elements, drawings, trajectories);
    onCaptureFrame?.();
  };

  const handleQualityChange = (quality: 'low' | 'medium' | 'high' | 'ultra') => {
    setAnimationQuality(quality);
    animationEngine.updateSettings({ quality });
  };

  const handleLoopChange = (loop: boolean) => {
    animationEngine.setLoop(loop);
  };

  const speedOptions = [
    { value: '0.25', label: '0.25x' },
    { value: '0.5', label: '0.5x' },
    { value: '1', label: '1x' },
    { value: '1.5', label: '1.5x' },
    { value: '2', label: '2x' },
    { value: '3', label: '3x' },
    { value: '5', label: '5x' },
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Video className="h-4 w-4" />
          Controles de Animação
        </CardTitle>
        <CardDescription className="text-xs">
          Reproduza e exporte animações dos drills
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Timeline */}
        {showTimeline && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span>{formatTime(animationEngine.currentFrame)}</span>
              <span>{formatTime(animationEngine.totalFrames)}</span>
            </div>
            <Slider
              value={[animationEngine.currentFrame]}
              onValueChange={handleFrameSliderChange}
              max={animationEngine.totalFrames}
              min={0}
              step={1}
              className="w-full"
            />
            <Progress value={animationEngine.progress} className="h-1" />
          </div>
        )}

        {/* Controles principais */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => animationEngine.seek(0)}
          >
            <Rewind className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => animationEngine.seek(Math.max(0, animationEngine.currentFrame - 1))}
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          
          <Button
            variant={animationEngine.isPlaying ? "secondary" : "default"}
            size="icon"
            className="h-10 w-10"
            onClick={() => animationEngine.isPlaying ? animationEngine.pause() : animationEngine.play()}
          >
            {animationEngine.isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => animationEngine.seek(Math.min(animationEngine.totalFrames, animationEngine.currentFrame + 1))}
          >
            <SkipForward className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => animationEngine.seek(animationEngine.totalFrames)}
          >
            <FastForward className="h-4 w-4" />
          </Button>
        </div>

        {/* Configurações de reprodução */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Velocidade</Label>
            <Select value={animationEngine.playbackState.speed.toString()} onValueChange={handleSpeedChange}>
              <SelectTrigger className="text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {speedOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label className="text-xs">Qualidade</Label>
            <Select value={animationQuality} onValueChange={handleQualityChange}>
              <SelectTrigger className="text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Baixa</SelectItem>
                <SelectItem value="medium">Média</SelectItem>
                <SelectItem value="high">Alta</SelectItem>
                <SelectItem value="ultra">Ultra</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Opções de visualização */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Visualização</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={showTrajectories ? "default" : "outline"}
              size="sm"
              onClick={() => setShowTrajectories(!showTrajectories)}
              className="text-xs"
            >
              <Route className="h-3 w-3 mr-1" />
              Trajetórias
            </Button>
            
            <Button
              variant={showTimeline ? "default" : "outline"}
              size="sm"
              onClick={() => setShowTimeline(!showTimeline)}
              className="text-xs"
            >
              <Timer className="h-3 w-3 mr-1" />
              Timeline
            </Button>
            
            <Button
              variant={animationEngine.playbackState.loop ? "default" : "outline"}
              size="sm"
              onClick={() => handleLoopChange(!animationEngine.playbackState.loop)}
              className="text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Loop
            </Button>
            
            <Button
              variant={recordingMode ? "destructive" : "outline"}
              size="sm"
              onClick={() => setRecordingMode(!recordingMode)}
              className="text-xs"
            >
              <Camera className="h-3 w-3 mr-1" />
              {recordingMode ? 'Gravando' : 'Gravar'}
            </Button>
          </div>
        </div>

        {/* Ações de exportação */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Exportar</Label>
          <div className="grid grid-cols-1 gap-2">
            <Button variant="outline" size="sm" onClick={onCaptureFrame}>
              <Camera className="h-3 w-3 mr-1" />
              Capturar Frame
            </Button>
            
            <Button variant="outline" size="sm" onClick={onExportAnimation}>
              <Video className="h-3 w-3 mr-1" />
              Exportar Vídeo
            </Button>
            
            <Button variant="outline" size="sm">
              <Download className="h-3 w-3 mr-1" />
              Exportar GIF
            </Button>
          </div>
        </div>

        {/* Keyframes */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Keyframes</Label>
            <Button variant="outline" size="sm" className="text-xs" onClick={handleAddKeyframe}>
              <Zap className="h-3 w-3 mr-1" />
              Adicionar
            </Button>
          </div>
          
          <div className="space-y-1 max-h-24 overflow-y-auto">
            {keyframes.map((frame, index) => (
              <div
                key={frame}
                className={`flex items-center justify-between p-2 rounded text-xs ${
                  animationEngine.currentFrame === frame ? 'bg-primary/10 border border-primary' : 'bg-muted/50'
                }`}
              >
                <span>Frame {frame}</span>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4"
                    onClick={() => animationEngine.seek(frame)}
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Estatísticas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="font-medium">Frame atual:</span>
              <span className="ml-1">{animationEngine.currentFrame}/{animationEngine.totalFrames}</span>
            </div>
            <div>
              <span className="font-medium">Duração:</span>
              <span className="ml-1">{formatTime(animationEngine.totalFrames)}</span>
            </div>
            <div>
              <span className="font-medium">FPS:</span>
              <span className="ml-1">{animationEngine.getSettings().fps}</span>
            </div>
            <div>
              <span className="font-medium">Velocidade:</span>
              <span className="ml-1">{animationEngine.playbackState.speed}x</span>
            </div>
          </div>
        </div>

        {/* Dicas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dicas:</strong> Use as setas do teclado para navegar frame por frame. 
            Pressione espaço para play/pause.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
