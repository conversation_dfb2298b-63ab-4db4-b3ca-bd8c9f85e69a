import React, { useState, useEffect } from 'react';
import { X, Building2, Save } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  updateMasterClub, 
  changeClubPlan,
  UpdateClubData, 
  MasterClub, 
  MasterPlan 
} from '@/api/masterClubs';

interface EditClubModalProps {
  club: MasterClub;
  plans: MasterPlan[];
  onClose: () => void;
  onSuccess: () => void;
}

export const EditClubModal: React.FC<EditClubModalProps> = ({
  club,
  plans,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<UpdateClubData>({
    name: club.name,
    email: club.email || '',
    phone: club.phone || '',
    document: club.document || '',
    address: club.address || {},
    notes: club.notes || ''
  });
  const [selectedPlanId, setSelectedPlanId] = useState(club.master_plan_id || 0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const currentPlan = plans.find(p => p.id === club.master_plan_id);
  const newPlan = plans.find(p => p.id === selectedPlanId);
  const planChanged = selectedPlanId !== club.master_plan_id;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name) {
      setError('Nome é obrigatório');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Atualizar dados básicos do clube
      await updateMasterClub(club.id, formData);

      // Se o plano mudou, alterar o plano
      if (planChanged && selectedPlanId > 0) {
        await changeClubPlan(club.id, selectedPlanId, 'Alteração via painel master');
      }

      onSuccess();
    } catch (err: any) {
      setError(err.message || 'Erro ao atualizar clube');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateClubData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case 'trial':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspenso</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-2">
            <Building2 className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold">Editar Clube</h2>
              <p className="text-sm text-gray-500">{club.name}</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="p-6">
          <Tabs defaultValue="basic" className="space-y-6">
            <TabsList>
              <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
              <TabsTrigger value="plan">Plano</TabsTrigger>
              <TabsTrigger value="status">Status</TabsTrigger>
            </TabsList>

            <form onSubmit={handleSubmit}>
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              <TabsContent value="basic" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Nome do Clube *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="document">CNPJ</Label>
                    <Input
                      id="document"
                      value={formData.document}
                      onChange={(e) => handleInputChange('document', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Observações</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={4}
                  />
                </div>
              </TabsContent>

              <TabsContent value="plan" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Plano atual */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Plano Atual</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {currentPlan ? (
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{currentPlan.name}</span>
                            <Badge variant={currentPlan.is_trial ? 'outline' : 'secondary'}>
                              {currentPlan.is_trial ? 'Trial' : 'Pago'}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-gray-600">{currentPlan.description}</p>
                          
                          <div className="text-lg font-bold text-blue-600">
                            {formatCurrency(currentPlan.price)}/{currentPlan.billing_cycle === 'yearly' ? 'ano' : 'mês'}
                          </div>
                          
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>Usuários: {currentPlan.max_users === -1 ? 'Ilimitado' : currentPlan.max_users}</div>
                            <div>Jogadores: {currentPlan.max_players === -1 ? 'Ilimitado' : currentPlan.max_players}</div>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-500">Nenhum plano definido</p>
                      )}
                    </CardContent>
                  </Card>

                  {/* Alterar plano */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Alterar Plano</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Select 
                          value={selectedPlanId.toString()} 
                          onValueChange={(value) => setSelectedPlanId(parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Escolha um plano" />
                          </SelectTrigger>
                          <SelectContent>
                            {plans.map(plan => (
                              <SelectItem key={plan.id} value={plan.id.toString()}>
                                <div className="flex items-center justify-between w-full">
                                  <span>{plan.name}</span>
                                  <span className="ml-2 text-sm text-gray-500">
                                    {formatCurrency(plan.price)}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        {planChanged && newPlan && (
                          <div className="p-3 bg-blue-50 rounded-lg">
                            <h4 className="font-medium text-blue-900 mb-2">Novo Plano: {newPlan.name}</h4>
                            <p className="text-sm text-blue-700 mb-2">{newPlan.description}</p>
                            <div className="text-sm text-blue-800">
                              <strong>{formatCurrency(newPlan.price)}</strong>/{newPlan.billing_cycle === 'yearly' ? 'ano' : 'mês'}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="status" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Status da Assinatura</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>Status:</span>
                        {getStatusBadge(club.subscription_status)}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span>Início:</span>
                        <span>{club.subscription_start_date ? formatDate(club.subscription_start_date) : '-'}</span>
                      </div>
                      
                      {club.is_trial && (
                        <>
                          <div className="flex items-center justify-between">
                            <span>Trial até:</span>
                            <span>{club.trial_end_date ? formatDate(club.trial_end_date) : '-'}</span>
                          </div>
                        </>
                      )}
                      
                      {club.suspended_at && (
                        <div className="p-3 bg-red-50 rounded-lg">
                          <p className="text-sm text-red-800">
                            <strong>Suspenso em:</strong> {formatDate(club.suspended_at)}
                          </p>
                          {club.suspension_reason && (
                            <p className="text-sm text-red-700 mt-1">
                              <strong>Motivo:</strong> {club.suspension_reason}
                            </p>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Status do Pagamento</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>Status:</span>
                        <Badge className={
                          club.payment_status === 'current' ? 'bg-green-100 text-green-800' :
                          club.payment_status === 'overdue' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {club.payment_status === 'current' ? 'Em Dia' :
                           club.payment_status === 'overdue' ? 'Em Atraso' : 'Cancelado'}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span>Último Pagamento:</span>
                        <span>{club.last_payment_date ? formatDate(club.last_payment_date) : '-'}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span>Próximo Vencimento:</span>
                        <span>{club.next_payment_date ? formatDate(club.next_payment_date) : '-'}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span>Período de Graça:</span>
                        <span>{club.grace_period_days} dias</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Botões */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Salvando...
                    </div>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Salvar Alterações
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Tabs>
        </div>
      </div>
    </div>
  );
};