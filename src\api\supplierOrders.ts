import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { SUPPLIER_PERMISSIONS } from "@/constants/permissions";
import { createFinancialAccount, deleteFinancialAccount } from "./accounts";

// Types
export type SupplierOrder = {
  id: number;
  club_id: number;
  supplier_id: number;
  description: string;
  purchase_date: string;
  amount: number;
  financial_account_id?: number;
  created_at: string;
  updated_at: string;
  // Additional fields from view
  supplier_name?: string;
  account_description?: string;
  account_status?: string;
  account_due_date?: string;
};

/**
 * Get all orders for a supplier
 * @param clubId Club ID
 * @param supplierId Supplier ID
 * @returns List of supplier orders
 */
export async function getSupplierOrders(
  clubId: number,
  supplierId: number,
  month?: number,
  year?: number
): Promise<SupplierOrder[]> {
  let query = supabase
    .from("supplier_orders_view")
    .select("*")
    .eq("club_id", clubId)
    .eq("supplier_id", supplierId);

  if (month && year) {
    const startDate = new Date(Date.UTC(year, month - 1, 1))
      .toISOString()
      .split("T")[0];
    const endDate = new Date(Date.UTC(year, month, 0)).toISOString().split("T")[0];
    query = query.gte("purchase_date", startDate).lte("purchase_date", endDate);
  }

  const { data, error } = await query.order("purchase_date", { ascending: false });

  if (error) {
    console.error("Error fetching supplier orders:", error);
    throw new Error(`Error fetching supplier orders: ${error.message}`);
  }

  return data || [];
}

/**
 * Get all orders for a club
 * @param clubId Club ID
 * @returns List of supplier orders
 */
export async function getAllSupplierOrders(
  clubId: number
): Promise<SupplierOrder[]> {
  const { data, error } = await supabase
    .from("supplier_orders_view")
    .select("*")
    .eq("club_id", clubId)
    .order("purchase_date", { ascending: false });

  if (error) {
    console.error("Error fetching all supplier orders:", error);
    throw new Error(`Error fetching all supplier orders: ${error.message}`);
  }

  return data || [];
}

/**
 * Create a new supplier order
 * @param clubId Club ID
 * @param userId User ID
 * @param order Order data
 * @param createAccount Whether to create a financial account for this order
 * @param dueDate Due date for the financial account (if creating one)
 * @returns Created order
 */
export async function createSupplierOrder(
  clubId: number,
  userId: string,
  order: Omit<SupplierOrder, "id" | "club_id" | "created_at" | "updated_at" | "financial_account_id" | "supplier_name" | "account_description" | "account_status" | "account_due_date">,
  createAccount: boolean = true,
  dueDate?: string
): Promise<SupplierOrder> {
  return withPermission(
    clubId,
    userId,
    SUPPLIER_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "supplier.order.create",
        { supplier_id: order.supplier_id, description: order.description },
        async () => {
          try {
            // Get supplier name for the financial account
            const { data: supplierData, error: supplierError } = await supabase
              .from("suppliers")
              .select("company_name")
              .eq("id", order.supplier_id)
              .single();

            if (supplierError) {
              console.error("Error fetching supplier:", supplierError);
              throw new Error(`Error fetching supplier: ${supplierError.message}`);
            }

            let financialAccountId = null;

            // Create financial account if requested
            if (createAccount) {
              const accountDueDate = dueDate || new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0];
              
              const account = await createFinancialAccount(clubId, {
                description: `Pedido: ${order.description}`,
                type: "a_pagar",
                supplier_client: supplierData.company_name,
                creation_date: order.purchase_date,
                due_date: accountDueDate,
                amount: order.amount,
                status: "pendente",
                category: "fornecedores",
              });

              financialAccountId = account.id;
            }

            // Create the order
            const { data, error } = await supabase
              .from("supplier_orders")
              .insert({
                club_id: clubId,
                supplier_id: order.supplier_id,
                description: order.description,
                purchase_date: order.purchase_date,
                amount: order.amount,
                financial_account_id: financialAccountId,
              })
              .select()
              .single();

            if (error) {
              console.error("Error creating supplier order:", error);
              throw new Error(`Error creating supplier order: ${error.message}`);
            }

            return {
              ...data,
              supplier_name: supplierData.company_name,
            };
          } catch (error: any) {
            console.error("Error creating supplier order:", error);
            throw new Error(error.message || "Error creating supplier order");
          }
        }
      );
    }
  );
}

/**
 * Delete a supplier order
 * @param clubId Club ID
 * @param userId User ID
 * @param orderId Order ID
 * @returns Success status
 */
export async function deleteSupplierOrder(
  clubId: number,
  userId: string,
  orderId: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    SUPPLIER_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "supplier.order.delete",
        { id: orderId },
        async () => {
          try {
            // Get the order to check if it has a linked financial account
            const { data: orderData, error: orderError } = await supabase
              .from("supplier_orders")
              .select("financial_account_id")
              .eq("id", orderId)
              .eq("club_id", clubId)
              .single();

            if (orderError) {
              console.error("Error fetching supplier order:", orderError);
              throw new Error(`Error fetching supplier order: ${orderError.message}`);
            }

            // First, delete the order itself to remove the foreign key constraint
            const { error: deleteError } = await supabase
              .from("supplier_orders")
              .delete()
              .eq("club_id", clubId)
              .eq("id", orderId);

            if (deleteError) {
              console.error("Error deleting supplier order:", deleteError);
              throw new Error(`Error deleting supplier order: ${deleteError.message}`);
            }

            // If the order was deleted successfully and had a linked financial account, delete it now
            if (orderData && orderData.financial_account_id) {
              await deleteFinancialAccount(clubId, orderData.financial_account_id);
            }

            return true;
          } catch (error: any) {
            console.error("Error during supplier order deletion process:", error);
            // The error might come from deleteFinancialAccount, but the order is already deleted.
            // We re-throw to ensure the audit log marks it as a failure, but the state is partially changed.
            throw new Error(error.message || "Error deleting supplier order");
          }
        }
      );
    }
  );
}