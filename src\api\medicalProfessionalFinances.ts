import { supabase } from "@/integrations/supabase/client";

export type MedicalProfessionalFinancialEntry = {
  month: number;
  year: number;
  amount: number;
  description: string;
};

export type MedicalProfessionalBankingInfo = {
  bank_name?: string;
  account_number?: string;
  agency?: string;
  pix?: string;
};

export type MedicalProfessionalFinancialData = {
  [key: string]: MedicalProfessionalFinancialEntry[]; // Chave no formato "YYYY-MM"
};

/**
 * Obtém os dados financeiros de um profissional
 * @param clubId ID do clube
 * @param professionalId ID do profissional
 * @returns Dados financeiros do profissional
 */
export async function getMedicalProfessionalFinancialData(
  clubId: number,
  professionalId: number
): Promise<MedicalProfessionalFinancialData> {
  try {
    const { data: professional, error } = await supabase
      .from("medical_professionals")
      .select("financial_data")
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter dados financeiros: ${error.message}`);
    }

    // Garantir que financial_data é um objeto e está no formato correto
    const financialData = professional?.financial_data;
    
    // Se for nulo, indefinido ou não for um objeto, retorna um objeto vazio
    if (!financialData || typeof financialData !== 'object') {
      return {};
    }
    
    // Se for um array, converte para o formato esperado
    if (Array.isArray(financialData)) {
      console.warn('Dados financeiros em formato de array detectado. Convertendo para o formato esperado.');
      return {};
    }
    
    // Se for um objeto, garante que todos os valores são arrays
    const result: MedicalProfessionalFinancialData = {};
    Object.entries(financialData).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        result[key] = value.filter(entry => 
          entry && 
          typeof entry === 'object' && 
          'amount' in entry && 
          'description' in entry
        );
      }
    });
    
    return result;
  } catch (error: any) {
    console.error("Erro ao obter dados financeiros:", error);
    // Em caso de erro, retorna um objeto vazio para evitar quebras na UI
    return {};
  }
}

/**
 * Adiciona uma entrada financeira para um profissional
 * @param clubId ID do clube
 * @param professionalId ID do profissional
 * @param month Mês (1-12)
 * @param year Ano
 * @param amount Valor (positivo para receita, negativo para despesa)
 * @param description Descrição
 * @param category Categoria da transação (opcional, padrão: "salários")
 * @returns Dados financeiros atualizados
 */
export async function addMedicalProfessionalFinancialEntry(
  clubId: number,
  professionalId: number,
  month: number,
  year: number,
  amount: number,
  description: string,
  category: string = "salários"
): Promise<MedicalProfessionalFinancialData> {
  try {
    // Validar mês
    if (month < 1 || month > 12) {
      throw new Error("Mês inválido. Deve ser um número entre 1 e 12.");
    }

    // Obter dados financeiros atuais
    const financialData = await getMedicalProfessionalFinancialData(clubId, professionalId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Adicionar nova entrada
    const entries = financialData[key] || [];
    entries.push({
      month,
      year,
      amount,
      description,
    });

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Obter nome do profissional para a descrição da transação financeira
    const { data: professionalData, error: professionalError } = await supabase
      .from("medical_professionals")
      .select("name")
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .single();

    if (professionalError) {
      console.error("Erro ao buscar nome do profissional:", professionalError);
      throw new Error(`Erro ao buscar nome do profissional: ${professionalError.message}`);
    }

    const professionalName = professionalData.name;

    // Criar uma transação financeira geral
    // Usar o primeiro dia do mês especificado para a transação
    const transactionDate = new Date(Date.UTC(year, month - 1, 1)).toISOString().split('T')[0];
    const transactionType = amount < 0 ? "despesa" : "receita";
    const transactionCategory = category;
    const transactionDescription = description;

    console.log("Criando transação financeira:", {
      date: transactionDate,
      type: transactionType,
      category: transactionCategory,
      amount: Math.abs(amount),
      description: transactionDescription,
      medical_professional_id: professionalId
    });

    try {
      // Inserir na tabela financial_transactions
      const { error: transactionError } = await supabase
        .from("financial_transactions")
        .insert({
          club_id: clubId,
          date: transactionDate,
          type: transactionType,
          category: transactionCategory,
          amount: Math.abs(amount), // Valor absoluto
          description: transactionDescription,
          medical_professional_id: professionalId
        });

      if (transactionError) {
        console.error("Erro ao criar transação financeira:", transactionError);
      }
    } catch (err) {
      console.error("Exceção ao criar transação financeira:", err);
    }

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("medical_professionals")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao adicionar entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao adicionar entrada financeira:", error);
    throw new Error(error.message || "Erro ao adicionar entrada financeira");
  }
}

/**
 * Remove uma entrada financeira de um profissional
 * @param clubId ID do clube
 * @param professionalId ID do profissional
 * @param month Mês (1-12)
 * @param year Ano
 * @param index Índice da entrada a ser removida
 * @returns Dados financeiros atualizados
 */
export async function removeMedicalProfessionalFinancialEntry(
  clubId: number,
  professionalId: number,
  month: number,
  year: number,
  index: number
): Promise<MedicalProfessionalFinancialData> {
  try {
    // Obter dados financeiros atuais
    const financialData = await getMedicalProfessionalFinancialData(clubId, professionalId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Verificar se existem entradas para o mês e ano especificados
    if (!financialData[key] || !financialData[key][index]) {
      throw new Error("Entrada financeira não encontrada.");
    }

    // Obter a entrada que será removida
    const entryToRemove = financialData[key][index];

    // Remover a entrada
    const entries = financialData[key].filter((_, i) => i !== index);

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Se não houver mais entradas para o mês, remover a chave
    if (entries.length === 0) {
      delete updatedData[key];
    }

    // Buscar transações financeiras relacionadas a esta entrada
    const transactionDate = new Date(Date.UTC(year, month - 1, 1)).toISOString().split('T')[0];
    const { data: transactions, error: fetchError } = await supabase
      .from("financial_transactions")
      .select("id")
      .eq("club_id", clubId)
      .eq("medical_professional_id", professionalId)
      .eq("date", transactionDate)
      .eq("amount", Math.abs(entryToRemove.amount))
      .eq("type", entryToRemove.amount < 0 ? "despesa" : "receita");

    if (!fetchError && transactions && transactions.length > 0) {
      // Remover a transação financeira correspondente
      const { error: deleteError } = await supabase
        .from("financial_transactions")
        .delete()
        .eq("id", transactions[0].id);

      if (deleteError) {
        console.error("Erro ao remover transação financeira:", deleteError);
      }
    }

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("medical_professionals")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao remover entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao remover entrada financeira:", error);
    throw new Error(error.message || "Erro ao remover entrada financeira");
  }
}

/**
 * Calcula o saldo mensal de um profissional
 * @param financialData Dados financeiros do profissional
 * @param month Mês (1-12)
 * @param year Ano
 * @returns Saldo mensal
 */
export function calculateMonthlyBalance(
  financialData: MedicalProfessionalFinancialData,
  month: number,
  year: number
): number {
  const key = `${year}-${month.toString().padStart(2, "0")}`;
  const entries = financialData[key] || [];
  return entries.reduce((total, entry) => total + entry.amount, 0);
}

/**
 * Calcula o saldo total de um profissional
 * @param financialData Dados financeiros do profissional
 * @returns Saldo total
 */
export function calculateTotalBalance(financialData: MedicalProfessionalFinancialData | null | undefined): number {
  // Se financialData for nulo, indefinido ou não for um objeto, retorna 0
  if (!financialData || typeof financialData !== 'object') {
    return 0;
  }

  try {
    return Object.values(financialData).reduce(
      (total, entries) => {
        // Verifica se entries é um array antes de tentar usar reduce
        if (!Array.isArray(entries)) {
          return total;
        }
        return total + entries.reduce((monthTotal, entry) => {
          // Verifica se entry tem a propriedade amount antes de tentar acessá-la
          const amount = typeof entry === 'object' && entry !== null && 'amount' in entry 
            ? Number(entry.amount) || 0 
            : 0;
          return monthTotal + amount;
        }, 0);
      },
      0
    );
  } catch (error) {
    console.error('Erro ao calcular saldo total:', error);
    return 0;
  }
}

/**
 * Obtém os dados financeiros de um profissional para um ano específico
 * @param financialData Dados financeiros do profissional
 * @param year Ano
 * @returns Dados financeiros do ano
 */
export function getMedicalProfessionalYearlyFinancialData(
  financialData: MedicalProfessionalFinancialData,
  year: number
): { [month: number]: MedicalProfessionalFinancialEntry[] } {
  const yearlyData: { [month: number]: MedicalProfessionalFinancialEntry[] } = {};

  // Inicializar todos os meses com arrays vazios
  for (let month = 1; month <= 12; month++) {
    yearlyData[month] = [];
  }

  // Preencher com os dados existentes
  Object.entries(financialData).forEach(([key, entries]) => {
    const [entryYear, entryMonth] = key.split("-").map(Number);
    if (entryYear === year) {
      yearlyData[entryMonth] = entries;
    }
  });

  return yearlyData;
}

/**
 * Obtém as informações bancárias de um profissional
 * @param clubId ID do clube
 * @param professionalId ID do profissional
 * @returns Informações bancárias do profissional
 */
export async function getMedicalProfessionalBankingInfo(
  clubId: number,
  professionalId: number
): Promise<MedicalProfessionalBankingInfo> {
  try {
    const { data, error } = await supabase
      .from("medical_professionals")
      .select("bank_info")
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter informações bancárias: ${error.message}`);
    }

    return data.bank_info || {};
  } catch (error: any) {
    console.error("Erro ao obter informações bancárias:", error);
    throw new Error(error.message || "Erro ao obter informações bancárias");
  }
}

/**
 * Atualiza as informações bancárias de um profissional
 * @param clubId ID do clube
 * @param professionalId ID do profissional
 * @param bankingInfo Informações bancárias
 * @returns Informações bancárias atualizadas
 */
export async function updateMedicalProfessionalBankingInfo(
  clubId: number,
  professionalId: number,
  bankingInfo: MedicalProfessionalBankingInfo
): Promise<MedicalProfessionalBankingInfo> {
  try {
    const { data, error } = await supabase
      .from("medical_professionals")
      .update({
        bank_info: bankingInfo,
      })
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .select("bank_info")
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar informações bancárias: ${error.message}`);
    }

    return data.bank_info;
  } catch (error: any) {
    console.error("Erro ao atualizar informações bancárias:", error);
    throw new Error(error.message || "Erro ao atualizar informações bancárias");
  }
}