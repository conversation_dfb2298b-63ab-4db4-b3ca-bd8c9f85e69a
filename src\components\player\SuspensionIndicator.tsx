import React from 'react';
import { AlertTriangle, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SuspensionIndicatorProps {
  isSuspended: boolean;
  reason?: string;
  matchesRemaining?: number;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export function SuspensionIndicator({ 
  isSuspended, 
  reason, 
  matchesRemaining, 
  size = 'md',
  showText = true 
}: SuspensionIndicatorProps) {
  if (!isSuspended) return null;

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4', 
    lg: 'h-5 w-5'
  };

  const badgeVariant = matchesRemaining && matchesRemaining > 1 ? 'destructive' : 'secondary';
  
  const tooltipContent = (
    <div className="space-y-1">
      <p className="font-medium">Jogador Suspenso</p>
      {reason && <p className="text-sm">Motivo: {reason}</p>}
      {matchesRemaining && (
        <p className="text-sm">
          {matchesRemaining === 1 
            ? 'Última partida de suspensão' 
            : `${matchesRemaining} partidas restantes`
          }
        </p>
      )}
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-1">
            <Badge variant={badgeVariant} className="flex items-center gap-1">
              <AlertTriangle className={sizeClasses[size]} />
              {showText && (
                <span className="text-xs">
                  Suspenso {matchesRemaining ? `(${matchesRemaining})` : ''}
                </span>
              )}
            </Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface SuspensionWarningProps {
  suspendedPlayers: Array<{
    id: string;
    name: string;
    reason: string;
    matchesRemaining: number;
  }>;
}

export function SuspensionWarning({ suspendedPlayers }: SuspensionWarningProps) {
  if (suspendedPlayers.length === 0) return null;

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
        <div className="flex-1">
          <h4 className="font-medium text-amber-800 mb-2">
            Jogadores Suspensos ({suspendedPlayers.length})
          </h4>
          <div className="space-y-2">
            {suspendedPlayers.map(player => (
              <div key={player.id} className="flex items-center justify-between text-sm">
                <div>
                  <span className="font-medium text-amber-900">{player.name}</span>
                  <span className="text-amber-700 ml-2">- {player.reason}</span>
                </div>
                <Badge variant="outline" className="text-amber-800 border-amber-300">
                  {player.matchesRemaining === 1 
                    ? 'Última partida' 
                    : `${player.matchesRemaining} jogos`
                  }
                </Badge>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}