import React, { useMemo, useEffect } from 'react';
import { 
  Building2, 
  Users, 
  DollarSign, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Calendar,
  CreditCard,
  RefreshCw
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useRequireMasterAuth } from '@/hooks/useMasterAuth';
import { useMasterLayoutContext } from '@/components/master/MasterLayout';
import { MasterErrorMessage } from '@/components/master/ErrorMessages';

const StatsCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ElementType;
  trend?: number;
  trendLabel?: string;
  color?: 'blue' | 'green' | 'red' | 'yellow';
}> = ({ title, value, icon: Icon, trend, trendLabel, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500'
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trend !== undefined && (
              <div className="flex items-center mt-1">
                {trend > 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(trend)}% {trendLabel}
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${colorClasses[color]}`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const MasterDashboard: React.FC = () => {
  const { masterUser } = useRequireMasterAuth();
  const { stats, activities, loading, error, refreshData } = useMasterLayoutContext();

  // Garantir que os dados estejam atualizados ao abrir o dashboard
  useEffect(() => {
    refreshData();
  }, []);

  // Placeholder calculation for revenue growth
  const revenueGrowth = useMemo(() => {
    const { totalClubs, newClubsThisMonth, monthlyRevenue } = stats;
    if (newClubsThisMonth > 0 && monthlyRevenue > 0) {
      return Math.min(Math.round((newClubsThisMonth / Math.max(totalClubs - newClubsThisMonth, 1)) * 100), 50);
    }
    return 0;
  }, [stats]);

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getActivityIcon = (action: string) => {
    switch (action) {
      case 'create_club_info':
        return <Building2 className="w-4 h-4 text-green-500" />;
      case 'suspend_club':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'reactivate_club':
        return <Building2 className="w-4 h-4 text-blue-500" />;
      case 'update_club_info':
        return <Building2 className="w-4 h-4 text-blue-500" />;
      case 'create_master_plans':
        return <CreditCard className="w-4 h-4 text-green-500" />;
      default:
        return <Calendar className="w-4 h-4 text-gray-500" />;
    }
  };

  const StatsCardSkeleton = () => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <Skeleton className="h-12 w-12 rounded-full" />
        </div>
      </CardContent>
    </Card>
  );

  const ErrorState = () => (
    <MasterErrorMessage
      error={error || 'Erro desconhecido'}
      onRetry={refreshData}
      isRetrying={loading}
      showDetails={true}
    />
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Master</h1>
          <p className="text-gray-600">
            Bem-vindo de volta, {masterUser?.name}! Aqui está um resumo do sistema.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            onClick={refreshData}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button>
            Gerar Relatório
          </Button>
        </div>
      </div>

      {/* Error state */}
      {error && <ErrorState />}

      {/* Cards de estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {loading ? (
          <>
            <StatsCardSkeleton />
            <StatsCardSkeleton />
            <StatsCardSkeleton />
            <StatsCardSkeleton />
          </>
        ) : (
          <>
            <StatsCard
              title="Total de Clubes"
              value={stats.totalClubs}
              icon={Building2}
              trend={stats.newClubsThisMonth > 0 ? Math.round((stats.newClubsThisMonth / Math.max(stats.totalClubs - stats.newClubsThisMonth, 1)) * 100) : undefined}
              trendLabel="este mês"
              color="blue"
            />
            
            <StatsCard
              title="Clubes Ativos"
              value={stats.activeClubs}
              icon={Users}
              trend={stats.totalClubs > 0 ? Math.round((stats.activeClubs / stats.totalClubs) * 100) : undefined}
              trendLabel="do total"
              color="green"
            />
            
            <StatsCard
              title="Receita Mensal"
              value={formatCurrency(stats.monthlyRevenue)}
              icon={DollarSign}
              trend={revenueGrowth}
              trendLabel="crescimento estimado"
              color="green"
            />
            
            <StatsCard
              title="Pagamentos em Atraso"
              value={stats.overduePayments}
              icon={AlertTriangle}
              color="red"
            />
          </>
        )}
      </div>

      {/* Cards secundários */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {loading ? (
          <>
            <Card><CardHeader><CardTitle><Skeleton className="h-5 w-24" /></CardTitle></CardHeader><CardContent><div className="space-y-3"><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-full" /></div></CardContent></Card>
            <Card><CardHeader><CardTitle><Skeleton className="h-5 w-24" /></CardTitle></CardHeader><CardContent><div className="text-center space-y-2"><Skeleton className="h-12 w-16 mx-auto" /><Skeleton className="h-4 w-20 mx-auto" /><Skeleton className="h-8 w-full mt-4" /></div></CardContent></Card>
            <Card><CardHeader><CardTitle><Skeleton className="h-5 w-24" /></CardTitle></CardHeader><CardContent><div className="space-y-3"><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-full" /></div></CardContent></Card>
          </>
        ) : (
          <>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="w-5 h-5 mr-2" />
                  Status dos Clubes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Ativos</span>
                    <Badge variant="secondary">{stats.activeClubs}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Em Trial</span>
                    <Badge variant="outline">{stats.trialClubs}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Suspensos</span>
                    <Badge variant="destructive">{stats.suspendedClubs}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Novos Clubes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold text-blue-600">{stats.newClubsThisMonth}</p>
                  <p className="text-sm text-gray-600">este mês</p>
                  <div className="mt-4">
                    <Button variant="outline" size="sm" className="w-full">
                      Ver Detalhes
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Crescimento
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Receita</span>
                    <span className="text-sm font-medium text-green-600">
                      {revenueGrowth > 0 ? `+${revenueGrowth}%` : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Clubes Novos</span>
                    <span className="text-sm font-medium text-blue-600">
                      {stats.newClubsThisMonth > 0 ? `+${stats.newClubsThisMonth}` : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Taxa Ativa</span>
                    <span className="text-sm font-medium text-green-600">
                      {stats.totalClubs > 0 ? `${Math.round((stats.activeClubs / stats.totalClubs) * 100)}%` : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Atividades recentes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Atividades Recentes
            {loading && (
              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Skeleton className="w-4 h-4" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : activities.length > 0 ? (
            <div className="space-y-4">
              {activities.slice(0, 8).map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getActivityIcon(activity.action)}
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500">
                        {new Date(activity.timestamp).toLocaleString('pt-BR')}
                      </p>
                      {activity.userName && (
                        <>
                          <span className="text-xs text-gray-400">•</span>
                          <p className="text-xs text-gray-500">por {activity.userName}</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Nenhuma atividade recente</p>
              <p className="text-xs text-gray-400 mt-2">
                As atividades aparecerão aqui conforme ações forem realizadas no sistema
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MasterDashboard;
