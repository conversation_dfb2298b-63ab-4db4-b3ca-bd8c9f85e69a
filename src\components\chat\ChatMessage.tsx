import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Reply, Edit, Trash2 } from 'lucide-react';
import { useChatStore } from '@/store/useChatStore';
import { useUser } from '@/context/UserContext';
import type { ChatMessage as ChatMessageType } from '@/types/chat';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ChatMessageProps {
  message: ChatMessageType;
  showAvatar: boolean;
  isOwn: boolean;
  onReply: () => void;
}

export function ChatMessage({ message, showAvatar, isOwn, onReply }: ChatMessageProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showActions, setShowActions] = useState(false);
  
  const { editMessage, deleteMessage } = useChatStore();
  const { user } = useUser();

  const handleEdit = async () => {
    if (editContent.trim() && editContent !== message.content) {
      await editMessage(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleDelete = async () => {
    if (confirm('Tem certeza que deseja deletar esta mensagem?')) {
      await deleteMessage(message.id);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditContent(message.content);
    }
  };

  return (
    <div
      className={`group flex gap-3 ${isOwn ? 'flex-row-reverse' : ''}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      <div className="flex-shrink-0">
        {showAvatar ? (
          <Avatar className="h-8 w-8">
            <AvatarImage src={message.user?.avatar_url} />
            <AvatarFallback className="text-xs">
              {message.user?.name?.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        ) : (
          <div className="h-8 w-8" />
        )}
      </div>

      {/* Message Content */}
      <div className={`flex-1 min-w-0 ${isOwn ? 'text-right' : ''}`}>
        {/* Header */}
        {showAvatar && (
          <div className={`flex items-center gap-2 mb-1 ${isOwn ? 'justify-end' : ''}`}>
            <span className="font-medium text-sm">
              {message.user?.name}
            </span>
            <span className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(message.created_at), {
                addSuffix: true,
                locale: ptBR
              })}
            </span>
          </div>
        )}

        {/* Reply Preview */}
        {message.reply_message && (
          <div className={`mb-2 ${isOwn ? 'ml-auto' : ''} max-w-xs`}>
            <div className="bg-muted/50 border-l-2 border-primary/50 pl-3 py-2 rounded text-xs">
              <div className="font-medium text-primary">
                {message.reply_message.user?.name}
              </div>
              <div className="text-muted-foreground truncate">
                {message.reply_message.content}
              </div>
            </div>
          </div>
        )}

        {/* Message Bubble */}
        <div className="relative">
          <div
            className={`inline-block max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
              isOwn
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted'
            }`}
          >
            {isEditing ? (
              <input
                type="text"
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyPress={handleKeyPress}
                onBlur={handleEdit}
                className="bg-transparent border-none outline-none w-full"
                autoFocus
              />
            ) : (
              <div>
                {message.content}
                {message.edited_at && (
                  <span className="text-xs opacity-70 ml-2">(editado)</span>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          {showActions && !isEditing && (
            <div className={`absolute top-0 ${isOwn ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} flex items-center gap-1 bg-background border rounded-md shadow-sm px-1 py-1`}>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={onReply}
              >
                <Reply className="h-3 w-3" />
              </Button>
              
              {isOwn && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-destructive hover:text-destructive"
                    onClick={handleDelete}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}