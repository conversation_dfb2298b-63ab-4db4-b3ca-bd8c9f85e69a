import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { GAME_OPERATION_PERMISSIONS } from "@/constants/permissions";

export type GameOperationStaff = {
  id: number;
  club_id: number;
  name: string;
  role: string;
  birth_date?: string | null;
  cpf?: string | null;
  pix_key?: string | null;
  created_at: string;
  updated_at: string;
};

export type OperationRole = {
  id: number;
  club_id: number;
  name: string;
  created_at: string;
  updated_at: string;
};

export type CallupOperationStaff = {
  id: number;
  club_id: number;
  callup_id: number;
  staff_id?: number | null;
  name: string;
  role: string;
  birth_date?: string | null;
  cpf?: string | null;
  pix_key?: string | null;
  value?: number | null;
  created_at: string;
};

export async function getGameOperationStaff(clubId: number): Promise<GameOperationStaff[]> {
  const { data, error } = await supabase
    .from('game_operation_staff')
    .select('*')
    .eq('club_id', clubId)
    .order('name');
  if (error) {
    throw new Error(`Erro ao buscar operação de jogo: ${error.message}`);
  }
  return data || [];
}

export async function getOperationRoles(clubId: number): Promise<OperationRole[]> {
  const { data, error } = await supabase
    .from('game_operation_roles')
    .select('*')
    .eq('club_id', clubId)
    .order('name');
  if (error) {
    throw new Error(`Erro ao buscar funções: ${error.message}`);
  }
  return data || [];
}

export async function createOperationRole(
  clubId: number,
  userId: string,
  name: string
): Promise<OperationRole> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.CREATE, () =>
    withAuditLog(clubId, userId, 'game_operation.role.create', { name }, async () => {
      const { data, error } = await supabase
        .from('game_operation_roles')
        .insert({ club_id: clubId, name } as any)
        .select()
        .single();
      if (error) {
        throw new Error(`Erro ao criar função: ${error.message}`);
      }
      return data as OperationRole;
    })
  );
}

export async function updateOperationRole(
  clubId: number,
  userId: string,
  id: number,
  name: string
): Promise<OperationRole> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.EDIT, () =>
    withAuditLog(clubId, userId, 'game_operation.role.update', { id, name }, async () => {
      const { data, error } = await supabase
        .from('game_operation_roles')
        .update({ name, updated_at: new Date().toISOString() } as any)
        .eq('club_id', clubId)
        .eq('id', id)
        .select()
        .single();
      if (error) {
        throw new Error(`Erro ao atualizar função: ${error.message}`);
      }
      return data as OperationRole;
    })
  );
}

export async function deleteOperationRole(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.DELETE, () =>
    withAuditLog(clubId, userId, 'game_operation.role.delete', { id }, async () => {
      const { error } = await supabase
        .from('game_operation_roles')
        .delete()
        .eq('club_id', clubId)
        .eq('id', id);
      if (error) {
        throw new Error(`Erro ao excluir função: ${error.message}`);
      }
      return true;
    })
  );
}

export async function createGameOperationStaff(
  clubId: number,
  userId: string,
  staff: Omit<GameOperationStaff, 'id' | 'club_id' | 'created_at' | 'updated_at'>
): Promise<GameOperationStaff> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.CREATE, () =>
    withAuditLog(clubId, userId, 'game_operation.create', { name: staff.name }, async () => {
      const { data, error } = await supabase
        .from('game_operation_staff')
        .insert({ ...staff, club_id: clubId } as any)
        .select()
        .single();
      if (error) {
        throw new Error(`Erro ao criar membro: ${error.message}`);
      }
      return data as GameOperationStaff;
    })
  );
}

export async function updateGameOperationStaff(
  clubId: number,
  userId: string,
  id: number,
  staff: Partial<Omit<GameOperationStaff, 'id' | 'club_id' | 'created_at' | 'updated_at'>>
): Promise<GameOperationStaff> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.EDIT, () =>
    withAuditLog(clubId, userId, 'game_operation.update', { id, ...staff }, async () => {
      const { data, error } = await supabase
        .from('game_operation_staff')
        .update({ ...staff, updated_at: new Date().toISOString() } as any)
        .eq('club_id', clubId)
        .eq('id', id)
        .select()
        .single();
      if (error) {
        throw new Error(`Erro ao atualizar membro: ${error.message}`);
      }
      return data as GameOperationStaff;
    })
  );
}

export async function deleteGameOperationStaff(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(clubId, userId, GAME_OPERATION_PERMISSIONS.DELETE, () =>
    withAuditLog(clubId, userId, 'game_operation.delete', { id }, async () => {
      const { error } = await supabase
        .from('game_operation_staff')
        .delete()
        .eq('club_id', clubId)
        .eq('id', id);
      if (error) {
        throw new Error(`Erro ao excluir membro: ${error.message}`);
      }
      return true;
    })
  );
}

export async function getCallupOperationStaff(
  clubId: number,
  callupId: number
): Promise<CallupOperationStaff[]> {
  const { data, error } = await supabase
    .from('callup_operation_staff')
    .select('*, staff:staff_id(name, role, birth_date, cpf, pix_key)')
    .eq('club_id', clubId)
    .eq('callup_id', callupId);
  if (error) {
    throw new Error(`Erro ao buscar membros da operação: ${error.message}`);
  }
  return (data || []).map((item: any) => ({
    id: item.id,
    club_id: item.club_id,
    callup_id: item.callup_id,
    staff_id: item.staff_id,
    name: item.name || item.staff?.name,
    role: item.role || item.staff?.role,
    birth_date: item.birth_date || item.staff?.birth_date,
    cpf: item.cpf || item.staff?.cpf,
    pix_key: item.pix_key || item.staff?.pix_key,
    value: item.value,
    created_at: item.created_at
  }));
}

export async function addOperationStaffToCallup(
  clubId: number,
  callupId: number,
  staff: { staff_id?: number; name?: string; role: string; birth_date?: string; cpf?: string; pix_key?: string | null; value?: number | null }
): Promise<CallupOperationStaff> {
  const { data, error } = await supabase
    .from('callup_operation_staff')
    .insert({
      club_id: clubId,
      callup_id: callupId,
      staff_id: staff.staff_id || null,
      name: staff.name || null,
      role: staff.role,
      birth_date: staff.birth_date || null,
      cpf: staff.cpf || null,
      pix_key: staff.pix_key || null,
      value: staff.value ?? null
    } as any)
    .select()
    .single();
  if (error) {
    throw new Error(`Erro ao adicionar membro à convocação: ${error.message}`);
  }
  return data as CallupOperationStaff;
}

export async function removeOperationStaffFromCallup(
  clubId: number,
  callupId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from('callup_operation_staff')
    .delete()
    .eq('club_id', clubId)
    .eq('callup_id', callupId)
    .eq('id', id);
  if (error) {
    throw new Error(`Erro ao remover membro: ${error.message}`);
  }
  return true;
}
