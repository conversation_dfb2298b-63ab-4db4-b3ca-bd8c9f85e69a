import { useUser } from "@/context/UserContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CreditCard, Users, UserPlus, Clock, AlertTriangle, CheckCircle, Calendar } from "lucide-react";
import { useLimits } from "@/components/guards/LimitGuard";
import { useClubAccess } from "@/context/ClubAccessContext";
import { usePaymentWarnings } from "@/hooks/usePaymentWarnings";

export function PlanInfo() {
    const { user } = useUser();
    const { accessInfo } = useClubAccess();
    const { limitInfo: userLimits } = useLimits('users');
    const { limitInfo: playerLimits } = useLimits('players');
    const { paymentWarning } = usePaymentWarnings();

    if (!user?.club_info) {
        return null;
    }

    const clubInfo = user.club_info;
    const plan = clubInfo.master_plans;

    const getStatusColor = () => {
        switch (accessInfo.subscriptionStatus) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'trial': return 'bg-blue-100 text-blue-800';
            case 'suspended': return 'bg-red-100 text-red-800';
            case 'cancelled': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = () => {
        switch (accessInfo.subscriptionStatus) {
            case 'active': return 'Ativo';
            case 'trial': return 'Trial';
            case 'suspended': return 'Suspenso';
            case 'cancelled': return 'Cancelado';
            default: return 'Desconhecido';
        }
    };

    const getPaymentStatusColor = () => {
        switch (accessInfo.paymentStatus) {
            case 'current': return 'bg-green-100 text-green-800';
            case 'overdue': return 'bg-red-100 text-red-800';
            case 'cancelled': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPaymentStatusText = () => {
        switch (accessInfo.paymentStatus) {
            case 'current': return 'Em Dia';
            case 'overdue': return 'Em Atraso';
            case 'cancelled': return 'Cancelado';
            default: return 'Desconhecido';
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(price);
    };

    const calculateTrialDaysLeft = () => {
        if (!clubInfo.is_trial || !clubInfo.trial_end_date) return 0;
        const trialEnd = new Date(clubInfo.trial_end_date);
        const now = new Date();
        return Math.max(0, Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    };

    const trialDaysLeft = calculateTrialDaysLeft();

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="w-5 h-5" />
                            Plano Atual
                        </CardTitle>
                        <CardDescription>
                            Informações sobre sua assinatura
                        </CardDescription>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('mailto:<EMAIL>?subject=Upgrade de Plano')}
                    >
                        Upgrade
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Informações do Plano */}
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm font-medium text-gray-600">Plano</p>
                        <p className="text-lg font-semibold">{plan?.name || 'Não definido'}</p>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-600">Valor</p>
                        <p className="text-lg font-semibold">
                            {plan?.price ? formatPrice(plan.price) : 'N/A'}/mês
                        </p>
                    </div>
                </div>

                {/* Status */}
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm font-medium text-gray-600">Status</p>
                        <Badge className={getStatusColor()}>
                            {getStatusText()}
                        </Badge>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-600">Pagamento</p>
                        <Badge className={getPaymentStatusColor()}>
                            {getPaymentStatusText()}
                        </Badge>
                    </div>
                </div>

                {/* Trial Info */}
                {clubInfo.is_trial && (
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex items-center gap-2 mb-2">
                            <Clock className="w-4 h-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-800">Período de Teste</span>
                        </div>
                        <p className="text-sm text-blue-700">
                            {trialDaysLeft > 0
                                ? `${trialDaysLeft} dia${trialDaysLeft !== 1 ? 's' : ''} restante${trialDaysLeft !== 1 ? 's' : ''}`
                                : 'Expirado'
                            }
                        </p>
                    </div>
                )}

                {/* Payment Info for Active Plans */}
                {!clubInfo.is_trial && clubInfo.subscription_status === 'active' && clubInfo.next_payment_date && (
                    <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                        <div className="flex items-center gap-2 mb-2">
                            <Calendar className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-green-800">Próximo Pagamento</span>
                        </div>
                        <p className="text-sm text-green-700">
                            {new Date(clubInfo.next_payment_date).toLocaleDateString('pt-BR')}
                        </p>
                        {paymentWarning?.daysUntilDue !== undefined && (
                            <p className="text-xs text-green-600 mt-1">
                                {paymentWarning.daysUntilDue === 0 
                                    ? 'Vence hoje' 
                                    : `${paymentWarning.daysUntilDue} dias restantes`
                                }
                            </p>
                        )}
                    </div>
                )}

                {/* Avisos */}
                {accessInfo.warningMessage && (
                    <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div className="flex items-center gap-2 mb-1">
                            <AlertTriangle className="w-4 h-4 text-yellow-600" />
                            <span className="text-sm font-medium text-yellow-800">Atenção</span>
                        </div>
                        <p className="text-sm text-yellow-700">{accessInfo.warningMessage}</p>
                    </div>
                )}

                {/* Limites de Uso */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-900">Limites de Uso</h4>

                    {/* Usuários */}
                    {userLimits && (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Users className="w-4 h-4 text-gray-500" />
                                    <span className="text-sm text-gray-600">Usuários</span>
                                </div>
                                <span className="text-sm font-medium">
                                    {userLimits.current} / {userLimits.limit || '∞'}
                                </span>
                            </div>
                            {userLimits.limit && (
                                <Progress
                                    value={userLimits.percentage}
                                    className="h-2"
                                />
                            )}
                        </div>
                    )}

                    {/* Jogadores */}
                    {playerLimits && (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <UserPlus className="w-4 h-4 text-gray-500" />
                                    <span className="text-sm text-gray-600">Jogadores</span>
                                </div>
                                <span className="text-sm font-medium">
                                    {playerLimits.current} / {playerLimits.limit || '∞'}
                                </span>
                            </div>
                            {playerLimits.limit && (
                                <Progress
                                    value={playerLimits.percentage}
                                    className="h-2"
                                />
                            )}
                        </div>
                    )}
                </div>

                {/* Módulos Disponíveis */}
                {plan?.modules && (
                    <div className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-900">Módulos Disponíveis</h4>
                        <div className="grid grid-cols-2 gap-2">
                            {Object.entries(plan.modules).map(([module, available]) => (
                                <div key={module} className="flex items-center gap-2">
                                    {available ? (
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                    ) : (
                                        <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                                    )}
                                    <span className={`text-sm ${available ? 'text-gray-900' : 'text-gray-400'}`}>
                                        {module.charAt(0).toUpperCase() + module.slice(1)}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Ações */}
                <div className="pt-4 border-t space-y-2">
                    <Button
                        className="w-full"
                        variant="outline"
                        onClick={() => window.open('mailto:<EMAIL>?subject=Informações sobre Planos')}
                    >
                        Falar com Suporte
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}