-- <PERSON><PERSON>r tabelas do sistema de cobrança
-- Execute este SQL no Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON> tabela de clientes
CREATE TABLE IF NOT EXISTS public.clients (
    id BIGSERIAL PRIMARY KEY,
    club_id BIGINT NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    pix_key TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. <PERSON>riar tabela de transações de cobrança
CREATE TABLE IF NOT EXISTS public.billing_transactions (
    id BIGSERIAL PRIMARY KEY,
    club_id BIGINT NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('cobranca', 'recebimento')),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('player', 'collaborator', 'client')),
    entity_id BIGINT,
    entity_name TEXT NOT NULL,
    pix_key TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'pago')),
    due_date DATE,
    paid_at TIMESTAMPTZ,
    qr_code_data TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id)
);

-- 3. Habilitar RLS
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_transactions ENABLE ROW LEVEL SECURITY;

-- 4. Criar políticas RLS para clients
CREATE POLICY "clients_select_policy" ON public.clients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "clients_insert_policy" ON public.clients
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "clients_update_policy" ON public.clients
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "clients_delete_policy" ON public.clients
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

-- 5. Criar políticas RLS para billing_transactions
CREATE POLICY "billing_transactions_select_policy" ON public.billing_transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "billing_transactions_insert_policy" ON public.billing_transactions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "billing_transactions_update_policy" ON public.billing_transactions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "billing_transactions_delete_policy" ON public.billing_transactions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

-- 6. Criar índices
CREATE INDEX IF NOT EXISTS idx_clients_club_id ON public.clients(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_club_id ON public.billing_transactions(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_status ON public.billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_entity ON public.billing_transactions(entity_type, entity_id);

-- 7. Função para atualizar updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Triggers para updated_at
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON public.clients
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_billing_transactions_updated_at 
    BEFORE UPDATE ON public.billing_transactions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();