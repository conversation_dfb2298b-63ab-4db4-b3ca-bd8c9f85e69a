-- Script SQL para adicionar novas permissões médicas
-- Execute este script no Supabase para adicionar as permissões necessárias

-- Inserir novas permissões médicas na tabela permissions
INSERT INTO permissions (name, description, category) VALUES
-- Permissões para aba de agenda
('medical.agenda.view', 'Visualizar aba de agenda médica', 'medical'),
('medical.appointments.create', 'Criar agendamentos médicos', 'medical'),
('medical.appointments.edit', 'Editar agendamentos médicos', 'medical'),
('medical.appointments.start', 'Iniciar agendamentos médicos', 'medical'),
('medical.appointments.delete', 'Excluir agendamentos médicos', 'medical'),

-- Permissões para aba de solicitações de exame
('medical.exam_requests.view', 'Visualizar aba de solicitações de exame', 'medical'),
('medical.exam_requests.create', 'Solicitar exames médicos', 'medical')

ON CONFLICT (name) DO NOTHING;

-- Atualizar permissões padrão para o role 'medical'
-- <PERSON>iro, vamos verificar se existe algum usuário com role 'medical' e atualizar suas permissões

-- Função para atualizar permissões de usuários médicos existentes
DO $$
DECLARE
    medical_user RECORD;
    current_permissions JSONB;
    updated_permissions JSONB;
BEGIN
    -- Buscar todos os usuários com role 'medical'
    FOR medical_user IN 
        SELECT DISTINCT cm.user_id, cm.club_id, cm.permissions
        FROM club_members cm
        WHERE cm.role = 'medical'
    LOOP
        -- Obter permissões atuais (se existirem)
        current_permissions := COALESCE(medical_user.permissions, '{}'::jsonb);
        
        -- Adicionar as novas permissões médicas
        updated_permissions := current_permissions || jsonb_build_object(
            'medical.agenda.view', true,
            'medical.appointments.create', true,
            'medical.appointments.edit', true,
            'medical.appointments.start', true,
            'medical.appointments.delete', true,
            'medical.exam_requests.view', true,
            'medical.exam_requests.create', true
        );
        
        -- Atualizar as permissões do usuário
        UPDATE club_members 
        SET permissions = updated_permissions
        WHERE user_id = medical_user.user_id AND club_id = medical_user.club_id;
        
        RAISE NOTICE 'Permissões atualizadas para usuário médico: %', medical_user.user_id;
    END LOOP;
END $$;

-- Verificar se as permissões foram inseridas corretamente
SELECT name, description, category 
FROM permissions 
WHERE category = 'medical' 
AND name IN (
    'medical.agenda.view',
    'medical.appointments.create',
    'medical.appointments.edit',
    'medical.appointments.start',
    'medical.appointments.delete',
    'medical.exam_requests.view',
    'medical.exam_requests.create'
)
ORDER BY name;