# Migração Chat: Supabase Realtime → Socket.IO

## Problema
O Supabase Realtime não está funcionando adequadamente para o sistema de chat em tempo real.

## Solução
Migrar para Socket.IO mantendo toda a estrutura de dados atual no Supabase.

## Arquitetura Proposta

### Backend
- **Servidor Socket.IO**: Node.js/Express separado
- **Banco de dados**: Manter Supabase PostgreSQL
- **Autenticação**: Manter Supabase Auth
- **API REST**: Manter APIs existentes do Supabase

### Fluxo
1. Frontend conecta no Socket.IO server
2. Socket.IO server valida token Supabase
3. Mensagens são salvas no Supabase via API
4. Socket.IO emite eventos para salas específicas

## Implementação Rápida

### 1. Servidor Socket.IO (30 min)
```bash
# Criar pasta do servidor
mkdir chat-server
cd chat-server
npm init -y
npm install socket.io express cors dotenv @supabase/supabase-js
```

### 2. Código do Servidor
```javascript
// server.js
const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:8080",
    methods: ["GET", "POST"]
  }
});

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Middleware de autenticação
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return next(new Error('Authentication failed'));
    }
    
    socket.userId = user.id;
    socket.userEmail = user.email;
    next();
  } catch (error) {
    next(new Error('Authentication failed'));
  }
});

io.on('connection', (socket) => {
  console.log(`User ${socket.userEmail} connected`);
  
  // Entrar em salas do usuário
  socket.on('join-rooms', async (clubId) => {
    try {
      // Buscar salas do usuário
      const { data: rooms } = await supabase
        .from('chat_room_participants')
        .select('room_id, chat_rooms(id, name)')
        .eq('user_id', socket.userId);
      
      // Entrar nas salas
      rooms?.forEach(({ room_id }) => {
        socket.join(`room:${room_id}`);
      });
      
      // Entrar na sala de presença do clube
      socket.join(`club:${clubId}`);
      
      // Atualizar presença
      await supabase
        .from('user_presence')
        .upsert({
          user_id: socket.userId,
          club_id: clubId,
          status: 'online',
          last_seen: new Date().toISOString()
        });
      
      // Notificar outros usuários
      socket.to(`club:${clubId}`).emit('user-online', {
        user_id: socket.userId,
        email: socket.userEmail
      });
      
    } catch (error) {
      console.error('Error joining rooms:', error);
    }
  });
  
  // Enviar mensagem
  socket.on('send-message', async (data) => {
    try {
      // Salvar no banco
      const { data: message, error } = await supabase
        .from('chat_messages')
        .insert({
          room_id: data.room_id,
          user_id: socket.userId,
          content: data.content,
          reply_to: data.reply_to
        })
        .select(`
          *,
          user:auth.users(email),
          reply_message:chat_messages(content, user:auth.users(email))
        `)
        .single();
      
      if (error) throw error;
      
      // Emitir para a sala
      io.to(`room:${data.room_id}`).emit('new-message', message);
      
    } catch (error) {
      socket.emit('error', { message: 'Failed to send message' });
    }
  });
  
  // Sair de sala
  socket.on('leave-room', (roomId) => {
    socket.leave(`room:${roomId}`);
  });
  
  // Desconexão
  socket.on('disconnect', async () => {
    try {
      // Atualizar presença para offline
      await supabase
        .from('user_presence')
        .update({
          status: 'offline',
          last_seen: new Date().toISOString()
        })
        .eq('user_id', socket.userId);
      
      console.log(`User ${socket.userEmail} disconnected`);
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Socket.IO server running on port ${PORT}`);
});
```

### 3. Frontend - Hook Socket.IO (15 min)
```typescript
// src/hooks/useSocketChat.ts
import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useChatStore } from '@/store/useChatStore';
import { useUser } from '@/context/UserContext';

export function useSocketChat() {
  const socketRef = useRef<Socket | null>(null);
  const { user } = useUser();
  const { addMessage, updateUserPresence } = useChatStore();
  
  useEffect(() => {
    if (!user?.access_token) return;
    
    // Conectar ao Socket.IO
    socketRef.current = io(process.env.VITE_SOCKET_URL || 'http://localhost:3001', {
      auth: {
        token: user.access_token
      }
    });
    
    const socket = socketRef.current;
    
    // Eventos
    socket.on('connect', () => {
      console.log('Connected to chat server');
      socket.emit('join-rooms', user.club_id);
    });
    
    socket.on('new-message', (message) => {
      addMessage(message.room_id, message);
    });
    
    socket.on('user-online', (userData) => {
      updateUserPresence(userData.user_id, 'online');
    });
    
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
    
    return () => {
      socket.disconnect();
    };
  }, [user?.access_token, user?.club_id]);
  
  const sendMessage = (roomId: string, content: string, replyTo?: string) => {
    socketRef.current?.emit('send-message', {
      room_id: roomId,
      content,
      reply_to: replyTo
    });
  };
  
  return { sendMessage };
}
```

### 4. Atualizar Store (10 min)
```typescript
// Adicionar ao useChatStore.ts
import { useSocketChat } from '@/hooks/useSocketChat';

// No store, substituir as funções de realtime por Socket.IO
export const useChatStore = create<ChatState & ChatActions>((set, get) => ({
  // ... estado existente
  
  // Substituir sendMessage para usar Socket.IO
  sendMessage: async (data: SendMessageData) => {
    const { sendMessage } = useSocketChat();
    sendMessage(data.room_id, data.content, data.reply_to);
  },
  
  // Adicionar função para receber mensagens do Socket.IO
  addMessage: (roomId: string, message: ChatMessage) => {
    set((state) => ({
      messages: {
        ...state.messages,
        [roomId]: [...(state.messages[roomId] || []), message]
      }
    }));
  }
}));
```

## Deploy Rápido

### Opção A: Vercel (5 min)
```bash
# No diretório chat-server
npm install -g vercel
vercel --prod
```

### Opção B: Railway (3 min)
1. Push para GitHub
2. Conectar no Railway
3. Deploy automático

### Opção C: Render (5 min)
1. Conectar repositório
2. Configurar variáveis de ambiente
3. Deploy

## Variáveis de Ambiente
```env
# chat-server/.env
SUPABASE_URL=sua_url_supabase
SUPABASE_SERVICE_KEY=sua_service_key
FRONTEND_URL=http://localhost:8080
PORT=3001
```

```env
# frontend/.env
VITE_SOCKET_URL=https://seu-chat-server.vercel.app
```

## Tempo Total: ~1 hora
- Servidor Socket.IO: 30 min
- Frontend: 25 min  
- Deploy: 5 min

## Vantagens
- ✅ Funciona imediatamente
- ✅ Mantém toda estrutura de dados atual
- ✅ Fallback automático se WebSocket falhar
- ✅ Reconexão automática
- ✅ Escalável