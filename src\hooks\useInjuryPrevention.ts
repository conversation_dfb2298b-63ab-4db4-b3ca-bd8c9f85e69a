import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@/context/UserContext';
import {
  getHighRiskPlayers,
  calculateRiskFactors,
  getPlayerRiskFactors,
  createWellnessData,
  createWorkloadData,
  RiskFactors,
  WellnessData,
  WorkloadData
} from '@/api/injuryPrevention';
import InjuryAlertService from '@/services/injuryAlertService';
import { toast } from 'sonner';

interface UseInjuryPreventionProps {
  clubId: number;
  autoRefresh?: boolean;
  refreshInterval?: number; // em minutos
}

interface InjuryPreventionState {
  highRiskPlayers: any[];
  loading: boolean;
  calculating: boolean;
  lastUpdate: string;
  error: string | null;
}

interface InjuryPreventionActions {
  refreshData: () => Promise<void>;
  calculatePlayerRisk: (playerId: number) => Promise<RiskFactors | null>;
  runFullAnalysis: () => Promise<void>;
  submitWellnessData: (data: Omit<WellnessData, 'id' | 'club_id'>) => Promise<void>;
  submitWorkloadData: (data: Omit<WorkloadData, 'id' | 'club_id'>) => Promise<void>;
  getPlayerCurrentRisk: (playerId: number) => Promise<RiskFactors | null>;
}

/**
 * Hook customizado para gerenciar funcionalidades de prevenção de lesões
 */
export function useInjuryPrevention({
  clubId,
  autoRefresh = false,
  refreshInterval = 30 // 30 minutos por padrão
}: UseInjuryPreventionProps): [InjuryPreventionState, InjuryPreventionActions] {
  
  const { user } = useUser();
  
  const [state, setState] = useState<InjuryPreventionState>({
    highRiskPlayers: [],
    loading: true,
    calculating: false,
    lastUpdate: '',
    error: null
  });

  // Função para carregar dados
  const refreshData = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const players = await getHighRiskPlayers(clubId, user.id, 25);
      
      setState(prev => ({
        ...prev,
        highRiskPlayers: players,
        loading: false,
        lastUpdate: new Date().toLocaleString('pt-BR')
      }));
      
    } catch (error) {
      console.error('Erro ao carregar dados de prevenção:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Erro ao carregar dados de prevenção de lesões'
      }));
    }
  }, [clubId, user?.id]);

  // Função para calcular risco de um jogador específico
  const calculatePlayerRisk = useCallback(async (playerId: number): Promise<RiskFactors | null> => {
    if (!user?.id) return null;
    
    try {
      setState(prev => ({ ...prev, calculating: true }));
      
      const riskFactors = await calculateRiskFactors(clubId, playerId);
      
      // Atualizar lista se o jogador estiver nela
      setState(prev => ({
        ...prev,
        calculating: false,
        highRiskPlayers: prev.highRiskPlayers.map(player => 
          player.player_id === playerId 
            ? { ...player, ...riskFactors }
            : player
        )
      }));
      
      return riskFactors;
      
    } catch (error) {
      console.error('Erro ao calcular risco:', error);
      setState(prev => ({ ...prev, calculating: false }));
      toast.error('Erro ao calcular risco do jogador');
      return null;
    }
  }, [clubId, user?.id]);

  // Função para executar análise completa
  const runFullAnalysis = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setState(prev => ({ ...prev, calculating: true }));
      toast.info('Executando análise completa...');
      
      const alerts = await InjuryAlertService.runAutomaticRiskAnalysis(clubId);
      
      // Recarregar dados após análise
      await refreshData();
      
      setState(prev => ({ ...prev, calculating: false }));
      
      if (alerts.length > 0) {
        toast.success(`Análise concluída! ${alerts.length} alertas gerados.`);
      } else {
        toast.success('Análise concluída! Nenhum alerta crítico detectado.');
      }
      
    } catch (error) {
      console.error('Erro na análise completa:', error);
      setState(prev => ({ ...prev, calculating: false }));
      toast.error('Erro na análise completa');
    }
  }, [clubId, user?.id, refreshData]);

  // Função para submeter dados de wellness
  const submitWellnessData = useCallback(async (data: Omit<WellnessData, 'id' | 'club_id'>) => {
    if (!user?.id) return;
    
    try {
      await createWellnessData(clubId, user.id, data);
      
      // Recalcular risco do jogador após submissão
      await calculatePlayerRisk(data.player_id);
      
      toast.success('Dados de wellness registrados com sucesso!');
      
    } catch (error) {
      console.error('Erro ao submeter dados de wellness:', error);
      toast.error('Erro ao registrar dados de wellness');
      throw error;
    }
  }, [clubId, user?.id, calculatePlayerRisk]);

  // Função para submeter dados de carga de trabalho
  const submitWorkloadData = useCallback(async (data: Omit<WorkloadData, 'id' | 'club_id'>) => {
    if (!user?.id) return;
    
    try {
      await createWorkloadData(clubId, user.id, data);
      
      // Recalcular risco do jogador após submissão
      await calculatePlayerRisk(data.player_id);
      
      toast.success('Dados de carga de trabalho registrados com sucesso!');
      
    } catch (error) {
      console.error('Erro ao submeter dados de carga:', error);
      toast.error('Erro ao registrar dados de carga de trabalho');
      throw error;
    }
  }, [clubId, user?.id, calculatePlayerRisk]);

  // Função para buscar risco atual de um jogador
  const getPlayerCurrentRisk = useCallback(async (playerId: number): Promise<RiskFactors | null> => {
    if (!user?.id) return null;
    
    try {
      return await getPlayerRiskFactors(clubId, user.id, playerId);
    } catch (error) {
      console.error('Erro ao buscar risco do jogador:', error);
      return null;
    }
  }, [clubId, user?.id]);

  // Carregar dados iniciais
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Auto-refresh se habilitado
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval * 60 * 1000); // converter minutos para ms
    
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshData]);

  // Retornar estado e ações
  return [
    state,
    {
      refreshData,
      calculatePlayerRisk,
      runFullAnalysis,
      submitWellnessData,
      submitWorkloadData,
      getPlayerCurrentRisk
    }
  ];
}

/**
 * Hook simplificado para verificar se um jogador está em risco
 */
export function usePlayerRiskStatus(clubId: number, playerId: number) {
  const { user } = useUser();
  const [riskData, setRiskData] = useState<RiskFactors | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user?.id || !playerId) return;

    const fetchRisk = async () => {
      try {
        setLoading(true);
        const risk = await getPlayerRiskFactors(clubId, user.id, playerId);
        setRiskData(risk);
      } catch (error) {
        console.error('Erro ao buscar risco do jogador:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRisk();
  }, [clubId, playerId, user?.id]);

  return {
    riskData,
    loading,
    isHighRisk: riskData ? riskData.overall_risk_score >= 50 : false,
    isCriticalRisk: riskData ? riskData.overall_risk_score >= 75 : false,
    riskLevel: riskData?.risk_level || 'baixo',
    riskScore: riskData?.overall_risk_score || 0
  };
}

/**
 * Hook para estatísticas gerais de prevenção
 */
export function useInjuryPreventionStats(clubId: number) {
  const [state] = useInjuryPrevention({ clubId });
  
  const stats = {
    totalMonitored: state.highRiskPlayers.length,
    criticalRisk: state.highRiskPlayers.filter(p => p.risk_level === 'crítico').length,
    highRisk: state.highRiskPlayers.filter(p => p.risk_level === 'alto').length,
    moderateRisk: state.highRiskPlayers.filter(p => p.risk_level === 'moderado').length,
    preventionRate: state.highRiskPlayers.length > 0 
      ? ((state.highRiskPlayers.length - state.highRiskPlayers.filter(p => ['crítico', 'alto'].includes(p.risk_level)).length) / state.highRiskPlayers.length * 100)
      : 100
  };

  return {
    ...stats,
    loading: state.loading,
    lastUpdate: state.lastUpdate
  };
}
