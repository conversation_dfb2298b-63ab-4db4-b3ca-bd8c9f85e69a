import { Bell, MessageCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ReactNode, useState, useEffect } from "react";
import { ProfileDialog } from "@/components/modals/ProfileDialog";
import { useTheme } from "@/context/ThemeContext";
import { useUser } from "@/context/UserContext";
import { SeasonSelector } from "@/components/SeasonSelector";
import { CategorySelector } from "@/components/CategorySelector";
import { useCurrentClubId } from "@/context/ClubContext";
import { useClubMembersStore } from "@/store/useClubMembersStore";
import { NotificationsBell } from "@/components/ui/notifications-bell";
import { usePermission } from "@/hooks/usePermission";
import { useChat } from "@/hooks/useChat";

interface HeaderProps {
  children?: ReactNode;
}

export function Header({ children }: HeaderProps) {
  const [profileOpen, setProfileOpen] = useState(false);
  const { logo, theme, clubName } = useTheme();
  const { user, loading, refreshUser } = useUser();
  const clubId = useCurrentClubId();
  const { members, fetchMembers } = useClubMembersStore();
  const { role, isLoaded } = usePermission();
  const { toggleChat, hasUnreadMessages, totalUnreadCount } = useChat();

  useEffect(() => {
    if (clubId) fetchMembers(clubId);
  }, [clubId, fetchMembers]);

  // Removido: refreshUser desnecessário que causava re-verificação de plano

  let currentRole = "";
  if (user && members.length > 0) {
    const found = members.find((m) => m.userId === user.id);
    if (found) currentRole = found.role;
  }

  // Verificar se o usuário é jogador ou médico para esconder seletores
  const shouldHideSelectors = isLoaded && (role === "player" || role === "medical");

  return (
    <header
      className="h-14 sm:h-16 border-b flex justify-between items-center px-2 sm:px-4"
      style={{ background: theme.colors.primary }}
    >
      <div className="flex items-center gap-1 sm:gap-3 min-w-0 flex-1">
        {children}
        {!shouldHideSelectors && (
          <div className="flex items-center gap-1 bg-white/10 backdrop-blur-sm p-1 rounded-lg">
            {/* Desktop: Show both selectors with full width */}
            <div className="hidden md:flex items-center gap-2">
              <SafeSeasonSelector />
              <SafeCategorySelector />
            </div>
            {/* Mobile/Tablet: Show both selectors stacked or side by side */}
            <div className="flex md:hidden items-center gap-1">
              <div className="min-w-0 flex-1">
                <SafeSeasonSelector />
              </div>
              <div className="min-w-0 flex-1">
                <SafeCategorySelector />
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-center gap-1 sm:gap-2 md:gap-4 flex-shrink-0">
        <NotificationsBell />
        
        {/* Chat Button */}
        <Button
          variant="ghost"
          size="icon"
          className="relative text-white hover:bg-white/20"
          onClick={toggleChat}
        >
          <MessageCircle className="h-5 w-5" />
          {hasUnreadMessages && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
            </Badge>
          )}
        </Button>

        <Button
          variant="ghost"
          className="relative flex items-center gap-1 sm:gap-2 p-1 sm:p-2"
          size="sm"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("Abrindo modal de perfil");
            setProfileOpen(true);
          }}
        >
          <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
            <AvatarImage
              src={user?.profile_image || localStorage.getItem("userProfileImage") || ""}
              onError={(e) => {
                const cachedImage = localStorage.getItem("userProfileImage");
                if (cachedImage) {
                  (e.target as HTMLImageElement).src = cachedImage;
                }
              }}
            />
            <AvatarFallback className="text-xs sm:text-sm">
              {user && user.name ? user.name.slice(0, 2).toUpperCase() : "US"}
            </AvatarFallback>
          </Avatar>
          <div className="hidden md:flex flex-col items-start text-sm">
            <p className="font-medium text-white truncate max-w-24 lg:max-w-32">
              {user ? user.name : "Usuário"}
            </p>
            <p className="text-xs truncate max-w-24 lg:max-w-32" style={{ color: theme.colors.secondary }}>
              {currentRole || ""}
            </p>
          </div>
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="text-red border-white hover:bg-red-600 hover:text-white hover:border-red-600 px-2 sm:px-3 text-xs sm:text-sm"
          onClick={() => {
            // Limpar todos os dados de autenticação
            localStorage.removeItem("token");
            localStorage.removeItem("userId");
            localStorage.removeItem("clubId");
            localStorage.removeItem("selectedCategoryId");

            // Limpar qualquer outro dado de sessão
            sessionStorage.clear();

            // Redirecionar para a página de login
            window.location.href = "/login";
          }}
        >
          <span className="hidden sm:inline">Sair</span>
          <span className="sm:hidden">×</span>
        </Button>

        <ProfileDialog open={profileOpen} onOpenChange={setProfileOpen} />
      </div>
    </header>
  );
}

function SafeSeasonSelector() {
  try {
    useCurrentClubId();
    return <SeasonSelector />;
  } catch {
    return null;
  }
}

function SafeCategorySelector() {
  try {
    const clubId = useCurrentClubId();
    return <CategorySelector onCategoryChange={(categoryId) => {
      // Armazenar a categoria selecionada no localStorage para uso global
      if (categoryId) {
        localStorage.setItem("selectedCategoryId", categoryId.toString());
      } else {
        localStorage.removeItem("selectedCategoryId");
      }
    }} />;
  } catch {
    return null;
  }
}
