import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type Category = Database["public"]["Tables"]["categories"]["Row"];
export type PlayerCategory = Database["public"]["Tables"]["player_categories"]["Row"];

// Funções para gerenciar categorias
export async function getCategories(clubId: number): Promise<Category[]> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .eq("club_id", clubId)
    .order("name");

  if (error) {
    console.error("Erro ao buscar categorias:", error);
    throw new Error(`Erro ao buscar categorias: ${error.message}`);
  }

  return data || [];
}

export async function getCategoryById(clubId: number, id: number): Promise<Category> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error(`Erro ao buscar categoria ${id}:`, error);
    throw new Error(`Erro ao buscar categoria: ${error.message}`);
  }

  return data;
}

export async function createCategory(clubId: number, category: Omit<Category, "id" | "club_id" | "created_at">): Promise<Category> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabase
    .from("categories")
    .insert({
      ...category,
      club_id: clubId
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar categoria:", error);
    throw new Error(`Erro ao criar categoria: ${error.message}`);
  }

  return data;
}

export async function updateCategory(clubId: number, id: number, category: Partial<Category>): Promise<Category> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabase
    .from("categories")
    .update(category)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar categoria ${id}:`, error);
    throw new Error(`Erro ao atualizar categoria: ${error.message}`);
  }

  return data;
}

export async function deleteCategory(clubId: number, id: number): Promise<boolean> {
  const supabase = getSupabaseClientWithClubId(clubId);
  // Primeiro, verificar se há jogadores associados a esta categoria
  const { data: playerCategories } = await supabase
    .from("player_categories")
    .select("id")
    .eq("category_id", id)
    .eq("club_id", clubId);

  if (playerCategories && playerCategories.length > 0) {
    throw new Error("Não é possível excluir uma categoria com jogadores associados");
  }

  // Verificar se há partidas associadas a esta categoria
  const { data: matches } = await supabase
    .from("matches")
    .select("id")
    .eq("category_id", id)
    .eq("club_id", clubId);

  if (matches && matches.length > 0) {
    throw new Error("Não é possível excluir uma categoria com partidas associadas");
  }

  // Verificar se há treinos associados a esta categoria
  const { data: trainings } = await supabase
    .from("trainings")
    .select("id")
    .eq("category_id", id)
    .eq("club_id", clubId);

  if (trainings && trainings.length > 0) {
    throw new Error("Não é possível excluir uma categoria com treinos associados");
  }

  const { error } = await supabase
    .from("categories")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error(`Erro ao excluir categoria ${id}:`, error);
    throw new Error(`Erro ao excluir categoria: ${error.message}`);
  }

  return true;
}

// Funções para gerenciar associações entre jogadores e categorias
export async function assignPlayerToCategory(clubId: number, playerId: string, categoryId: number): Promise<PlayerCategory> {
  const supabase = getSupabaseClientWithClubId(clubId);
  // Verificar se o jogador já está associado a alguma categoria
  const { data: existingAssignments, error: fetchError } = await supabase
    .from("player_categories")
    .select("*")
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (fetchError) {
    console.error("Erro ao verificar categorias do jogador:", fetchError);
    throw new Error(`Erro ao verificar categorias do jogador: ${fetchError.message}`);
  }

  let data;
  let error;

  if (existingAssignments && existingAssignments.length > 0) {
    // Atualizar a categoria existente
    const assignment = existingAssignments[0];
    
    // Se já está na mesma categoria, não faz nada
    if (assignment.category_id === categoryId) {
      console.log("O jogador já está associado a esta categoria");
      return assignment;
    }

    // Atualiza a categoria existente
    const { data: updatedData, error: updateError } = await supabase
      .from("player_categories")
      .update({ category_id: categoryId })
      .eq("id", assignment.id)
      .select()
      .single();
    
    data = updatedData;
    error = updateError;
  } else {
    // Criar uma nova associação
    const { data: newData, error: insertError } = await supabase
      .from("player_categories")
      .insert({
        club_id: clubId,
        player_id: playerId,
        category_id: categoryId
      })
      .select()
      .single();
    
    data = newData;
    error = insertError;
  }

  if (error) {
    console.error("Erro ao atualizar/associar jogador à categoria:", error);
    throw new Error(`Erro ao atualizar/associar jogador à categoria: ${error.message}`);
  }

  // Após a associação, mover o jogador entre treinos/convocações futuros
  try {
    if (existingAssignments && existingAssignments.length > 0) {
      const previousCategory = existingAssignments[0].category_id;
      if (previousCategory && previousCategory !== categoryId) {
        await removePlayerFromUpcomingTrainings(clubId, playerId, previousCategory);
        await removePlayerFromUpcomingCallups(clubId, playerId, previousCategory);
      }
    }

    await addPlayerToUpcomingTrainings(clubId, playerId, categoryId);
    await addPlayerToUpcomingCallups(clubId, playerId, categoryId);
  } catch (e) {
    console.error("Erro ao ajustar jogador nos eventos futuros:", e);
  }

  return data!;
}

export async function removePlayerFromCategory(clubId: number, playerId: string, categoryId: number): Promise<boolean> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { error } = await supabase
    .from("player_categories")
    .delete()
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .eq("category_id", categoryId);

  if (error) {
    console.error(`Erro ao remover jogador da categoria:`, error);
    throw new Error(`Erro ao remover jogador da categoria: ${error.message}`);
  }
  try {
    await removePlayerFromUpcomingTrainings(clubId, playerId, categoryId);
    await removePlayerFromUpcomingCallups(clubId, playerId, categoryId);
  } catch (e) {
    console.error("Erro ao remover jogador de eventos futuros:", e);
  }

  return true;
}

export async function getPlayerCategories(clubId: number, playerId: string): Promise<Category[]> {
  const supabase = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabase
    .from("player_categories")
    .select(`
      category_id,
      categories!player_categories_category_id_fkey(*)
    `)
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (error) {
    console.error(`Erro ao buscar categorias do jogador:`, error);
    throw new Error(`Erro ao buscar categorias do jogador: ${error.message}`);
  }

  return data?.map(item => item.categories) || [];
}

export async function getCategoryPlayers(
  clubId: number,
  categoryId: number,
  options?: { includeInactive?: boolean, includeLoaned?: boolean }
): Promise<any[]> {
  console.log(`Buscando jogadores para a categoria ${categoryId} do clube ${clubId}`);

  try {
    const supabase = getSupabaseClientWithClubId(clubId);
    // Primeiro, buscar os IDs dos jogadores na categoria
    const { data: playerCategoriesData, error: playerCategoriesError } = await supabase
      .from("player_categories")
      .select("player_id")
      .eq("club_id", clubId)
      .eq("category_id", categoryId);

    if (playerCategoriesError) {
      console.error(`Erro ao buscar jogadores da categoria:`, playerCategoriesError);
      throw new Error(`Erro ao buscar jogadores da categoria: ${playerCategoriesError.message}`);
    }

    // Se não houver jogadores na categoria, retornar array vazio
    if (!playerCategoriesData || playerCategoriesData.length === 0) {
      console.log(`Nenhum jogador encontrado para a categoria ${categoryId}`);
      return [];
    }

    // Extrair os IDs dos jogadores
    const playerIds = playerCategoriesData.map(item => item.player_id);
    console.log(`IDs dos jogadores encontrados:`, playerIds);

    // Buscar detalhes dos jogadores
    const { data: playersData, error: playersError } = await supabase
      .from("players")
      .select("*")
      .in("id", playerIds);

    if (playersError) {
      console.error(`Erro ao buscar detalhes dos jogadores:`, playersError);
      throw new Error(`Erro ao buscar detalhes dos jogadores: ${playersError.message}`);
    }

    let players = playersData || [];
    console.log(`${players.length} jogadores encontrados`);

    // Filtrar jogadores com base nas opções
    if (options) {
      const includeInactive = options.includeInactive ?? true;
      const includeLoaned = options.includeLoaned ?? true;

      if (!includeInactive || !includeLoaned) {
        players = players.filter(player => {
          // Filtrar jogadores inativos se necessário
          if (!includeInactive && player.status === "inativo") {
            return false;
          }

          // Filtrar jogadores emprestados se necessário
          if (!includeLoaned && player.status === "emprestado") {
            return false;
          }

          return true;
        });
      }
    }

    return players;
  } catch (error) {
    console.error(`Erro ao buscar jogadores da categoria:`, error);
    throw new Error(`Erro ao buscar jogadores da categoria: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Função para migrar jogadores da base juvenil para o novo sistema de categorias
export async function migrateYouthPlayers(clubId: number): Promise<boolean> {
  const supabase = getSupabaseClientWithClubId(clubId);
  try {
    // 1. Verificar se já existem categorias padrão
    const { data: existingCategories } = await supabase
      .from("categories")
      .select("*")
      .eq("club_id", clubId);

    // 2. Criar categorias padrão se não existirem
    let sub15Id: number | null = null;
    let sub17Id: number | null = null;
    let sub20Id: number | null = null;
    let profissionalId: number | null = null;

    if (!existingCategories || existingCategories.length === 0) {
      // Criar categoria Sub-15
      const { data: sub15 } = await supabase
        .from("categories")
        .insert({
          club_id: clubId,
          name: "Sub-15",
          type: "age_group",
          description: "Categoria Sub-15"
        })
        .select()
        .single();

      // Criar categoria Sub-17
      const { data: sub17 } = await supabase
        .from("categories")
        .insert({
          club_id: clubId,
          name: "Sub-17",
          type: "age_group",
          description: "Categoria Sub-17"
        })
        .select()
        .single();

      // Criar categoria Sub-20
      const { data: sub20 } = await supabase
        .from("categories")
        .insert({
          club_id: clubId,
          name: "Sub-20",
          type: "age_group",
          description: "Categoria Sub-20"
        })
        .select()
        .single();

      // Criar categoria Profissional
      const { data: profissional } = await supabase
        .from("categories")
        .insert({
          club_id: clubId,
          name: "Profissional",
          type: "age_group",
          description: "Categoria Profissional"
        })
        .select()
        .single();

      sub15Id = sub15?.id || null;
      sub17Id = sub17?.id || null;
      sub20Id = sub20?.id || null;
      profissionalId = profissional?.id || null;
    } else {
      // Usar categorias existentes
      sub15Id = existingCategories.find(c => c.name === "Sub-15")?.id || null;
      sub17Id = existingCategories.find(c => c.name === "Sub-17")?.id || null;
      sub20Id = existingCategories.find(c => c.name === "Sub-20")?.id || null;
      profissionalId = existingCategories.find(c => c.name === "Profissional")?.id || null;
    }

    // 3. Buscar jogadores da base juvenil
    const { data: youthPlayers } = await supabase
      .from("youth_players")
      .select("*")
      .eq("club_id", clubId);

    if (youthPlayers && youthPlayers.length > 0) {
      // 4. Para cada jogador da base, criar um jogador regular e associá-lo à categoria apropriada
      for (const youthPlayer of youthPlayers) {
        // Determinar a categoria com base na idade
        let categoryId = null;
        if (youthPlayer.age < 15 && sub15Id) {
          categoryId = sub15Id;
        } else if (youthPlayer.age >= 15 && youthPlayer.age < 17 && sub17Id) {
          categoryId = sub17Id;
        } else if (youthPlayer.age >= 17 && youthPlayer.age <= 20 && sub20Id) {
          categoryId = sub20Id;
        }

        if (categoryId) {
          // Verificar se o jogador já existe na tabela players
          const { data: existingPlayer } = await supabase
            .from("players")
            .select("id")
            .eq("club_id", clubId)
            .eq("name", youthPlayer.name)
            .eq("number", youthPlayer.number);

          if (!existingPlayer || existingPlayer.length === 0) {
            // Criar jogador na tabela players
            const { data: newPlayer, error: playerError } = await supabase
              .from("players")
              .insert({
                id: youthPlayer.id, // Manter o mesmo ID
                club_id: clubId,
                name: youthPlayer.name,
                position: youthPlayer.position,
                age: youthPlayer.age,
                number: youthPlayer.number,
                nationality: youthPlayer.nationality,
                height: youthPlayer.height,
                weight: youthPlayer.weight,
                birthdate: youthPlayer.birthdate,
                birthplace: youthPlayer.birthplace,
                status: youthPlayer.status,
                image: youthPlayer.image
              })
              .select()
              .single();

            if (playerError) {
              console.error("Erro ao criar jogador:", playerError);
              continue;
            }

            // Associar jogador à categoria
            await supabase
              .from("player_categories")
              .insert({
                club_id: clubId,
                player_id: newPlayer.id,
                category_id: categoryId
              });
          } else {
            // Jogador já existe, apenas associá-lo à categoria
            await supabase
              .from("player_categories")
              .insert({
                club_id: clubId,
                player_id: existingPlayer[0].id,
                category_id: categoryId
              });
          }
        }
      }
    }

    // 5. Buscar jogadores regulares que não estão associados a nenhuma categoria
    const { data: regularPlayers } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId);

    if (regularPlayers && regularPlayers.length > 0 && profissionalId) {
      for (const player of regularPlayers) {
        // Verificar se o jogador já está associado a alguma categoria
        const { data: playerCategories } = await supabase
          .from("player_categories")
          .select("*")
          .eq("club_id", clubId)
          .eq("player_id", player.id);

        if (!playerCategories || playerCategories.length === 0) {
          // Associar jogador à categoria Profissional
          await supabase
            .from("player_categories")
            .insert({
              club_id: clubId,
              player_id: player.id,
              category_id: profissionalId
            });
        }
      }
    }

    return true;
  } catch (error) {
    console.error("Erro ao migrar jogadores da base juvenil:", error);
    throw error;
  }
}

// Helper: adiciona jogador a treinos futuros da categoria
async function addPlayerToUpcomingTrainings(clubId: number, playerId: string, categoryId: number) {
  const supabase = getSupabaseClientWithClubId(clubId);
  const todayStr = new Date().toISOString().split('T')[0];

  const { data, error } = await supabase
    .from('trainings')
    .select('id, date, notes')
    .eq('club_id', clubId)
    .eq('category_id', categoryId)
    .gte('date', todayStr);

  if (error) {
    console.error('Erro ao buscar treinos futuros:', error);
    return;
  }

  for (const training of data || []) {
    const status = training.notes?.split('|')[4] || 'agendado';
    if (status === 'concluído') continue;

    const { data: existing } = await supabase
      .from('training_players')
      .select('id')
      .eq('club_id', clubId)
      .eq('training_id', training.id)
      .eq('player_id', playerId)
      .maybeSingle();

    if (!existing) {
      await supabase.from('training_players').insert({
        club_id: clubId,
        training_id: training.id,
        player_id: playerId,
      });
    }
  }
}

// Helper: adiciona jogador a convocações futuras da categoria
async function addPlayerToUpcomingCallups(clubId: number, playerId: string, categoryId: number) {
  const supabase = getSupabaseClientWithClubId(clubId);
  const nowIso = new Date().toISOString();

  const { data, error } = await supabase
    .from('callups')
    .select('id, match_date')
    .eq('club_id', clubId)
    .eq('category_id', categoryId)
    .gte('match_date', nowIso);

  if (error) {
    console.error('Erro ao buscar convocações futuras:', error);
    return;
  }

  for (const callup of data || []) {
    const matchDate = new Date(callup.match_date);
    const oneDayAfter = new Date(matchDate.getTime() + 24 * 60 * 60 * 1000);
    if (oneDayAfter < new Date()) continue;

    const { data: existing } = await supabase
      .from('callup_players')
      .select('id')
      .eq('club_id', clubId)
      .eq('callup_id', callup.id)
      .eq('player_id', playerId)
      .maybeSingle();

    if (!existing) {
      await supabase.from('callup_players').insert({
        club_id: clubId,
        callup_id: callup.id,
        player_id: playerId,
        role: 'Atleta',
      });
    }
  }
}

// Helper: remove jogador de treinos futuros da categoria anterior
async function removePlayerFromUpcomingTrainings(clubId: number, playerId: string, categoryId: number) {
  const supabase = getSupabaseClientWithClubId(clubId);
  const todayStr = new Date().toISOString().split('T')[0];

  const { data, error } = await supabase
    .from('trainings')
    .select('id, date, notes')
    .eq('club_id', clubId)
    .eq('category_id', categoryId)
    .gte('date', todayStr);

  if (error) {
    console.error('Erro ao buscar treinos futuros para remoção:', error);
    return;
  }

  for (const training of data || []) {
    const status = training.notes?.split('|')[4] || 'agendado';
    if (status === 'concluído') continue;

    const { error: delError } = await supabase
      .from('training_players')
      .delete()
      .eq('club_id', clubId)
      .eq('training_id', training.id)
      .eq('player_id', playerId);

    if (delError) {
      console.error(`Erro ao remover jogador ${playerId} do treino ${training.id}:`, delError);
    }
  }
}

// Helper: remove jogador de convocações futuras da categoria anterior
async function removePlayerFromUpcomingCallups(clubId: number, playerId: string, categoryId: number) {
  const supabase = getSupabaseClientWithClubId(clubId);
  const nowIso = new Date().toISOString();

  const { data, error } = await supabase
    .from('callups')
    .select('id, match_date')
    .eq('club_id', clubId)
    .eq('category_id', categoryId)
    .gte('match_date', nowIso);

  if (error) {
    console.error('Erro ao buscar convocações futuras para remoção:', error);
    return;
  }

  for (const callup of data || []) {
    const matchDate = new Date(callup.match_date);
    const oneDayAfter = new Date(matchDate.getTime() + 24 * 60 * 60 * 1000);
    if (oneDayAfter < new Date()) continue;

    const { error: delError } = await supabase
      .from('callup_players')
      .delete()
      .eq('club_id', clubId)
      .eq('callup_id', callup.id)
      .eq('player_id', playerId);

    if (delError) {
      console.error(`Erro ao remover jogador ${playerId} da convocação ${callup.id}:`, delError);
    }
  }
}