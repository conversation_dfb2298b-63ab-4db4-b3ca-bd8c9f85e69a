import { useState, useEffect } from 'react';
import { useUser } from '@/context/UserContext';

export interface PaymentWarning {
  type: 'payment_due_soon' | 'payment_overdue' | 'payment_current';
  message: string;
  daysUntilDue?: number;
  daysOverdue?: number;
  nextPaymentDate?: string;
  shouldShow: boolean;
  severity: 'info' | 'warning' | 'error';
}

export function usePaymentWarnings() {
  const { user } = useUser();
  const [paymentWarning, setPaymentWarning] = useState<PaymentWarning | null>(null);
  const [loading, setLoading] = useState(true);

  const calculatePaymentWarning = () => {
    if (!user?.club_info) {
      setPaymentWarning(null);
      setLoading(false);
      return;
    }

    const clubInfo = user.club_info;

    // Só mostrar avisos para clubes com planos ativos (não trial)
    if (clubInfo.is_trial || clubInfo.subscription_status !== 'active') {
      setPaymentWarning(null);
      setLoading(false);
      return;
    }

    // Verificar se há data de próximo pagamento
    const nextPaymentDate = clubInfo.next_payment_date;
    if (!nextPaymentDate) {
      setPaymentWarning(null);
      setLoading(false);
      return;
    }

    const now = new Date();
    const paymentDate = new Date(nextPaymentDate);
    const diffTime = paymentDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Pagamento em atraso
    if (diffDays < 0) {
      const daysOverdue = Math.abs(diffDays);
      setPaymentWarning({
        type: 'payment_overdue',
        message: `Pagamento em atraso há ${daysOverdue} dia${daysOverdue !== 1 ? 's' : ''}`,
        daysOverdue,
        nextPaymentDate,
        shouldShow: true,
        severity: 'error'
      });
      setLoading(false);
      return;
    }

    // Pagamento vence em breve (7 dias ou menos) - Banner aparece
    if (diffDays <= 7) {
      setPaymentWarning({
        type: 'payment_due_soon',
        message: diffDays === 0 
          ? 'Pagamento vence hoje' 
          : `Pagamento vence em ${diffDays} dia${diffDays !== 1 ? 's' : ''}`,
        daysUntilDue: diffDays,
        nextPaymentDate,
        shouldShow: true, // Banner aparece
        severity: diffDays <= 2 ? 'error' : 'warning'
      });
      setLoading(false);
      return;
    }

    // Pagamento em dia - Card aparece, banner não
    setPaymentWarning({
      type: 'payment_current',
      message: 'Pagamento em dia',
      daysUntilDue: diffDays,
      nextPaymentDate,
      shouldShow: false, // Banner NÃO aparece
      severity: 'info'
    });
    setLoading(false);
  };

  useEffect(() => {
    calculatePaymentWarning();
  }, [user?.club_info?.next_payment_date, user?.club_info?.subscription_status]);

  return {
    paymentWarning,
    loading,
    refreshPaymentWarning: calculatePaymentWarning
  };
}