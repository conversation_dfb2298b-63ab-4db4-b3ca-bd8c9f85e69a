import { supabase } from "@/integrations/supabase/client";
import { requireSession } from "./session";

/**
 * Verifica se o usuário atual tem acesso ao clube especificado
 */
export async function validateUserClubAccess(clubId: number): Promise<boolean> {
  try {
    await requireSession();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    // Verificar se o usuário é membro do clube
    const { data: membership, error } = await supabase
      .from("club_members")
      .select("id, status")
      .eq("club_id", clubId)
      .eq("user_id", user.id)
      .single();

    if (error || !membership) {
      console.warn(`Tentativa de acesso não autorizado ao clube ${clubId} pelo usuário ${user.id}`);
      return false;
    }

    // Verificar se o membro está ativo (aceitar tanto 'active' quanto 'ativo')
    if (membership.status !== 'active' && membership.status !== 'ativo') {
      console.warn(`Usuário ${user.id} tem status inativo no clube ${clubId} (status: ${membership.status})`);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Erro ao validar acesso ao clube:", error);
    return false;
  }
}

/**
 * Verifica se o usuário tem acesso ao clube pelo slug
 * Só executa se o usuário estiver completamente logado
 */
export async function validateUserClubAccessBySlug(slug: string, skipIfNotLoggedIn: boolean = false): Promise<{ hasAccess: boolean; clubId?: number; notLoggedIn?: boolean }> {
  try {
    // Verificar se há sessão ativa
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      if (skipIfNotLoggedIn) {
        return { hasAccess: false, notLoggedIn: true };
      }
      return { hasAccess: false };
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      if (skipIfNotLoggedIn) {
        return { hasAccess: false, notLoggedIn: true };
      }
      return { hasAccess: false };
    }

    // Buscar o clube pelo slug
    const { data: club, error: clubError } = await supabase
      .from("club_info")
      .select("id")
      .eq("slug", slug)
      .single();

    if (clubError || !club) {
      console.warn(`Clube com slug '${slug}' não encontrado`);
      return { hasAccess: false };
    }

    // Verificar se o usuário é membro do clube
    const { data: membership, error: membershipError } = await supabase
      .from("club_members")
      .select("id, status")
      .eq("club_id", club.id)
      .eq("user_id", user.id)
      .single();

    if (membershipError || !membership) {
      console.warn(`TENTATIVA DE ACESSO NÃO AUTORIZADO: Usuário ${user.id} tentou acessar clube ${club.id} (${slug}) sem permissão`);
      return { hasAccess: false, clubId: club.id };
    }

    // Verificar se o membro está ativo (aceitar tanto 'active' quanto 'ativo')
    if (membership.status !== 'active' && membership.status !== 'ativo') {
      console.warn(`Usuário ${user.id} tem status inativo no clube ${club.id} (${slug}) - status: ${membership.status}`);
      return { hasAccess: false, clubId: club.id };
    }

    return { hasAccess: true, clubId: club.id };
  } catch (error) {
    console.error("Erro ao validar acesso ao clube por slug:", error);
    return { hasAccess: false };
  }
}

/**
 * Obtém todos os clubes que o usuário tem acesso
 */
export async function getUserAuthorizedClubs(): Promise<{ id: number; slug: string | null; name: string }[]> {
  try {
    await requireSession();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return [];
    }

    // Buscar clubes do usuário com informações básicas
    const { data: clubs, error } = await supabase
      .from("club_members")
      .select(`
        club_id,
        club_info!inner (
          id,
          slug,
          name
        )
      `)
      .eq("user_id", user.id)
      .in("status", ["active", "ativo"]);

    if (error) {
      console.error("Erro ao buscar clubes autorizados:", error);
      return [];
    }

    return clubs?.map(item => ({
      id: item.club_info.id,
      slug: item.club_info.slug,
      name: item.club_info.name
    })) || [];
  } catch (error) {
    console.error("Erro ao obter clubes autorizados:", error);
    return [];
  }
}