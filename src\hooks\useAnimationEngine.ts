import { useEffect, useRef, useState, useCallback } from 'react';
import { AnimationEngine, AnimationSettings, PlaybackState, AnimationFrame, TimelineEvent } from '@/lib/AnimationEngine';
import { TrainingElement, DrawingElement } from '@/components/training/InteractiveTrainingBuilder';
import { Trajectory } from '@/components/training/TrajectorySystem';

export interface UseAnimationEngineOptions {
  settings?: Partial<AnimationSettings>;
  autoStart?: boolean;
  onFrameChange?: (frame: number) => void;
  onPlayStateChange?: (playing: boolean) => void;
  onComplete?: () => void;
  onProgress?: (progress: number) => void;
}

export interface AnimationEngineHook {
  // Engine instance
  engine: AnimationEngine;
  
  // Playback state
  playbackState: PlaybackState;
  isPlaying: boolean;
  currentFrame: number;
  totalFrames: number;
  progress: number;
  
  // Playback controls
  play: () => void;
  pause: () => void;
  stop: () => void;
  seek: (frame: number) => void;
  setSpeed: (speed: number) => void;
  setLoop: (loop: boolean) => void;
  setDirection: (direction: 'forward' | 'backward') => void;
  
  // Frame management
  addFrame: (elements: TrainingElement[], drawings: DrawingElement[], trajectories: Trajectory[], timestamp?: number) => string;
  removeFrame: (frameId: string) => boolean;
  updateFrame: (frameId: string, updates: Partial<AnimationFrame>) => boolean;
  getCurrentFrame: () => AnimationFrame | null;
  getFrameAtTime: (timestamp: number) => AnimationFrame | null;
  
  // Trajectory management
  addTrajectory: (trajectory: Trajectory) => void;
  updateTrajectory: (id: string, updates: Partial<Trajectory>) => void;
  removeTrajectory: (id: string) => void;
  
  // Timeline management
  addTimelineEvent: (event: Omit<TimelineEvent, 'id'>) => string;
  removeTimelineEvent: (eventId: string) => boolean;
  getTimelineEvents: (startTime: number, endTime: number) => TimelineEvent[];
  
  // Settings
  updateSettings: (settings: Partial<AnimationSettings>) => void;
  getSettings: () => AnimationSettings;
  
  // Utility
  reset: () => void;
  canPlay: () => boolean;
  getCurrentTime: () => number;
  
  // Export/Import
  exportFrames: () => AnimationFrame[];
  exportTimeline: () => TimelineEvent[];
  importFrames: (frames: AnimationFrame[]) => void;
  importTimeline: (timeline: TimelineEvent[]) => void;
}

export const useAnimationEngine = (options: UseAnimationEngineOptions = {}): AnimationEngineHook => {
  const {
    settings = {},
    autoStart = false,
    onFrameChange,
    onPlayStateChange,
    onComplete,
    onProgress
  } = options;

  // Create engine instance
  const engineRef = useRef<AnimationEngine | null>(null);
  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    currentFrame: 0,
    totalFrames: 0,
    speed: 1,
    loop: false,
    direction: 'forward'
  });

  // Initialize engine
  useEffect(() => {
    if (!engineRef.current) {
      engineRef.current = new AnimationEngine(settings);
      
      // Set up callbacks
      engineRef.current.onFrameChange((frame) => {
        setPlaybackState(prev => ({ ...prev, currentFrame: frame }));
        onFrameChange?.(frame);
      });
      
      engineRef.current.onPlayStateChange((playing) => {
        setPlaybackState(prev => ({ ...prev, isPlaying: playing }));
        onPlayStateChange?.(playing);
      });
      
      engineRef.current.onComplete(() => {
        onComplete?.();
      });
      
      engineRef.current.onProgress((progress) => {
        onProgress?.(progress);
      });
      
      // Initialize playback state
      setPlaybackState(engineRef.current.getPlaybackState());
    }
    
    return () => {
      if (engineRef.current) {
        engineRef.current.destroy();
        engineRef.current = null;
      }
    };
  }, []);

  // Update settings when they change
  useEffect(() => {
    if (engineRef.current && Object.keys(settings).length > 0) {
      engineRef.current.updateSettings(settings);
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, [settings]);

  // Auto start if requested
  useEffect(() => {
    if (autoStart && engineRef.current && engineRef.current.canPlay()) {
      engineRef.current.play();
    }
  }, [autoStart]);

  // Playback controls
  const play = useCallback(() => {
    engineRef.current?.play();
  }, []);

  const pause = useCallback(() => {
    engineRef.current?.pause();
  }, []);

  const stop = useCallback(() => {
    engineRef.current?.stop();
  }, []);

  const seek = useCallback((frame: number) => {
    engineRef.current?.seek(frame);
  }, []);

  const setSpeed = useCallback((speed: number) => {
    engineRef.current?.setSpeed(speed);
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  const setLoop = useCallback((loop: boolean) => {
    engineRef.current?.setLoop(loop);
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  const setDirection = useCallback((direction: 'forward' | 'backward') => {
    engineRef.current?.setDirection(direction);
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  // Frame management
  const addFrame = useCallback((
    elements: TrainingElement[], 
    drawings: DrawingElement[], 
    trajectories: Trajectory[], 
    timestamp?: number
  ): string => {
    if (!engineRef.current) return '';
    
    const frameTimestamp = timestamp ?? engineRef.current.getCurrentTime();
    const frameId = engineRef.current.addFrame({
      timestamp: frameTimestamp,
      elements: [...elements],
      drawings: [...drawings],
      trajectories: [...trajectories]
    });
    
    setPlaybackState(engineRef.current.getPlaybackState());
    return frameId;
  }, []);

  const removeFrame = useCallback((frameId: string): boolean => {
    if (!engineRef.current) return false;
    
    const result = engineRef.current.removeFrame(frameId);
    if (result) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
    return result;
  }, []);

  const updateFrame = useCallback((frameId: string, updates: Partial<AnimationFrame>): boolean => {
    if (!engineRef.current) return false;
    
    const result = engineRef.current.updateFrame(frameId, updates);
    if (result) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
    return result;
  }, []);

  const getCurrentFrame = useCallback((): AnimationFrame | null => {
    return engineRef.current?.getCurrentFrame() ?? null;
  }, []);

  const getFrameAtTime = useCallback((timestamp: number): AnimationFrame | null => {
    return engineRef.current?.getFrameAtTime(timestamp) ?? null;
  }, []);

  // Trajectory management
  const addTrajectory = useCallback((trajectory: Trajectory) => {
    engineRef.current?.addTrajectory(trajectory);
  }, []);

  const updateTrajectory = useCallback((id: string, updates: Partial<Trajectory>) => {
    engineRef.current?.updateTrajectory(id, updates);
  }, []);

  const removeTrajectory = useCallback((id: string) => {
    engineRef.current?.removeTrajectory(id);
  }, []);

  // Timeline management
  const addTimelineEvent = useCallback((event: Omit<TimelineEvent, 'id'>): string => {
    return engineRef.current?.addTimelineEvent(event) ?? '';
  }, []);

  const removeTimelineEvent = useCallback((eventId: string): boolean => {
    return engineRef.current?.removeTimelineEvent(eventId) ?? false;
  }, []);

  const getTimelineEvents = useCallback((startTime: number, endTime: number): TimelineEvent[] => {
    return engineRef.current?.getTimelineEvents(startTime, endTime) ?? [];
  }, []);

  // Settings
  const updateSettings = useCallback((newSettings: Partial<AnimationSettings>) => {
    engineRef.current?.updateSettings(newSettings);
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  const getSettings = useCallback((): AnimationSettings => {
    return engineRef.current?.getSettings() ?? {
      fps: 30,
      duration: 10000,
      quality: 'high',
      smoothing: true,
      interpolation: 'ease'
    };
  }, []);

  // Utility
  const reset = useCallback(() => {
    engineRef.current?.reset();
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  const canPlay = useCallback((): boolean => {
    return engineRef.current?.canPlay() ?? false;
  }, []);

  const getCurrentTime = useCallback((): number => {
    return engineRef.current?.getCurrentTime() ?? 0;
  }, []);

  // Export/Import
  const exportFrames = useCallback((): AnimationFrame[] => {
    return engineRef.current?.exportFrames() ?? [];
  }, []);

  const exportTimeline = useCallback((): TimelineEvent[] => {
    return engineRef.current?.exportTimeline() ?? [];
  }, []);

  const importFrames = useCallback((frames: AnimationFrame[]) => {
    engineRef.current?.importFrames(frames);
    if (engineRef.current) {
      setPlaybackState(engineRef.current.getPlaybackState());
    }
  }, []);

  const importTimeline = useCallback((timeline: TimelineEvent[]) => {
    engineRef.current?.importTimeline(timeline);
  }, []);

  // Computed values
  const isPlaying = playbackState.isPlaying;
  const currentFrame = playbackState.currentFrame;
  const totalFrames = playbackState.totalFrames;
  const progress = totalFrames > 0 ? (currentFrame / (totalFrames - 1)) * 100 : 0;

  return {
    engine: engineRef.current!,
    playbackState,
    isPlaying,
    currentFrame,
    totalFrames,
    progress,
    play,
    pause,
    stop,
    seek,
    setSpeed,
    setLoop,
    setDirection,
    addFrame,
    removeFrame,
    updateFrame,
    getCurrentFrame,
    getFrameAtTime,
    addTrajectory,
    updateTrajectory,
    removeTrajectory,
    addTimelineEvent,
    removeTimelineEvent,
    getTimelineEvents,
    updateSettings,
    getSettings,
    reset,
    canPlay,
    getCurrentTime,
    exportFrames,
    exportTimeline,
    importFrames,
    importTimeline
  };
};

// Hook for managing multiple animation engines
export const useMultipleAnimationEngines = (count: number, options: UseAnimationEngineOptions = {}) => {
  const engines = Array.from({ length: count }, () => useAnimationEngine(options));
  
  const playAll = useCallback(() => {
    engines.forEach(engine => engine.play());
  }, [engines]);
  
  const pauseAll = useCallback(() => {
    engines.forEach(engine => engine.pause());
  }, [engines]);
  
  const stopAll = useCallback(() => {
    engines.forEach(engine => engine.stop());
  }, [engines]);
  
  const resetAll = useCallback(() => {
    engines.forEach(engine => engine.reset());
  }, [engines]);
  
  return {
    engines,
    playAll,
    pauseAll,
    stopAll,
    resetAll
  };
};