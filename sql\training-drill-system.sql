-- Training Drill System Database Schema
-- This creates the complete database structure for the interactive training system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Training Drills table
CREATE TABLE IF NOT EXISTS training_drills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    club_id INTEGER NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('tactical', 'technical', 'physical', 'transition', 'finishing')),
    difficulty VARCHAR(20) NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced', 'expert')),
    total_duration INTEGER NOT NULL DEFAULT 300, -- in seconds
    players_required INTEGER NOT NULL DEFAULT 11,
    equipment_needed TEXT[], -- array of equipment names
    objectives TEXT[], -- array of objectives
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drill Steps table
CREATE TABLE IF NOT EXISTS drill_steps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER NOT NULL DEFAULT 300, -- in seconds
    transition_time INTEGER DEFAULT 5, -- transition time to next step in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(drill_id, step_order)
);

-- Training Elements table (cones, players, balls, etc.)
CREATE TABLE IF NOT EXISTS training_elements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    step_id UUID NOT NULL REFERENCES drill_steps(id) ON DELETE CASCADE,
    element_type VARCHAR(50) NOT NULL CHECK (element_type IN ('cone', 'ball', 'goal', 'player', 'marker', 'annotation')),
    position_x DECIMAL(5,2) NOT NULL, -- percentage position on field
    position_y DECIMAL(5,2) NOT NULL, -- percentage position on field
    properties JSONB DEFAULT '{}', -- color, size, label, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Training Drawings table (lines, arrows, shapes, etc.)
CREATE TABLE IF NOT EXISTS training_drawings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    step_id UUID NOT NULL REFERENCES drill_steps(id) ON DELETE CASCADE,
    drawing_type VARCHAR(50) NOT NULL CHECK (drawing_type IN ('line', 'arrow', 'circle', 'rectangle', 'polygon', 'text', 'freehand')),
    points JSONB NOT NULL, -- array of {x, y} coordinates
    properties JSONB DEFAULT '{}', -- stroke color, width, fill, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drill Sequences table (saved sequences of drills)
CREATE TABLE IF NOT EXISTS drill_sequences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    club_id INTEGER NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    total_duration INTEGER NOT NULL DEFAULT 0,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drill Sequence Items table (drills within a sequence)
CREATE TABLE IF NOT EXISTS drill_sequence_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sequence_id UUID NOT NULL REFERENCES drill_sequences(id) ON DELETE CASCADE,
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    sequence_order INTEGER NOT NULL,
    duration_override INTEGER, -- override drill duration for this sequence
    transition_time INTEGER DEFAULT 5,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(sequence_id, sequence_order)
);

-- Training Settings table (user preferences)
CREATE TABLE IF NOT EXISTS training_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    club_id INTEGER NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    settings_category VARCHAR(50) NOT NULL, -- 'field', 'ui', 'performance', etc.
    settings_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, club_id, settings_category)
);

-- Training Favorites table
CREATE TABLE IF NOT EXISTS training_favorites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, drill_id)
);

-- Training Analysis table (tactical analysis results)
CREATE TABLE IF NOT EXISTS training_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    drill_id UUID NOT NULL REFERENCES training_drills(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL, -- 'spatial_coverage', 'player_interaction', etc.
    score DECIMAL(5,2),
    metrics JSONB DEFAULT '{}',
    recommendations TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(drill_id, analysis_type)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_training_drills_club_id ON training_drills(club_id);
CREATE INDEX IF NOT EXISTS idx_training_drills_category ON training_drills(category);
CREATE INDEX IF NOT EXISTS idx_training_drills_difficulty ON training_drills(difficulty);
CREATE INDEX IF NOT EXISTS idx_training_drills_created_by ON training_drills(created_by);
CREATE INDEX IF NOT EXISTS idx_drill_steps_drill_id ON drill_steps(drill_id);
CREATE INDEX IF NOT EXISTS idx_drill_steps_order ON drill_steps(drill_id, step_order);
CREATE INDEX IF NOT EXISTS idx_training_elements_step_id ON training_elements(step_id);
CREATE INDEX IF NOT EXISTS idx_training_drawings_step_id ON training_drawings(step_id);
CREATE INDEX IF NOT EXISTS idx_drill_sequences_club_id ON drill_sequences(club_id);
CREATE INDEX IF NOT EXISTS idx_drill_sequence_items_sequence_id ON drill_sequence_items(sequence_id);
CREATE INDEX IF NOT EXISTS idx_training_settings_user_club ON training_settings(user_id, club_id);
CREATE INDEX IF NOT EXISTS idx_training_favorites_user_id ON training_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_training_analysis_drill_id ON training_analysis(drill_id);

-- Row Level Security (RLS) Policies

-- Training Drills RLS
ALTER TABLE training_drills ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view drills from their club or public drills" ON training_drills
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid()
        ) OR is_public = true
    );

CREATE POLICY "Users can create drills for their club" ON training_drills
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own drills" ON training_drills
    FOR UPDATE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'coach')
        )
    );

CREATE POLICY "Users can delete their own drills" ON training_drills
    FOR DELETE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'coach')
        )
    );

-- Drill Steps RLS
ALTER TABLE drill_steps ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view drill steps from accessible drills" ON drill_steps
    FOR SELECT USING (
        drill_id IN (
            SELECT id FROM training_drills 
            WHERE club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid()
            ) OR is_public = true
        )
    );

CREATE POLICY "Users can manage drill steps for accessible drills" ON drill_steps
    FOR ALL USING (
        drill_id IN (
            SELECT id FROM training_drills 
            WHERE created_by = auth.uid() OR 
            club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid() 
                AND role IN ('admin', 'coach')
            )
        )
    );

-- Training Elements RLS
ALTER TABLE training_elements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage training elements for accessible steps" ON training_elements
    FOR ALL USING (
        step_id IN (
            SELECT ds.id FROM drill_steps ds
            JOIN training_drills td ON ds.drill_id = td.id
            WHERE td.created_by = auth.uid() OR 
            td.club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid() 
                AND role IN ('admin', 'coach')
            ) OR td.is_public = true
        )
    );

-- Training Drawings RLS
ALTER TABLE training_drawings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage training drawings for accessible steps" ON training_drawings
    FOR ALL USING (
        step_id IN (
            SELECT ds.id FROM drill_steps ds
            JOIN training_drills td ON ds.drill_id = td.id
            WHERE td.created_by = auth.uid() OR 
            td.club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid() 
                AND role IN ('admin', 'coach')
            ) OR td.is_public = true
        )
    );

-- Drill Sequences RLS
ALTER TABLE drill_sequences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view sequences from their club" ON drill_sequences
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create sequences for their club" ON drill_sequences
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own sequences" ON drill_sequences
    FOR UPDATE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'coach')
        )
    );

CREATE POLICY "Users can delete their own sequences" ON drill_sequences
    FOR DELETE USING (
        created_by = auth.uid() OR 
        club_id IN (
            SELECT club_id FROM user_clubs 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'coach')
        )
    );

-- Drill Sequence Items RLS
ALTER TABLE drill_sequence_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage sequence items for accessible sequences" ON drill_sequence_items
    FOR ALL USING (
        sequence_id IN (
            SELECT id FROM drill_sequences 
            WHERE created_by = auth.uid() OR 
            club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid() 
                AND role IN ('admin', 'coach')
            )
        )
    );

-- Training Settings RLS
ALTER TABLE training_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own settings" ON training_settings
    FOR ALL USING (user_id = auth.uid());

-- Training Favorites RLS
ALTER TABLE training_favorites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own favorites" ON training_favorites
    FOR ALL USING (user_id = auth.uid());

-- Training Analysis RLS
ALTER TABLE training_analysis ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view analysis for accessible drills" ON training_analysis
    FOR SELECT USING (
        drill_id IN (
            SELECT id FROM training_drills 
            WHERE club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid()
            ) OR is_public = true
        )
    );

CREATE POLICY "Users can create analysis for accessible drills" ON training_analysis
    FOR INSERT WITH CHECK (
        drill_id IN (
            SELECT id FROM training_drills 
            WHERE created_by = auth.uid() OR 
            club_id IN (
                SELECT club_id FROM user_clubs 
                WHERE user_id = auth.uid() 
                AND role IN ('admin', 'coach')
            )
        )
    );

-- Functions for calculating drill metrics
CREATE OR REPLACE FUNCTION calculate_drill_metrics(drill_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    element_count INTEGER;
    spatial_coverage DECIMAL;
    player_interaction DECIMAL;
BEGIN
    -- Count elements in the drill
    SELECT COUNT(*) INTO element_count
    FROM training_elements te
    JOIN drill_steps ds ON te.step_id = ds.id
    WHERE ds.drill_id = drill_uuid;
    
    -- Calculate spatial coverage (simplified)
    SELECT COALESCE(
        (MAX(position_x) - MIN(position_x)) * (MAX(position_y) - MIN(position_y)) * 100,
        0
    ) INTO spatial_coverage
    FROM training_elements te
    JOIN drill_steps ds ON te.step_id = ds.id
    WHERE ds.drill_id = drill_uuid;
    
    -- Calculate player interaction (simplified)
    SELECT COALESCE(
        COUNT(*) * 10.0,
        0
    ) INTO player_interaction
    FROM training_elements te
    JOIN drill_steps ds ON te.step_id = ds.id
    WHERE ds.drill_id = drill_uuid AND te.element_type = 'player';
    
    result := jsonb_build_object(
        'element_count', element_count,
        'spatial_coverage', LEAST(spatial_coverage, 100),
        'player_interaction', LEAST(player_interaction, 100)
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_training_drills_updated_at BEFORE UPDATE ON training_drills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drill_steps_updated_at BEFORE UPDATE ON drill_steps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drill_sequences_updated_at BEFORE UPDATE ON drill_sequences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_settings_updated_at BEFORE UPDATE ON training_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_analysis_updated_at BEFORE UPDATE ON training_analysis
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();