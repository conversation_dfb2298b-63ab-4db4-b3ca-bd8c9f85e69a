-- Add financial tracking to medical_professionals
ALTER TABLE medical_professionals
  ADD COLUMN IF NOT EXISTS financial_data JSONB DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS bank_info JSONB DEFAULT '{}';

-- Add reference to medical professionals in financial_transactions
ALTER TABLE financial_transactions
  ADD COLUMN IF NOT EXISTS medical_professional_id INTEGER REFERENCES medical_professionals(id);

-- Allow salary advances for medical professionals
-- No schema change needed besides documentation, as person_type is free text.