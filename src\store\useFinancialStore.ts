import { create } from "zustand";
import { supabase } from "@/integrations/supabase/client";
import { FinancialTransaction } from "../api/api";
import { createFinancialTransaction, updateFinancialTransaction, deleteFinancialTransaction, markTransactionAsPaid } from "../api/api";

interface FinancialState {
  transactions: FinancialTransaction[];
  loading: boolean;
  error: string | null;
  fetchTransactions: (clubId: number, month?: number, year?: number) => Promise<void>;
  addTransaction: (clubId: number, tx: Omit<FinancialTransaction, "id">) => Promise<FinancialTransaction>;
  updateTransaction: (clubId: number, id: number, tx: Partial<FinancialTransaction>) => Promise<void>;
  deleteTransaction: (clubId: number, id: number) => Promise<void>;
  markAsPaid: (clubId: number, id: number) => Promise<void>;
}

export const useFinancialStore = create<FinancialState>((set) => ({
  transactions: [],
  loading: false,
  error: null,

  fetchTransactions: async (clubId: number, month?: number, year?: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      // Primeiro, buscar todas as transações do clube
      const { data, error } = await supabase
        .from('financial_transactions')
        .select('*')
        .eq('club_id', clubId);

      if (error) {
        console.error("Erro ao buscar transações financeiras:", error);
        throw new Error(`Erro ao buscar transações financeiras: ${error.message}`);
      }

      // Filtrar por mês/ano se necessário
      let filteredData = data || [];
      
      if (month !== undefined && year !== undefined) {
        filteredData = filteredData.filter(transaction => {
          if (!transaction.date) return false;
          const transactionDate = new Date(transaction.date);
          return transactionDate.getMonth() === month && 
                 transactionDate.getFullYear() === year;
        });
      }

      // Ordenar por data (mais recente primeiro)
      filteredData.sort((a, b) => {
        const dateA = a.date ? new Date(a.date).getTime() : 0;
        const dateB = b.date ? new Date(b.date).getTime() : 0;
        return dateB - dateA;
      });

      // Processar os dados para extrair informações adicionais
      const transactions = await Promise.all(
        filteredData.map(async (transaction: any) => {
          let playerName = null;
          if (transaction.player_id && transaction.description?.includes(" - ")) {
            const parts = transaction.description.split(" - ");
            if (parts.length > 1) {
              playerName = parts[1];
            }
          }

          return {
            ...transaction,
            payment_status: (transaction.payment_status || '').toLowerCase(),
            player_name: playerName,
          };
        })
      );

      console.log(`Transações carregadas (${month !== undefined ? `Mês: ${month + 1}, Ano: ${year}` : 'Sem filtro de data'}):`, transactions.length);
      set({ transactions, loading: false });
    } catch (err: unknown) {
      console.error("Erro ao buscar transações:", err);
      set({ 
        error: err instanceof Error ? err.message : "Erro ao buscar transações", 
        loading: false 
      });
    }
  },

  addTransaction: async (clubId: number, transaction: Omit<FinancialTransaction, "id">): Promise<FinancialTransaction> => {
    set({ loading: true, error: null });
    try {
      const newTransaction = await createFinancialTransaction(clubId, transaction);
      set((state) => ({ transactions: [...state.transactions, newTransaction], loading: false }));
      return newTransaction;
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar transação", loading: false });
      throw err;
    }
  },

  updateTransaction: async (clubId: number, id: number, transaction: Partial<FinancialTransaction>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateFinancialTransaction(clubId, id, transaction);
      if (updated) {
        set((state) => ({ transactions: state.transactions.map(t => t.id === id ? updated : t), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar transação", loading: false });
    }
  },

  deleteTransaction: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteFinancialTransaction(clubId, id);
      if (ok) {
        set((state) => ({ transactions: state.transactions.filter(t => t.id !== id), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar transação", loading: false });
    }
  },

  markAsPaid: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await markTransactionAsPaid(clubId, id);
      if (updated) {
        set((state) => ({ transactions: state.transactions.map(t => t.id === id ? updated : t), loading: false }));
      } else {
        set({ error: "Transação não encontrada", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao marcar transação como paga", loading: false });
    }
  },
}));
