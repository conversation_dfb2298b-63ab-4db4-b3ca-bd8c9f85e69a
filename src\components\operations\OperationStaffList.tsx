import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { getGameOperationStaff, deleteGameOperationStaff, GameOperationStaff } from '@/api/gameOperations';
import { useCurrentClubId } from '@/context/ClubContext';
import { useUser } from '@/context/UserContext';
import { toast } from '@/components/ui/use-toast';

interface OperationStaffListProps {
  onAdd: () => void;
  onEdit: (staff: GameOperationStaff) => void;
  refreshSignal?: boolean;
}

export function OperationStaffList({ onAdd, onEdit, refreshSignal }: OperationStaffListProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [staff, setStaff] = useState<GameOperationStaff[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');

  const fetchData = async () => {
    try {
      setLoading(true);
      const data = await getGameOperationStaff(clubId);
      setStaff(data);
    } catch (err) {
      console.error(err);
      toast({ title: 'Erro', description: 'Não foi possível carregar membros', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { fetchData(); }, [clubId, refreshSignal]);

  const filtered = staff.filter(s =>
    s.name.toLowerCase().includes(search.toLowerCase()) ||
    s.role.toLowerCase().includes(search.toLowerCase())
  );

  const handleDelete = async (s: GameOperationStaff) => {
    if (!confirm(`Excluir ${s.name}?`)) return;
    try {
      await deleteGameOperationStaff(clubId, user?.id || '', s.id);
      toast({ title: 'Sucesso', description: 'Membro excluído' });
      fetchData();
    } catch (err) {
      console.error(err);
      toast({ title: 'Erro', description: 'Não foi possível excluir', variant: 'destructive' });
    }
  };

  return (
    <Card>
      <CardHeader className="flex items-center justify-between">
        <CardTitle>Membros de Operação de Jogo</CardTitle>
        <Button onClick={onAdd}>
          <Plus className="h-4 w-4 mr-1" />
          Novo Membro
        </Button>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <Input placeholder="Buscar membro..." value={search} onChange={e => setSearch(e.target.value)} />
        </div>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : filtered.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {search ? 'Nenhum membro encontrado' : 'Nenhum membro cadastrado'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead className="hidden md:table-cell">Função</TableHead>
                <TableHead className="hidden md:table-cell">CPF</TableHead>
                <TableHead className="hidden md:table-cell">Pix/Conta</TableHead>
                <TableHead className="w-[120px] text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filtered.map(m => (
                <TableRow key={m.id}>
                  <TableCell className="font-medium">{m.name}</TableCell>
                  <TableCell className="hidden md:table-cell">{m.role}</TableCell>
                  <TableCell className="hidden md:table-cell">{m.cpf || '-'}</TableCell>
                  <TableCell className="hidden md:table-cell">{m.pix_key || '-'}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button variant="ghost" size="sm" onClick={() => onEdit(m)} title="Editar">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDelete(m)} title="Excluir" className="text-red-500 hover:text-red-700 hover:bg-red-50">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
