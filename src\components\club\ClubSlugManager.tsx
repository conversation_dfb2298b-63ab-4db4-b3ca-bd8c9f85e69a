import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCurrentClubId } from '@/context/ClubContext';
import { useClubSlug } from '@/hooks/useClubSlug';
import { 
  updateClubSlug, 
  isSlugAvailable, 
  validateSlug, 
  generateSlugFromName 
} from '@/api/clubs';
import { toast } from 'sonner';
import { Link, ExternalLink, AlertCircle, CheckCircle } from 'lucide-react';

export function ClubSlugManager() {
  const clubId = useCurrentClubId();
  const { club, clubSlug, redirectToClub } = useClubSlug();
  const [newSlug, setNewSlug] = useState('');
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [validation, setValidation] = useState<{ valid: boolean; error?: string }>({ valid: true });
  const [availability, setAvailability] = useState<{ available: boolean; checked: boolean }>({ 
    available: true, 
    checked: false 
  });

  useEffect(() => {
    if (club?.slug) {
      setNewSlug(club.slug);
    }
  }, [club]);

  // Validar slug em tempo real
  useEffect(() => {
    if (newSlug) {
      const validationResult = validateSlug(newSlug);
      setValidation(validationResult);
      
      // Se válido e diferente do atual, verificar disponibilidade
      if (validationResult.valid && newSlug !== clubSlug) {
        checkAvailability(newSlug);
      } else if (newSlug === clubSlug) {
        setAvailability({ available: true, checked: true });
      }
    } else {
      setValidation({ valid: false, error: "Slug é obrigatório" });
      setAvailability({ available: false, checked: false });
    }
  }, [newSlug, clubSlug]);

  const checkAvailability = async (slug: string) => {
    setChecking(true);
    try {
      const available = await isSlugAvailable(slug, clubId);
      setAvailability({ available, checked: true });
    } catch (error) {
      console.error('Erro ao verificar disponibilidade:', error);
      setAvailability({ available: false, checked: false });
    } finally {
      setChecking(false);
    }
  };

  const handleGenerateFromName = () => {
    if (club?.name) {
      const generated = generateSlugFromName(club.name);
      setNewSlug(generated);
    }
  };

  const handleSave = async () => {
    if (!validation.valid || !availability.available) {
      return;
    }

    setLoading(true);
    try {
      await updateClubSlug(clubId, newSlug);
      toast.success('URL personalizada atualizada com sucesso!');
      
      // Redirecionar para a nova URL
      setTimeout(() => {
        redirectToClub(newSlug, '/configuracoes');
      }, 1000);
    } catch (error) {
      console.error('Erro ao atualizar slug:', error);
      toast.error('Erro ao atualizar URL personalizada');
    } finally {
      setLoading(false);
    }
  };

  const currentUrl = `${window.location.origin}/${clubSlug}`;
  const newUrl = `${window.location.origin}/${newSlug}`;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="w-5 h-5" />
          URL Personalizada do Clube
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label htmlFor="current-url">URL Atual</Label>
          <div className="flex items-center gap-2 mt-1">
            <Input
              id="current-url"
              value={currentUrl}
              readOnly
              className="bg-gray-50"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(currentUrl, '_blank')}
            >
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="new-slug">Nova URL Personalizada</Label>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">{window.location.origin}/</span>
              <Input
                id="new-slug"
                value={newSlug}
                onChange={(e) => setNewSlug(e.target.value.toLowerCase())}
                placeholder="meu-clube"
                className={`flex-1 ${
                  !validation.valid ? 'border-red-500' : 
                  validation.valid && availability.available ? 'border-green-500' : ''
                }`}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateFromName}
                disabled={!club?.name}
              >
                Gerar do Nome
              </Button>
            </div>
            
            {/* Preview da nova URL */}
            <div className="text-sm text-gray-600">
              Preview: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{newUrl}</span>
            </div>
          </div>
        </div>

        {/* Validação e disponibilidade */}
        <div className="space-y-2">
          {!validation.valid && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validation.error}</AlertDescription>
            </Alert>
          )}
          
          {validation.valid && availability.checked && !availability.available && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>Esta URL já está em uso por outro clube</AlertDescription>
            </Alert>
          )}
          
          {validation.valid && availability.available && newSlug !== clubSlug && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>URL disponível!</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Regras para slugs */}
        <div className="text-sm text-gray-600 space-y-1">
          <p className="font-medium">Regras para URL personalizada:</p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Apenas letras minúsculas, números e hífens</li>
            <li>Entre 2 e 50 caracteres</li>
            <li>Não pode começar ou terminar com hífen</li>
            <li>Não pode conter hífens consecutivos</li>
            <li>Não pode usar palavras reservadas (admin, api, etc.)</li>
          </ul>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={
              loading || 
              checking || 
              !validation.valid || 
              !availability.available || 
              newSlug === clubSlug
            }
          >
            {loading ? 'Salvando...' : 'Salvar URL'}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setNewSlug(club?.slug || '')}
            disabled={loading}
          >
            Cancelar
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}