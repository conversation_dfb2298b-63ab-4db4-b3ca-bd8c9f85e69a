-- Atualizar políticas de notificação para incluir president e manager

-- Remover política existente de inserção
DROP POLICY IF EXISTS notifications_insert ON notifications;

-- Criar nova política de inserção incluindo president e manager
CREATE POLICY notifications_insert ON notifications
    FOR INSERT
    WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical', 'president', 'manager')
        )
    );

-- Remover política existente de atualização
DROP POLICY IF EXISTS notifications_update ON notifications;

-- Criar nova política de atualização incluindo president e manager
CREATE POLICY notifications_update ON notifications
    FOR UPDATE
    USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical', 'president', 'manager')
        )
    );

-- Remover política existente de exclusão
DROP POLICY IF EXISTS notifications_delete ON notifications;

-- Criar nova política de exclusão incluindo president e manager
CREATE POLICY notifications_delete ON notifications
    FOR DELETE
    USING (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical', 'president', 'manager')
        )
    );