import InjuryAlertService from './injuryAlertService';

/**
 * Serviço para simular cron jobs no frontend
 * Em produção, isso seria executado no backend
 */
export class CronJobService {
  private static intervals: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Inicia o serviço de cron jobs
   */
  static start() {
    console.log('[CronJob] Iniciando serviços de cron jobs...');
    
    // Análise de prevenção de lesões - todo dia às 6h (simulado a cada 1 hora para demo)
    this.scheduleInjuryPreventionAnalysis();
    
    // Outros cron jobs podem ser adicionados aqui
    // this.scheduleOtherJobs();
  }

  /**
   * Para todos os cron jobs
   */
  static stop() {
    console.log('[CronJob] Parando todos os cron jobs...');
    
    this.intervals.forEach((interval, name) => {
      clearInterval(interval);
      console.log(`[CronJob] Parado: ${name}`);
    });
    
    this.intervals.clear();
  }

  /**
   * Agenda análise automática de prevenção de lesões
   */
  private static scheduleInjuryPreventionAnalysis() {
    const jobName = 'injury-prevention-analysis';
    
    // Em desenvolvimento: executa a cada 1 hora
    // Em produção: seria configurado para 6h da manhã
    const interval = setInterval(async () => {
      try {
        console.log('[CronJob] Executando análise de prevenção de lesões...');
        await InjuryAlertService.scheduleAutomaticAnalysis();
        console.log('[CronJob] Análise de prevenção concluída com sucesso');
      } catch (error) {
        console.error('[CronJob] Erro na análise de prevenção:', error);
      }
    }, 60 * 60 * 1000); // 1 hora em desenvolvimento

    this.intervals.set(jobName, interval);
    console.log(`[CronJob] Agendado: ${jobName} (a cada 1 hora)`);
  }

  /**
   * Executa análise manual de prevenção de lesões
   */
  static async runInjuryPreventionAnalysisNow(): Promise<void> {
    try {
      console.log('[CronJob] Executando análise manual de prevenção...');
      await InjuryAlertService.scheduleAutomaticAnalysis();
      console.log('[CronJob] Análise manual concluída com sucesso');
    } catch (error) {
      console.error('[CronJob] Erro na análise manual:', error);
      throw error;
    }
  }

  /**
   * Verifica se um cron job está rodando
   */
  static isRunning(jobName: string): boolean {
    return this.intervals.has(jobName);
  }

  /**
   * Lista todos os cron jobs ativos
   */
  static getActiveJobs(): string[] {
    return Array.from(this.intervals.keys());
  }

  /**
   * Para um cron job específico
   */
  static stopJob(jobName: string): boolean {
    const interval = this.intervals.get(jobName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(jobName);
      console.log(`[CronJob] Parado: ${jobName}`);
      return true;
    }
    return false;
  }

  /**
   * Reinicia um cron job específico
   */
  static restartJob(jobName: string): boolean {
    if (this.stopJob(jobName)) {
      switch (jobName) {
        case 'injury-prevention-analysis':
          this.scheduleInjuryPreventionAnalysis();
          return true;
        default:
          console.warn(`[CronJob] Job desconhecido: ${jobName}`);
          return false;
      }
    }
    return false;
  }

  /**
   * Configurações de produção para cron jobs reais
   * Esta função seria usada no backend
   */
  static getProductionCronConfig() {
    return {
      'injury-prevention-analysis': {
        schedule: '0 6 * * *', // Todo dia às 6h
        description: 'Análise automática de prevenção de lesões',
        timezone: 'America/Sao_Paulo'
      },
      'monthly-reports': {
        schedule: '0 8 1 * *', // Todo dia 1 às 8h
        description: 'Geração de relatórios mensais',
        timezone: 'America/Sao_Paulo'
      },
      'backup-database': {
        schedule: '0 2 * * *', // Todo dia às 2h
        description: 'Backup automático do banco de dados',
        timezone: 'America/Sao_Paulo'
      },
      'cleanup-temp-files': {
        schedule: '0 3 * * 0', // Todo domingo às 3h
        description: 'Limpeza de arquivos temporários',
        timezone: 'America/Sao_Paulo'
      }
    };
  }
}

// Inicializar automaticamente quando o módulo for carregado
// Em produção, isso seria controlado pelo sistema de backend
if (typeof window !== 'undefined') {
  // Só executar no browser, não no SSR
  window.addEventListener('load', () => {
    CronJobService.start();
  });

  window.addEventListener('beforeunload', () => {
    CronJobService.stop();
  });
}

export default CronJobService;
