import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Mail, CheckCircle } from 'lucide-react';

interface LeadMagnetProps {
  title: string;
  description: string;
  downloadUrl: string;
  fileName: string;
  type: 'planilha' | 'pdf' | 'template' | 'checklist' | 'calculadora';
  preview?: string;
  benefits?: string[];
}

export function LeadMagnet({
  title,
  description,
  downloadUrl,
  fileName,
  type,
  preview,
  benefits = []
}: LeadMagnetProps) {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const typeIcons = {
    planilha: '📊',
    pdf: '📄',
    template: '📋',
    checklist: '✅',
    calculadora: '🧮'
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    try {
      // Enviar email para lista
      await fetch('/api/lead-magnets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          leadMagnet: fileName,
          source: 'blog'
        })
      });

      // Track no GA4
      if (typeof gtag !== 'undefined') {
        gtag('event', 'download', {
          event_category: 'lead_magnet',
          event_label: fileName,
          value: 1
        });
      }

      setIsSubmitted(true);
      
      // Iniciar download após 2 segundos
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, 2000);

    } catch (error) {
      console.error('Erro ao processar lead magnet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Download iniciado!
            </h3>
            <p className="text-green-700 mb-4">
              Enviamos o link por email também. Verifique sua caixa de entrada.
            </p>
            <p className="text-sm text-green-600">
              Você receberá dicas exclusivas sobre gestão de clubes semanalmente.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader>
        <div className="flex items-center gap-3">
          <span className="text-2xl">{typeIcons[type]}</span>
          <div>
            <CardTitle className="text-xl text-blue-900">{title}</CardTitle>
            <CardDescription className="text-blue-700">
              {description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {benefits.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-blue-900 mb-2">O que você vai receber:</h4>
            <ul className="space-y-1">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2 text-sm text-blue-800">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
        )}

        {preview && (
          <div className="mb-4">
            <img 
              src={preview} 
              alt={`Preview de ${title}`}
              className="w-full rounded-lg border border-blue-200"
            />
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-3">
          <div>
            <Input
              type="email"
              placeholder="Seu melhor email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="border-blue-300 focus:border-blue-500"
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-blue-600 hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Baixar {type.charAt(0).toUpperCase() + type.slice(1)} Grátis
              </>
            )}
          </Button>
        </form>

        <p className="text-xs text-blue-600 mt-3 text-center">
          <Mail className="h-3 w-3 inline mr-1" />
          Sem spam. Cancele quando quiser.
        </p>
      </CardContent>
    </Card>
  );
}