import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveLayout({ children, className }: ResponsiveLayoutProps) {
  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      "px-2 sm:px-4 md:px-6 lg:px-8",
      "py-4 sm:py-6 md:py-8",
      className
    )}>
      <div className="max-w-7xl mx-auto">
        {children}
      </div>
    </div>
  );
}

interface ResponsivePageHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export function ResponsivePageHeader({ 
  title, 
  description, 
  actions,
  className 
}: ResponsivePageHeaderProps) {
  return (
    <div className={cn(
      "flex flex-col sm:flex-row sm:items-center sm:justify-between",
      "gap-4 mb-6 sm:mb-8",
      className
    )}>
      <div className="min-w-0 flex-1">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">
          {title}
        </h1>
        {description && (
          <p className="text-sm sm:text-base text-gray-600 mt-1">
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          {actions}
        </div>
      )}
    </div>
  );
}

interface ResponsiveTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    content: React.ReactNode;
    icon?: React.ReactNode;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export function ResponsiveTabs({ 
  tabs, 
  activeTab, 
  onTabChange,
  className 
}: ResponsiveTabsProps) {
  return (
    <div className={cn("w-full", className)}>
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-1 overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                "flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-3",
                "text-xs sm:text-sm font-medium whitespace-nowrap",
                "border-b-2 transition-colors duration-200",
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              {tab.icon}
              <span className="hidden sm:inline">{tab.label}</span>
              <span className="sm:hidden">{tab.label.slice(0, 4)}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {tabs.find(tab => tab.id === activeTab)?.content}
      </div>
    </div>
  );
}

interface ResponsiveStatsGridProps {
  stats: Array<{
    title: string;
    value: string | number;
    description?: string;
    icon?: React.ReactNode;
    color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
    trend?: {
      value: number;
      isPositive: boolean;
    };
  }>;
  className?: string;
}

export function ResponsiveStatsGrid({ stats, className }: ResponsiveStatsGridProps) {
  const getColorClasses = (color: string = 'blue') => {
    const colors = {
      blue: 'from-blue-50 to-blue-100 text-blue-900 border-blue-200',
      green: 'from-green-50 to-green-100 text-green-900 border-green-200',
      yellow: 'from-yellow-50 to-yellow-100 text-yellow-900 border-yellow-200',
      red: 'from-red-50 to-red-100 text-red-900 border-red-200',
      purple: 'from-purple-50 to-purple-100 text-purple-900 border-purple-200',
      gray: 'from-gray-50 to-gray-100 text-gray-900 border-gray-200',
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className={cn(
      "grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",
      className
    )}>
      {stats.map((stat, index) => (
        <div
          key={index}
          className={cn(
            "relative overflow-hidden rounded-lg border p-4 sm:p-6",
            "bg-gradient-to-br shadow-sm hover:shadow-md transition-shadow",
            getColorClasses(stat.color)
          )}
        >
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium opacity-80 mb-1">
                {stat.title}
              </p>
              <p className="text-xl sm:text-2xl lg:text-3xl font-bold">
                {stat.value}
              </p>
              {stat.description && (
                <p className="text-xs opacity-70 mt-1">
                  {stat.description}
                </p>
              )}
              {stat.trend && (
                <div className={cn(
                  "flex items-center mt-2 text-xs",
                  stat.trend.isPositive ? "text-green-600" : "text-red-600"
                )}>
                  <span className="mr-1">
                    {stat.trend.isPositive ? '↗' : '↘'}
                  </span>
                  {Math.abs(stat.trend.value)}%
                </div>
              )}
            </div>
            {stat.icon && (
              <div className="flex-shrink-0 ml-4">
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white/20 flex items-center justify-center">
                  {stat.icon}
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

interface ResponsiveCardGridProps {
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ResponsiveCardGrid({ 
  children, 
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  className 
}: ResponsiveCardGridProps) {
  const gapClasses = {
    sm: 'gap-2 sm:gap-3',
    md: 'gap-4 sm:gap-6',
    lg: 'gap-6 sm:gap-8',
  };

  const getColClasses = () => {
    const mobileClass = `grid-cols-${cols.mobile}`;
    const tabletClass = `sm:grid-cols-${cols.tablet}`;
    const desktopClass = `lg:grid-cols-${cols.desktop}`;
    
    return `${mobileClass} ${tabletClass} ${desktopClass}`;
  };

  return (
    <div className={cn(
      "grid",
      getColClasses(),
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveSearchBarProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onFilter?: () => void;
  className?: string;
}

export function ResponsiveSearchBar({ 
  placeholder = "Buscar...", 
  value, 
  onChange, 
  onFilter,
  className 
}: ResponsiveSearchBarProps) {
  return (
    <div className={cn("relative flex items-center", className)}>
      <div className="relative flex-1">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={cn(
            "block w-full pl-10 pr-3 py-2 sm:py-2.5",
            "text-sm sm:text-base",
            "border border-gray-300 rounded-md",
            "placeholder-gray-400",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "bg-white"
          )}
        />
      </div>
      
      {onFilter && (
        <button
          onClick={onFilter}
          className={cn(
            "ml-2 p-2 sm:p-2.5",
            "text-gray-400 hover:text-gray-600",
            "border border-gray-300 rounded-md",
            "hover:bg-gray-50",
            "focus:outline-none focus:ring-2 focus:ring-blue-500"
          )}
        >
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
          </svg>
        </button>
      )}
    </div>
  );
}

// Utility function to handle responsive breakpoints
export const breakpoints = {
  mobile: '(max-width: 639px)',
  tablet: '(min-width: 640px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)',
} as const;

// Hook to use media queries
export function useMediaQuery(query: string) {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addListener(listener);
    return () => media.removeListener(listener);
  }, [matches, query]);

  return matches;
}