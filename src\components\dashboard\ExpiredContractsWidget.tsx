import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, AlertTriangle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { differenceInDays, parseISO } from "date-fns";
import type { Player } from "@/api/api";

interface ExpiredContractsWidgetProps {
  players: Player[];
}

export function ExpiredContractsWidget({ players }: ExpiredContractsWidgetProps) {
  const navigate = useNavigate();

  const today = new Date();
  const expiredContracts = players
    .filter(player => player.contract_end_date && player.status !== 'inativo')
    .map(player => ({
      ...player,
      daysRemaining: differenceInDays(parseISO(player.contract_end_date!), today)
    }))
    .filter(player => player.daysRemaining <= 0)
    .sort((a, b) => a.daysRemaining - b.daysRemaining);

  const handleViewPlayer = (playerId: string) => {
    import('@/utils/clubNavigation').then(({ navigateToClubPath }) => {
      navigateToClubPath(`/jogador/${playerId}`);
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Contratos Vencidos</CardTitle>
        <AlertTriangle className="h-4 w-4 text-red-600" />
      </CardHeader>
      <CardContent>
        {expiredContracts.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Nenhum contrato vencido.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {expiredContracts.map(player => (
              <div
                key={player.id}
                className="flex items-center justify-between p-2 bg-muted/20 rounded-lg hover:bg-muted/40 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-red-600" />
                  <div>
                    <p className="font-medium text-sm">{player.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {player.daysRemaining === 0
                        ? 'Vence hoje'
                        : `Venceu há ${Math.abs(player.daysRemaining)} ${Math.abs(player.daysRemaining) === 1 ? 'dia' : 'dias'}`}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleViewPlayer(player.id)}
                  className="text-xs"
                >
                  Ver
                </Button>
              </div>
            ))}

            {expiredContracts.length > 5 && (
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2"
                onClick={() => navigate('/elenco')}
              >
                Ver todos ({expiredContracts.length})
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}