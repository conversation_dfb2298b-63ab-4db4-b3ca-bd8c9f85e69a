import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { searchDrills, getDrill, deleteDrill } from "@/lib/training-api";
import { TrainingDrill } from "./InteractiveTrainingBuilder";
import { useToast } from "@/components/ui/use-toast";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

interface SavedDrillsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  onSelectDrill: (drill: TrainingDrill) => void;
}

interface DrillSummary {
  id: string;
  name: string;
  category: string;
  difficulty: string;
  created_at: string;
}

export function SavedDrillsDialog({
  open,
  onOpenChange,
  clubId,
  onSelectDrill,
}: SavedDrillsDialogProps) {
  const { toast } = useToast();
  const [drills, setDrills] = useState<DrillSummary[]>([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [drillToDelete, setDrillToDelete] = useState<DrillSummary | null>(null);

  useEffect(() => {
    const fetchDrills = async () => {
      if (!open) return;
      setLoading(true);
      const result = await searchDrills(clubId);
      if (result.success) {
        setDrills(result.data as DrillSummary[]);
      }
      setLoading(false);
    };

    fetchDrills();
  }, [open, clubId]);

  const filteredDrills = drills.filter((d) =>
    d.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleLoad = async (id: string) => {
    const drill = await getDrill(id);
    if (drill) {
      onSelectDrill(drill);
      onOpenChange(false);
      toast({
        title: "Drill Carregado",
        description: `O drill "${drill.name}" foi carregado com sucesso.`,
      });
    } else {
      toast({
        title: "Erro",
        description: "Não foi possível carregar o drill",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!drillToDelete) return;
    const result = await deleteDrill(drillToDelete.id);
    if (result.success) {
      setDrills((prev) => prev.filter((d) => d.id !== drillToDelete.id));
      toast({
        title: "Drill Excluído",
        description: `O drill "${drillToDelete.name}" foi removido com sucesso.`,
      });
    } else {
      toast({
        title: "Erro",
        description: "Não foi possível excluir o drill",
        variant: "destructive",
      });
    }
    setDeleteDialogOpen(false);
    setDrillToDelete(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Drills Salvos</DialogTitle>
          <DialogDescription>
            Selecione um drill salvo para carregar no editor
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center gap-2 mb-4">
          <Input
            placeholder="Buscar drill..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <ScrollArea className="flex-1">
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredDrills.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhum drill encontrado
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead className="hidden md:table-cell">Categoria</TableHead>
                  <TableHead className="hidden md:table-cell">Dificuldade</TableHead>
                  <TableHead className="hidden md:table-cell">Criado em</TableHead>
                  <TableHead className="w-24 text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDrills.map((drill) => (
                  <TableRow key={drill.id}>
                    <TableCell className="font-medium">{drill.name}</TableCell>
                    <TableCell className="hidden md:table-cell capitalize">
                      {drill.category}
                    </TableCell>
                    <TableCell className="hidden md:table-cell capitalize">
                      {drill.difficulty}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {new Date(drill.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right flex justify-end">
                      <Button size="sm" onClick={() => handleLoad(drill.id)}>
                        Carregar
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="ml-2"
                        onClick={() => {
                          setDrillToDelete(drill);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        Excluir
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </ScrollArea>
        <div className="pt-4 border-t flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </div>
      </DialogContent>
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Excluir Drill"
        description={`Tem certeza que deseja excluir o drill "${drillToDelete?.name}"? Esta ação não pode ser desfeita.`}
        onConfirm={handleDelete}
      />
    </Dialog>
  );
}