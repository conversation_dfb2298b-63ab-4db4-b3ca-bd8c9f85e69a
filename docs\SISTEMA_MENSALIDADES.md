# 📋 Sistema de Mensalidades - Documentação Completa

## 📖 Índice
1. [<PERSON>isão Geral](#visão-geral)
2. [Funcionalidades](#funcionalidades)
3. [Estrutura do Sistema](#estrutura-do-sistema)
4. [<PERSON> Usar](#como-usar)
5. [Configuração Inicial](#configuração-inicial)
6. [Fluxo de Trabalho](#fluxo-de-trabalho)
7. [Permissões](#permissões)
8. [Integração com Outros Sistemas](#integração-com-outros-sistemas)
9. [Troubleshooting](#troubleshooting)

---

## 🎯 Visão Geral

O Sistema de Mensalidades é uma funcionalidade completa desenvolvida para escolinhas de futebol gerenciarem cobranças mensais de atletas. O sistema integra-se perfeitamente com a infraestrutura existente do Game Day Nexus, reutilizando:

- **Sistema de Billing** existente para transações PIX
- **Serviço de Email Brevo** para notificações automáticas
- **Geração de QR Codes PIX** para pagamentos
- **Sistema de Permissões** para controle de acesso
- **Aba Finanças** do jogador para histórico

### 🌟 Principais Benefícios
- ✅ **Automação completa** de cobrança de mensalidades
- ✅ **Integração PIX** com QR codes automáticos
- ✅ **Emails automáticos** com templates profissionais
- ✅ **Portal do atleta** para pagamentos e comprovantes
- ✅ **Gestão de comprovantes** com aprovação/rejeição
- ✅ **Relatórios e estatísticas** em tempo real
- ✅ **Configuração flexível** por categoria de atletas

---

## 🚀 Funcionalidades

### 📊 Dashboard Principal
- **Estatísticas em tempo real**: Total, pagas, pendentes, em atraso
- **Geração automática** de mensalidades por mês/ano
- **Atualização de status** (pendente → em atraso)
- **Envio de links de pagamento** por email
- **Marcação manual** como pago
- **Filtros avançados** por status, mês, ano

### ⚙️ Configurações de Mensalidades
- **Configuração por categoria** de atletas
- **Valores personalizados** por configuração
- **Dia de vencimento** configurável (1-31)
- **Sistema de lembretes** (X dias antes do vencimento)
- **Multas por atraso** (percentual configurável)
- **Descontos para pagamento antecipado**
- **Chaves PIX específicas** por configuração
- **Ativação/desativação** de configurações

### 📄 Gestão de Comprovantes
- **Upload de comprovantes** pelos atletas
- **Análise e aprovação** por colaboradores
- **Sistema de status**: Pendente, Aprovado, Rejeitado
- **Comentários de revisão** para feedback
- **Emails automáticos** de confirmação
- **Histórico completo** de comprovantes

### 👤 Portal do Atleta
- **Visualização de mensalidades** (pendentes/pagas)
- **Geração de PIX** com QR code
- **Upload de comprovantes** de pagamento
- **Acompanhamento de status** dos comprovantes
- **Histórico completo** de pagamentos

### 📧 Sistema de Emails Automáticos
- **Lembretes de pagamento** (X dias antes)
- **Notificações de atraso** com multas
- **Links de pagamento** com PIX
- **Confirmações de comprovantes** (recebido/aprovado/rejeitado)
- **Templates profissionais** responsivos

---

## 🏗️ Estrutura do Sistema

### 📁 Arquivos Principais

#### **Backend/API**
```
src/api/monthlyFees.ts          # API principal do sistema
src/services/monthlyFeeEmailService.ts  # Serviço de emails
```

#### **Componentes React**
```
src/components/monthly-fees/
├── MonthlyFeesManager.tsx           # Interface principal
├── MonthlyFeeDashboard.tsx          # Dashboard e estatísticas
├── MonthlyFeeSettingsManager.tsx    # Configurações
├── MonthlyFeeReceiptsManager.tsx    # Gestão de comprovantes
└── PlayerMonthlyFeesPortal.tsx      # Portal do atleta
```

#### **Banco de Dados**
```
sql/create-monthly-fees-system.sql  # Estrutura completa do BD
```

#### **Páginas**
```
src/pages/MonthlyFeesPage.tsx        # Página principal
```

### 🗄️ Estrutura do Banco de Dados

#### **Tabelas Principais**

1. **`monthly_fee_settings`** - Configurações de mensalidades
   - Valores, vencimentos, multas, descontos
   - Vinculação com categorias de atletas
   - Chaves PIX específicas

2. **`player_monthly_fees`** - Mensalidades individuais
   - Referência mês/ano
   - Status (pendente/pago/atraso/cancelado)
   - Valores com multas/descontos aplicados
   - Link com sistema de billing

3. **`monthly_fee_receipts`** - Comprovantes de pagamento
   - Upload de arquivos
   - Status de aprovação
   - Comentários de revisão

4. **`monthly_fee_email_log`** - Log de emails enviados
   - Auditoria completa de comunicações
   - Status de entrega e erros

#### **Funções do Banco**

- **`generate_monthly_fees_for_month()`** - Gera mensalidades automaticamente
- **`update_overdue_monthly_fees()`** - Atualiza status de atraso
- **`mark_monthly_fee_as_paid()`** - Marca como pago e integra com billing

---

## 📋 Como Usar

### 🔧 Para Administradores

#### **1. Acesso ao Sistema**
- Menu lateral → **"Mensalidades"**
- Ou URL direta: `/mensalidades`

#### **2. Configuração Inicial**
1. **Aba "Configurações"**
2. Clique em **"Nova Configuração"**
3. Preencha os dados:
   - **Nome**: Ex: "Mensalidade Sub-15"
   - **Categoria**: Selecione ou deixe "Todas"
   - **Valor**: Valor da mensalidade
   - **Dia do Vencimento**: 1-31
   - **Lembrete**: Dias antes para enviar email
   - **Multa**: Percentual por atraso (opcional)
   - **Desconto**: Percentual para pagamento antecipado (opcional)
   - **Chave PIX**: Específica ou usar a do clube
4. Clique em **"Criar"**

#### **3. Geração de Mensalidades**
1. **Aba "Dashboard"**
2. Clique em **"Gerar Mensalidades"**
3. Selecione **mês** e **ano**
4. Clique em **"Gerar Mensalidades"**
5. Sistema criará automaticamente para todos os atletas ativos

#### **4. Gestão de Pagamentos**
- **Visualizar**: Filtrar por status, mês, ano
- **Enviar Link**: Botão de email para enviar PIX
- **Marcar como Pago**: Para pagamentos manuais
- **Atualizar Atrasos**: Botão para processar mensalidades vencidas

#### **5. Aprovação de Comprovantes**
1. **Aba "Comprovantes"**
2. Visualizar comprovantes pendentes
3. Clicar no **ícone de olho** para ver o arquivo
4. **Aprovar** ✅ ou **Rejeitar** ❌
5. Adicionar comentários se necessário
6. Sistema enviará email automático ao atleta

### 👤 Para Atletas

#### **1. Acesso às Mensalidades**
- Perfil do jogador → Aba **"Finanças"** → Sub-aba **"Mensalidades"**

#### **2. Visualização de Mensalidades**
- Ver todas as mensalidades (pendentes/pagas)
- Status de cada mensalidade
- Valores com multas/descontos aplicados

#### **3. Pagamento via PIX**
1. Clicar em **"Ver Dados de Pagamento"**
2. **QR Code** será gerado automaticamente
3. **Código PIX** para copiar e colar
4. Realizar pagamento no app bancário

#### **4. Envio de Comprovante**
1. Após pagamento, clicar em **"Enviar Comprovante"**
2. Selecionar arquivo (JPG, PNG, PDF)
3. Clicar em **"Enviar Comprovante"**
4. Aguardar análise da administração

#### **5. Acompanhamento**
- Status do comprovante: **Analisando** → **Aprovado**/**Rejeitado**
- Emails automáticos de confirmação
- Histórico completo de pagamentos

---

## ⚙️ Configuração Inicial

### 🎯 Pré-requisitos
1. **Chave PIX configurada** no clube
2. **Brevo configurado** para envio de emails
3. **Categorias de atletas** criadas (opcional)
4. **Permissões** configuradas para usuários

### 🔧 Passos de Configuração

#### **1. Configurar Chave PIX do Clube**
- Configurações → Dados do Clube → Chave PIX

#### **2. Criar Configurações de Mensalidades**
```
Exemplo de Configuração:
- Nome: "Mensalidade Sub-15"
- Categoria: Sub-15
- Valor: R$ 150,00
- Vencimento: Dia 5
- Lembrete: 3 dias antes
- Multa: 2% por atraso
- Desconto: 5% se pago 5 dias antes
```

#### **3. Gerar Primeira Mensalidade**
- Dashboard → Gerar Mensalidades → Selecionar mês/ano atual

#### **4. Testar Fluxo Completo**
1. Gerar mensalidade de teste
2. Acessar portal do atleta
3. Gerar PIX e testar pagamento
4. Enviar comprovante de teste
5. Aprovar comprovante

---

## 🔄 Fluxo de Trabalho

### 📅 Fluxo Mensal Típico

#### **Início do Mês**
1. **Administrador** gera mensalidades do mês
2. **Sistema** calcula valores com descontos/multas
3. **Emails automáticos** são enviados (se configurado)

#### **Durante o Mês**
1. **Atletas** recebem lembretes por email
2. **Atletas** geram PIX e fazem pagamentos
3. **Atletas** enviam comprovantes
4. **Administradores** aprovam/rejeitam comprovantes
5. **Sistema** marca mensalidades como pagas automaticamente

#### **Final do Mês**
1. **Sistema** atualiza mensalidades em atraso
2. **Emails de cobrança** são enviados automaticamente
3. **Multas** são aplicadas conforme configuração
4. **Relatórios** são gerados para análise

### 🔄 Fluxo de Pagamento

```
1. Mensalidade Gerada → 2. Email de Lembrete → 3. Atleta Acessa Portal
                                                        ↓
8. Email de Confirmação ← 7. Aprovação ← 6. Upload de Comprovante ← 4. Pagamento PIX
        ↓
9. Mensalidade Marcada como Paga
```

---

## 🔐 Permissões

### 👥 Níveis de Acesso

#### **Administradores/Presidentes**
- ✅ Acesso completo ao sistema
- ✅ Criar/editar configurações
- ✅ Gerar mensalidades
- ✅ Aprovar/rejeitar comprovantes
- ✅ Marcar como pago manualmente
- ✅ Visualizar relatórios completos

#### **Colaboradores Financeiros**
- ✅ Visualizar mensalidades
- ✅ Aprovar/rejeitar comprovantes
- ✅ Marcar como pago
- ✅ Enviar links de pagamento
- ❌ Criar/editar configurações

#### **Atletas**
- ✅ Visualizar suas mensalidades
- ✅ Gerar PIX para pagamento
- ✅ Enviar comprovantes
- ✅ Acompanhar status
- ❌ Acesso a dados de outros atletas

### 🛡️ Permissão Necessária
- **`finances.view`** - Para acessar o sistema de mensalidades

---

## 🔗 Integração com Outros Sistemas

### 💳 Sistema de Billing
- **Reutilização completa** da API de billing existente
- **Transações PIX** automáticas
- **QR Codes** gerados dinamicamente
- **Histórico integrado** na aba Finanças

### 📧 Sistema de Email (Brevo)
- **Templates profissionais** responsivos
- **Envio automático** de notificações
- **Log completo** de emails enviados
- **Tratamento de erros** de entrega

### 👥 Sistema de Usuários
- **Integração com permissões** existentes
- **Vinculação automática** atleta ↔ usuário
- **Portal personalizado** por atleta

### 📊 Sistema de Relatórios
- **Estatísticas em tempo real**
- **Dados para dashboards** gerenciais
- **Exportação** de dados (futuro)

---

## 🛠️ Troubleshooting

### ❌ Problemas Comuns

#### **1. Erro "Jogador não encontrado"**
**Causa**: Atleta não possui `user_id` vinculado
**Solução**: 
- Verificar se o atleta tem conta de usuário
- Vincular conta na edição do atleta

#### **2. PIX não é gerado**
**Causa**: Chave PIX não configurada
**Solução**:
- Configurar chave PIX do clube
- Ou configurar chave PIX específica na configuração de mensalidade

#### **3. Emails não são enviados**
**Causa**: Brevo não configurado ou atleta sem email
**Solução**:
- Verificar configuração do Brevo
- Verificar se atleta tem email cadastrado

#### **4. Comprovante não é aprovado automaticamente**
**Causa**: Sistema requer aprovação manual
**Solução**:
- Acessar aba "Comprovantes"
- Aprovar manualmente o comprovante

#### **5. Mensalidades não são geradas**
**Causa**: Nenhuma configuração ativa ou atletas inativos
**Solução**:
- Verificar se há configurações ativas
- Verificar se atletas estão com status "ativo"

### 🔧 Comandos de Diagnóstico

#### **Verificar Configurações Ativas**
```sql
SELECT * FROM monthly_fee_settings 
WHERE club_id = [SEU_CLUB_ID] AND is_active = true;
```

#### **Verificar Mensalidades do Mês**
```sql
SELECT * FROM player_monthly_fees 
WHERE club_id = [SEU_CLUB_ID] 
AND reference_month = [MES] 
AND reference_year = [ANO];
```

#### **Verificar Logs de Email**
```sql
SELECT * FROM monthly_fee_email_log 
WHERE club_id = [SEU_CLUB_ID] 
ORDER BY sent_at DESC 
LIMIT 10;
```

### 📞 Suporte

Para problemas não resolvidos:
1. Verificar logs do navegador (F12 → Console)
2. Verificar logs do servidor
3. Contatar suporte técnico com detalhes do erro

---

## 📈 Próximas Funcionalidades (Roadmap)

### 🔮 Funcionalidades Planejadas
- [ ] **Relatórios avançados** com gráficos
- [ ] **Exportação** de dados (Excel/PDF)
- [ ] **Integração com bancos** para confirmação automática
- [ ] **Parcelamento** de mensalidades
- [ ] **Descontos por família** (múltiplos atletas)
- [ ] **Campanhas de cobrança** automatizadas
- [ ] **Dashboard financeiro** executivo
- [ ] **API externa** para integrações

### 🎯 Melhorias Técnicas
- [ ] **Testes automatizados** completos
- [ ] **Performance** otimizada para grandes volumes
- [ ] **Cache** de dados frequentes
- [ ] **Backup automático** de comprovantes
- [ ] **Auditoria avançada** de operações

---

## 📝 Conclusão

O Sistema de Mensalidades do Game Day Nexus é uma solução completa e integrada que automatiza todo o processo de cobrança de mensalidades em escolinhas de futebol. Com funcionalidades avançadas de PIX, emails automáticos e gestão de comprovantes, o sistema proporciona uma experiência fluida tanto para administradores quanto para atletas.

A integração perfeita com os sistemas existentes garante consistência e facilita a adoção, enquanto a flexibilidade de configuração permite adaptação a diferentes modelos de negócio.

---

**📅 Última atualização**: Janeiro 2025  
**👨‍💻 Desenvolvido por**: Equipe Game Day Nexus  
**📧 Suporte**: <EMAIL>