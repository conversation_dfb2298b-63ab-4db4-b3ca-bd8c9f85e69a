import { useState, useEffect, useCallback } from 'react';
import { useChatStore } from '@/store/useChatStore';
import { useSocketChat } from '@/hooks/useSocketChat';
import { useUser } from '@/context/UserContext';

export function useChat() {
    const [isChatOpen, setIsChatOpen] = useState(false);
    const [hasUnreadMessages, setHasUnreadMessages] = useState(false);

    const {
        rooms,
        onlineUsers,
        currentRoom,
        loadRooms,
        isConnected,
        cleanup
    } = useChatStore();

    // Hook do Socket.IO
    const { 
        sendMessage, 
        createRoom, 
        joinRoom, 
        createDirectChat, 
        markAsRead 
    } = useSocketChat();

    const { user } = useUser();
    
    // Pegar clubId do usuário ou localStorage como fallback
    const clubId = user?.club_id || localStorage.getItem('clubId');

    // Calcular mensagens não lidas
    useEffect(() => {
        const unreadCount = rooms.reduce((total, room) => {
            return total + (room.unread_count || 0);
        }, 0);

        setHasUnreadMessages(unreadCount > 0);
    }, [rooms]);

    // Carregar salas quando abrir o chat
    useEffect(() => {
        if (isChatOpen && user && clubId) {
            loadRooms(clubId.toString());
        }
    }, [isChatOpen, user, clubId, loadRooms]);

    const toggleChat = useCallback(() => {
        setIsChatOpen(prev => !prev);
    }, []);

    const openChat = useCallback(() => {
        setIsChatOpen(true);
    }, []);

    const closeChat = useCallback(() => {
        setIsChatOpen(false);
        cleanup();
    }, [cleanup]);

    return {
        // Estado
        isChatOpen,
        hasUnreadMessages,
        rooms,
        onlineUsers,
        currentRoom,
        isConnected,

        // Ações
        toggleChat,
        openChat,
        closeChat,

        // Ações do Socket.IO
        sendMessage,
        createRoom,
        joinRoom,
        createDirectChat,
        markAsRead,

        // Dados computados
        totalUnreadCount: rooms.reduce((total, room) => total + (room.unread_count || 0), 0),
        onlineUsersCount: onlineUsers.length,
        isUserOnline: (userId: string) => onlineUsers.some(u => u.user_id === userId)
    };
}