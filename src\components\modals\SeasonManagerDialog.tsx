import { useState } from "react";
import { useSeasonStore } from "@/store/useSeasonStore";
import { SeasonDialog } from "./SeasonDialog";
import { Pencil, Trash2, Plus } from "lucide-react";
import { toast } from "react-toastify";
import { deleteSeason } from "@/api/seasonApi";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface SeasonManagerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function SeasonManagerDialog({ open, onOpenChange, clubId }: SeasonManagerDialogProps) {
  const { seasons, fetchSeasons, setActiveSeason, activeSeason } = useSeasonStore();
  const [editDialog, setEditDialog] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [deleteConfirm, setDeleteConfirm] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Gerenciar Temporadas</DialogTitle>
        </DialogHeader>
        <div className="flex items-center justify-between mb-2">
          <Button onClick={() => setDialogOpen(true)}>
            <Plus size={18} className="mr-2" /> Nova Temporada
          </Button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm border rounded-lg">
            <thead>
              <tr className="bg-gray-50 text-gray-700">
                <th className="py-2 px-3 text-left">Nome</th>
                <th className="py-2 px-3 text-left">Datas</th>
                <th className="py-2 px-3 text-left">Status</th>
                <th className="py-2 px-3 text-center">Ações</th>
              </tr>
            </thead>
            <tbody>
              {seasons.map(season => (
                <tr key={season.id} className={`border-b ${activeSeason?.id === season.id ? 'bg-blue-50' : ''}`}>
                  <td className="py-2 px-3 font-medium">{season.name}</td>
                  <td className="py-2 px-3">{season.start_date} - {season.end_date}</td>
                  <td className="py-2 px-3">{activeSeason?.id === season.id ? <span className="text-blue-600 font-semibold">Ativa</span> : <span className="text-gray-400">Inativa</span>}</td>
                  <td className="py-2 px-3 flex gap-2 justify-center">
                    <Button variant="outline" size="icon" onClick={() => setEditDialog({ open: true, season })}>
                      <Pencil size={18} />
                    </Button>
                    <Button variant="destructive" size="icon" onClick={() => setDeleteConfirm({ open: true, season })}>
                      <Trash2 size={18} />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              Fechar
            </Button>
          </DialogClose>
        </DialogFooter>
        <SeasonDialog open={dialogOpen} onOpenChange={setDialogOpen} clubId={clubId} />
        {editDialog.open && (
          <SeasonDialog
            open={editDialog.open}
            onOpenChange={open => setEditDialog({ open, season: open ? editDialog.season : null })}
            clubId={clubId}
            season={{
              ...editDialog.season,
              club_id: (editDialog.season && 'club_id' in editDialog.season)
                ? editDialog.season.club_id
                : clubId
            }}
          />
        )}
        {deleteConfirm.open && (
          <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
            <div className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3">
              <h2 className="font-semibold text-lg mb-2">Excluir Temporada</h2>
              <p>Tem certeza que deseja excluir a temporada <b>{deleteConfirm.season.name}</b>?</p>
              <div className="flex gap-2 mt-2">
                <Button variant="outline" onClick={() => setDeleteConfirm({ open: false, season: null })}>
                  Cancelar
                </Button>
                <Button variant="destructive"
                  onClick={async () => {
                    try {
                      if (deleteConfirm.season) {
                        await deleteSeason(clubId, deleteConfirm.season.id);
                        toast.success('Temporada excluída com sucesso');
                        if (activeSeason && activeSeason.id === deleteConfirm.season.id) {
                          setActiveSeason(null);
                        }
                        await fetchSeasons(clubId);
                      }
                    } catch (error: any) {
                      toast.error(error.message || 'Erro ao excluir temporada');
                    } finally {
                      setDeleteConfirm({ open: false, season: null });
                    }
                  }}>
                  Excluir
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
