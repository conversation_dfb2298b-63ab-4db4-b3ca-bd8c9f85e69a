import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useClubNavigation } from "@/hooks/useClubNavigation";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { DocumentUpload } from "@/components/ui/document-upload";
import { getAddressByCEP, formatCEP, formatCPF, validateCPF } from "@/api/external";
import {
  MedicalProfessional,
  MedicalProfessionalRole,
  createMedicalProfessional,
  updateMedicalProfessional,
  getMedicalProfessionalById,
  getMedicalProfessionalByUserId,
  uploadMedicalProfessionalCertificate,
  createUserInvitation
} from "@/api/api";
import { usePermission } from "@/hooks/usePermission";
import { v4 as uuidv4 } from "uuid";

export default function MedicosCadastro() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const navigate = useNavigate();
  const { navigateToClubPage } = useClubNavigation();
  const { role: userRole } = usePermission();

  // Form state
  const [name, setName] = useState("");
  const [cpf, setCpf] = useState("");
  const [birthDate, setBirthDate] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");
  const [address, setAddress] = useState("");
  const [credential, setCredential] = useState("");
  const [role, setRole] = useState<MedicalProfessionalRole>("médico");
  const [certificateUrl, setCertificateUrl] = useState<string | null>(null);
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [email, setEmail] = useState("");

  // UI state
  const [loading, setLoading] = useState(false);
  const [loadingCEP, setLoadingCEP] = useState(false);
  const [error, setError] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [professionalId, setProfessionalId] = useState<number | null>(null);

  // Carregar dados do médico em modo de edição
  useEffect(() => {
    const loadMedicalProfessional = async () => {
      // Verificar se estamos em modo de edição
      const professionalId = window.location.pathname.match(/\/medicos\/editar\/(\d+)/);
      const isEditing = !!professionalId;
      const id = isEditing ? parseInt(professionalId[1]) : null;

      // Se for médico editando próprio perfil ou estiver editando outro médico
      if ((userRole === "medical" && user?.id) || isEditing) {
        try {
          setLoading(true);
          let professional;
          
          if (isEditing && id) {
            // Buscar pelo ID se estiver editando outro médico
            professional = await getMedicalProfessionalById(clubId, id);
          } else {
            // Buscar pelo user_id se for médico editando próprio perfil
            professional = await getMedicalProfessionalByUserId(clubId, user!.id);
          }

          if (professional) {
            // Preencher o formulário com os dados do médico
            setName(professional.name || "");
            setCpf(professional.cpf || "");
            setBirthDate(professional.birth_date || "");
            setZipCode(professional.zip_code || "");
            setState(professional.state || "");
            setCity(professional.city || "");
            setAddress(professional.address || "");
            setCredential(professional.credential || "");
            setRole(professional.role as MedicalProfessionalRole || "médico");
            setCertificateUrl(professional.certificate_url || null);
            setProfessionalId(professional.id);
            setIsEditMode(true);
          }
        } catch (err: any) {
          console.error("Erro ao carregar dados do profissional médico:", err);
          toast({
            title: "Erro",
            description: "Não foi possível carregar seus dados",
            variant: "destructive",
          });
        } finally {
          setLoading(false);
        }
      }
    };

    loadMedicalProfessional();
  }, [clubId, user?.id, userRole]);

  // Função para buscar endereço pelo CEP
  const handleCEPSearch = async () => {
    if (!zipCode || zipCode.length < 8) {
      return;
    }

    try {
      setLoadingCEP(true);
      const addressData = await getAddressByCEP(zipCode);

      // Preencher campos de endereço
      setAddress(addressData.logradouro);
      setCity(addressData.localidade);
      setState(addressData.uf);

      // Formatar CEP
      setZipCode(formatCEP(addressData.cep));
    } catch (err: any) {
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar endereço pelo CEP",
        variant: "destructive",
      });
    } finally {
      setLoadingCEP(false);
    }
  };

  // Função para salvar o profissional médico
  const handleSave = async () => {
    try {
      setLoading(true);
      setError("");

      // Validar campos obrigatórios
      if (!name) {
        throw new Error("O nome é obrigatório");
      }

      if (!role) {
        throw new Error("A função é obrigatória");
      }

      // Validar CPF se preenchido
      if (cpf && !validateCPF(cpf)) {
        throw new Error("CPF inválido");
      }

      // Dados do profissional médico
      const professionalData = {
        name,
        cpf: cpf || undefined,
        birth_date: birthDate || undefined,
        zip_code: zipCode || undefined,
        state: state || undefined,
        city: city || undefined,
        address: address || undefined,
        credential: credential || undefined,
        role,
        certificate_url: certificateUrl || undefined,
      };

      let medicalProfessional;

      // Atualizar ou criar o profissional médico
      if (isEditMode && professionalId) {
        // Atualizar profissional existente
        medicalProfessional = await updateMedicalProfessional(
          clubId,
          user?.id || "",
          professionalId,
          professionalData
        );
      } else {
        // Criar novo profissional
        medicalProfessional = await createMedicalProfessional(
          clubId,
          user?.id || "",
          professionalData
        );
      }

      // Fazer upload do certificado se houver
      if (certificateFile && medicalProfessional.id) {
        const uploadedUrl = await uploadMedicalProfessionalCertificate(
          clubId,
          user?.id || "",
          medicalProfessional.id,
          certificateFile
        );

        // Atualizar a URL do certificado no objeto
        medicalProfessional.certificate_url = uploadedUrl;
      }

      // Criar convite para o usuário se o email for fornecido (apenas para novos profissionais)
      if (!isEditMode && email) {
        await createUserInvitation(
          clubId,
          email,
          "medical",
          null,
          {
            "medical.view": true,
            "medical.edit_own": true,
          }
        );

        toast({
          title: "Convite enviado",
          description: `Um convite foi enviado para ${email}`,
        });
      }

      toast({
        title: "Sucesso",
        description: isEditMode
          ? "Profissional médico atualizado com sucesso"
          : "Profissional médico cadastrado com sucesso",
      });

      // Redirecionar para a página de médicos
      navigateToClubPage("/medico");
    } catch (err: any) {
      console.error(`Erro ao ${isEditMode ? 'atualizar' : 'cadastrar'} profissional médico:`, err);
      setError(err.message || `Erro ao ${isEditMode ? 'atualizar' : 'cadastrar'} profissional médico`);
      toast({
        title: "Erro",
        description: err.message || `Erro ao ${isEditMode ? 'atualizar' : 'cadastrar'} profissional médico`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {isEditMode ? "Editar Perfil Médico" : "Cadastro de Profissional Médico"}
        </h1>
        <p className="text-muted-foreground">
          {isEditMode
            ? "Atualize suas informações profissionais"
            : "Cadastre um novo profissional médico para o seu clube"}
        </p>
      </div>

      <Separator />

      <Card>
        <CardHeader>
          <CardTitle>Informações do Profissional</CardTitle>
          <CardDescription>
            {isEditMode
              ? "Atualize seus dados profissionais. Os campos marcados com * são obrigatórios."
              : "Preencha os dados do profissional médico. Os campos marcados com * são obrigatórios."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome completo*</Label>
                <Input
                  id="name"
                  placeholder="Nome completo"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Função*</Label>
                <Select value={role} onValueChange={value => setRole(value as MedicalProfessionalRole)}>
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Selecione a função" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="médico">Médico</SelectItem>
                    <SelectItem value="fisioterapeuta">Fisioterapeuta</SelectItem>
                    <SelectItem value="enfermeiro">Enfermeiro</SelectItem>
                    <SelectItem value="massagista">Massagista</SelectItem>
                    <SelectItem value="outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cpf">CPF</Label>
                <Input
                  id="cpf"
                  placeholder="CPF"
                  value={cpf}
                  onChange={e => {
                    let value = e.target.value;
                    // Se o usuário estiver digitando números, formatar automaticamente
                    if (value.match(/^\d+$/)) {
                      // Limitar a 11 dígitos
                      value = value.slice(0, 11);

                      // Formatar CPF automaticamente enquanto digita
                      if (value.length > 9) {
                        value = value.replace(/^(\d{3})(\d{3})(\d{3})(\d{0,2}).*/, "$1.$2.$3-$4");
                      } else if (value.length > 6) {
                        value = value.replace(/^(\d{3})(\d{3})(\d{0,3}).*/, "$1.$2.$3");
                      } else if (value.length > 3) {
                        value = value.replace(/^(\d{3})(\d{0,3}).*/, "$1.$2");
                      }
                    }
                    setCpf(value);
                  }}
                  maxLength={14} // 11 dígitos + 3 caracteres de formatação (. e -)
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="birthDate">Data de nascimento</Label>
                <Input
                  id="birthDate"
                  type="date"
                  value={birthDate}
                  onChange={e => setBirthDate(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="zipCode">CEP</Label>
                <div className="flex space-x-2">
                  <Input
                    id="zipCode"
                    placeholder="CEP"
                    value={zipCode}
                    onChange={e => setZipCode(e.target.value)}
                    onBlur={handleCEPSearch}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCEPSearch}
                    disabled={loadingCEP || !zipCode || zipCode.length < 8}
                  >
                    {loadingCEP ? "..." : "Buscar"}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  placeholder="Cidade"
                  value={city}
                  onChange={e => setCity(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">Estado</Label>
                <Input
                  id="state"
                  placeholder="Estado"
                  value={state}
                  onChange={e => setState(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <Input
                id="address"
                placeholder="Endereço"
                value={address}
                onChange={e => setAddress(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="credential">Credencial</Label>
                <Input
                  id="credential"
                  placeholder="Número da credencial"
                  value={credential}
                  onChange={e => setCredential(e.target.value)}
                />
              </div>
              {!isEditMode && (
                <div className="space-y-2">
                  <Label htmlFor="email">E-mail para convite</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="E-mail para enviar convite"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Certificado</Label>
              <DocumentUpload
                documentType="certificate"
                documentLabel="Certificado"
                value={certificateUrl || undefined}
                onChange={(value, file) => {
                  setCertificateUrl(value);
                  setCertificateFile(file || null);
                }}
                status="missing"
              />
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded">
                <div className="flex">
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => navigateToClubPage("/medico")}>
                Cancelar
              </Button>
              <Button onClick={handleSave} disabled={loading}>
                {loading ? "Salvando..." : isEditMode ? "Atualizar" : "Salvar"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
