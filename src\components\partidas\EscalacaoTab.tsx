import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Plus, Trash2, Users, UserCheck, Briefcase, Shield, FileDown } from "lucide-react";
import { toast } from "sonner";
import { FootballField } from "./FootballField";
import { PlayerSelector } from "./PlayerSelector";
import { CollaboratorSelector } from "./CollaboratorSelector";
import { FlexibleCollaboratorSelector } from "./FlexibleCollaboratorSelector";
import { StaffMemberCard } from "./StaffMemberCard";
import { validateLineup, validatePlayerPosition, validateMatchSquad } from "@/utils/lineupValidations";
import type { Collaborator } from "@/api/api";
import {
  getMatchLineup,
  saveMatchLineup,
  getMatchSquad,
  addMatchSquadMember,
  removeMatchSquadMember,
  getAvailablePlayersForMatch,
  type MatchLineup,
  type MatchSquadMember,
  type PlayerWithCategories
} from "@/api/matchLineups";
import type { Player } from "@/api/api";
import type { MappingPosition } from "@/api/categoryMappings";
import { getClubInfo } from "@/api/api";
import { generateLineupPDF } from "@/utils/pdfGenerator";

interface EscalacaoTabProps {
  clubId: number;
  matchId: string | null;
  isMatchSelected: boolean;
}

// Formações disponíveis
const formations = [
  { id: "4-4-2", name: "4-4-2", description: "Tradicional" },
  { id: "4-3-3", name: "4-3-3", description: "Padrão" },
  { id: "3-5-2", name: "3-5-2", description: "Com 3 zagueiros" },
  { id: "4-2-3-1", name: "4-2-3-1", description: "Com volantes" },
  { id: "5-3-2", name: "5-3-2", description: "Defensivo" },
];

// Posições por formação
const positionsByFormation: Record<string, string[]> = {
  "4-4-2": ["GK", "RB", "CB1", "CB2", "LB", "RM", "CM1", "CM2", "LM", "ST1", "ST2"],
  "4-3-3": ["GK", "RB", "CB1", "CB2", "LB", "CDM", "CM1", "CM2", "RW", "ST", "LW"],
  "3-5-2": ["GK", "CB1", "CB2", "CB3", "RWB", "CM1", "CM2", "CM3", "LWB", "ST1", "ST2"],
  "4-2-3-1": ["GK", "RB", "CB1", "CB2", "LB", "CDM1", "CDM2", "CAM1", "CAM2", "CAM3", "ST"],
  "5-3-2": ["GK", "RB", "CB1", "CB2", "CB3", "LB", "CM1", "CM2", "CM3", "ST1", "ST2"],
};

// Labels das posições
const positionLabels: Record<string, string> = {
  GK: "Goleiro",
  RB: "Lateral Direito",
  CB1: "Zagueiro Central",
  CB2: "Zagueiro Central",
  CB3: "Zagueiro Central",
  LB: "Lateral Esquerdo",
  RWB: "Ala Direito",
  LWB: "Ala Esquerdo",
  CDM: "Volante",
  CDM1: "Volante",
  CDM2: "Volante",
  CM1: "Meio-campo",
  CM2: "Meio-campo",
  CM3: "Meio-campo",
  RM: "Meio-campo Direito",
  LM: "Meio-campo Esquerdo",
  CAM1: "Meia Atacante",
  CAM2: "Meia Atacante",
  CAM3: "Meia Atacante",
  RW: "Ponta Direita",
  LW: "Ponta Esquerda",
  ST: "Atacante",
  ST1: "Atacante",
  ST2: "Atacante",
};

// Mapeamento de posições do campo para posições de jogador (para filtro)
function getPlayerPositionFromFieldPosition(fieldPosition: string): string {
  const positionMap: Record<string, string> = {
    // Goleiro
    'GK': 'Goleiro',

    // Defesa
    'RB': 'Lateral',
    'CB1': 'Zagueiro',
    'CB2': 'Zagueiro',
    'CB3': 'Zagueiro',
    'LB': 'Lateral',
    'RWB': 'Lateral',
    'LWB': 'Lateral',

    // Meio-campo
    'CDM': 'Volante',
    'CDM1': 'Volante',
    'CDM2': 'Volante',
    'CM1': 'Meio-campista',
    'CM2': 'Meio-campista',
    'CM3': 'Meio-campista',
    'RM': 'Meio-campista',
    'LM': 'Meio-campista',
    'CAM1': 'Meio-campista',
    'CAM2': 'Meio-campista',
    'CAM3': 'Meio-campista',

    // Ataque
    'RW': 'Extremo',
    'LW': 'Extremo',
    'ST': 'Atacante',
    'ST1': 'Atacante',
    'ST2': 'Atacante',
  };

  return positionMap[fieldPosition] || '';
}

export function EscalacaoTab({ clubId, matchId, isMatchSelected }: EscalacaoTabProps) {
  const [lineup, setLineup] = useState<Record<string, Player | undefined>>({});
  const [formation, setFormation] = useState("4-4-2");
  const [squad, setSquad] = useState<MatchSquadMember[]>([]);
  const [availablePlayers, setAvailablePlayers] = useState<PlayerWithCategories[]>([]);
  const [matchCategoryId, setMatchCategoryId] = useState<number | null>(null);
  const [matchCompetitionId, setMatchCompetitionId] = useState<string | null>(null);
  const [selectingFor, setSelectingFor] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("campo");
  const [loading, setLoading] = useState(false);
  const [tablesExist, setTablesExist] = useState(true);

  // Carregar dados quando a partida for selecionada
  useEffect(() => {
    if (isMatchSelected && matchId && clubId) {
      loadMatchData();
    }
  }, [isMatchSelected, matchId, clubId]);

  const loadMatchData = async () => {
    if (!matchId || !clubId) return;

    setLoading(true);
    try {
      // Primeiro carregar jogadores disponíveis, categoria e competição da partida
      const { players, matchCategoryId: categoryId, matchCompetitionId: competitionId } = await getAvailablePlayersForMatch(clubId, matchId);
      setAvailablePlayers(players);
      setMatchCategoryId(categoryId);
      setMatchCompetitionId(competitionId);

      // Carregar escalação existente
      const matchLineup = await getMatchLineup(clubId, matchId);
      if (matchLineup) {
        setFormation(matchLineup.formation);
        // Converter IDs para objetos Player
        const lineupObj: Record<string, Player | undefined> = {};
        const positions = positionsByFormation[matchLineup.formation] || [];

        for (const pos of positions) {
          const playerId = matchLineup.lineup[pos];
          if (playerId) {
            // Buscar o jogador nos dados disponíveis
            const player = players.find(p => p.id === playerId);
            lineupObj[pos] = player;
          }
        }
        setLineup(lineupObj);
      }

      // Carregar squad da partida
      const matchSquad = await getMatchSquad(clubId, matchId);
      setSquad(matchSquad);

    } catch (error: any) {
      console.error("Erro ao carregar dados da escalação:", error);

      // Verificar se é erro de tabelas não existentes
      if (error.message?.includes('tabela') && error.message?.includes('não existe')) {
        setTablesExist(false);
        toast.error("Tabelas do sistema de escalação não encontradas. Execute o script SQL para criar as tabelas.");
      } else {
        toast.error("Erro ao carregar dados da escalação");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFormationChange = (newFormation: string) => {
    setFormation(newFormation);
    // Migrar jogadores para a nova formação
    const oldPositions = positionsByFormation[formation] || [];
    const newPositions = positionsByFormation[newFormation] || [];
    const newLineup: Record<string, Player | undefined> = {};
    
    // Tentar manter jogadores em posições similares
    newPositions.forEach(newPos => {
      // Procurar posição similar na formação anterior
      const similarPos = oldPositions.find(oldPos => 
        oldPos.includes(newPos.substring(0, 2)) || newPos.includes(oldPos.substring(0, 2))
      );
      if (similarPos && lineup[similarPos]) {
        newLineup[newPos] = lineup[similarPos];
      }
    });
    
    setLineup(newLineup);
  };

  const handleSaveLineup = async () => {
    if (!matchId || !clubId) return;

    try {
      // Validar escalação antes de salvar
      const validation = validateLineup(lineup, formation);

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          toast.error(error);
        });
        return;
      }

      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }

      // Converter lineup para IDs
      const positions = positionsByFormation[formation] || [];
      const lineupIds: Record<string, string | null> = {};
      positions.forEach(pos => {
        lineupIds[pos] = lineup[pos]?.id || null;
      });

      await saveMatchLineup(clubId, matchId, lineupIds, formation);
      toast.success("Escalação salva com sucesso!");

      // Recarregar dados para sincronizar com o squad
      await loadMatchData();
    } catch (error) {
      console.error("Erro ao salvar escalação:", error);
      toast.error("Erro ao salvar escalação");
    }
  };

  const handleGeneratePDF = async () => {
    if (!clubId) return;
    try {
      const clubInfo = await getClubInfo(clubId);

      const lineupData: Record<string, MappingPosition | null> = {};
      Object.entries(lineup).forEach(([pos, player]) => {
        if (player) {
          lineupData[pos] = {
            player_id: player.id,
            player_name: player.name,
            player_nickname: player.nickname,
            player_number: player.number,
            player_image: player.image,
          };
        } else {
          lineupData[pos] = null;
        }
      });

      const reservesData = substitutes.map(r => ({
        player_id: r.player?.id || '',
        player_name: r.player?.name || '',
        player_nickname: r.player?.nickname,
        player_number: r.player?.number || 0,
        player_image: r.player?.image,
      }));

      const coachName =
        coaches[0]?.collaborator?.nickname ||
        coaches[0]?.collaborator?.full_name?.split(' ')[0] ||
        coaches[0]?.user?.name?.split(' ')[0];
      const coachImage = coaches[0]?.collaborator?.image;

      await generateLineupPDF({
        formation,
        clubName: clubInfo?.name || 'Clube',
        lineup: lineupData,
        reserves: reservesData,
        coachName,
        coachImage,
        date: new Date().toLocaleDateString('pt-BR'),
        clubLogo: clubInfo?.logo_url || clubInfo?.logo,
      });

      toast.success('PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar PDF');
    }
  };


  const handleSelectPosition = (position: string) => {
    setSelectingFor(position);
    setActiveTab("jogadores");
  };

  const handleAssignPlayer = (player: Player) => {
    if (selectingFor) {
      // Validar se o jogador pode ser colocado nesta posição
      const positionValidation = validatePlayerPosition(player, selectingFor);

      if (positionValidation.warnings.length > 0) {
        // Mostrar aviso mas permitir a escalação
        positionValidation.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }

      setLineup(prev => ({
        ...prev,
        [selectingFor]: player
      }));
      setSelectingFor(null);
      setActiveTab("campo");
    }
  };

  const handleRemovePlayer = (position: string) => {
    setLineup(prev => {
      const newLineup = { ...prev };
      delete newLineup[position];
      return newLineup;
    });
  };

  const handleAddToSquad = async (player: Player, role: 'substitute' | 'technical_staff' | 'staff' | 'executive') => {
    if (!matchId || !clubId) return;

    try {
      // Verificar se o jogador já está no squad
      const existingMember = squad.find(member => member.player_id === player.id);
      if (existingMember) {
        toast.error(`${player.name} já está no squad como ${existingMember.role}`);
        return;
      }

      // Validar limites do squad
      const currentSquad = [...squad, { player_id: player.id, role } as any];
      const squadValidation = validateMatchSquad(currentSquad);

      if (squadValidation.warnings.length > 0) {
        squadValidation.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }

      await addMatchSquadMember(clubId, matchId, {
        player_id: player.id,
        role: role
      });
      await loadMatchData(); // Recarregar dados
      toast.success("Jogador adicionado ao squad!");
    } catch (error) {
      console.error("Erro ao adicionar ao squad:", error);
      toast.error("Erro ao adicionar ao squad");
    }
  };

  const handleRemoveFromSquad = async (memberId: number) => {
    if (!matchId || !clubId) return;

    try {
      await removeMatchSquadMember(clubId, matchId, memberId);
      await loadMatchData(); // Recarregar dados
      toast.success("Membro removido do squad!");
    } catch (error) {
      console.error("Erro ao remover do squad:", error);
      toast.error("Erro ao remover do squad");
    }
  };

  const handleAddCollaboratorWithCustomRole = async (collaborator: Collaborator, customRole: string, squadRole: 'technical_staff' | 'staff' | 'executive') => {
    if (!matchId || !clubId) return;

    try {
      // Verificar se o colaborador já está no squad
      const existingMember = squad.find(member => {
        if (member.collaborator_id && collaborator.id) {
          return member.collaborator_id === collaborator.id;
        }
        if (member.user_id && collaborator.user_id) {
          return member.user_id === collaborator.user_id;
        }
        return false;
      });

      if (existingMember) {
        toast.error(`${collaborator.full_name} já está no squad`);
        return;
      }

      // Usar collaborator_id se disponível, senão user_id
      const memberData: any = {
        role: squadRole,
        custom_role: customRole
      };

      if (collaborator.id) {
        memberData.collaborator_id = collaborator.id;
      } else if (collaborator.user_id) {
        memberData.user_id = collaborator.user_id;
      }

      await addMatchSquadMember(clubId, matchId, memberData);
      await loadMatchData();
      toast.success(`${collaborator.full_name} adicionado como ${customRole}!`);
    } catch (error) {
      console.error("Erro ao adicionar colaborador ao squad:", error);
      toast.error("Erro ao adicionar colaborador ao squad");
    }
  };

  const handleEditMemberRole = async (memberId: number, newRole: string) => {
    if (!matchId || !clubId) return;

    try {
      await updateMatchSquadMember(clubId, matchId, memberId, {
        custom_role: newRole
      });
      await loadMatchData();
      toast.success("Função atualizada com sucesso!");
    } catch (error) {
      console.error("Erro ao atualizar função:", error);
      toast.error("Erro ao atualizar função");
    }
  };

  const handleAddCollaboratorToSquad = async (collaborator: Collaborator, role: 'technical_staff' | 'staff' | 'executive') => {
    if (!matchId || !clubId) return;

    try {
      // Verificar se o colaborador já está no squad (verificação mais robusta)
      const existingMember = squad.find(member => {
        // Verificar por collaborator_id primeiro
        if (member.collaborator_id && collaborator.id) {
          return member.collaborator_id === collaborator.id;
        }
        // Verificar por user_id se disponível
        if (member.user_id && collaborator.user_id) {
          return member.user_id === collaborator.user_id;
        }
        // Verificar por nome como fallback (caso haja problemas com IDs)
        if (member.collaborator?.full_name && collaborator.full_name) {
          return member.collaborator.full_name.toLowerCase() === collaborator.full_name.toLowerCase();
        }
        return false;
      });

      if (existingMember) {
        const roleLabels = {
          'technical_staff': 'Comissão Técnica',
          'staff': 'Staff',
          'executive': 'Diretoria'
        };
        toast.error(`${collaborator.full_name} já está no squad como ${roleLabels[existingMember.role as keyof typeof roleLabels] || existingMember.role}`);
        return;
      }

      // Validar se temos um ID válido para adicionar
      if (!collaborator.id && !collaborator.user_id) {
        toast.error("Colaborador sem ID válido. Não é possível adicionar ao squad.");
        return;
      }

      // Usar collaborator_id se disponível, senão user_id
      const memberData: any = {
        role: role
      };

      if (collaborator.id) {
        memberData.collaborator_id = collaborator.id;
      } else if (collaborator.user_id) {
        memberData.user_id = collaborator.user_id;
      }

      await addMatchSquadMember(clubId, matchId, memberData);
      await loadMatchData(); // Recarregar dados

      const roleLabels = {
        'technical_staff': 'Comissão Técnica',
        'staff': 'Staff',
        'executive': 'Diretoria'
      };
      toast.success(`${collaborator.full_name} adicionado como ${roleLabels[role]}!`);
    } catch (error) {
      console.error("Erro ao adicionar colaborador ao squad:", error);
      toast.error("Erro ao adicionar colaborador ao squad");
    }
  };

  if (!isMatchSelected) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Selecione uma partida</h3>
          <p className="text-muted-foreground">
            Escolha uma partida acima para gerenciar a escalação
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!tablesExist) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Shield className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-medium mb-2 text-red-600">Tabelas do Sistema de Escalação Não Encontradas</h3>
          <p className="text-muted-foreground mb-4">
            As tabelas necessárias para o sistema de escalação não foram criadas no banco de dados.
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-yellow-800 mb-2">Como corrigir:</h4>
            <ol className="text-sm text-yellow-700 text-left list-decimal list-inside space-y-1">
              <li>Acesse o Supabase SQL Editor</li>
              <li>Execute o arquivo: <code className="bg-yellow-100 px-1 rounded">sql/fix-match-lineup-tables.sql</code></li>
              <li>Recarregue esta página</li>
            </ol>
          </div>
          <Button onClick={() => window.location.reload()} variant="outline">
            Recarregar Página
          </Button>
        </CardContent>
      </Card>
    );
  }

  const positions = positionsByFormation[formation] || [];
  const starters = squad.filter(m => m.role === 'starter');
  const substitutes = squad.filter(m => m.role === 'substitute');
  const technicalStaff = squad.filter(m => m.role === 'technical_staff');
  const staff = squad.filter(m => m.role === 'staff');
  const executive = squad.filter(m => m.role === 'executive');
  const coaches = technicalStaff.filter(m => {
    const role = m.collaborator?.role?.toLowerCase() || '';
    return role.includes('técnic') || role.includes('tecnic');
  });

  // Filtrar jogadores disponíveis para reservas (não estão na escalação principal nem já são reservas)
  const playersInLineup = Object.values(lineup).filter(Boolean).map(p => p!.id);
  const playersInSquad = squad.map(m => m.player_id).filter(Boolean);
  const availableForReserves = availablePlayers.filter(player =>
    !playersInLineup.includes(player.id) &&
    !playersInSquad.includes(player.id) &&
    player.status !== 'inativo' &&
    player.status !== 'emprestado'
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Escalação da Partida</h2>
        <div className="flex gap-2">
          <select
            value={formation}
            onChange={(e) => handleFormationChange(e.target.value)}
            className="border rounded px-3 py-2"
          >
            {formations.map(f => (
              <option key={f.id} value={f.id}>
                {f.name} - {f.description}
              </option>
            ))}
          </select>
          <Button onClick={handleSaveLineup} disabled={loading}>
            Salvar Escalação
          </Button>
          <Button onClick={handleGeneratePDF} variant="outline">
            <FileDown className="h-4 w-4 mr-1" /> Gerar PDF
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="campo">Campo</TabsTrigger>
          <TabsTrigger value="jogadores">Jogadores</TabsTrigger>
          <TabsTrigger value="reservas">Reservas</TabsTrigger>
          <TabsTrigger value="adicionar-reservas">+ Reservas</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
          <TabsTrigger value="adicionar-staff">+ Staff</TabsTrigger>
        </TabsList>

        <TabsContent value="campo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Escalação Titular - {formation}</CardTitle>
            </CardHeader>
            <CardContent>
              <FootballField
                formation={formation}
                lineup={lineup}
                onPositionClick={handleSelectPosition}
                onPlayerRemove={handleRemovePlayer}
              />
               <div className="mt-6 space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Reservas</h3>
                  <div className="flex flex-wrap gap-4">
                    {substitutes.map(member => (
                      <div key={member.id} className="flex flex-col items-center w-24">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={member.player?.image || ''} />
                          <AvatarFallback>{member.player?.number}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-center">
                          {member.player?.nickname || member.player?.name}
                          {member.player?.number ? ` #${member.player.number}` : ''}
                        </span>
                        <span className="text-xs text-muted-foreground text-center">
                          {member.player?.position}
                        </span>
                      </div>
                    ))}
                    {substitutes.length === 0 && (
                      <p className="text-muted-foreground text-sm">Nenhum jogador reserva adicionado</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Técnico</h3>
                  <div className="space-y-2">
                    {coaches.map(member => (
                      <div key={member.id} className="flex items-center gap-3 p-2 border rounded">
                        <Avatar>
                          <AvatarImage src={member.collaborator?.image || member.user?.avatar_url || ''} />
                          <AvatarFallback>{
                            member.collaborator?.nickname?.charAt(0) ||
                            member.collaborator?.full_name?.charAt(0) ||
                            member.user?.name?.charAt(0) ||
                            'T'
                          }</AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                        <span className="text-sm font-medium">
                            {member.collaborator?.nickname
                              ? member.collaborator.nickname
                              : member.collaborator?.full_name
                                ? member.collaborator.full_name.split(' ')[0]
                                : member.user?.name?.split(' ')[0] || member.user?.email}
                          </span>
                          <span className="text-xs text-muted-foreground">{member.collaborator?.role || 'Técnico'}</span>
                        </div>
                      </div>
                    ))}
                    {coaches.length === 0 && (
                      <p className="text-muted-foreground text-sm">Nenhum técnico adicionado</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="jogadores" className="space-y-4">
          <PlayerSelector
            players={availablePlayers}
            onPlayerSelect={selectingFor ? handleAssignPlayer : (player) => handleAddToSquad(player, 'substitute')}
            selectedPosition={selectingFor || undefined}
            title={selectingFor ? "Selecionar Jogador para Escalação" : "Jogadores Disponíveis"}
            subtitle={selectingFor ? `Posição: ${positionLabels[selectingFor]}` : "Clique para adicionar como reserva"}
            initialPositionFilter={selectingFor ? getPlayerPositionFromFieldPosition(selectingFor) : ""}
            initialCategoryFilter={matchCategoryId || ""}
            clubId={clubId}
            competitionId={matchCompetitionId || undefined}
            categoryId={matchCategoryId || undefined}
          />
        </TabsContent>

        <TabsContent value="adicionar-reservas" className="space-y-4">
          <PlayerSelector
            players={availableForReserves}
            onPlayerSelect={(player) => handleAddToSquad(player, 'substitute')}
            title="Adicionar Jogadores Reservas"
            subtitle="Selecione jogadores da categoria para adicionar como reservas"
            initialCategoryFilter={matchCategoryId || ""}
            clubId={clubId}
            competitionId={matchCompetitionId || undefined}
            categoryId={matchCategoryId || undefined}
          />
        </TabsContent>

        <TabsContent value="reservas" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <UserCheck className="h-5 w-5" />
                  Jogadores Reservas
                </div>
                <Button
                  onClick={() => setActiveTab("adicionar-reservas")}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Adicionar Reservas
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {substitutes.map(member => (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={member.player?.image || ""} />
                        <AvatarFallback>{member.player?.number}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {member.player?.nickname || member.player?.name}
                          {member.player?.number ? ` #${member.player.number}` : ''}
                        </div>
                        <div className="text-sm text-muted-foreground">{member.player?.position}</div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRemoveFromSquad(member.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {substitutes.length === 0 && (
                  <p className="text-muted-foreground text-center py-4">
                    Nenhum jogador reserva adicionado
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>



        <TabsContent value="staff" className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Comissão Técnica & Staff</h3>
            <Button
              onClick={() => setActiveTab("adicionar-staff")}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Staff
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Comissão Técnica */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Briefcase className="h-4 w-4" />
                  Comissão Técnica
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {technicalStaff.map(member => (
                    <StaffMemberCard
                      key={member.id}
                      member={member}
                      onRemove={() => handleRemoveFromSquad(member.id)}
                      onEditRole={(newRole) => handleEditMemberRole(member.id, newRole)}
                    />
                  ))}
                  {technicalStaff.length === 0 && (
                    <p className="text-muted-foreground text-sm text-center py-4">
                      Nenhum membro da comissão técnica
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Staff Operacional */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Users className="h-4 w-4" />
                  Staff Operacional
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {staff.map(member => (
                    <StaffMemberCard
                      key={member.id}
                      member={member}
                      onRemove={() => handleRemoveFromSquad(member.id)}
                      onEditRole={(newRole) => handleEditMemberRole(member.id, newRole)}
                    />
                  ))}
                  {staff.length === 0 && (
                    <p className="text-muted-foreground text-sm text-center py-4">
                      Nenhum membro do staff
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Diretoria */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Shield className="h-4 w-4" />
                  Diretoria
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {executive.map(member => (
                    <StaffMemberCard
                      key={member.id}
                      member={member}
                      onRemove={() => handleRemoveFromSquad(member.id)}
                      onEditRole={(newRole) => handleEditMemberRole(member.id, newRole)}
                    />
                  ))}
                  {executive.length === 0 && (
                    <p className="text-muted-foreground text-sm text-center py-4">
                      Nenhum membro da diretoria
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Resumo do Squad */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Users className="h-4 w-4" />
                Resumo do Squad da Partida
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{starters.length}</div>
                  <div className="text-sm text-green-700">Titulares</div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{substitutes.length}</div>
                  <div className="text-sm text-blue-700">Reservas</div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{technicalStaff.length}</div>
                  <div className="text-sm text-purple-700">Com. Técnica</div>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{staff.length}</div>
                  <div className="text-sm text-orange-700">Staff</div>
                </div>
                <div className="p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{executive.length}</div>
                  <div className="text-sm text-red-700">Diretoria</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="adicionar-staff" className="space-y-4">
          <FlexibleCollaboratorSelector
            clubId={clubId}
            onCollaboratorSelect={handleAddCollaboratorWithCustomRole}
            existingMembers={squad}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}