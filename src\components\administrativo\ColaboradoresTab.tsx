import { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  Pencil,
  Trash2,
  FileText,
  User,
  Phone,
  Mail,
  Calendar,
  Search,
  DollarSign,
  Image,
  ChevronDown,
  UserPlus,
  Link2
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Collaborator, getCollaborators, deleteCollaborator, updateCollaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { NovoColaboradorDialog } from "@/components/administrativo/NovoColaboradorDialog";
import { ColaboradorInviteDialog } from "@/components/administrativo/ColaboradorInviteDialog";
import { LinkUserDialog } from "@/components/administrativo/LinkUserDialog";

import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Input } from "@/components/ui/input";
import { formatDate } from "@/lib/utils";
import { ColaboradorDocumentosDialog } from "@/components/administrativo/ColaboradorDocumentosDialog";
import { ColaboradorFinanceiroDialog } from "@/components/administrativo/ColaboradorFinanceiroDialog";
import { ColaboradorFotoDialog } from "@/components/administrativo/ColaboradorFotoDialog";
import { ColaboradorCombinadoDialog } from "@/components/administrativo/ColaboradorCombinadoDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePermission } from "@/hooks/usePermission";
import { COLLABORATOR_PERMISSIONS } from "@/constants/permissions";
import { DataPagination } from "@/components/ui/data-pagination";
import { useLimits } from "@/components/guards/LimitGuard";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { MedicalProfessionalsList } from "@/components/medical/MedicalProfessionalsList";
import { MedicalProfessional } from "@/api/api";

interface ColaboradoresTabProps {
  collaborators: Collaborator[];
  loading: boolean;
  error: string | null;
  clubId: number;
  onRefresh: () => void;
}

export function ColaboradoresTab({
  collaborators,
  loading,
  error,
  clubId,
  onRefresh
}: ColaboradoresTabProps) {
  const [novoColaboradorDialogOpen, setNovoColaboradorDialogOpen] = useState(false);
  const [excluirColaboradorDialogOpen, setExcluirColaboradorDialogOpen] = useState(false);
  const [documentosDialogOpen, setDocumentosDialogOpen] = useState(false);
  const [financeiroDialogOpen, setFinanceiroDialogOpen] = useState(false);
  const [fotoDialogOpen, setFotoDialogOpen] = useState(false);
  const [combinadoDialogOpen, setCombinadoDialogOpen] = useState(false);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [colaboradorSelecionado, setColaboradorSelecionado] = useState<Collaborator | null>(null);
  const [professionalSelecionado, setProfessionalSelecionado] = useState<MedicalProfessional | null>(null);
  const [professionalsRefreshKey, setProfessionalsRefreshKey] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const { user } = useUser();
  const { role, can } = usePermission();
  const { limitInfo: userLimitInfo } = useLimits('users');

  const canViewAll = can(COLLABORATOR_PERMISSIONS.VIEW);
  const canViewOwn = can(COLLABORATOR_PERMISSIONS.VIEW_OWN);
  const canCreate = can(COLLABORATOR_PERMISSIONS.CREATE);
  const canEdit = can(COLLABORATOR_PERMISSIONS.EDIT);
  const canDelete = can(COLLABORATOR_PERMISSIONS.DELETE);
  const canUpdateStatus = can(COLLABORATOR_PERMISSIONS.STATUS.UPDATE);
  const canViewFinance = can(COLLABORATOR_PERMISSIONS.FINANCE.VIEW);

  // Filtrar colaboradores visíveis de acordo com permissões
  const visibleCollaborators = canViewAll
    ? collaborators
    : canViewOwn && user?.id
      ? collaborators.filter((c) => c.user_id === user.id)
      : [];

  // Filtrar colaboradores com base no termo de busca
  const filteredCollaborators = visibleCollaborators.filter(
    (colaborador) =>
      colaborador.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      colaborador.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (colaborador.email && colaborador.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Paginação
  const totalPages = Math.ceil(filteredCollaborators.length / itemsPerPage);
  const paginatedCollaborators = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredCollaborators.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredCollaborators, currentPage, itemsPerPage]);

  // Reset page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Função para excluir um colaborador
  const handleDeleteCollaborator = async () => {
    if (!colaboradorSelecionado || !user) return;

    try {
      await deleteCollaborator(clubId, user.id, colaboradorSelecionado.id);

      toast({
        title: "Sucesso",
        description: "Colaborador excluído com sucesso",
      });

      setExcluirColaboradorDialogOpen(false);
      onRefresh();
    } catch (err: any) {
      console.error("Erro ao excluir colaborador:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir colaborador",
        variant: "destructive",
      });
    }
  };

  // Função para atualizar o status de um colaborador
  const handleStatusChange = async (collaboratorId: number, newStatus: string) => {
    if (!user) return;

    try {
      await updateCollaborator(clubId, user.id, collaboratorId, {
        status: newStatus
      });

      toast({
        title: "Sucesso",
        description: "Status do colaborador atualizado com sucesso",
      });

      onRefresh();
    } catch (err: any) {
      console.error("Erro ao atualizar status do colaborador:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar status do colaborador",
        variant: "destructive",
      });
    }
  };



  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Colaboradores</CardTitle>
          <CardDescription>
            Gerenciar colaboradores do clube
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">Erro ao carregar colaboradores: {error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Colaboradores</CardTitle>
          <CardDescription>
            Gerenciar colaboradores do clube
          </CardDescription>
        </div>
        {canCreate && (
          <Button
            onClick={() => {
              setColaboradorSelecionado(null);
              setProfessionalSelecionado(null);
              setNovoColaboradorDialogOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Colaborador
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="mb-4 relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar colaboradores..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {loading ? (
          <div className="text-center py-4">Carregando colaboradores...</div>
        ) : filteredCollaborators.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm ? "Nenhum colaborador encontrado com esse termo." : "Nenhum colaborador cadastrado."}
          </div>
        ) : (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Função</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Contato</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedCollaborators.map((colaborador) => (
                  <TableRow key={colaborador.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          {colaborador.image ? (
                            <AvatarImage src={colaborador.image} alt={colaborador.full_name} />
                          ) : null}
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            {colaborador.full_name.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{colaborador.full_name}</div>
                          <div className="text-xs text-muted-foreground">#{colaborador.registration_number}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{colaborador.role}</TableCell>
                    <TableCell>
                      {canUpdateStatus ? (
                        <Select
                          value={colaborador.status || "available"}
                          onValueChange={(value) => handleStatusChange(colaborador.id, value)}
                        >
                          <SelectTrigger className="w-[130px] h-8">
                            <SelectValue>
                              <div className="flex items-center">
                                <Badge
                                  className={
                                    colaborador.status === "available"
                                      ? "bg-green-100 text-green-800"
                                      : colaborador.status === "inactive"
                                      ? "bg-gray-100 text-gray-800"
                                      : colaborador.status === "vacation"
                                      ? "bg-primary/10 text-primary"
                                      : colaborador.status === "away"
                                      ? "bg-amber-100 text-amber-800"
                                      : colaborador.status === "medical"
                                      ? "bg-red-100 text-red-800"
                                      : ""
                                  }
                                >
                                  {colaborador.status === "available"
                                    ? "Disponível"
                                    : colaborador.status === "inactive"
                                    ? "Inativo"
                                    : colaborador.status === "vacation"
                                    ? "Férias"
                                    : colaborador.status === "away"
                                    ? "Afastado"
                                    : colaborador.status === "medical"
                                    ? "Ordem Médica"
                                    : colaborador.status || "Não definido"}
                                </Badge>
                              </div>
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="available">
                              <Badge className="bg-green-100 text-green-800">Disponível</Badge>
                            </SelectItem>
                            <SelectItem value="inactive">
                              <Badge className="bg-gray-100 text-gray-800">Inativo</Badge>
                            </SelectItem>
                            <SelectItem value="vacation">
                              <Badge className="bg-blue-100 text-blue-800">Férias</Badge>
                            </SelectItem>
                            <SelectItem value="away">
                              <Badge className="bg-amber-100 text-amber-800">Afastado</Badge>
                            </SelectItem>
                            <SelectItem value="medical">
                              <Badge className="bg-red-100 text-red-800">Ordem Médica</Badge>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <Badge
                          className={
                            colaborador.status === "available"
                              ? "bg-green-100 text-green-800"
                              : colaborador.status === "inactive"
                              ? "bg-gray-100 text-gray-800"
                              : colaborador.status === "vacation"
                              ? "bg-primary/10 text-primary"
                              : colaborador.status === "away"
                              ? "bg-amber-100 text-amber-800"
                              : colaborador.status === "medical"
                              ? "bg-red-100 text-red-800"
                              : ""
                          }
                        >
                          {colaborador.status === "available"
                            ? "Disponível"
                            : colaborador.status === "inactive"
                            ? "Inativo"
                            : colaborador.status === "vacation"
                            ? "Férias"
                            : colaborador.status === "away"
                            ? "Afastado"
                            : colaborador.status === "medical"
                            ? "Ordem Médica"
                            : colaborador.status || "Não definido"}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {colaborador.phone && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Phone className="h-3 w-3 mr-1" />
                          {colaborador.phone}
                        </div>
                      )}
                      {colaborador.email && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="h-3 w-3 mr-1" />
                          {colaborador.email}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {canViewFinance && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setColaboradorSelecionado(colaborador);
                              setFinanceiroDialogOpen(true);
                            }}
                            title="Financeiro"
                          >
                            <DollarSign className="h-4 w-4" />
                          </Button>
                        )}
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setColaboradorSelecionado(colaborador);
                              setFotoDialogOpen(true);
                            }}
                            title="Atualizar Foto"
                          >
                            <Image className="h-4 w-4" />
                          </Button>
                        )}
                        {canCreate && !colaborador.user_id && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setColaboradorSelecionado(colaborador);
                              setLinkDialogOpen(true);
                            }}
                            title="Vincular Usuário"
                            className="text-green-500 hover:text-green-700 hover:bg-green-50"
                          >
                            <Link2 className="h-4 w-4" />
                          </Button>
                        )}
                        {canCreate && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    if (userLimitInfo?.canAdd) {
                                      setColaboradorSelecionado(colaborador);
                                      setInviteDialogOpen(true);
                                    }
                                  }}
                                  disabled={!userLimitInfo?.canAdd}
                                  className={`${
                                    userLimitInfo?.canAdd 
                                      ? "text-blue-500 hover:text-blue-700 hover:bg-blue-50" 
                                      : "text-gray-400 cursor-not-allowed"
                                  }`}
                                >
                                  <UserPlus className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {userLimitInfo?.canAdd ? (
                                  <p>Criar Conta de Usuário</p>
                                ) : (
                                  <div className="text-center">
                                    <p className="font-medium">Limite de usuários atingido</p>
                                    <p className="text-xs">
                                      {userLimitInfo?.current} / {userLimitInfo?.limit} usuários
                                    </p>
                                    <p className="text-xs">Faça upgrade do plano para adicionar mais</p>
                                  </div>
                                )}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {canDelete && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => {
                              setColaboradorSelecionado(colaborador);
                              setExcluirColaboradorDialogOpen(true);
                            }}
                            title="Excluir"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setColaboradorSelecionado(colaborador);
                              setProfessionalSelecionado(null);
                              setNovoColaboradorDialogOpen(true);
                            }}
                            title="Editar Colaborador"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {totalPages > 1 && (
              <div className="mt-4">
                <DataPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  showInfo={true}
                  totalItems={filteredCollaborators.length}
                  itemsPerPage={itemsPerPage}
                />
              </div>
            )}
          </>
        )}
      </CardContent>

      {/* Diálogos */}
      {canCreate && (
        <NovoColaboradorDialog
          open={novoColaboradorDialogOpen}
          onOpenChange={(open) => {
            setNovoColaboradorDialogOpen(open);
            if (!open) {
              setColaboradorSelecionado(null);
              setProfessionalSelecionado(null);
            }
          }}
          clubId={clubId}
          onSuccess={() => {
            onRefresh();
            setProfessionalsRefreshKey((k) => k + 1);
          }}
          collaborator={colaboradorSelecionado || undefined}
          medicalProfessional={professionalSelecionado || undefined}
        />
      )}

      {colaboradorSelecionado && (
        <>
          <ColaboradorCombinadoDialog
            open={combinadoDialogOpen}
            onOpenChange={setCombinadoDialogOpen}
            clubId={clubId}
            collaborator={colaboradorSelecionado}
            onSuccess={onRefresh}
            allowedTabs={role === 'staff' ? ['uploads'] : undefined}
          />

          {canViewFinance && (
            <ColaboradorFinanceiroDialog
              open={financeiroDialogOpen}
              onOpenChange={setFinanceiroDialogOpen}
              clubId={clubId}
              collaborator={colaboradorSelecionado}
              onSuccess={onRefresh}
            />
          )}

          {canEdit && (
            <ColaboradorFotoDialog
              open={fotoDialogOpen}
              onOpenChange={setFotoDialogOpen}
              clubId={clubId}
              collaborator={colaboradorSelecionado}
              onSuccess={onRefresh}
            />
          )}

          {canCreate && (
            <ColaboradorInviteDialog
              open={inviteDialogOpen}
              onOpenChange={setInviteDialogOpen}
              clubId={clubId}
              collaborator={colaboradorSelecionado}
              onSuccess={onRefresh}
            />
          )}

          {canCreate && (
            <LinkUserDialog
              open={linkDialogOpen}
              onOpenChange={setLinkDialogOpen}
              clubId={clubId}
              collaborator={colaboradorSelecionado}
              onSuccess={onRefresh}
            />
          )}

          {canDelete && (
            <ConfirmDialog
              open={excluirColaboradorDialogOpen}
              onOpenChange={setExcluirColaboradorDialogOpen}
              title="Excluir Colaborador"
              description={`Tem certeza que deseja excluir o colaborador ${colaboradorSelecionado.full_name}? Esta ação não pode ser desfeita.`}
              onConfirm={handleDeleteCollaborator}
            />
          )}
        </>
      )}
    </Card>
    <MedicalProfessionalsList
      key={professionalsRefreshKey}
      onEdit={(p) => {
        setProfessionalSelecionado(p);
        setColaboradorSelecionado(null);
        setNovoColaboradorDialogOpen(true);
      }}
    />
    </>
  );
}