import { supabase } from "@/integrations/supabase/client";
import { calculateRiskFactors, getHighRiskPlayers, RiskFactors } from "@/api/injuryPrevention";
import { getClubInfo } from "@/api/api";
import { sendEmail } from "@/services/emailService";

interface AlertConfig {
  clubId: number;
  enabled: boolean;
  criticalThreshold: number;
  highThreshold: number;
  moderateThreshold: number;
  emailNotifications: boolean;
  whatsappNotifications: boolean;
  alertRecipients: string[]; // emails dos destinatários
}

interface InjuryAlert {
  id?: number;
  club_id: number;
  player_id: number;
  player_name: string;
  alert_type: 'critical' | 'high' | 'moderate' | 'improvement';
  risk_score: number;
  previous_risk_score?: number;
  alert_message: string;
  recommendations: any[];
  created_at?: string;
  acknowledged?: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
}

/**
 * Serviço principal para gerenciamento de alertas de lesão
 */
export class InjuryAlertService {
  
  /**
   * Executa análise automática de riscos e gera alertas
   */
  static async runAutomaticRiskAnalysis(clubId: number): Promise<InjuryAlert[]> {
    try {
      console.log(`[InjuryAlert] Iniciando análise automática para clube ${clubId}`);
      
      // 1. Buscar todos os jogadores do clube
      const { data: players, error: playersError } = await supabase
        .from('players')
        .select('id, name')
        .eq('club_id', clubId)
        .eq('status', 'ativo');

      if (playersError) throw playersError;
      if (!players || players.length === 0) {
        console.log(`[InjuryAlert] Nenhum jogador ativo encontrado para clube ${clubId}`);
        return [];
      }

      // 2. Calcular riscos para todos os jogadores
      const riskCalculations = await Promise.allSettled(
        players.map(player => this.calculatePlayerRiskWithHistory(clubId, player.id))
      );

      // 3. Processar resultados e gerar alertas
      const alerts: InjuryAlert[] = [];
      
      for (let i = 0; i < riskCalculations.length; i++) {
        const result = riskCalculations[i];
        const player = players[i];
        
        if (result.status === 'fulfilled' && result.value) {
          const { currentRisk, previousRisk } = result.value;
          const alert = await this.generateAlertIfNeeded(
            clubId, 
            player.id, 
            player.name, 
            currentRisk, 
            previousRisk
          );
          
          if (alert) {
            alerts.push(alert);
          }
        }
      }

      // 4. Salvar alertas no banco
      if (alerts.length > 0) {
        await this.saveAlerts(alerts);
        
        // 5. Enviar notificações
        await this.sendNotifications(clubId, alerts);
      }

      console.log(`[InjuryAlert] Análise concluída. ${alerts.length} alertas gerados.`);
      return alerts;
      
    } catch (error) {
      console.error('[InjuryAlert] Erro na análise automática:', error);
      throw error;
    }
  }

  /**
   * Calcula risco atual e busca risco anterior para comparação
   */
  private static async calculatePlayerRiskWithHistory(
    clubId: number, 
    playerId: number
  ): Promise<{ currentRisk: RiskFactors; previousRisk?: RiskFactors } | null> {
    try {
      // Calcular risco atual
      const currentRisk = await calculateRiskFactors(clubId, playerId);
      
      // Buscar risco anterior (ontem)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];
      
      const { data: previousRisk } = await supabase
        .from('injury_risk_factors')
        .select('*')
        .eq('club_id', clubId)
        .eq('player_id', playerId)
        .eq('calculated_date', yesterdayStr)
        .single();

      return {
        currentRisk,
        previousRisk: previousRisk || undefined
      };
      
    } catch (error) {
      console.error(`[InjuryAlert] Erro ao calcular risco para jogador ${playerId}:`, error);
      return null;
    }
  }

  /**
   * Gera alerta se necessário baseado nos thresholds
   */
  private static async generateAlertIfNeeded(
    clubId: number,
    playerId: number,
    playerName: string,
    currentRisk: RiskFactors,
    previousRisk?: RiskFactors
  ): Promise<InjuryAlert | null> {
    
    const currentScore = currentRisk.overall_risk_score;
    const previousScore = previousRisk?.overall_risk_score || 0;
    
    // Determinar tipo de alerta
    let alertType: InjuryAlert['alert_type'] | null = null;
    let alertMessage = '';
    
    // Alertas de risco alto
    if (currentScore >= 75) {
      alertType = 'critical';
      alertMessage = `🚨 RISCO CRÍTICO: ${playerName} apresenta risco crítico de lesão (${currentScore.toFixed(1)}%). Avaliação médica urgente recomendada.`;
    } else if (currentScore >= 50) {
      alertType = 'high';
      alertMessage = `⚠️ RISCO ALTO: ${playerName} está com risco elevado de lesão (${currentScore.toFixed(1)}%). Monitoramento intensivo necessário.`;
    } else if (currentScore >= 25) {
      alertType = 'moderate';
      alertMessage = `⚡ RISCO MODERADO: ${playerName} apresenta risco moderado de lesão (${currentScore.toFixed(1)}%). Acompanhamento recomendado.`;
    }
    
    // Alertas de melhoria (se houve redução significativa)
    if (previousScore > 0 && (previousScore - currentScore) >= 15) {
      alertType = 'improvement';
      alertMessage = `✅ MELHORIA: ${playerName} teve redução significativa no risco de lesão (${previousScore.toFixed(1)}% → ${currentScore.toFixed(1)}%).`;
    }
    
    // Só gerar alerta se houver mudança significativa ou risco alto
    if (!alertType) return null;
    
    // Verificar se já existe alerta similar hoje
    const today = new Date().toISOString().split('T')[0];
    const { data: existingAlert } = await supabase
      .from('injury_alerts')
      .select('id')
      .eq('club_id', clubId)
      .eq('player_id', playerId)
      .eq('alert_type', alertType)
      .gte('created_at', `${today}T00:00:00`)
      .single();
    
    if (existingAlert) {
      console.log(`[InjuryAlert] Alerta similar já existe para jogador ${playerId}`);
      return null;
    }

    return {
      club_id: clubId,
      player_id: playerId,
      player_name: playerName,
      alert_type: alertType,
      risk_score: currentScore,
      previous_risk_score: previousScore,
      alert_message: alertMessage,
      recommendations: currentRisk.recommendations || [],
      acknowledged: false
    };
  }

  /**
   * Salva alertas no banco de dados
   */
  private static async saveAlerts(alerts: InjuryAlert[]): Promise<void> {
    try {
      const { error } = await supabase
        .from('injury_alerts')
        .insert(alerts);

      if (error) throw error;
      
      console.log(`[InjuryAlert] ${alerts.length} alertas salvos no banco`);
    } catch (error) {
      console.error('[InjuryAlert] Erro ao salvar alertas:', error);
      throw error;
    }
  }

  /**
   * Envia notificações por email e WhatsApp
   */
  private static async sendNotifications(clubId: number, alerts: InjuryAlert[]): Promise<void> {
    try {
      // Buscar configurações do clube
      const clubInfo = await getClubInfo(clubId);
      if (!clubInfo) return;

      // Filtrar apenas alertas críticos e altos para notificação
      const criticalAlerts = alerts.filter(alert => 
        alert.alert_type === 'critical' || alert.alert_type === 'high'
      );

      if (criticalAlerts.length === 0) return;

      // Buscar destinatários (usuários admin/president do clube)
      const { data: recipients } = await supabase
        .from('club_members')
        .select(`
          users!inner(email),
          role
        `)
        .eq('club_id', clubId)
        .in('role', ['admin', 'president', 'medical']);

      if (!recipients || recipients.length === 0) return;

      // Preparar email
      const emailSubject = `🚨 Alertas de Prevenção de Lesões - ${clubInfo.name}`;
      const emailBody = this.generateEmailBody(clubInfo.name, criticalAlerts);

      // Enviar emails
      for (const recipient of recipients) {
        try {
          await sendEmail({
            to: recipient.users.email,
            subject: emailSubject,
            html: emailBody
          });
        } catch (emailError) {
          console.error(`[InjuryAlert] Erro ao enviar email para ${recipient.users.email}:`, emailError);
        }
      }

      console.log(`[InjuryAlert] Notificações enviadas para ${recipients.length} destinatários`);
      
    } catch (error) {
      console.error('[InjuryAlert] Erro ao enviar notificações:', error);
    }
  }

  /**
   * Gera corpo do email de notificação
   */
  private static generateEmailBody(clubName: string, alerts: InjuryAlert[]): string {
    const criticalAlerts = alerts.filter(a => a.alert_type === 'critical');
    const highAlerts = alerts.filter(a => a.alert_type === 'high');

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
          <h1>🧠 IA - Prevenção de Lesões</h1>
          <h2>${clubName}</h2>
        </div>
        
        <div style="padding: 20px; background: #f8f9fa;">
          <p>Foram detectados <strong>${alerts.length} alertas</strong> de risco de lesão que requerem atenção:</p>
          
          ${criticalAlerts.length > 0 ? `
            <div style="background: #fee; border-left: 4px solid #dc3545; padding: 15px; margin: 15px 0;">
              <h3 style="color: #dc3545; margin-top: 0;">🚨 Alertas Críticos (${criticalAlerts.length})</h3>
              ${criticalAlerts.map(alert => `
                <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                  <strong>${alert.player_name}</strong><br>
                  <span style="color: #dc3545;">Risco: ${alert.risk_score.toFixed(1)}%</span><br>
                  <em>Avaliação médica urgente recomendada</em>
                </div>
              `).join('')}
            </div>
          ` : ''}
          
          ${highAlerts.length > 0 ? `
            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 15px 0;">
              <h3 style="color: #856404; margin-top: 0;">⚠️ Alertas de Risco Alto (${highAlerts.length})</h3>
              ${highAlerts.map(alert => `
                <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                  <strong>${alert.player_name}</strong><br>
                  <span style="color: #856404;">Risco: ${alert.risk_score.toFixed(1)}%</span><br>
                  <em>Monitoramento intensivo necessário</em>
                </div>
              `).join('')}
            </div>
          ` : ''}
          
          <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 5px;">
            <p><strong>Próximos passos recomendados:</strong></p>
            <ul>
              <li>Revisar dados de wellness dos jogadores em risco</li>
              <li>Ajustar cargas de treino conforme necessário</li>
              <li>Agendar avaliações médicas para casos críticos</li>
              <li>Monitorar evolução diária dos riscos</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 20px;">
            <a href="${process.env.VITE_SITE_URL}/medico" 
               style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Acessar Dashboard Médico
            </a>
          </div>
        </div>
        
        <div style="background: #6c757d; color: white; padding: 15px; text-align: center; font-size: 12px;">
          <p>Este é um alerta automático do sistema de IA para prevenção de lesões.<br>
          Game Day Nexus Platform - Tecnologia a serviço do esporte</p>
        </div>
      </div>
    `;
  }

  /**
   * Busca alertas não reconhecidos de um clube
   */
  static async getUnacknowledgedAlerts(clubId: number): Promise<InjuryAlert[]> {
    try {
      const { data, error } = await supabase
        .from('injury_alerts')
        .select('*')
        .eq('club_id', clubId)
        .eq('acknowledged', false)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
      
    } catch (error) {
      console.error('[InjuryAlert] Erro ao buscar alertas:', error);
      return [];
    }
  }

  /**
   * Marca alerta como reconhecido
   */
  static async acknowledgeAlert(alertId: number, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('injury_alerts')
        .update({
          acknowledged: true,
          acknowledged_by: userId,
          acknowledged_at: new Date().toISOString()
        })
        .eq('id', alertId);

      if (error) throw error;
      
    } catch (error) {
      console.error('[InjuryAlert] Erro ao reconhecer alerta:', error);
      throw error;
    }
  }

  /**
   * Configura execução automática (seria chamado por um cron job)
   */
  static async scheduleAutomaticAnalysis(): Promise<void> {
    // Esta função seria chamada por um sistema de cron jobs
    // Por exemplo, todo dia às 6h da manhã
    console.log('[InjuryAlert] Executando análise automática agendada...');
    
    try {
      // Buscar todos os clubes ativos
      const { data: clubs } = await supabase
        .from('club_info')
        .select('id')
        .eq('active', true);

      if (!clubs) return;

      // Executar análise para cada clube
      for (const club of clubs) {
        try {
          await this.runAutomaticRiskAnalysis(club.id);
        } catch (error) {
          console.error(`[InjuryAlert] Erro na análise do clube ${club.id}:`, error);
        }
      }
      
    } catch (error) {
      console.error('[InjuryAlert] Erro na análise automática agendada:', error);
    }
  }
}

// Exportar para uso em outros módulos
export default InjuryAlertService;
