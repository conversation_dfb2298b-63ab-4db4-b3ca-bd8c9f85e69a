import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ImagePlus,
  Trash2,
  Plus,
  Users,
  Calendar,
  Home,
  Shirt,
  Award,
  UserCog,
  Stethoscope,
  Briefcase,
  Info,
  Ban
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { Callup, CallupPlayer } from "@/api/callups";
import { CallupOperationStaff, GameOperationStaff, getGameOperationStaff, getOperationRoles } from "@/api/gameOperations";
import { Category } from "@/api/api";
import { ClubInfo } from "@/api/api";
import { ClubUser } from "@/api/api";
import { Player } from "@/api/api";
import { Collaborator } from "@/api/collaborators";
import { getCategoryPlayers, getPlayers } from "@/api/api";
import { CallupImageUpload } from "./CallupImageUpload";
import { formatCurrency } from "@/utils/formatters";
import { isPlayerSuspendedForMatch } from "@/api/cardSuspensions";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CallupDesignEditorProps {
  callup: Partial<Callup>;
  onCallupChange: (callup: Partial<Callup>) => void;
  players: CallupPlayer[];
  onPlayersChange: (players: CallupPlayer[]) => void;
  operationStaff: CallupOperationStaff[];
  onOperationStaffChange: (ops: CallupOperationStaff[]) => void;
  categoryPlayers: Player[];
  clubUsers: ClubUser[];
  collaborators: Collaborator[];
  clubInfo: ClubInfo | null;
  categories: Category[];
  clubId: number;
}

export function CallupDesignEditor({
  callup,
  onCallupChange,
  players,
  onPlayersChange,
  operationStaff,
  onOperationStaffChange,
  categoryPlayers,
  clubUsers,
  collaborators,
  clubInfo,
  categories,
  clubId
}: CallupDesignEditorProps) {
  const [activeTab, setActiveTab] = useState("images");
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [showStaffDialog, setShowStaffDialog] = useState(false);
  const [showOperationDialog, setShowOperationDialog] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    players: true,
    schedule: true,
    hotel: true,
    notices: true,
    staff: true,
    technical: true,
    executive: true
  });

  // Função para atualizar um campo do callup
  const updateCallup = (field: string, value: any) => {
    onCallupChange({
      ...callup,
      [field]: value
    });
  };

  // Função para adicionar um jogador à convocação
  const addPlayer = (playerId: string, role: string = "Atleta") => {
    const newPlayer: CallupPlayer = {
      id: 0, // Será definido pelo backend
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      player_id: playerId,
      role: role,
      player_name: categoryPlayers.find(p => p.id === playerId)?.name || ""
    };

    onPlayersChange([...players, newPlayer]);
  };

  // Função para adicionar um membro da comissão à convocação
  const addStaff = (userId: string, role: string) => {
    // Verificar se o ID é de um colaborador (começa com número) ou de um usuário
    const isCollaborator = !isNaN(Number(userId));

    let userName = "";

    if (isCollaborator) {
      // Buscar o nome do colaborador
      const collaborator = collaborators.find(c => c.id.toString() === userId);
      userName = collaborator?.full_name || "";
    } else {
      // Buscar o nome do usuário
      userName = clubUsers.find(u => u.id === userId)?.name || "";
    }

    const newStaff: CallupPlayer = {
      id: 0, // Será definido pelo backend
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      user_id: userId,
      role: role,
      user_name: userName
    };

    onPlayersChange([...players, newStaff]);
  };

  // Função para remover um membro da convocação
  const removeCallupMember = (index: number) => {
    const updatedPlayers = [...players];
    updatedPlayers.splice(index, 1);
    onPlayersChange(updatedPlayers);
  };

  // Função para adicionar um jogador manualmente
  const addManualPlayer = (name: string, role: string = "Atleta") => {
    const newPlayer: CallupPlayer = {
      id: 0,
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      role: role,
      player_name: name
    };

    onPlayersChange([...players, newPlayer]);
  };

  // Função para adicionar um membro da comissão manualmente
  const addManualStaff = (name: string, role: string) => {
    const newStaff: CallupPlayer = {
      id: 0,
      club_id: callup.club_id || 0,
      callup_id: callup.id || 0,
      role: role,
      user_name: name
    };

    onPlayersChange([...players, newStaff]);
  };

  // Operação de jogo
  const addOperationMember = (member: CallupOperationStaff) => {
    onOperationStaffChange([...operationStaff, member]);
  };


  const removeOperationMember = (index: number) => {
    const updated = [...operationStaff];
    updated.splice(index, 1);
    onOperationStaffChange(updated);
  };

  // Função para atualizar os campos selecionados
  const toggleField = (field: string, checked: boolean) => {
    setSelectedFields({
      ...selectedFields,
      [field]: checked
    });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="images">Imagens</TabsTrigger>
          <TabsTrigger value="content">Conteúdo</TabsTrigger>
          <TabsTrigger value="layout">Layout</TabsTrigger>
        </TabsList>

        <TabsContent value="images" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Emblema do Clube Mandante</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.home_club_logo || ""}
                  onImageChange={(url) => updateCallup("home_club_logo", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="home-logo"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Emblema do Clube Visitante</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.away_club_logo || ""}
                  onImageChange={(url) => updateCallup("away_club_logo", url)}
                  maxWidth={200}
                  maxHeight={200}
                  clubId={clubInfo?.id}
                  imageType="away-logo"
                />
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Patrocinador 1</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.sponsor_image1 || ""}
                  onImageChange={(url) => updateCallup("sponsor_image1", url)}
                  maxWidth={200}
                  maxHeight={100}
                  clubId={clubInfo?.id}
                  imageType="sponsor1"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Patrocinador 2</CardTitle>
              </CardHeader>
              <CardContent>
                <CallupImageUpload
                  imageUrl={callup.sponsor_image2 || ""}
                  onImageChange={(url) => updateCallup("sponsor_image2", url)}
                  maxWidth={200}
                  maxHeight={100}
                  clubId={clubInfo?.id}
                  imageType="sponsor2"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          {/* Conteúdo da convocação */}
          <Card>
            <CardHeader>
              <CardTitle>Programação do Jogo</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Detalhes da programação do jogo..."
                value={callup.match_schedule || ""}
                onChange={(e) => updateCallup("match_schedule", e.target.value)}
                className="min-h-[150px]"
              />
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader>
              <CardTitle>Controle de Hospedagem</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Informações sobre hospedagem..."
                value={callup.hotel_control || ""}
                onChange={(e) => updateCallup("hotel_control", e.target.value)}
                className="min-h-[150px]"
              />
            </CardContent>
          </Card> */}

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Jogadores Convocados</CardTitle>
              <Button size="sm" onClick={() => setShowPlayerDialog(true)}>
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </Button>
            </CardHeader>
            <CardContent>
              {players.filter(p => ["Atleta", "Titular", "Reserva"].includes(p.role)).length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum jogador convocado
                </div>
              ) : (
                <div className="space-y-2">
                  {players
                    .filter(p => ["Atleta", "Titular", "Reserva"].includes(p.role))
                    .map((player, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2">
                        <div>{player.player_name || player.user_name}</div>
                        {/* <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCallupMember(players.indexOf(player))}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button> */}
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Comissão Técnica e Staff</CardTitle>
              <div className="text-sm text-muted-foreground mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <Info className="h-4 w-4 inline mr-2" />
                A comissão técnica e staff são adicionados automaticamente através da <strong>Escalação da Partida</strong>.
                Vá na aba "Escalação" da partida para gerenciar os membros.
              </div>
            </CardHeader>
            <CardContent>
              {players.filter(p => p.role !== "Atleta").length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Nenhum membro da comissão adicionado</p>
                  <p className="text-xs mt-1">Configure na escalação da partida</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {players
                    .filter(p => p.role !== "Atleta")
                    .map((staff, index) => (
                      <div key={index} className="flex items-center justify-between border-b pb-2">
                        <div className="flex flex-col">
                          <span className="font-medium">{staff.player_name || staff.user_name}</span>
                          <span className="text-xs text-muted-foreground">{staff.role}</span>
                          <span className="text-xs text-blue-600">Adicionado via escalação</span>
                        </div>
                        {/* <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCallupMember(players.indexOf(staff))}
                          title="Remover da convocação"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button> */}
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Operação de Jogo</CardTitle>
              <Button size="sm" onClick={() => setShowOperationDialog(true)}>
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </Button>
            </CardHeader>
            <CardContent>
              {operationStaff.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum membro adicionado
                </div>
              ) : (
                <div className="space-y-2">
                  {operationStaff.map((op, index) => (
                    <div key={index} className="flex items-center justify-between border-b pb-2">
                      <div className="flex flex-col">
                        <span>{op.name}</span>
                        <span className="text-xs text-muted-foreground">{op.role}</span>
                        {op.value !== undefined && op.value !== null && (
                          <span className="text-xs text-muted-foreground">{formatCurrency(op.value)}</span>
                        )}
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => removeOperationMember(index)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="layout" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Campos a Exibir no Relatório</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="players"
                    checked={selectedFields.players}
                    onCheckedChange={(checked) => toggleField("players", checked as boolean)}
                  />
                  <Label htmlFor="players" className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Jogadores Convocados
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="schedule"
                    checked={selectedFields.schedule}
                    onCheckedChange={(checked) => toggleField("schedule", checked as boolean)}
                  />
                  <Label htmlFor="schedule" className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Programação do Jogo
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hotel"
                    checked={selectedFields.hotel}
                    onCheckedChange={(checked) => toggleField("hotel", checked as boolean)}
                  />
                  <Label htmlFor="hotel" className="flex items-center">
                    <Home className="h-4 w-4 mr-2" />
                    Controle de Hospedagem
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notices"
                    checked={selectedFields.notices}
                    onCheckedChange={(checked) => toggleField("notices", checked as boolean)}
                  />
                  <Label htmlFor="notices" className="flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    Avisos Importantes
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="staff"
                    checked={selectedFields.staff}
                    onCheckedChange={(checked) => toggleField("staff", checked as boolean)}
                  />
                  <Label htmlFor="staff" className="flex items-center">
                    <UserCog className="h-4 w-4 mr-2" />
                    Staff
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="technical"
                    checked={selectedFields.technical}
                    onCheckedChange={(checked) => toggleField("technical", checked as boolean)}
                  />
                  <Label htmlFor="technical" className="flex items-center">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    Comissão Técnica
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="executive"
                    checked={selectedFields.executive}
                    onCheckedChange={(checked) => toggleField("executive", checked as boolean)}
                  />
                  <Label htmlFor="executive" className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2" />
                    Diretoria Executiva
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Diálogo para adicionar jogadores */}
      <AddPlayerDialog
        open={showPlayerDialog}
        onOpenChange={setShowPlayerDialog}
        players={categoryPlayers}
        categories={categories}
        clubId={clubId}
        defaultCategoryId={callup.category_id}
        onAddPlayer={addPlayer}
        onAddManualPlayer={addManualPlayer}
        existingPlayers={players.filter(p => ["Atleta", "Titular", "Reserva"].includes(p.role))}
        callup={callup}
      />

      {/* Diálogo para adicionar membros da comissão */}
      <AddStaffDialog
        open={showStaffDialog}
        onOpenChange={setShowStaffDialog}
        users={clubUsers}
        collaborators={collaborators}
        onAddStaff={addStaff}
        onAddManualStaff={addManualStaff}
        existingStaff={players.filter(p => !["Atleta", "Titular", "Reserva"].includes(p.role))}
      />

      <AddOperationDialog
        open={showOperationDialog}
        onOpenChange={setShowOperationDialog}
        onAddOperation={addOperationMember}
        existing={operationStaff}
        collaborators={collaborators}
        categories={categories}
        defaultCategoryId={callup.category_id}
      />
    </div>
  );
}

// Componente para adicionar jogadores
interface AddPlayerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  players: Player[];
  categories: Category[];
  clubId: number;
  defaultCategoryId?: number | null;
  onAddPlayer: (playerId: string, role?: string) => void;
  onAddManualPlayer: (name: string, role?: string) => void;
  existingPlayers: CallupPlayer[];
  callup?: Partial<Callup>;
}

function AddPlayerDialog({
  open,
  onOpenChange,
  players,
  categories,
  clubId,
  defaultCategoryId,
  onAddPlayer,
  onAddManualPlayer,
  existingPlayers,
  callup
}: AddPlayerDialogProps) {
  const [activeTab, setActiveTab] = useState("registered");
  const [manualName, setManualName] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>(
    defaultCategoryId ? String(defaultCategoryId) : "all"
  );
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>(players);
  const [suspensionInfo, setSuspensionInfo] = useState<Record<string, {
    isSuspended: boolean;
    reason?: string;
    matchesRemaining?: number;
  }>>({});

  useEffect(() => {
    const fetchPlayers = async () => {
      if (!open) return;
      try {
        if (categoryFilter === "all") {
          const all = await getPlayers(clubId);
          setAvailablePlayers(all);
        } else {
          const catPlayers = await getCategoryPlayers(
            clubId,
            parseInt(categoryFilter)
          );
          setAvailablePlayers(catPlayers);
        }
      } catch (err) {
        console.error("Erro ao buscar jogadores:", err);
        setAvailablePlayers([]);
      }
    };
    fetchPlayers();
  }, [categoryFilter, open, clubId]);

  // Carregar informações de suspensão quando os jogadores mudarem
  useEffect(() => {
    const loadSuspensionInfo = async () => {
      if (!open || availablePlayers.length === 0 || !defaultCategoryId) return;

      const info: Record<string, any> = {};

      try {
        // Buscar a partida vinculada à convocação para obter o competition_id
        let competitionId: string | undefined = undefined;

        // Assumindo que temos acesso ao callup.id, vamos buscar a partida vinculada
        const { data: matchData } = await supabase
          .from('matches')
          .select('competition_id')
          .eq('club_id', clubId)
          .eq('callup_id', callup?.id)
          .single();

        if (matchData?.competition_id) {
          competitionId = matchData.competition_id;
          console.log('🎯 [CONVOCAÇÃO] Competition ID encontrado:', competitionId);
        } else {
          console.log('⚠️ [CONVOCAÇÃO] Nenhuma partida vinculada encontrada, verificando apenas por categoria');
        }

        await Promise.all(
          availablePlayers.map(async (player) => {
            // Verificar suspensões com competição e categoria (se disponíveis)
            const suspensionData = await isPlayerSuspendedForMatch(clubId, player.id, competitionId, defaultCategoryId);
            info[player.id] = suspensionData;
          })
        );

        setSuspensionInfo(info);
      } catch (error) {
        console.error('Erro ao carregar informações de suspensão:', error);
      }
    };

    loadSuspensionInfo();
  }, [clubId, availablePlayers, defaultCategoryId, open, callup?.id]);

  const filteredPlayers = availablePlayers.filter(player =>
    player.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !existingPlayers.some(p => p.player_id === player.id)
  );

  const handleAddManualPlayer = () => {
    if (manualName.trim()) {
      onAddManualPlayer(manualName.trim());
      setManualName("");
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Jogador</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="registered">Jogadores Cadastrados</TabsTrigger>
            <TabsTrigger value="manual">Adicionar Manualmente</TabsTrigger>
          </TabsList>

          <TabsContent value="registered" className="space-y-4">
            <div className="space-y-2">
              <Label>Categoria</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  {categories.map(c => (
                    <SelectItem key={c.id} value={String(c.id)}>{c.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Input
              placeholder="Buscar jogador..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            <ScrollArea className="h-[300px]">
              {filteredPlayers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum jogador encontrado
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredPlayers.map((player) => {
                    const isSuspended = suspensionInfo[player.id]?.isSuspended || false;
                    const suspensionReason = suspensionInfo[player.id]?.reason;
                    const matchesRemaining = suspensionInfo[player.id]?.matchesRemaining;

                    return (
                      <div
                        key={player.id}
                        className={`flex items-center justify-between border-b pb-2 ${isSuspended ? 'opacity-60 bg-red-50' : ''
                          }`}
                      >
                        <div className="flex items-center gap-2">
                          <div>
                            <div className="font-medium">{player.name}</div>
                            {isSuspended && (
                              <div className="text-xs text-red-600 flex items-center gap-1">
                                <Ban className="h-3 w-3" />
                                Suspenso - {suspensionReason}
                                {matchesRemaining && ` (${matchesRemaining} jogo${matchesRemaining > 1 ? 's' : ''})`}
                              </div>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isSuspended}
                          onClick={() => {
                            if (!isSuspended) {
                              onAddPlayer(player.id);
                              onOpenChange(false);
                            }
                          }}
                          title={isSuspended ? `Jogador suspenso: ${suspensionReason}` : 'Adicionar jogador à convocação'}
                        >
                          {isSuspended ? (
                            <>
                              <Ban className="h-4 w-4 mr-1" />
                              Suspenso
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-1" />
                              Adicionar
                            </>
                          )}
                        </Button>
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="manual-name">Nome do Jogador</Label>
              <Input
                id="manual-name"
                placeholder="Digite o nome do jogador..."
                value={manualName}
                onChange={(e) => setManualName(e.target.value)}
              />
            </div>

            <Button
              onClick={handleAddManualPlayer}
              disabled={!manualName.trim()}
              className="w-full"
            >
              Adicionar Jogador
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

// Componente para adicionar membros da comissão
interface AddStaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  users: ClubUser[];
  collaborators: Collaborator[];
  onAddStaff: (userId: string, role: string) => void;
  onAddManualStaff: (name: string, role: string) => void;
  existingStaff: CallupPlayer[];
}

function AddStaffDialog({
  open,
  onOpenChange,
  users,
  collaborators,
  onAddStaff,
  onAddManualStaff,
  existingStaff
}: AddStaffDialogProps) {
  const [activeTab, setActiveTab] = useState("registered");
  const [manualName, setManualName] = useState("");
  const [manualRole, setManualRole] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("Técnico");

  const staffRoles = [
    "Técnico",
    "Analista de Desempenho",
    "Auxiliar Técnico",
    "Assessor de Imprensa",
    "Comunicação",
    "Preparador de goleiro",
    "Fotógrafo",
    "Preparador Físico",
    "Fisioterapeuta",
    "Nutricionista",
    "Médico",
    "Massagista",
    "Motorista",
    "Segurança",
    "Supervisor",
    "Roupeiro",
    "Diretor",
    "CEO",
    "Fisiologista",
    "Presidente"
  ];

  // Filter collaborators
  const filteredCollaborators = collaborators.filter(collab =>
    collab.full_name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !existingStaff.some(s => s.user_id === collab.id.toString())
  );

  // Convert collaborators to a format compatible with the component
  const collaboratorsAsUsers = filteredCollaborators.map(collab => ({
    id: collab.id.toString(),
    name: collab.full_name,
    role: collab.role,
    isCollaborator: true
  }));

  const handleAddManualStaff = () => {
    if (manualName.trim() && manualRole.trim()) {
      onAddManualStaff(manualName.trim(), manualRole.trim());
      setManualName("");
      setManualRole("");
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Membro da Comissão</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="registered">Membros Cadastrados</TabsTrigger>
            <TabsTrigger value="manual">Adicionar Manualmente</TabsTrigger>
          </TabsList>

          <TabsContent value="registered" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="role">Função</Label>
              <select
                id="role"
                className="w-full p-2 border rounded-md"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                {staffRoles.map((role) => (
                  <option key={role} value={role}>
                    {role}
                  </option>
                ))}
              </select>
            </div>

            <Input
              placeholder="Buscar membro..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            <ScrollArea className="h-[250px]">
              {collaboratorsAsUsers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhum membro encontrado
                </div>
              ) : (
                <div className="space-y-2">
                  {collaboratorsAsUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between border-b pb-2">
                      <div>{user.name}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          onAddStaff(user.id, selectedRole);
                          onOpenChange(false);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="manual-name">Nome</Label>
              <Input
                id="manual-name"
                placeholder="Digite o nome..."
                value={manualName}
                onChange={(e) => setManualName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="manual-role">Função</Label>
              <Input
                id="manual-role"
                placeholder="Digite a função..."
                value={manualRole}
                onChange={(e) => setManualRole(e.target.value)}
                list="role-suggestions"
              />
              <datalist id="role-suggestions">
                {staffRoles.map((role) => (
                  <option key={role} value={role} />
                ))}
              </datalist>
            </div>

            <Button
              onClick={handleAddManualStaff}
              disabled={!manualName.trim() || !manualRole.trim()}
              className="w-full"
            >
              Adicionar Membro
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

interface AddOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddOperation: (member: CallupOperationStaff) => void;
  existing: CallupOperationStaff[];
  collaborators: Collaborator[];
  categories: Category[];
  defaultCategoryId?: number | null;
}

function AddOperationDialog({
  open,
  onOpenChange,
  onAddOperation,
  existing,
  collaborators,
  categories,
  defaultCategoryId
}: AddOperationDialogProps) {
  const clubId = useCurrentClubId();
  const [activeTab, setActiveTab] = useState('registered');
  const [registered, setRegistered] = useState<GameOperationStaff[]>([]);
  const [players, setPlayers] = useState<Player[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [searchRegistered, setSearchRegistered] = useState('');
  const [searchCollaborator, setSearchCollaborator] = useState('');
  const [searchPlayer, setSearchPlayer] = useState('');
  const [manualName, setManualName] = useState('');
  const [manualBirth, setManualBirth] = useState('');
  const [manualCpf, setManualCpf] = useState('');
  const [manualPix, setManualPix] = useState('');
  const [value, setValue] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>(
    defaultCategoryId ? String(defaultCategoryId) : 'all'
  );
  const [selectedRole, setSelectedRole] = useState('');

  useEffect(() => {
    if (!open) return;
    getGameOperationStaff(clubId)
      .then(setRegistered)
      .catch(console.error);
    getOperationRoles(clubId)
      .then(r => setRoles(r.map(x => x.name)))
      .catch(console.error);
    setValue('');
  }, [open, clubId]);

  useEffect(() => {
    const fetchPlayers = async () => {
      if (!open) return;
      try {
        if (categoryFilter === 'all') {
          const all = await getPlayers(clubId);
          setPlayers(all);
        } else {
          const catPlayers = await getCategoryPlayers(clubId, parseInt(categoryFilter));
          setPlayers(catPlayers);
        }
      } catch (err) {
        console.error('Erro ao buscar jogadores:', err);
        setPlayers([]);
      }
    };
    fetchPlayers();
  }, [categoryFilter, open, clubId]);

  const filteredCollabs = registered
    .filter(r =>
      (r.name.toLowerCase().includes(searchRegistered.toLowerCase()) ||
        r.role.toLowerCase().includes(searchRegistered.toLowerCase())) &&
      !existing.some(e => e.staff_id === r.id)
    )
    .sort((a, b) => a.name.localeCompare(b.name, 'pt-BR'));

  const filteredPlayers = players
    .filter(p => p.name.toLowerCase().includes(searchPlayer.toLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name, 'pt-BR'));

  const filteredClubCollabs = collaborators
    .filter(c => c.full_name.toLowerCase().includes(searchCollaborator.toLowerCase()))
    .sort((a, b) => a.full_name.localeCompare(b.full_name, 'pt-BR'));

  const handleAddRegistered = (m: GameOperationStaff) => {
    if (!selectedRole) return;
    onAddOperation({
      id: 0,
      club_id: clubId,
      callup_id: 0,
      staff_id: m.id,
      name: m.name,
      role: selectedRole,
      birth_date: m.birth_date,
      cpf: m.cpf,
      pix_key: m.pix_key || null,
      value: value ? parseFloat(value) : null,
      created_at: new Date().toISOString(),
    });
    onOpenChange(false);
    setValue('');
  };

  const handleAddPlayer = (p: Player) => {
    if (!selectedRole) return;
    onAddOperation({
      id: 0,
      club_id: clubId,
      callup_id: 0,
      name: p.name,
      role: selectedRole,
      birth_date: p.birthdate || null,
      cpf: p.cpf_number || null,
      pix_key: (p as any).bank_pix_key || (p as any).bank_info?.pix || null,
      value: value ? parseFloat(value) : null,
      created_at: new Date().toISOString(),
    });
    onOpenChange(false);
    setValue('');
  };

  const handleAddClubCollaborator = (c: Collaborator) => {
    if (!selectedRole) return;
    onAddOperation({
      id: 0,
      club_id: clubId,
      callup_id: 0,
      name: c.full_name,
      role: selectedRole,
      birth_date: c.birth_date || null,
      cpf: c.cpf || null,
      pix_key: c.bank_info?.pix || null,
      value: value ? parseFloat(value) : null,
      created_at: new Date().toISOString(),
    });
    onOpenChange(false);
    setValue('');
  };

  const handleAddManual = () => {
    if (!manualName || !selectedRole) return;
    onAddOperation({
      id: 0,
      club_id: clubId,
      callup_id: 0,
      name: manualName,
      role: selectedRole,
      birth_date: manualBirth || null,
      cpf: manualCpf || null,
      pix_key: manualPix || null,
      value: value ? parseFloat(value) : null,
      created_at: new Date().toISOString(),
    });
    setManualName('');
    setManualBirth('');
    setManualCpf('');
    setManualPix('');
    setValue('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Membro de Operação</DialogTitle>
        </DialogHeader>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="registered">Cadastrados</TabsTrigger>
            <TabsTrigger value="club">Colaboradores</TabsTrigger>
            <TabsTrigger value="players">Jogadores</TabsTrigger>
            <TabsTrigger value="manual">Manual</TabsTrigger>
          </TabsList>

          <TabsContent value="registered" className="space-y-4">
            <div className="space-y-2">
              <Label>Função</Label>
              <select className="w-full p-2 border rounded-md" value={selectedRole} onChange={e => setSelectedRole(e.target.value)}>
                <option value="">Selecione</option>
                {roles.map(r => (
                  <option key={r} value={r}>{r}</option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label>Valor</Label>
              <Input type="number" value={value} onChange={e => setValue(e.target.value)} />
            </div>
            <Input placeholder="Buscar membro..." value={searchRegistered} onChange={e => setSearchRegistered(e.target.value)} />
            <ScrollArea className="h-[250px]">
              {filteredCollabs.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">Nenhum membro encontrado</div>
              ) : (
                <div className="space-y-2">
                  {filteredCollabs.map(m => (
                    <div key={m.id} className="flex items-center justify-between border-b pb-1">
                      <div className="flex flex-col">
                        <span>{m.name}</span>
                        <span className="text-xs text-muted-foreground">{m.role}</span>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => handleAddRegistered(m)} disabled={!selectedRole}>
                        <Plus className="h-4 w-4 mr-1" />Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="club" className="space-y-4">
            <div className="space-y-2">
              <Label>Função</Label>
              <select className="w-full p-2 border rounded-md" value={selectedRole} onChange={e => setSelectedRole(e.target.value)}>
                <option value="">Selecione</option>
                {roles.map(r => (
                  <option key={r} value={r}>{r}</option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label>Valor</Label>
              <Input type="number" value={value} onChange={e => setValue(e.target.value)} />
            </div>
            <Input placeholder="Buscar colaborador..." value={searchCollaborator} onChange={e => setSearchCollaborator(e.target.value)} />
            <ScrollArea className="h-[250px]">
              {filteredClubCollabs.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">Nenhum colaborador encontrado</div>
              ) : (
                <div className="space-y-2">
                  {filteredClubCollabs.map(c => (
                    <div key={c.id} className="flex items-center justify-between border-b pb-1">
                      <div>{c.full_name}</div>
                      <Button variant="outline" size="sm" onClick={() => handleAddClubCollaborator(c)} disabled={!selectedRole}>
                        <Plus className="h-4 w-4 mr-1" />Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="players" className="space-y-4">
            <div className="space-y-2">
              <Label>Função</Label>
              <select className="w-full p-2 border rounded-md" value={selectedRole} onChange={e => setSelectedRole(e.target.value)}>
                <option value="">Selecione</option>
                {roles.map(r => (
                  <option key={r} value={r}>{r}</option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label>Valor</Label>
              <Input type="number" value={value} onChange={e => setValue(e.target.value)} />
            </div>

            <div className="space-y-2">
              <Label>Categoria</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  {categories.map(c => (
                    <SelectItem key={c.id} value={String(c.id)}>{c.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Input placeholder="Buscar jogador..." value={searchPlayer} onChange={e => setSearchPlayer(e.target.value)} />

            <ScrollArea className="h-[250px]">
              {filteredPlayers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">Nenhum jogador encontrado</div>
              ) : (
                <div className="space-y-2">
                  {filteredPlayers.map(p => (
                    <div key={p.id} className="flex items-center justify-between border-b pb-1">
                      <div>{p.name}</div>
                      <Button variant="outline" size="sm" onClick={() => handleAddPlayer(p)} disabled={!selectedRole}>
                        <Plus className="h-4 w-4 mr-1" />Adicionar
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label>Função</Label>
              <select className="w-full p-2 border rounded-md" value={selectedRole} onChange={e => setSelectedRole(e.target.value)}>
                <option value="">Selecione</option>
                {roles.map(r => (
                  <option key={r} value={r}>{r}</option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label>Nome</Label>
              <Input value={manualName} onChange={e => setManualName(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Data de Nascimento</Label>
              <Input type="date" value={manualBirth} onChange={e => setManualBirth(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>CPF</Label>
              <Input value={manualCpf} onChange={e => setManualCpf(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Chave PIX / Conta</Label>
              <Input value={manualPix} onChange={e => setManualPix(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Valor</Label>
              <Input type="number" value={value} onChange={e => setValue(e.target.value)} />
            </div>
            <Button className="w-full" disabled={!manualName || !selectedRole} onClick={handleAddManual}>
              Adicionar
            </Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}