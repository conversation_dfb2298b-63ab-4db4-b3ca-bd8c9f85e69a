import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";

export interface Client {
  id: number;
  club_id: number;
  name: string;
  email?: string;
  phone?: string;
  pix_key?: string;
  created_at: string;
  updated_at: string;
}

export interface BillingTransaction {
  id: number;
  club_id: number;
  type: 'recebe' | 'paga';
  entity_type: 'player' | 'collaborator' | 'client';
  entity_id?: number;
  entity_name: string;
  pix_key: string;
  amount: number;
  description: string;
  status: 'pendente' | 'pago';
  due_date?: string;
  paid_at?: string;
  qr_code_data?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface BillingTransactionItem {
  id: number;
  club_id: number;
  transaction_id: number;
  product_id: number;
  quantity: number;
  unit_price?: number;
  total_price?: number;
  created_at: string;
  updated_at: string;
  // Campos adicionais para joins
  product_name?: string;
  product_department?: string;
  available_quantity?: number;
}

export interface CreateClientData {
  name: string;
  email?: string;
  phone?: string;
  pix_key?: string;
}

export interface CreateBillingTransactionData {
  type: 'recebe' | 'paga';
  entity_type: 'player' | 'collaborator' | 'client';
  entity_id?: number;
  entity_name: string;
  pix_key: string;
  amount: number;
  description: string;
  due_date?: string;
  qr_code_data?: string;
  items?: CreateBillingTransactionItemData[];
}

export interface CreateBillingTransactionItemData {
  product_id: number;
  quantity: number;
  unit_price?: number;
}

// Clientes
export const getClients = async (clubId: number): Promise<Client[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('clients')
    .select('*')
    .eq('club_id', clubId)
    .order('name');

  if (error) throw error;
  return data || [];
};

export const createClient = async (clubId: number, clientData: CreateClientData): Promise<Client> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('clients')
    .insert({
      club_id: clubId,
      ...clientData
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const updateClient = async (clubId: number, id: number, clientData: Partial<CreateClientData>): Promise<Client> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('clients')
    .update(clientData)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const deleteClient = async (clubId: number, id: number): Promise<void> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { error } = await supabaseWithClubId
    .from('clients')
    .delete()
    .eq('id', id);

  if (error) throw error;
};

// Transações de cobrança
export const getBillingTransactions = async (
  clubId: number,
  filters?: {
    status?: 'pendente' | 'pago';
    entity_type?: 'player' | 'collaborator' | 'client';
    start_date?: string;
    end_date?: string;
  }
): Promise<BillingTransaction[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  let query = supabaseWithClubId
    .from('billing_transactions')
    .select('*')
    .eq('club_id', clubId);

  if (filters?.status) {
    query = query.eq('status', filters.status);
  }

  if (filters?.entity_type) {
    query = query.eq('entity_type', filters.entity_type);
  }

  if (filters?.start_date) {
    query = query.gte('created_at', filters.start_date);
  }

  if (filters?.end_date) {
    query = query.lte('created_at', filters.end_date);
  }

  const { data, error } = await query.order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const createBillingTransaction = async (
  clubId: number,
  transactionData: CreateBillingTransactionData
): Promise<BillingTransaction> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  
  // Separar os itens dos dados da transação
  const { items, ...transactionDataWithoutItems } = transactionData;
  
  // Criar a transação
  const { data: transaction, error: transactionError } = await supabaseWithClubId
    .from('billing_transactions')
    .insert({
      club_id: clubId,
      ...transactionDataWithoutItems
    })
    .select()
    .single();

  if (transactionError) throw transactionError;

  // Se há itens, criar os itens da transação
  if (items && items.length > 0) {
    const itemsToInsert = items.map(item => ({
      club_id: clubId,
      transaction_id: transaction.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.unit_price || 0,
      total_price: (item.unit_price || 0) * item.quantity
    }));

    const { error: itemsError } = await supabaseWithClubId
      .from('billing_transaction_items')
      .insert(itemsToInsert);

    if (itemsError) {
      // Se falhar ao criar itens, excluir a transação criada
      await supabaseWithClubId
        .from('billing_transactions')
        .delete()
        .eq('id', transaction.id);
      throw itemsError;
    }
  }

  return transaction;
};

export const updateBillingTransaction = async (
  clubId: number,
  id: number,
  updates: Partial<BillingTransaction>
): Promise<BillingTransaction> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('billing_transactions')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const markBillingTransactionAsPaid = async (clubId: number, id: number): Promise<BillingTransaction> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  
  // Primeiro, buscar a transação para obter os dados
  const { data: transaction, error: fetchError } = await supabaseWithClubId
    .from('billing_transactions')
    .select('*')
    .eq('id', id)
    .single();

  if (fetchError) throw fetchError;

  // Marcar como pago
  const { data, error } = await supabaseWithClubId
    .from('billing_transactions')
    .update({
      status: 'pago',
      paid_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;

  // Criar transação financeira correspondente
  try {
    const financialTransactionData = {
      club_id: clubId,
      date: new Date().toISOString().split('T')[0], // Data atual
      type: transaction.type === 'recebe' ? 'receita' : 'despesa',
      category: transaction.type === 'recebe' ? 'Cobranças' : 'Pagamentos',
      amount: transaction.amount,
      description: `${transaction.type === 'recebe' ? 'Cobrança recebida' : 'Pagamento efetuado'} - ${transaction.entity_name}: ${transaction.description}`,
      payment_status: 'paid',
      // Adicionar referência à entidade se for jogador ou colaborador
      ...(transaction.entity_type === 'player' && transaction.entity_id ? { player_id: transaction.entity_id } : {}),
      ...(transaction.entity_type === 'collaborator' && transaction.entity_id ? { collaborator_id: transaction.entity_id } : {})
    };

    const { error: financialError } = await supabaseWithClubId
      .from('financial_transactions')
      .insert(financialTransactionData);

    if (financialError) {
      console.error('Erro ao criar transação financeira:', financialError);
      // Não falhar a operação principal, mas log o erro
    }
  } catch (financialError) {
    console.error('Erro ao criar transação financeira:', financialError);
  }

  // Processar os itens do estoque (se houver)
  try {
    const { data: user } = await supabaseWithClubId.auth.getUser();
    if (user?.user?.id) {
      await supabaseWithClubId.rpc('process_billing_transaction_inventory', {
        p_club_id: clubId,
        p_transaction_id: id,
        p_user_id: user.user.id
      });
    }
  } catch (inventoryError) {
    console.error('Erro ao processar itens do estoque:', inventoryError);
    // Não falhar a operação se o processamento do estoque falhar
    // mas log o erro para investigação
  }

  return data;
};

export const deleteBillingTransaction = async (clubId: number, id: number): Promise<void> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  
  // Primeiro, buscar a transação para verificar se ela foi paga (e portanto tem transação financeira)
  const { data: transaction, error: fetchError } = await supabaseWithClubId
    .from('billing_transactions')
    .select('*')
    .eq('id', id)
    .single();

  if (fetchError) throw fetchError;

  // Se a transação foi paga, tentar encontrar e excluir a transação financeira correspondente
  if (transaction.status === 'pago') {
    try {
      // Buscar transação financeira correspondente pela descrição (que contém o nome da entidade)
      const { data: financialTransactions, error: financialFetchError } = await supabaseWithClubId
        .from('financial_transactions')
        .select('id')
        .eq('club_id', clubId)
        .ilike('description', `%${transaction.entity_name}%`)
        .ilike('description', `%${transaction.description}%`)
        .eq('amount', transaction.amount);

      if (!financialFetchError && financialTransactions && financialTransactions.length > 0) {
        // Excluir a transação financeira correspondente
        const { error: deleteFinancialError } = await supabaseWithClubId
          .from('financial_transactions')
          .delete()
          .eq('id', financialTransactions[0].id);

        if (deleteFinancialError) {
          console.error('Erro ao excluir transação financeira correspondente:', deleteFinancialError);
        }
      }
    } catch (financialError) {
      console.error('Erro ao buscar/excluir transação financeira:', financialError);
      // Não falhar a operação principal
    }
  }

  // Excluir a transação de cobrança
  const { error } = await supabaseWithClubId
    .from('billing_transactions')
    .delete()
    .eq('id', id);

  if (error) throw error;
};

// Buscar jogadores e colaboradores para seleção
export const getPlayersAndCollaborators = async (clubId: number) => {
  console.log('Buscando jogadores e colaboradores para clube:', clubId);

  // Buscar jogadores - removendo filtro de status para debug
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data: players, error: playersError } = await supabaseWithClubId
    .from('players')
    .select('id, name, bank_pix_key, status')
    .eq('club_id', clubId)
    .order('name');

  console.log('Resultado jogadores:', { players, playersError });

  if (playersError) throw playersError;

  // Buscar colaboradores
  const { data: collaborators, error: collaboratorsError } = await supabaseWithClubId
    .from('collaborators')
    .select('id, full_name, bank_info')
    .eq('club_id', clubId)
    .order('full_name');

  console.log('Resultado colaboradores:', { collaborators, collaboratorsError });

  if (collaboratorsError) throw collaboratorsError;

  // Processar colaboradores para extrair chave PIX do JSON
  const processedCollaborators = (collaborators || []).map(collab => {
    console.log('Processando colaborador:', collab.full_name, 'bank_info:', collab.bank_info);
    return {
      id: collab.id,
      name: collab.full_name,
      pix_key: (collab.bank_info as any)?.pix || null
    };
  });

  // Processar jogadores para usar o campo correto
  const processedPlayers = (players || []).map(player => {
    console.log('Processando jogador:', player.name, 'bank_pix_key:', player.bank_pix_key);
    return {
      id: player.id,
      name: player.name,
      pix_key: player.bank_pix_key || null
    };
  });

  console.log('Resultado final:', { 
    players: processedPlayers, 
    collaborators: processedCollaborators 
  });

  return {
    players: processedPlayers,
    collaborators: processedCollaborators
  };
};

// Funções para gerenciar itens das transações de cobrança
export const getBillingTransactionItems = async (
  clubId: number,
  transactionId: number
): Promise<BillingTransactionItem[]> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('billing_transaction_items')
    .select(`
      *,
      inventory_products:product_id (
        id,
        name,
        department,
        quantity
      )
    `)
    .eq('club_id', clubId)
    .eq('transaction_id', transactionId)
    .order('created_at');

  if (error) throw error;

  // Formatar os dados para incluir informações do produto
  return (data || []).map(item => ({
    ...item,
    product_name: item.inventory_products?.name || 'Produto não encontrado',
    product_department: item.inventory_products?.department || 'Departamento não encontrado',
    available_quantity: item.inventory_products?.quantity || 0
  }));
};

export const addBillingTransactionItem = async (
  clubId: number,
  transactionId: number,
  itemData: CreateBillingTransactionItemData
): Promise<BillingTransactionItem> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  
  // Verificar se o produto existe e tem estoque suficiente
  const { data: product, error: productError } = await supabaseWithClubId
    .from('inventory_products')
    .select('id, name, quantity')
    .eq('club_id', clubId)
    .eq('id', itemData.product_id)
    .single();

  if (productError) throw new Error('Produto não encontrado');
  
  if (product.quantity < itemData.quantity) {
    throw new Error(`Estoque insuficiente. Disponível: ${product.quantity}, Solicitado: ${itemData.quantity}`);
  }

  const { data, error } = await supabaseWithClubId
    .from('billing_transaction_items')
    .insert({
      club_id: clubId,
      transaction_id: transactionId,
      product_id: itemData.product_id,
      quantity: itemData.quantity,
      unit_price: itemData.unit_price || 0,
      total_price: (itemData.unit_price || 0) * itemData.quantity
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const updateBillingTransactionItem = async (
  clubId: number,
  itemId: number,
  itemData: Partial<CreateBillingTransactionItemData>
): Promise<BillingTransactionItem> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  
  // Se está atualizando quantidade, verificar estoque
  if (itemData.quantity) {
    const { data: currentItem, error: currentError } = await supabaseWithClubId
      .from('billing_transaction_items')
      .select('product_id')
      .eq('id', itemId)
      .single();

    if (currentError) throw currentError;

    const { data: product, error: productError } = await supabaseWithClubId
      .from('inventory_products')
      .select('quantity')
      .eq('club_id', clubId)
      .eq('id', currentItem.product_id)
      .single();

    if (productError) throw new Error('Produto não encontrado');
    
    if (product.quantity < itemData.quantity) {
      throw new Error(`Estoque insuficiente. Disponível: ${product.quantity}, Solicitado: ${itemData.quantity}`);
    }
  }

  const updateData: any = { ...itemData };
  if (itemData.quantity && itemData.unit_price) {
    updateData.total_price = itemData.quantity * itemData.unit_price;
  }

  const { data, error } = await supabaseWithClubId
    .from('billing_transaction_items')
    .update(updateData)
    .eq('id', itemId)
    .eq('club_id', clubId)
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const deleteBillingTransactionItem = async (
  clubId: number,
  itemId: number
): Promise<void> => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { error } = await supabaseWithClubId
    .from('billing_transaction_items')
    .delete()
    .eq('id', itemId)
    .eq('club_id', clubId);

  if (error) throw error;
};

// Buscar produtos do estoque para seleção
export const getInventoryProductsForBilling = async (clubId: number) => {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
  const { data, error } = await supabaseWithClubId
    .from('inventory_products')
    .select('id, name, quantity, department, unit_of_measure, sale_price')
    .eq('club_id', clubId)
    .eq('department', 'A Venda') // Apenas produtos do departamento "A Venda"
    .gt('quantity', 0) // Apenas produtos com estoque
    .not('sale_price', 'is', null) // Apenas produtos com preço definido
    .gt('sale_price', 0) // Apenas produtos com preço maior que zero
    .order('name');

  if (error) throw error;
  return data || [];
};