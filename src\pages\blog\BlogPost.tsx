import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Calendar, Clock, User, ArrowLeft, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { BlogSEO } from '@/components/blog/BlogSEO';
import { BlogFAQ } from '@/components/blog/BlogFAQ';
import { BlogCTA } from '@/components/blog/BlogCTA';
import { BlogTableOfContents } from '@/components/blog/BlogTableOfContents';
import { BlogBreadcrumbs } from '@/components/blog/BlogBreadcrumbs';
import { ReadingProgress } from '@/components/blog/ReadingProgress';
import { ShareButtons } from '@/components/blog/ShareButtons';
import { <PERSON><PERSON><PERSON>enderer } from '@/components/blog/MarkdownRenderer';
import { getBlogPostBySlug, getRelatedPosts, BlogPost } from '@/data/blog-posts';

export default function BlogPost() {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (slug) {
      const foundPost = getBlogPostBySlug(slug);
      setPost(foundPost || null);
    }
    setLoading(false);
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando post...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Post não encontrado</h1>
          <p className="text-gray-600 mb-6">O post que você está procurando não existe ou foi removido.</p>
          <Link to="/blog">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar ao Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const relatedPosts = getRelatedPosts(post.slug, post.relatedPosts);

  return (
    <>
      <ReadingProgress />
      <BlogSEO
        title={post.title}
        description={post.description}
        canonical={`/blog/${post.slug}`}
        type="article"
        image={post.image}
        publishedAt={post.publishedAt}
        modifiedAt={post.modifiedAt}
        author={post.author}
        tags={post.tags}
        readTime={post.readTime}
        wordCount={post.wordCount}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Simple Header */}
        <header className="bg-white shadow-sm border-b sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/" className="flex items-center space-x-2">
                <img src="/logo-branca.png" alt="Game Day Nexus Logo" className="h-10 w-auto" />
                <div className="hidden sm:flex flex-col">
                  <span className="font-bold text-xl text-gray-900">Game Day Nexus</span>
                  <span className="text-xs text-blue-600 font-medium">Gestão Esportiva</span>
                </div>
              </Link>

              <div className="flex items-center space-x-4">
                <Link to="/blog" className="text-gray-600 hover:text-blue-600 font-medium">
                  Blog
                </Link>
                <Link to="/" className="text-gray-600 hover:text-blue-600 font-medium">
                  Home
                </Link>
                <Link to="/login">
                  <Button variant="outline" size="sm">Entrar</Button>
                </Link>
                <Link to="/trial">
                  <Button size="sm">Teste Grátis</Button>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <BlogBreadcrumbs 
              items={[
                { label: 'Blog', href: '/blog' },
                { label: post.category },
                { label: post.title }
              ]}
              className="mb-6"
            />
            
            <Link to="/blog" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar ao Blog
            </Link>

            <div className="mb-6">
              <div className="flex items-center gap-3 mb-4">
                <Badge variant="secondary">{post.category}</Badge>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-1" />
                  {post.readTime} min de leitura
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  {new Date(post.publishedAt).toLocaleDateString('pt-BR')}
                </div>
              </div>

              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {post.title}
              </h1>

              <p className="text-xl text-gray-600 mb-6">
                {post.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm text-gray-500">
                  <User className="h-4 w-4 mr-1" />
                  {post.author}
                </div>
                <ShareButtons 
                  url={`/blog/${post.slug}`}
                  title={post.title}
                  description={post.description}
                />
              </div>
            </div>

            {post.image && (
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-64 md:h-96 object-cover rounded-lg"
              />
            )}
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Table of Contents - Hidden on mobile */}
            <div className="hidden lg:block lg:col-span-1">
              <div className="sticky top-8">
                <BlogTableOfContents content={post.content} />
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 col-span-full">
              {/* Lead Magnet */}
              {post.leadMagnet && (
                <div className="mb-8 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg transform rotate-1"></div>
                  <Card className="relative bg-white border-2 border-blue-200 shadow-lg">
                    <CardHeader className="text-center">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white mb-4 mx-auto">
                        <Download className="h-8 w-8" />
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        🎁 {post.leadMagnet.title}
                      </CardTitle>
                      <CardDescription className="text-gray-600 text-base">
                        {post.leadMagnet.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="text-center">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                        <p className="text-green-800 font-medium text-sm">
                          ✅ 100% Gratuito • ✅ Download Imediato • ✅ Sem Cadastro
                        </p>
                      </div>
                      <Button size="lg" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3">
                        <Download className="h-5 w-5 mr-2" />
                        Baixar Grátis Agora
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Article Content */}
              <MarkdownRenderer content={post.content} />

              <Separator className="my-8" />

              {/* Tags */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-3">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* FAQ */}
              {post.faq && post.faq.length > 0 && (
                <BlogFAQ faqs={post.faq} />
              )}

              {/* CTA */}
              <BlogCTA
                title="Pronto para profissionalizar seu clube?"
                description="Teste todas as funcionalidades do Game Day Nexus gratuitamente por 14 dias."
                primaryAction={{
                  text: "Começar Teste Grátis",
                  href: "/trial"
                }}
                secondaryAction={{
                  text: "Agendar Demonstração",
                  href: "/demo"
                }}
              />

              {/* Related Posts */}
              {relatedPosts.length > 0 && (
                <div className="mt-12">
                  <h3 className="text-2xl font-bold mb-6">Leia também</h3>
                  <div className="grid md:grid-cols-2 gap-6">
                    {relatedPosts.map((relatedPost) => (
                      <Card key={relatedPost.id}>
                        <CardHeader>
                          <CardTitle className="text-lg">
                            <Link to={`/blog/${relatedPost.slug}`} className="hover:text-blue-600">
                              {relatedPost.title}
                            </Link>
                          </CardTitle>
                          <CardDescription>
                            {relatedPost.description}
                          </CardDescription>
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}