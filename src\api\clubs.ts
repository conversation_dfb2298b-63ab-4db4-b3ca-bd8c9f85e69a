import { supabase } from "@/integrations/supabase/client";
import { requireSession } from "./session";
import { validateUserClubAccess } from "./clubAccess";

export interface ClubInfo {
  id: number;
  name: string;
  slug: string | null;
  logo_url: string | null;
  emblem_url: string | null;
  primary_color: string | null;
  secondary_color: string | null;
  president: string | null;
  founded_year: string | null;
  stadium: string | null;
  address: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  notes: string | null;
  created_by: string | null;
  updated_at: string | null;
}

/**
 * Busca um clube pelo slug
 */
export async function getClubBySlug(slug: string): Promise<ClubInfo | null> {
  const { data, error } = await supabase
    .from("club_info")
    .select("*")
    .eq("slug", slug)
    .single();

  if (error) {
    console.error("Erro ao buscar clube por slug:", error);
    return null;
  }

  return data as ClubInfo;
}

/**
 * Verifica se um slug está disponível
 */
export async function isSlugAvailable(slug: string, excludeClubId?: number): Promise<boolean> {
  let query = supabase
    .from("club_info")
    .select("id")
    .eq("slug", slug);

  if (excludeClubId) {
    query = query.neq("id", excludeClubId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao verificar disponibilidade do slug:", error);
    return false;
  }

  return !data || data.length === 0;
}

/**
 * Atualiza o slug de um clube
 */
export async function updateClubSlug(clubId: number, slug: string): Promise<boolean> {
  await requireSession();

  // VALIDAÇÃO DE SEGURANÇA: Verificar se o usuário tem acesso ao clube
  const hasAccess = await validateUserClubAccess(clubId);
  if (!hasAccess) {
    console.error(`TENTATIVA DE ALTERAÇÃO NÃO AUTORIZADA: Usuário tentou alterar slug do clube ${clubId}`);
    throw new Error("Acesso negado: Você não tem permissão para alterar este clube");
  }

  // Verificar se o slug está disponível
  const available = await isSlugAvailable(slug, clubId);
  if (!available) {
    throw new Error("Este slug já está em uso por outro clube");
  }

  const { error } = await supabase
    .from("club_info")
    .update({ slug })
    .eq("id", clubId);

  if (error) {
    console.error("Erro ao atualizar slug do clube:", error);
    throw new Error(`Erro ao atualizar slug: ${error.message}`);
  }

  return true;
}

/**
 * Gera um slug baseado no nome do clube
 */
export function generateSlugFromName(name: string): string {
  // Mapa de caracteres acentuados para não acentuados
  const accentMap: { [key: string]: string } = {
    'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
    'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
    'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
    'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
    'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
    'ç': 'c', 'ñ': 'n',
    'Á': 'A', 'À': 'A', 'Ã': 'A', 'Â': 'A', 'Ä': 'A',
    'É': 'E', 'È': 'E', 'Ê': 'E', 'Ë': 'E',
    'Í': 'I', 'Ì': 'I', 'Î': 'I', 'Ï': 'I',
    'Ó': 'O', 'Ò': 'O', 'Õ': 'O', 'Ô': 'O', 'Ö': 'O',
    'Ú': 'U', 'Ù': 'U', 'Û': 'U', 'Ü': 'U',
    'Ç': 'C', 'Ñ': 'N'
  };

  return name
    .toLowerCase()
    // Remover acentos usando o mapa
    .replace(/[áàãâäéèêëíìîïóòõôöúùûüçñÁÀÃÂÄÉÈÊËÍÌÎÏÓÒÕÔÖÚÙÛÜÇÑ]/g, (match) => accentMap[match] || match)
    // Remove caracteres especiais, mantém apenas letras, números, espaços e hífens
    .replace(/[^a-z0-9\s-]/g, '')
    // Substitui múltiplos espaços por um hífen
    .replace(/\s+/g, '-')
    // Remove hífens consecutivos
    .replace(/-+/g, '-')
    // Remove hífens do início e fim
    .replace(/^-+|-+$/g, '')
    .trim();
}

/**
 * Valida se um slug tem formato válido
 */
export function validateSlug(slug: string): { valid: boolean; error?: string } {
  if (!slug || slug.length < 2) {
    return { valid: false, error: "Slug deve ter pelo menos 2 caracteres" };
  }

  if (slug.length > 50) {
    return { valid: false, error: "Slug deve ter no máximo 50 caracteres" };
  }

  if (!/^[a-z0-9-]+$/.test(slug)) {
    return { valid: false, error: "Slug deve conter apenas letras minúsculas, números e hífens" };
  }

  if (slug.startsWith('-') || slug.endsWith('-')) {
    return { valid: false, error: "Slug não pode começar ou terminar com hífen" };
  }

  if (slug.includes('--')) {
    return { valid: false, error: "Slug não pode conter hífens consecutivos" };
  }

  // Palavras reservadas
  const reservedWords = [
    'admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store', 
    'app', 'mobile', 'web', 'site', 'portal', 'dashboard', 'login',
    'register', 'auth', 'oauth', 'master', 'root', 'system'
  ];

  if (reservedWords.includes(slug)) {
    return { valid: false, error: "Este slug é uma palavra reservada" };
  }

  return { valid: true };
}