import type { ChatRoom } from '@/types/chat';

/**
 * Return a human readable name for a chat room.
 * Falls back to the room name when no friendly name is available.
 */
export function getRoomDisplayName(room: ChatRoom, currentUserName?: string): string {
  if (room.display_name) return room.display_name;
  if (room.is_general) return room.name;

  if (room.description?.startsWith('Chat entre ')) {
    const names = room.description.replace('Chat entre ', '').split(' e ');
    if (names.length === 2 && currentUserName) {
      const lower = currentUserName.toLowerCase();
      const first = names[0];
      const second = names[1];
      if (first.toLowerCase() === lower) return second;
      if (second.toLowerCase() === lower) return first;
    }
    return names.join(' e ');
  }

  return room.name;
}