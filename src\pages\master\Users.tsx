import React, { useState, useEffect } from 'react';
import {
    Users as UsersIcon,
    Plus,
    Search,
    Filter,
    MoreHorizontal,
    Edit,
    Trash2,
    Shield,
    ShieldCheck,
    ShieldAlert,
    Eye,
    UserCheck,
    UserX,
    Mail,
    Calendar,
    Activity,
    Key
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
    getMasterUsers,
    createMasterUser,
    updateMasterUser,
    deleteMasterUser,
    toggleMasterUserStatus,
    resetMasterUserPassword,
    generateTemporaryPassword,
    type MasterUser,
    type CreateMasterUserData,
    type UpdateMasterUserData
} from '@/api/masterUsers';

interface MasterUser {
    id: string;
    name: string;
    email: string;
    role: 'super_admin' | 'admin' | 'support' | 'viewer';
    permissions: Record<string, any>;
    is_active: boolean;
    last_login_at?: string;
    created_at: string;
    updated_at: string;
    organization_id?: number;
}

interface UserFormData {
    name: string;
    email: string;
    password: string;
    role: string;
    is_active: boolean;
    permissions: {
        clubs: { view: boolean; manage: boolean; create: boolean; delete: boolean };
        plans: { view: boolean; manage: boolean; create: boolean; delete: boolean };
        payments: { view: boolean; manage: boolean; create: boolean; delete: boolean };
        users: { view: boolean; manage: boolean; create: boolean; delete: boolean };
        reports: { view: boolean; generate: boolean; export: boolean };
        analytics: { view: boolean; export: boolean };
        audit: { view: boolean; export: boolean };
        settings: { view: boolean; manage: boolean };
    };
}

export const Users: React.FC = () => {
    const [users, setUsers] = useState<MasterUser[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState<string>('all');
    const [statusFilter, setStatusFilter] = useState<string>('all');
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<MasterUser | null>(null);
    const [formData, setFormData] = useState<UserFormData>({
        name: '',
        email: '',
        password: '',
        role: 'viewer',
        is_active: true,
        permissions: {
            clubs: { view: false, manage: false, create: false, delete: false },
            plans: { view: false, manage: false, create: false, delete: false },
            payments: { view: false, manage: false, create: false, delete: false },
            users: { view: false, manage: false, create: false, delete: false },
            reports: { view: false, generate: false, export: false },
            analytics: { view: false, export: false },
            audit: { view: false, export: false },
            settings: { view: false, manage: false }
        }
    });

    useEffect(() => {
        loadUsers();
    }, []);

    const loadUsers = async () => {
        try {
            setLoading(true);
            const data = await getMasterUsers();
            setUsers(data);
        } catch (error: any) {
            console.error('Erro ao carregar usuários:', error);
            toast.error('Erro ao carregar usuários master');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateUser = async () => {
        try {
            if (!formData.name || !formData.email || !formData.password) {
                toast.error('Preencha todos os campos obrigatórios');
                return;
            }

            const userData: CreateMasterUserData = {
                name: formData.name,
                email: formData.email,
                password: formData.password,
                role: formData.role as any,
                permissions: formData.permissions,
                is_active: formData.is_active
            };

            await createMasterUser(userData);
            toast.success('Usuário criado com sucesso!');
            setIsCreateModalOpen(false);
            resetForm();
            loadUsers();
        } catch (error: any) {
            console.error('Erro ao criar usuário:', error);
            toast.error(error.message || 'Erro ao criar usuário');
        }
    };

    const handleUpdateUser = async () => {
        if (!selectedUser) return;

        try {
            const userData: UpdateMasterUserData = {
                name: formData.name,
                role: formData.role as any,
                is_active: formData.is_active,
                permissions: formData.permissions
            };

            await updateMasterUser(selectedUser.id, userData);
            toast.success('Usuário atualizado com sucesso!');
            setIsEditModalOpen(false);
            setSelectedUser(null);
            resetForm();
            loadUsers();
        } catch (error: any) {
            console.error('Erro ao atualizar usuário:', error);
            toast.error(error.message || 'Erro ao atualizar usuário');
        }
    };

    const handleDeleteUser = async (userId: string) => {
        if (!confirm('Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita.')) return;

        try {
            await deleteMasterUser(userId);
            toast.success('Usuário excluído com sucesso!');
            loadUsers();
        } catch (error: any) {
            console.error('Erro ao excluir usuário:', error);
            toast.error(error.message || 'Erro ao excluir usuário');
        }
    };

    const handleToggleStatus = async (userId: string, currentStatus: boolean) => {
        try {
            await toggleMasterUserStatus(userId, !currentStatus);
            toast.success(`Usuário ${!currentStatus ? 'ativado' : 'desativado'} com sucesso!`);
            loadUsers();
        } catch (error: any) {
            console.error('Erro ao alterar status:', error);
            toast.error(error.message || 'Erro ao alterar status do usuário');
        }
    };

    const handleResetPassword = async (userId: string, userName: string) => {
        if (!confirm(`Redefinir senha do usuário ${userName}? Uma nova senha temporária será gerada.`)) return;

        try {
            const newPassword = generateTemporaryPassword();
            await resetMasterUserPassword(userId, newPassword);
            
            // Show the new password to the admin
            alert(`Nova senha temporária para ${userName}:\n\n${newPassword}\n\nAnote esta senha, ela não será mostrada novamente.`);
            
            toast.success('Senha redefinida com sucesso!');
        } catch (error: any) {
            console.error('Erro ao redefinir senha:', error);
            toast.error(error.message || 'Erro ao redefinir senha');
        }
    };

    const openEditModal = (user: MasterUser) => {
        setSelectedUser(user);
        setFormData({
            name: user.name,
            email: user.email,
            password: '', // Don't show existing password
            role: user.role,
            is_active: user.is_active,
            permissions: user.permissions as any || {
                clubs: { view: false, manage: false, create: false, delete: false },
                plans: { view: false, manage: false, create: false, delete: false },
                payments: { view: false, manage: false, create: false, delete: false },
                users: { view: false, manage: false, create: false, delete: false },
                reports: { view: false, generate: false, export: false },
                analytics: { view: false, export: false },
                audit: { view: false, export: false },
                settings: { view: false, manage: false }
            }
        });
        setIsEditModalOpen(true);
    };

    const resetForm = () => {
        setFormData({
            name: '',
            email: '',
            password: '',
            role: 'viewer',
            is_active: true,
            permissions: {
                clubs: { view: false, manage: false, create: false, delete: false },
                plans: { view: false, manage: false, create: false, delete: false },
                payments: { view: false, manage: false, create: false, delete: false },
                users: { view: false, manage: false, create: false, delete: false },
                reports: { view: false, generate: false, export: false },
                analytics: { view: false, export: false },
                audit: { view: false, export: false },
                settings: { view: false, manage: false }
            }
        });
    };

    const getRoleIcon = (role: string) => {
        switch (role) {
            case 'super_admin':
                return <ShieldCheck className="w-4 h-4 text-red-600" />;
            case 'admin':
                return <Shield className="w-4 h-4 text-blue-600" />;
            case 'support':
                return <ShieldAlert className="w-4 h-4 text-yellow-600" />;
            case 'viewer':
                return <Eye className="w-4 h-4 text-gray-600" />;
            default:
                return <Shield className="w-4 h-4 text-gray-600" />;
        }
    };

    const getRoleBadge = (role: string) => {
        const variants = {
            super_admin: { variant: 'destructive' as const, label: 'Super Admin' },
            admin: { variant: 'default' as const, label: 'Admin' },
            support: { variant: 'secondary' as const, label: 'Suporte' },
            viewer: { variant: 'outline' as const, label: 'Visualizador' }
        };

        const config = variants[role as keyof typeof variants] || variants.viewer;
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const getStatusBadge = (isActive: boolean) => {
        return (
            <Badge variant={isActive ? 'default' : 'secondary'}>
                {isActive ? 'Ativo' : 'Inativo'}
            </Badge>
        );
    };

    const updatePermission = (module: string, permission: string, value: boolean) => {
        setFormData(prev => ({
            ...prev,
            permissions: {
                ...prev.permissions,
                [module]: {
                    ...prev.permissions[module as keyof typeof prev.permissions],
                    [permission]: value
                }
            }
        }));
    };

    const filteredUsers = users.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesRole = roleFilter === 'all' || user.role === roleFilter;
        const matchesStatus = statusFilter === 'all' ||
            (statusFilter === 'active' && user.is_active) ||
            (statusFilter === 'inactive' && !user.is_active);

        return matchesSearch && matchesRole && matchesStatus;
    });

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Carregando usuários...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Usuários Master</h1>
                    <p className="text-gray-600 mt-1">
                        Gerencie usuários com acesso ao painel master
                    </p>
                </div>
                <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                    <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                            <Plus className="w-4 h-4" />
                            Novo Usuário
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>Criar Novo Usuário Master</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Nome *</Label>
                                    <Input
                                        id="name"
                                        value={formData.name}
                                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                        placeholder="Nome completo"
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="email">Email *</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                        placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="password">Senha *</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={formData.password}
                                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                                    placeholder="Senha (mínimo 6 caracteres)"
                                    required
                                    minLength={6}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    A senha deve ter pelo menos 6 caracteres
                                </p>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="role">Função</Label>
                                    <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="viewer">Visualizador</SelectItem>
                                            <SelectItem value="support">Suporte</SelectItem>
                                            <SelectItem value="admin">Administrador</SelectItem>
                                            <SelectItem value="super_admin">Super Admin</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="flex items-center space-x-2 pt-6">
                                    <Switch
                                        id="is_active"
                                        checked={formData.is_active}
                                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                                    />
                                    <Label htmlFor="is_active">Usuário ativo</Label>
                                </div>
                            </div>

                            {/* Permissões */}
                            <div>
                                <Label>Permissões</Label>
                                <div className="mt-2 space-y-4 border rounded-lg p-4">
                                    {Object.entries(formData.permissions).map(([module, perms]) => (
                                        <div key={module} className="space-y-2">
                                            <h4 className="font-medium capitalize">{module}</h4>
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                {Object.entries(perms).map(([perm, value]) => (
                                                    <div key={perm} className="flex items-center space-x-2">
                                                        <Switch
                                                            id={`${module}-${perm}`}
                                                            checked={value as boolean}
                                                            onCheckedChange={(checked) => updatePermission(module, perm, checked)}
                                                        />
                                                        <Label htmlFor={`${module}-${perm}`} className="text-sm capitalize">
                                                            {perm}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                                    Cancelar
                                </Button>
                                <Button onClick={handleCreateUser}>
                                    Criar Usuário
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Estatísticas */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total de Usuários</p>
                                <p className="text-2xl font-bold">{users.length}</p>
                            </div>
                            <UsersIcon className="w-8 h-8 text-blue-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Usuários Ativos</p>
                                <p className="text-2xl font-bold">{users.filter(u => u.is_active).length}</p>
                            </div>
                            <UserCheck className="w-8 h-8 text-green-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Administradores</p>
                                <p className="text-2xl font-bold">{users.filter(u => u.role === 'admin' || u.role === 'super_admin').length}</p>
                            </div>
                            <Shield className="w-8 h-8 text-purple-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Usuários Inativos</p>
                                <p className="text-2xl font-bold">{users.filter(u => !u.is_active).length}</p>
                            </div>
                            <UserX className="w-8 h-8 text-red-600" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filtros */}
            <Card>
                <CardContent className="p-4">
                    <div className="flex flex-wrap gap-4 items-center">
                        <div className="flex-1 min-w-[200px]">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <Input
                                    placeholder="Buscar usuários..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <Select value={roleFilter} onValueChange={setRoleFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Todas as funções</SelectItem>
                                <SelectItem value="super_admin">Super Admin</SelectItem>
                                <SelectItem value="admin">Admin</SelectItem>
                                <SelectItem value="support">Suporte</SelectItem>
                                <SelectItem value="viewer">Visualizador</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Todos os status</SelectItem>
                                <SelectItem value="active">Ativos</SelectItem>
                                <SelectItem value="inactive">Inativos</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </CardContent>
            </Card>

            {/* Lista de Usuários */}
            <Card>
                <CardHeader>
                    <CardTitle>Usuários ({filteredUsers.length})</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {filteredUsers.map((user) => (
                            <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                <div className="flex items-center gap-4">
                                    <Avatar>
                                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.name}`} />
                                        <AvatarFallback>
                                            {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <h3 className="font-medium">{user.name}</h3>
                                            {getRoleIcon(user.role)}
                                        </div>
                                        <p className="text-sm text-gray-600">{user.email}</p>
                                        <div className="flex items-center gap-2 mt-1">
                                            <span className="text-xs text-gray-500">
                                                Criado em {format(new Date(user.created_at), 'dd/MM/yyyy', { locale: ptBR })}
                                            </span>
                                            {user.last_login_at && (
                                                <>
                                                    <span className="text-xs text-gray-400">•</span>
                                                    <span className="text-xs text-gray-500">
                                                        Último login: {format(new Date(user.last_login_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                                    </span>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center gap-3">
                                    {getRoleBadge(user.role)}
                                    {getStatusBadge(user.is_active)}
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal className="w-4 h-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => openEditModal(user)}>
                                                <Edit className="w-4 h-4 mr-2" />
                                                Editar
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleResetPassword(user.id, user.name)}>
                                                <Key className="w-4 h-4 mr-2" />
                                                Redefinir Senha
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleToggleStatus(user.id, user.is_active)}>
                                                {user.is_active ? (
                                                    <>
                                                        <UserX className="w-4 h-4 mr-2" />
                                                        Desativar
                                                    </>
                                                ) : (
                                                    <>
                                                        <UserCheck className="w-4 h-4 mr-2" />
                                                        Ativar
                                                    </>
                                                )}
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                onClick={() => handleDeleteUser(user.id)}
                                                className="text-red-600"
                                            >
                                                <Trash2 className="w-4 h-4 mr-2" />
                                                Excluir
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        ))}

                        {filteredUsers.length === 0 && (
                            <div className="text-center py-8">
                                <UsersIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-600">Nenhum usuário encontrado</p>
                                <p className="text-sm text-gray-500">Ajuste os filtros ou crie um novo usuário</p>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Modal de Edição */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Editar Usuário Master</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit-name">Nome</Label>
                                <Input
                                    id="edit-name"
                                    value={formData.name}
                                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="Nome completo"
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit-email">Email</Label>
                                <Input
                                    id="edit-email"
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                    placeholder="<EMAIL>"
                                    disabled
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit-role">Função</Label>
                                <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="viewer">Visualizador</SelectItem>
                                        <SelectItem value="support">Suporte</SelectItem>
                                        <SelectItem value="admin">Administrador</SelectItem>
                                        <SelectItem value="super_admin">Super Admin</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-center space-x-2 pt-6">
                                <Switch
                                    id="edit-is_active"
                                    checked={formData.is_active}
                                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                                />
                                <Label htmlFor="edit-is_active">Usuário ativo</Label>
                            </div>
                        </div>

                        {/* Permissões */}
                        <div>
                            <Label>Permissões</Label>
                            <div className="mt-2 space-y-4 border rounded-lg p-4">
                                {Object.entries(formData.permissions).map(([module, perms]) => (
                                    <div key={module} className="space-y-2">
                                        <h4 className="font-medium capitalize">{module}</h4>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                            {Object.entries(perms).map(([perm, value]) => (
                                                <div key={perm} className="flex items-center space-x-2">
                                                    <Switch
                                                        id={`edit-${module}-${perm}`}
                                                        checked={value as boolean}
                                                        onCheckedChange={(checked) => updatePermission(module, perm, checked)}
                                                    />
                                                    <Label htmlFor={`edit-${module}-${perm}`} className="text-sm capitalize">
                                                        {perm}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                Cancelar
                            </Button>
                            <Button onClick={handleUpdateUser}>
                                Salvar Alterações
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};