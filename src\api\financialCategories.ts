import { supabase } from "@/integrations/supabase/client";

export interface FinancialCategory {
  id: number;
  club_id: number;
  name: string;
  created_at?: string;
  updated_at?: string;
}

export async function getFinancialCategories(clubId: number): Promise<FinancialCategory[]> {
  const { data, error } = await supabase
    .from("financial_categories")
    .select("*")
    .eq("club_id", clubId)
    .order("name");

  if (error) {
    console.error("Erro ao buscar categorias financeiras:", error);
    throw new Error(`Erro ao buscar categorias financeiras: ${error.message}`);
  }

  return (data || []) as FinancialCategory[];
}

export async function createFinancialCategory(clubId: number, name: string): Promise<FinancialCategory> {
  const { data, error } = await supabase
    .from("financial_categories")
    .insert({ club_id: clubId, name })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar categoria financeira:", error);
    throw new Error(`Erro ao criar categoria financeira: ${error.message}`);
  }

  return data as FinancialCategory;
}

export async function updateFinancialCategory(clubId: number, id: number, name: string): Promise<FinancialCategory> {
  const { data, error } = await supabase
    .from("financial_categories")
    .update({ name })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar categoria financeira:", error);
    throw new Error(`Erro ao atualizar categoria financeira: ${error.message}`);
  }

  return data as FinancialCategory;
}

export async function deleteFinancialCategory(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("financial_categories")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao excluir categoria financeira:", error);
    throw new Error(`Erro ao excluir categoria financeira: ${error.message}`);
  }

  return true;
}