import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { supabase } from "@/integrations/supabase/client";
import { generatePixString, generatePixQRCode } from "@/utils/pixGenerator";

export interface EvaluationPayment {
  id: number;
  club_id: number;
  player_id: string;
  player_evaluation_invitation_id?: number;
  amount: number;
  period_description: string;
  pix_key: string;
  pix_code?: string;
  qr_code_data?: string;
  status: 'pending' | 'paid' | 'cancelled';
  payment_token: string;
  receipt_file_url?: string;
  receipt_uploaded_at?: string;
  verified_by?: string;
  verified_at?: string;
  verification_notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  
  // Relations
  player?: {
    id: string;
    name: string;
    email?: string;
  };
  created_by_user?: {
    id: string;
    name: string;
  };
}

/**
 * Create evaluation payment and generate PIX code
 */
export async function createEvaluationPayment(
  clubId: number,
  playerId: string,
  amount: number,
  periodDescription: string,
  pixKey: string,
  createdBy: string,
  playerEvaluationInvitationId?: number
): Promise<EvaluationPayment> {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  // Get club info for PIX generation
  const { data: clubData } = await supabaseWithClubId
    .from("club_info")
    .select("name")
    .eq("id", clubId)
    .single();

  const clubName = clubData?.name || "Clube";

  // Generate PIX code
  const pixData = {
    pixKey,
    amount,
    description: `Avaliação - ${periodDescription}`,
    merchantName: clubName,
    merchantCity: "Brasil"
  };

  const pixCode = generatePixString(pixData);
  const qrCodeData = await generatePixQRCode(pixData);

  // Create payment record
  const { data, error } = await supabaseWithClubId
    .from("evaluation_payments")
    .insert({
      club_id: clubId,
      player_id: playerId,
      player_evaluation_invitation_id: playerEvaluationInvitationId,
      amount,
      period_description: periodDescription,
      pix_key: pixKey,
      pix_code: pixCode,
      qr_code_data: qrCodeData,
      created_by: createdBy
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Erro ao criar cobrança: ${error.message}`);
  }

  return data as EvaluationPayment;
}

/**
 * Get evaluation payments for a player
 */
export async function getPlayerEvaluationPayments(
  clubId: number,
  playerId: string
): Promise<EvaluationPayment[]> {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  const { data, error } = await supabaseWithClubId
    .from("evaluation_payments")
    .select(`
      *,
      player:players(id, name, email)
    `)
    .eq("club_id", clubId)
    .eq("player_id", playerId)
    .order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Erro ao buscar cobranças: ${error.message}`);
  }

  // Get user information for created_by field
  if (data && data.length > 0) {
    const userIds = [...new Set(data.map(payment => payment.created_by).filter(Boolean))];
    
    if (userIds.length > 0) {
      const { data: usersData } = await supabase
        .from("users")
        .select("id, name")
        .in("id", userIds);

      if (usersData) {
        const userMap = new Map(usersData.map(user => [user.id, user]));
        
        data.forEach(payment => {
          if (payment.created_by && userMap.has(payment.created_by)) {
            payment.created_by_user = userMap.get(payment.created_by);
          }
        });
      }
    }
  }

  return data as EvaluationPayment[];
}

/**
 * Get evaluation payment by token (for public access)
 */
export async function getEvaluationPaymentByToken(
  token: string
): Promise<EvaluationPayment | null> {
  const { data, error } = await supabase
    .from("evaluation_payments")
    .select(`
      *,
      player:players(id, name, email)
    `)
    .eq("payment_token", token)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Not found
    }
    throw new Error(`Erro ao buscar cobrança: ${error.message}`);
  }

  // Get club information
  if (data) {
    const { data: clubData } = await supabase
      .from("club_info")
      .select("id, name")
      .eq("id", data.club_id)
      .single();

    if (clubData) {
      (data as any).club_info = clubData;
    }
  }

  return data as EvaluationPayment;
}

/**
 * Upload payment receipt
 */
export async function uploadPaymentReceipt(
  token: string,
  receiptFile: File
): Promise<void> {
  // First get the payment to validate token
  const payment = await getEvaluationPaymentByToken(token);
  if (!payment) {
    throw new Error("Token de pagamento inválido");
  }

  if (payment.status !== 'pending') {
    throw new Error("Este pagamento já foi processado");
  }

  // Upload file to Supabase Storage
  const fileExt = receiptFile.name.split('.').pop();
  const fileName = `evaluation-payment-${payment.id}-${Date.now()}.${fileExt}`;
  const filePath = `clubs/${payment.club_id}/evaluation-payments/${fileName}`;

  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('playerdocuments')
    .upload(filePath, receiptFile);

  if (uploadError) {
    throw new Error(`Erro ao fazer upload: ${uploadError.message}`);
  }

  // Get public URL
  const { data: urlData } = supabase.storage
    .from('playerdocuments')
    .getPublicUrl(filePath);

  // Update payment record directly
  const { error: updateError } = await supabase
    .from("evaluation_payments")
    .update({
      receipt_file_url: urlData.publicUrl,
      receipt_uploaded_at: new Date().toISOString()
    })
    .eq("payment_token", token)
    .eq("status", "pending")
    .is("receipt_file_url", null); // Only update if no receipt exists yet

  if (updateError) {
    throw new Error(`Erro ao atualizar cobrança: ${updateError.message}`);
  }
}

/**
 * Verify payment receipt
 */
export async function verifyPaymentReceipt(
  clubId: number,
  paymentId: number,
  status: 'paid' | 'cancelled',
  verifiedBy: string,
  notes?: string
): Promise<void> {
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  const { error } = await supabaseWithClubId
    .from("evaluation_payments")
    .update({
      status,
      verified_by: verifiedBy,
      verified_at: new Date().toISOString(),
      verification_notes: notes
    })
    .eq("id", paymentId)
    .eq("club_id", clubId);

  if (error) {
    throw new Error(`Erro ao verificar pagamento: ${error.message}`);
  }
}

/**
 * Send evaluation payment email
 */
export async function sendEvaluationPaymentEmail(
  playerEmail: string,
  playerName: string,
  amount: number,
  periodDescription: string,
  pixKey: string,
  pixCode: string,
  qrCodeData: string,
  paymentToken: string,
  clubName: string
): Promise<void> {
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const receiptUploadUrl = `${SITE_URL}/payment-receipt?token=${paymentToken}`;

  // Call Supabase Edge Function to send email
  const { error } = await supabase.functions.invoke('send-evaluation-payment-email', {
    body: {
      to: playerEmail,
      playerName,
      amount,
      periodDescription,
      pixKey,
      pixCode,
      qrCodeData,
      receiptUploadUrl,
      clubName
    }
  });

  if (error) {
    throw new Error(`Erro ao enviar email: ${error.message}`);
  }
}