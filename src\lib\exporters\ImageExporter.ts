import html2canvas from 'html2canvas';
import { TrainingDrill, TrainingElement, DrillStep } from '@/components/training/InteractiveTrainingBuilder';
import { 
  BaseExporter, 
  ImageExportOptions, 
  ExportResult, 
  ExportFormat 
} from '../ExportEngine';

// Image rendering configuration
interface ImageConfig {
  width: number;
  height: number;
  scale: number;
  backgroundColor: string;
  quality: number;
  format: 'png' | 'jpg' | 'webp';
}

// Canvas rendering utilities
class CanvasRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor(width: number, height: number) {
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d')!;
  }

  getCanvas(): HTMLCanvasElement {
    return this.canvas;
  }

  getContext(): CanvasRenderingContext2D {
    return this.ctx;
  }

  clear(backgroundColor: string = '#ffffff'): void {
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }

  drawField(config: { 
    fieldColor: string; 
    lineColor: string; 
    lineWidth: number;
    showGrid: boolean;
    gridSize: number;
  }): void {
    const { width, height } = this.canvas;
    
    // Field background
    this.ctx.fillStyle = config.fieldColor;
    this.ctx.fillRect(0, 0, width, height);
    
    // Field lines
    this.ctx.strokeStyle = config.lineColor;
    this.ctx.lineWidth = config.lineWidth;
    
    // Border
    this.ctx.strokeRect(0, 0, width, height);
    
    // Center line
    this.ctx.beginPath();
    this.ctx.moveTo(width / 2, 0);
    this.ctx.lineTo(width / 2, height);
    this.ctx.stroke();
    
    // Center circle
    this.ctx.beginPath();
    this.ctx.arc(width / 2, height / 2, Math.min(width, height) * 0.1, 0, 2 * Math.PI);
    this.ctx.stroke();
    
    // Goal areas
    const goalWidth = width * 0.15;
    const goalHeight = height * 0.4;
    const goalY = (height - goalHeight) / 2;
    
    // Left goal area
    this.ctx.strokeRect(0, goalY, goalWidth, goalHeight);
    
    // Right goal area
    this.ctx.strokeRect(width - goalWidth, goalY, goalWidth, goalHeight);
    
    // Grid (if enabled)
    if (config.showGrid) {
      this.drawGrid(config.gridSize);
    }
  }

  private drawGrid(gridSize: number): void {
    const { width, height } = this.canvas;
    
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 0.5;
    
    // Vertical lines
    for (let x = gridSize; x < width; x += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, height);
      this.ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = gridSize; y < height; y += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(width, y);
      this.ctx.stroke();
    }
  }

  drawElement(element: TrainingElement, scale: number = 1): void {
    const x = (element.position.x / 100) * this.canvas.width;
    const y = (element.position.y / 100) * this.canvas.height;
    const size = this.resolveSize(element, 10) * scale;
    
    this.ctx.save();
    
    // Apply rotation if present
    if (element.properties.rotation) {
      this.ctx.translate(x, y);
      this.ctx.rotate((element.properties.rotation * Math.PI) / 180);
      this.ctx.translate(-x, -y);
    }
    
    // Set element style
    const color = this.getElementColor(element);
    this.ctx.fillStyle = color;
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 1;
    
    // Draw element based on type
    switch (element.type) {
      case 'player':
        this.drawPlayer(x, y, size, element);
        break;
      case 'ball':
        this.drawBall(x, y, size);
        break;
      case 'cone':
        this.drawCone(x, y, size);
        break;
      case 'goal':
        this.drawGoal(x, y, size);
        break;
      case 'obstacle':
        this.drawObstacle(x, y, size);
        break;
      default:
        this.drawDefault(x, y, size);
    }
    
    // Draw label or player name if present
    const label =
      element.properties.label ||
      (element.type === 'player'
        ? element.properties.playerName?.split(' ')[0]
        : undefined);
    if (label) {
      this.drawLabel(x, y + size + 2, label);    }
    
    this.ctx.restore();
  }

  private drawPlayer(x: number, y: number, size: number, element: TrainingElement): void {
    // Player circle
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
    
    // Player number
    if (element.properties.number) {
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = `${size}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(element.properties.number.toString(), x, y);
    }
  }

  private drawBall(x: number, y: number, size: number): void {
    // Ball circle
    this.ctx.fillStyle = '#ffffff';
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
    
    // Ball pattern
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.arc(x, y, size * 0.7, 0, Math.PI);
    this.ctx.stroke();
  }

  private drawCone(x: number, y: number, size: number): void {
    // Cone triangle
    this.ctx.beginPath();
    this.ctx.moveTo(x, y - size);
    this.ctx.lineTo(x - size * 0.7, y + size * 0.5);
    this.ctx.lineTo(x + size * 0.7, y + size * 0.5);
    this.ctx.closePath();
    this.ctx.fill();
    this.ctx.stroke();
  }

  private drawGoal(x: number, y: number, size: number): void {
    // Goal rectangle
    this.ctx.strokeRect(x - size, y - size * 0.5, size * 2, size);
  }

  private drawObstacle(x: number, y: number, size: number): void {
    // Obstacle square
    this.ctx.fillRect(x - size * 0.5, y - size * 0.5, size, size);
    this.ctx.strokeRect(x - size * 0.5, y - size * 0.5, size, size);
  }

  private drawDefault(x: number, y: number, size: number): void {
    // Default circle
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();
  }

  private drawLabel(x: number, y: number, text: string): void {
    this.ctx.fillStyle = '#000000';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(text, x, y);
  }

  drawTrajectory(points: { x: number; y: number }[], style: {
    color: string;
    width: number;
    dashed?: boolean;
    arrowSize?: number;
  }): void {
    if (points.length < 2) return;
    
    this.ctx.strokeStyle = style.color;
    this.ctx.lineWidth = style.width;
    
    if (style.dashed) {
      this.ctx.setLineDash([5, 5]);
    }
    
    // Draw path
    this.ctx.beginPath();
    this.ctx.moveTo(
      (points[0].x / 100) * this.canvas.width,
      (points[0].y / 100) * this.canvas.height
    );
    
    for (let i = 1; i < points.length; i++) {
      this.ctx.lineTo(
        (points[i].x / 100) * this.canvas.width,
        (points[i].y / 100) * this.canvas.height
      );
    }
    this.ctx.stroke();
    
    // Draw arrow at the end
    if (style.arrowSize && points.length >= 2) {
      const lastPoint = points[points.length - 1];
      const secondLastPoint = points[points.length - 2];
      
      const endX = (lastPoint.x / 100) * this.canvas.width;
      const endY = (lastPoint.y / 100) * this.canvas.height;
      const startX = (secondLastPoint.x / 100) * this.canvas.width;
      const startY = (secondLastPoint.y / 100) * this.canvas.height;
      
      this.drawArrow(startX, startY, endX, endY, style.arrowSize);
    }
    
    this.ctx.setLineDash([]);
  }

  private drawArrow(startX: number, startY: number, endX: number, endY: number, size: number): void {
    const angle = Math.atan2(endY - startY, endX - startX);
    const arrowAngle = Math.PI / 6;
    
    this.ctx.beginPath();
    this.ctx.moveTo(endX, endY);
    this.ctx.lineTo(
      endX - size * Math.cos(angle - arrowAngle),
      endY - size * Math.sin(angle - arrowAngle)
    );
    this.ctx.moveTo(endX, endY);
    this.ctx.lineTo(
      endX - size * Math.cos(angle + arrowAngle),
      endY - size * Math.sin(angle + arrowAngle)
    );
    this.ctx.stroke();
  }

  drawWatermark(text: string, position: string, opacity: number = 0.3): void {
    this.ctx.save();
    this.ctx.globalAlpha = opacity;
    this.ctx.fillStyle = '#cccccc';
    this.ctx.font = '48px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    let x = this.canvas.width / 2;
    let y = this.canvas.height / 2;
    
    switch (position) {
      case 'top-left':
        x = 100; y = 50;
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'top';
        break;
      case 'top-right':
        x = this.canvas.width - 100; y = 50;
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'top';
        break;
      case 'bottom-left':
        x = 100; y = this.canvas.height - 50;
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'bottom';
        break;
      case 'bottom-right':
        x = this.canvas.width - 100; y = this.canvas.height - 50;
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        break;
    }
    
    this.ctx.rotate(Math.PI / 4);
    this.ctx.fillText(text, x, y);
    this.ctx.restore();
  }

  private resolveSize(element: TrainingElement, fallback: number): number {
    const size = (element as any).properties.size;
    if (typeof size === 'number') return size;
    switch (size) {
      case 'small':
        return fallback * 0.5;
      case 'large':
        return fallback * 1.5;
      default:
        return fallback;
    }
  }

  private getElementColor(element: TrainingElement): string {
    if (element.properties.color) {
      return element.properties.color;
    }
    const colors: Record<string, string> = {
      player: '#3b82f6',
      ball: '#ffffff',
      cone: '#f59e0b',
      goal: '#000000',
      obstacle: '#ef4444'
    };
    return colors[element.type] || '#6b7280';
  }

  toBlob(format: 'png' | 'jpg' | 'webp' = 'png', quality: number = 0.9): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;
      this.canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create image blob'));
        }
      }, mimeType, quality);
    });
  }
}

export class ImageExporter implements BaseExporter {
  private defaultOptions: ImageExportOptions = {
    format: 'image',
    quality: 'high',
    imageFormat: 'png',
    width: 1920,
    height: 1080,
    backgroundColor: '#ffffff',
    includeField: true,
    includeMetadata: true
  };

  async export(drill: TrainingDrill, options: ImageExportOptions): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const config = this.getImageConfig(mergedOptions);
      
      // Create canvas renderer
      const renderer = new CanvasRenderer(config.width, config.height);
      
      // Clear canvas with background color
      renderer.clear(config.backgroundColor);
      
      // Render field if requested
      if (mergedOptions.includeField) {
        renderer.drawField({
          fieldColor: '#4ade80',
          lineColor: '#ffffff',
          lineWidth: 2,
          showGrid: false,
          gridSize: 20
        });
      }
      
      // Render drill content
      await this.renderDrillContent(renderer, drill, mergedOptions);
      
      // Add watermark if specified
      if (mergedOptions.watermark) {
        renderer.drawWatermark(
          mergedOptions.watermark.text,
          mergedOptions.watermark.position,
          mergedOptions.watermark.opacity
        );
      }
      
      // Convert to blob
      const imageBlob = await renderer.toBlob(config.format, config.quality);
      const filename = this.generateFilename(drill, config.format);
      
      return {
        success: true,
        data: imageBlob,
        filename,
        size: imageBlob.size,
        metadata: {
          format: 'image',
          fileSize: imageBlob.size,
          exportedAt: new Date(),
          dimensions: { width: config.width, height: config.height }
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: this.generateFilename(drill, 'png'),
        error: error instanceof Error ? error.message : 'Image export failed'
      };
    }
  }

  private getImageConfig(options: ImageExportOptions): ImageConfig {
    const qualitySettings = {
      low: { scale: 1, quality: 0.6 },
      medium: { scale: 1.5, quality: 0.8 },
      high: { scale: 2, quality: 0.9 },
      ultra: { scale: 3, quality: 1.0 }
    };
    
    const settings = qualitySettings[options.quality || 'high'];
    
    return {
      width: options.width || 1920,
      height: options.height || 1080,
      scale: settings.scale,
      backgroundColor: options.backgroundColor || '#ffffff',
      quality: settings.quality,
      format: options.imageFormat || 'png'
    };
  }

  private async renderDrillContent(
    renderer: CanvasRenderer,
    drill: TrainingDrill,
    options: ImageExportOptions
  ): Promise<void> {
    // For single image export, we'll render the first step or a composite view
    if (drill.steps.length > 0) {
      const step = drill.steps[0];
      
      // Render elements
      step.elements.forEach(element => {
        renderer.drawElement(element, 1);
      });
      
      // Render trajectories (if available)
      if (step.annotations) {
        step.annotations.forEach(annotation => {
          if (annotation.type === 'trajectory' && annotation.points) {
            renderer.drawTrajectory(annotation.points, {
              color: annotation.color || '#ff0000',
              width: annotation.width || 2,
              dashed: annotation.dashed || false,
              arrowSize: 10
            });
          }
        });
      }
      
      // Render drawings
      if (step.drawings) {
        step.drawings.forEach(drawing => {
          if (drawing.type === 'line' && drawing.points && drawing.points.length >= 2) {
            renderer.drawTrajectory(drawing.points, {
              color: drawing.color || '#000000',
              width: drawing.width || 1,
              dashed: drawing.dashed || false
            });
          }
        });
      }
    }
    
    // Add title overlay
    this.addTitleOverlay(renderer, drill);
  }

  private addTitleOverlay(renderer: CanvasRenderer, drill: TrainingDrill): void {
    const ctx = renderer.getContext();
    const canvas = renderer.getCanvas();
    
    // Semi-transparent overlay for title
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, 80);
    
    // Title text
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText(drill.name, 20, 25);
    
    // Subtitle
    ctx.font = '18px Arial';
    const subtitle = `${drill.category} • ${drill.difficulty} • ${drill.playersRequired} jogadores`;
    ctx.fillText(subtitle, 20, 55);
  }

  // Alternative method: Export from DOM element
  async exportFromElement(element: HTMLElement, options: ImageExportOptions): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const config = this.getImageConfig(mergedOptions);
      
      // Use html2canvas to capture the element
      const canvas = await html2canvas(element, {
        width: config.width,
        height: config.height,
        scale: config.scale,
        backgroundColor: config.backgroundColor,
        useCORS: true,
        allowTaint: true,
        logging: false
      });
      
      // Convert to blob
      const imageBlob = await new Promise<Blob>((resolve, reject) => {
        const mimeType = config.format === 'jpg' ? 'image/jpeg' : `image/${config.format}`;
        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
          else reject(new Error('Failed to create image blob'));
        }, mimeType, config.quality);
      });
      
      const filename = `drill_capture_${Date.now()}.${config.format}`;
      
      return {
        success: true,
        data: imageBlob,
        filename,
        size: imageBlob.size,
        metadata: {
          format: 'image',
          fileSize: imageBlob.size,
          exportedAt: new Date(),
          dimensions: { width: canvas.width, height: canvas.height }
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: `drill_capture_failed.png`,
        error: error instanceof Error ? error.message : 'DOM capture failed'
      };
    }
  }

  // Export multiple steps as separate images
  async exportSteps(drill: TrainingDrill, options: ImageExportOptions): Promise<ExportResult[]> {
    const results: ExportResult[] = [];
    
    for (let i = 0; i < drill.steps.length; i++) {
      const step = drill.steps[i];
      
      // Create a temporary drill with just this step
      const stepDrill: TrainingDrill = {
        ...drill,
        steps: [step],
        name: `${drill.name} - Passo ${i + 1}`
      };
      
      const result = await this.export(stepDrill, options);
      results.push(result);
    }
    
    return results;
  }

  private generateFilename(drill: TrainingDrill, format: string): string {
    const timestamp = new Date().toISOString().slice(0, 10);
    const safeName = drill.name.replace(/[^a-zA-Z0-9]/g, '_');
    return `${safeName}_${timestamp}.${format}`;
  }

  validateOptions(options: ImageExportOptions): boolean {
    if (options.format !== 'image') return false;
    
    const validFormats = ['png', 'jpg', 'webp'];
    if (options.imageFormat && !validFormats.includes(options.imageFormat)) return false;
    
    if (options.width && (options.width < 100 || options.width > 8000)) return false;
    if (options.height && (options.height < 100 || options.height > 8000)) return false;
    
    return true;
  }

  getDefaultOptions(): ImageExportOptions {
    return { ...this.defaultOptions };
  }

  getSupportedFormats(): ExportFormat[] {
    return ['image'];
  }
}