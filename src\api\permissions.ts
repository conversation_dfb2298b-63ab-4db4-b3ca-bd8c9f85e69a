import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";
import { createNotification } from "./api";
import { INVENTORY_PERMISSIONS, ROLE_PERMISSIONS } from "@/constants/permissions";

export type UserInvitation = {
  id: number;
  club_id: number;
  email: string;
  role: string;
  department_id: number | null;
  permissions: Record<string, any>;
  token: string;
  status: "pending" | "accepted" | "expired";
  created_at: string;
  expires_at: string;
  department_name?: string; // Campo adicional para junção
  custom_password?: string | null; // Senha personalizada (opcional)
  collaborator_id?: number | null; // ID do colaborador associado
  collaborator_name?: string; // Nome do colaborador (para exibição)
};

export type UserPermissions = {
  role: string;
  permissions: Record<string, boolean>;
  departments?: {
    id: number;
    name: string;
    role: string;
    permissions: Record<string, boolean>;
  }[];
};

/**
 * Obtém as permissões de um usuário em um clube
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns Permissões do usuário
 */
export async function getUserPermissions(
  clubId: number,
  userId: string
): Promise<UserPermissions | null> {
  console.log(`[getUserPermissions] Buscando permissões para usuário ${userId} no clube ${clubId}`);

  try {
    // Verificar se o usuário é membro do clube
    console.log(`[getUserPermissions] Buscando role e permissões diretas do usuário`);
    const { data: member, error: memberError } = await supabase
      .from("club_members")
      .select("role, permissions")
      .eq("club_id", clubId)
      .eq("user_id", userId)
      .maybeSingle();

    if (memberError) {
      console.error(`[getUserPermissions] Erro ao verificar membro do clube:`, memberError);
      throw new Error(`Erro ao verificar membro do clube: ${memberError.message}`);
    }

    if (!member) {
      console.log(`[getUserPermissions] Usuário não é membro do clube`);
      return null;
    }

    // Obter permissões do usuário
    console.log(`[getUserPermissions] Buscando permissões do usuário`);
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role, permissions")
      .eq("id", userId)
      .single();

    if (userError) {
      console.error(`[getUserPermissions] Erro ao obter usuário:`, userError);
      throw new Error(`Erro ao obter usuário: ${userError.message}`);
    }

    // Obter departamentos do usuário e suas permissões
    // Em implementações anteriores o sistema consultava a tabelaa
    // `user_departments`, porém essa tabela não está sendo utilizada
    // atualmente. Para evitar erros de consulta e simplificar o fluxo,
    // ignoramos essa etapa e retornamos um array vazio.
    const departments: any[] = [];

    const role = member.role || user.role || "user";

    // Combinar permissões padrão do papel, permissões do usuário e do clube
    const defaultRolePermissions =
      ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {};

    let permissions = {
      ...defaultRolePermissions,
      ...user.permissions,
      ...member.permissions,
    };

    // Adicionar permissões de departamentos
    departments.forEach(dept => {
      permissions = {
        ...permissions,
        ...dept.permissions
      };
    });

    return {
      role,
      permissions,
      departments
    };
  } catch (error: any) {
    console.error("Erro ao obter permissões do usuário:", error);
    throw new Error(error.message || "Erro ao obter permissões do usuário");
  }
}

/**
 * Atualiza as permissões de um usuário em um clube
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param permissions Novas permissões
 * @returns true se as permissões foram atualizadas com sucesso
 */
export async function updateUserPermissions(
  clubId: number,
  userId: string,
  permissions: Record<string, boolean>
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("club_members")
      .update({
        permissions,
      })
      .eq("club_id", clubId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Erro ao atualizar permissões: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao atualizar permissões:", error);
    throw new Error(error.message || "Erro ao atualizar permissões");
  }
}

/**
 * Cria um convite para um novo usuário
 * @param clubId ID do clube
 * @param email Email do usuário
 * @param role Papel do usuário
 * @param departmentId ID do departamento (opcional)
 * @param permissions Permissões do usuário (opcional)
 * @param password Senha do usuário (opcional, será gerada se não fornecida)
 * @param collaboratorId ID do colaborador associado (opcional)
 * @returns Convite criado
 */
export async function createUserInvitation(
  clubId: number,
  email: string,
  role: string,
  departmentId?: number,
  permissions?: Record<string, boolean>,
  password?: string,
  collaboratorId?: number
): Promise<UserInvitation> {
  try {
    // Verificar se já existe um convite pendente para este email
    const { data: existing, error: existingError } = await supabase
      .from("user_invitations")
      .select("id")
      .eq("club_id", clubId)
      .eq("email", email)
      .eq("status", "pending")
      .maybeSingle();

    if (existingError) {
      throw new Error(`Erro ao verificar convite existente: ${existingError.message}`);
    }

    if (existing) {
      throw new Error("Já existe um convite pendente para este email");
    }

    // Verificar se o usuário já existe no sistema
    const { data: existingUser, error: userError } = await supabase
      .from("users")
      .select("id")
      .eq("email", email)
      .maybeSingle();

    if (userError) {
      console.error("Erro ao verificar usuário existente:", userError);
      // Não interromper o fluxo, apenas logar o erro
    }

    // Gerar token único
    const token = uuidv4();

    // Verificar se o collaboratorId é válido
    if (collaboratorId) {
      const { data: collaborator, error: collaboratorError } = await supabase
        .from("collaborators")
        .select("id, full_name, role")
        .eq("id", collaboratorId)
        .eq("club_id", clubId)
        .single();

      if (collaboratorError) {
        console.error("Erro ao verificar colaborador:", collaboratorError);
        // Não interromper o fluxo, apenas logar o erro
      }

      if (!collaborator) {
        throw new Error("Colaborador não encontrado");
      }

      // Usar o role do colaborador para o convite, garantindo consistência
      if (collaborator.role) {
        role = collaborator.role;
        console.log(`Usando o role do colaborador (${role}) para o convite`);
      }

      // Atualizar o role do colaborador na tabela collaborators para manter consistência
      const { error: updateError } = await supabase
        .from("collaborators")
        .update({ role })
        .eq("id", collaboratorId)
        .eq("club_id", clubId);

      if (updateError) {
        console.error("Erro ao atualizar role do colaborador:", updateError);
        // Não interromper o fluxo, apenas logar o erro
      }
    }

    // Criar convite
    const { data, error } = await supabase
      .from("user_invitations")
      .insert({
        club_id: clubId,
        email,
        role,
        department_id: departmentId || null,
        permissions: permissions || {},
        token,
        status: "pending",
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 dias
        custom_password: password || null, // Armazenar senha personalizada se fornecida
        collaborator_id: collaboratorId || null, // Associar ao colaborador se fornecida
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao criar convite: ${error.message}`);
    }

    // Obter o nome do clube
    const { data: clubData, error: clubError } = await supabase
      .from("club_info")
      .select("name")
      .eq("id", clubId)
      .single();

    if (clubError) {
      console.error("Erro ao obter nome do clube:", clubError);
      // Não interromper o fluxo, apenas logar o erro
    }

    const clubName = clubData?.name || "Game Day Nexus";

    // Enviar email de convite
    try {
      // Importar a função diretamente para evitar dependência circular
      const { sendInvitationEmail } = await import('@/services/brevoEmailService');
      const emailSent = await sendInvitationEmail(email, email.split('@')[0], token, clubName);

      if (emailSent) {
        console.log(`Email de convite enviado com sucesso para ${email}`);
      } else {
        console.error(`Falha ao enviar email de convite para ${email}`);
      }
    } catch (emailError) {
      console.error("Erro ao enviar email de convite:", emailError);
      // Não interromper o fluxo, apenas logar o erro
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao criar convite:", error);
    throw new Error(error.message || "Erro ao criar convite");
  }
}

/**
 * Verifica se um convite existe pelo token
 * @param token Token do convite
 * @returns true se o convite existe, false caso contrário
 */
export async function checkInvitationExists(token: string): Promise<boolean> {
  try {
    if (!token || token.trim() === '') {
      console.error("Token de convite inválido ou vazio");
      return false;
    }

    console.log(`Verificando existência do convite com token: ${token}`);

    // Usar uma consulta direta para verificar a existência
    const { data, error } = await supabase
      .from("user_invitations")
      .select("id")
      .eq("token", token)
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Erro "não encontrado" é esperado quando não há resultados
        console.log(`Nenhum convite encontrado com o token: ${token}`);
        return false;
      }

      console.error(`Erro ao verificar existência do convite: ${error.message}`, error);
      return false;
    }

    console.log(`Convite encontrado com ID: ${data?.id}`);
    return true;
  } catch (error: any) {
    console.error("Erro ao verificar existência do convite:", error);
    return false;
  }
}

/**
 * Obtém um convite pelo token
 * @param token Token do convite
 * @returns Convite ou null se não encontrado
 */
export async function getUserInvitationByToken(token: string): Promise<UserInvitation | null> {
  try {
    console.log(`Buscando convite com token: ${token}`);

    // Verificar se o token é válido
    if (!token || token.trim() === '') {
      console.error("Token de convite inválido ou vazio");
      return null;
    }

    // Buscar o convite diretamente no banco de dados
    // Não usar maybeSingle() para evitar erros quando não encontrar
    const { data, error } = await supabase
      .from("user_invitations")
      .select(`
        *,
        departments:department_id (
          id,
          name
        )
      `)
      .eq("token", token)
      .limit(1);

    if (error) {
      console.error(`Erro ao obter convite do banco de dados: ${error.message}`, error);
      throw new Error(`Erro ao obter convite: ${error.message}`);
    }

    // Verificar se retornou algum dado
    if (!data || data.length === 0) {
      console.error(`Nenhum convite encontrado com o token: ${token}`);
      return null;
    }

    const invitation = data[0];
    console.log(`Convite encontrado:`, invitation);

    // Formatar os dados para incluir o nome do departamento
    return {
      ...invitation,
      department_name: invitation.departments?.name || null,
    };
  } catch (error: any) {
    console.error("Erro ao obter convite:", error);
    throw new Error(error.message || "Erro ao obter convite");
  }
}

/**
 * Aceita um convite de usuário
 * @param token Token do convite
 * @param userId ID do usuário que está aceitando o convite
 * @returns true se o convite foi aceito com sucesso
 */
export async function acceptUserInvitation(token: string, userId: string): Promise<boolean> {
  try {
    console.log(`Aceitando convite com token: ${token} para usuário: ${userId}`);

    // Verificar se o token é válido
    if (!token || token.trim() === '') {
      console.error("Token de convite inválido ou vazio");
      throw new Error("Token de convite inválido");
    }

    // Obter o convite diretamente
    const { data: invitationData, error: invitationError } = await supabase
      .from("user_invitations")
      .select("*")
      .eq("token", token)
      .limit(1);

    if (invitationError) {
      console.error(`Erro ao obter convite: ${invitationError.message}`, invitationError);
      throw new Error(`Erro ao obter convite: ${invitationError.message}`);
    }

    if (!invitationData || invitationData.length === 0) {
      console.error(`Nenhum convite encontrado com o token: ${token}`);
      throw new Error("Convite não encontrado");
    }

    const invitation = invitationData[0];
    console.log(`Convite encontrado:`, invitation);

    if (invitation.status !== "pending") {
      console.error(`Convite com status inválido: ${invitation.status}`);
      throw new Error(`Convite já ${invitation.status === "accepted" ? "aceito" : "expirado"}`);
    }

    // Verificar se o usuário já existe
    const { data: existingUser, error: userError } = await supabase
      .from("users")
      .select("id")
      .eq("email", invitation.email)
      .maybeSingle();

    if (userError) {
      console.error("Erro ao verificar usuário existente:", userError);
      // Não interromper o fluxo, apenas logar o erro
    }

    // Se o usuário já existe e é diferente do userId fornecido, usar o ID existente
    if (existingUser && existingUser.id !== userId) {
      console.log(`Usuário já existe com ID ${existingUser.id}, usando este ID em vez de ${userId}`);
      userId = existingUser.id;
    }

    // Verificar se o convite expirou
    if (new Date(invitation.expires_at) < new Date()) {
      // Atualizar status do convite para expirado
      await supabase
        .from("user_invitations")
        .update({
          status: "expired",
        })
        .eq("id", invitation.id);

      throw new Error("Convite expirado");
    }

    // Adicionar usuário ao clube
    const { error: memberError } = await supabase
      .from("club_members")
      .insert({
        club_id: invitation.club_id,
        user_id: userId,
        role: invitation.role,
        permissions: invitation.permissions,
        status: "ativo",
      });

    if (memberError) {
      throw new Error(`Erro ao adicionar usuário ao clube: ${memberError.message}`);
    }

    // Se houver departamento, adicionar usuário ao departamento
    if (invitation.department_id) {
      const { error: departmentError } = await supabase
        .from("user_departments")
        .insert({
          club_id: invitation.club_id,
          user_id: userId,
          department_id: invitation.department_id,
          role: invitation.role,
        });

      if (departmentError) {
        console.error("Erro ao adicionar usuário ao departamento:", departmentError);
        // Continuar mesmo se houver erro ao adicionar ao departamento
      }
    }

    // Se houver um colaborador associado ao convite, vincular o usuário ao colaborador
    if (invitation.collaborator_id) {
      // Primeiro, obter o role do colaborador
      const { data: collaborator, error: fetchError } = await supabase
        .from("collaborators")
        .select("role")
        .eq("id", invitation.collaborator_id)
        .eq("club_id", invitation.club_id)
        .single();

      if (fetchError) {
        console.error("Erro ao obter informações do colaborador:", fetchError);
        // Não interromper o fluxo, apenas logar o erro
      }

      // Vincular o usuário ao colaborador
      const { error: collaboratorError } = await supabase
        .from("collaborators")
        .update({
          user_id: userId
        })
        .eq("id", invitation.collaborator_id)
        .eq("club_id", invitation.club_id);

      if (collaboratorError) {
        console.error("Erro ao vincular usuário ao colaborador:", collaboratorError);
        // Não interromper o fluxo, apenas logar o erro
      } else {
        console.log(`Usuário ${userId} vinculado ao colaborador ${invitation.collaborator_id}`);

        // Atualizar o role do usuário na tabela users para corresponder ao role do colaborador
        if (collaborator && collaborator.role) {
          const { error: userUpdateError } = await supabase
            .from("users")
            .update({
              role: invitation.role // Usar o role do convite, que deve corresponder ao do colaborador
            })
            .eq("id", userId);

          if (userUpdateError) {
            console.error("Erro ao atualizar role do usuário:", userUpdateError);
            // Não interromper o fluxo, apenas logar o erro
          } else {
            console.log(`Role do usuário ${userId} atualizado para ${invitation.role}`);
          }
        }
      }

      // Se não houver collaborator_id no convite, atualizar o role do usuário na tabela users
      if (!invitation.collaborator_id) {
        const { error: userUpdateError } = await supabase
          .from("users")
          .update({
            role: invitation.role
          })
          .eq("id", userId);

        if (userUpdateError) {
          console.error("Erro ao atualizar role do usuário:", userUpdateError);
          // Não interromper o fluxo, apenas logar o erro
        } else {
          console.log(`Role do usuário ${userId} atualizado para ${invitation.role}`);
        }
      }
    }

    // Atualizar status do convite
    const { error: updateInvitationError } = await supabase
      .from("user_invitations")
      .update({
        status: "accepted",
      })
      .eq("id", invitation.id);

    if (updateInvitationError) {
      throw new Error(`Erro ao atualizar convite: ${updateInvitationError.message}`);
    }

    // Criar notificação para o usuário
    try {
      await createNotification({
        user_id: userId,
        club_id: invitation.club_id,
        title: "Bem-vindo ao clube!",
        description: `Você agora é membro do clube com o papel de ${invitation.role}.`,
      });
    } catch (notificationError) {
      console.error("Erro ao criar notificação:", notificationError);
      // Continuar mesmo se houver erro ao criar notificação
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao aceitar convite:", error);
    throw new Error(error.message || "Erro ao aceitar convite");
  }
}

/**
 * Cancela um convite de usuário
 * @param invitationId ID do convite
 * @returns true se o convite foi cancelado com sucesso
 */
export async function cancelUserInvitation(invitationId: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("user_invitations")
      .delete()
      .eq("id", invitationId);

    if (error) {
      throw new Error(`Erro ao cancelar convite: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao cancelar convite:", error);
    throw new Error(error.message || "Erro ao cancelar convite");
  }
}

/**
 * Obtém todos os convites de um clube
 * @param clubId ID do clube
 * @returns Lista de convites
 */
export async function getClubInvitations(clubId: number): Promise<UserInvitation[]> {
  try {
    const { data, error } = await supabase
      .from("user_invitations")
      .select(`
        *,
        departments:department_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Erro ao obter convites: ${error.message}`);
    }

    // Buscar nomes dos colaboradores para os convites que têm collaborator_id
    const invitationsWithCollaboratorIds = data?.filter(item => item.collaborator_id) || [];

    if (invitationsWithCollaboratorIds.length > 0) {
      const collaboratorIds = invitationsWithCollaboratorIds.map(item => item.collaborator_id);

      const { data: collaborators, error: collaboratorsError } = await supabase
        .from("collaborators")
        .select("id, full_name")
        .in("id", collaboratorIds)
        .eq("club_id", clubId);

      if (collaboratorsError) {
        console.error("Erro ao buscar nomes dos colaboradores:", collaboratorsError);
      }

      // Criar um mapa de id -> nome para facilitar o acesso
      const collaboratorMap = new Map();
      collaborators?.forEach(collab => {
        collaboratorMap.set(collab.id, collab.full_name);
      });

      // Formatar os dados para incluir o nome do departamento e do colaborador
      return (data || []).map((item) => ({
        ...item,
        department_name: item.departments?.name || null,
        collaborator_name: item.collaborator_id ? collaboratorMap.get(item.collaborator_id) || "Colaborador não encontrado" : null,
      }));
    }

    // Se não houver convites com collaborator_id, apenas formatar com o nome do departamento
    return (data || []).map((item) => ({
      ...item,
      department_name: item.departments?.name || null,
    }));
  } catch (error: any) {
    console.error("Erro ao obter convites:", error);
    throw new Error(error.message || "Erro ao obter convites");
  }
}

/**
 * Verifica se um usuário tem uma permissão específica
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param permission Permissão a ser verificada
 * @returns true se o usuário tem a permissão
 */
export async function hasPermission(
  clubId: number,
  userId: string,
  permission: string,
  departmentId?: number
): Promise<boolean> {
  console.log(`[hasPermission] Verificando permissão: ${permission} para o usuário ${userId} no clube ${clubId}`);
  
  try {
    console.log(`[hasPermission] Obtendo permissões do usuário ${userId}`);
    const userPermissions = await getUserPermissions(clubId, userId);
    console.log(`[hasPermission] Permissões do usuário:`, userPermissions);

    if (!userPermissions) {
      console.log(`[hasPermission] Nenhuma permissão encontrada para o usuário ${userId}`);
      return false;
    }

    // Jogadores têm acesso muito restrito
    if (userPermissions.role === "player") {
      console.log(`[hasPermission] Usuário é um jogador, verificando permissões restritas`);
      const playerPermissions = [
        "dashboard.view",
        "players.view_own",
        "players.edit_own",
        INVENTORY_PERMISSIONS.REQUESTS.CREATE,
        INVENTORY_PERMISSIONS.REQUESTS.EDIT,
      ];
      const hasPermission = playerPermissions.includes(permission);
      console.log(`[hasPermission] Permissão ${permission} ${hasPermission ? 'concedida' : 'negada'} para jogador`);
      return hasPermission;
    }

    // Presidente tem todas as permissões
    if (userPermissions.role === "president") {
      console.log(`[hasPermission] Usuário é presidente, permissão concedida`);
      return true;
    }

    // Administrador tem todas as permissões exceto as específicas de presidente
    if (userPermissions.role === "admin" && !permission.startsWith("president.")) {
      console.log(`[hasPermission] Usuário é admin, permissão concedida`);
      return true;
    }

    // Usuários médicos têm permissões específicas
    if (userPermissions.role === "medical") {
      console.log(`[hasPermission] Usuário é médico, verificando permissões médicas`);
      
      // Permite todas as permissões relacionadas a medical
      if (permission.startsWith("medical.")) {
        console.log(`[hasPermission] Verificando permissão médica: ${permission}`);
        
        // Se for uma permissão de visualização ou criação básica, concede acesso
        if (permission.endsWith(".view") || 
            permission.endsWith(".view_own") || 
            permission.endsWith(".create") ||
            permission === "medical.appointments.create" ||
            permission === "medical.appointments.edit" ||
            permission === "medical.appointments.start" ||
            permission === "medical.appointments.delete" ||
            permission === "medical.agenda.view" ||
            permission === "medical.exam_requests.view" ||
            permission === "medical.exam_requests.create") {
          console.log(`[hasPermission] Permissão médica básica concedida: ${permission}`);
          return true;
        }
        
        // Verifica se o usuário tem a permissão específica
        if (userPermissions.permissions && userPermissions.permissions[permission]) {
          console.log(`[hasPermission] Permissão específica encontrada: ${permission}`);
          return true;
        }
        
        console.log(`[hasPermission] Permissão médica não encontrada: ${permission}`);
      } else {
        console.log(`[hasPermission] Permissão não é relacionada a medical: ${permission}`);
      }
    }

    // Se um departamento específico foi fornecido, verificar permissões desse departamento
    if (departmentId && userPermissions.departments) {
      const department = userPermissions.departments.find(d => d.id === departmentId);
      if (department) {
        // Verificar permissão específica no departamento
        if (department.permissions[permission]) {
          return true;
        }
      }
    }

    // Verificar permissão específica nas permissões gerais
    return !!userPermissions.permissions[permission];
  } catch (error: any) {
    console.error("Erro ao verificar permissão:", error);
    return false;
  }
}

/**
 * Verifica se um usuário tem permissão em um departamento específico
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param departmentId ID do departamento
 * @param permission Permissão a ser verificada
 * @returns true se o usuário tem a permissão no departamento
 */
export async function hasDepartmentPermission(
  clubId: number,
  userId: string,
  departmentId: number,
  permission: string
): Promise<boolean> {
  return hasPermission(clubId, userId, permission, departmentId);
}
