import { useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useClubSlugStore } from '@/store/useClubSlugStore';

/**
 * Hook otimizado para navegação rápida dentro do clube
 * Usa cache para evitar recarregamentos desnecessários
 */
export function useFastClubNavigation() {
  const navigate = useNavigate();
  const { clubSlug } = useParams<{ clubSlug: string }>();
  const { currentSlug, getClubFromCache } = useClubSlugStore();

  const navigateToClubPage = useCallback((path: string) => {
    const activeSlug = clubSlug || currentSlug;
    
    if (!activeSlug) {
      console.warn('Slug do clube não encontrado, redirecionando para login');
      navigate('/login');
      return;
    }

    // Verificar se o clube está em cache para navegação mais rápida
    const cachedClub = getClubFromCache(activeSlug);
    if (cachedClub) {
      // Navegação rápida com dados em cache
      const cleanPath = path.startsWith('/') ? path : `/${path}`;
      navigate(`/${activeSlug}${cleanPath}`);
    } else {
      // Fallback para navegação normal
      const cleanPath = path.startsWith('/') ? path : `/${path}`;
      navigate(`/${activeSlug}${cleanPath}`);
    }
  }, [navigate, clubSlug, currentSlug, getClubFromCache]);

  const navigateToClubPageReplace = useCallback((path: string) => {
    const activeSlug = clubSlug || currentSlug;
    
    if (!activeSlug) {
      console.warn('Slug do clube não encontrado, redirecionando para login');
      navigate('/login', { replace: true });
      return;
    }

    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    navigate(`/${activeSlug}${cleanPath}`, { replace: true });
  }, [navigate, clubSlug, currentSlug]);

  const getClubUrl = useCallback((path: string) => {
    const activeSlug = clubSlug || currentSlug;
    if (!activeSlug) return path;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `/${activeSlug}${cleanPath}`;
  }, [clubSlug, currentSlug]);

  return {
    navigateToClubPage,
    navigateToClubPageReplace,
    getClubUrl,
    clubSlug: clubSlug || currentSlug
  };
}