import { supabase } from '@/integrations/supabase/client';

export function generateDigitalSignatureText(userName: string, userRole: string): string {
  const date = new Date().toLocaleDateString('pt-BR');
  const time = new Date().toLocaleTimeString('pt-BR');
  const roleTranslation: Record<string, string> = {
    president: 'President<PERSON>',
    admin: '<PERSON><PERSON><PERSON><PERSON>',
    manager: '<PERSON><PERSON><PERSON>',
    coach: '<PERSON><PERSON><PERSON><PERSON>',
    medical: '<PERSON><PERSON><PERSON><PERSON>',
    staff: '<PERSON><PERSON><PERSON><PERSON>',
    player: '<PERSON><PERSON><PERSON>'
  };
  const translatedRole = roleTranslation[userRole] || userRole;
  return `Documento assinado digitalmente por: ${userName}\nData: ${date}\nHorário: ${time}\nConforme MP 2.200-2/2001`;
}

/**
 * Gera uma assinatura digital automática e faz upload para o Supabase Storage
 * @param clubId ID do clube
 * @param userName Nome do usuário
 * @param userRole Papel/função do usuário
 * @param identifier Identificador do arquivo (ex: 'alta_medica_123')
 * @returns URL pública da assinatura ou null em caso de erro
 */
export async function generateDigitalSignatureImage(
  clubId: number,
  userName: string,
  userRole: string,
  identifier: string
): Promise<string | null> {
  try {
    if (typeof document === 'undefined') {
      console.log('Canvas não disponível no servidor, pulando geração de assinatura automática');
      return null;
    }

    const signatureText = generateDigitalSignatureText(userName, userRole);

    const canvas = document.createElement('canvas');
    canvas.width = 450;
    canvas.height = 150;
    const ctx = canvas.getContext('2d');

    if (!ctx) return null;

    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    const lines = signatureText.split('\n');
    const lineHeight = 20;
    const startY = (canvas.height - lines.length * lineHeight) / 2 + lineHeight;

    lines.forEach((line, index) => {
      ctx.fillText(line, canvas.width / 2, startY + index * lineHeight);
    });

    const signatureDataUrl = canvas.toDataURL('image/png');
    const response = await fetch(signatureDataUrl);
    const blob = await response.blob();

    const fileName = `signatures/${clubId}/${identifier}_${Date.now()}.png`;
    const { error } = await supabase.storage
      .from('profileimages')
      .upload(fileName, blob, { contentType: 'image/png', upsert: true });

    if (error) {
      console.error('Erro ao fazer upload da assinatura automática:', error);
      return null;
    }

    const { data } = supabase.storage.from('profileimages').getPublicUrl(fileName);
    return data.publicUrl;
  } catch (err) {
    console.error('Erro ao gerar assinatura automática:', err);
    return null;
  }
}
