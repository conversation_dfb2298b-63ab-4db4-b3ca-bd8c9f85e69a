import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Download, Filter } from 'lucide-react';
import { startOfWeek, format, addWeeks, subWeeks, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { generateTrainingSchedulePDF } from '@/utils/trainingScheduleExporter';
import { useToast } from '@/hooks/use-toast';
import type { Training } from "@/api/api";
import type { ClubInfo } from "@/api/api";

interface TrainingScheduleExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainings: Training[];
  clubInfo: ClubInfo;
  categories: Array<{ id: number; name: string }>;
}

export function TrainingScheduleExportDialog({
  open,
  onOpenChange,
  trainings,
  clubInfo,
  categories
}: TrainingScheduleExportDialogProps) {
  const { toast } = useToast();
  
  // Estados do formulário
  const [selectedWeek, setSelectedWeek] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isExporting, setIsExporting] = useState(false);
  
  // Opções de semanas (4 semanas para trás e 8 para frente)
  const getWeekOptions = () => {
    const options = [];
    const today = new Date();
    
    // 4 semanas para trás
    for (let i = 4; i >= 1; i--) {
      const weekStart = startOfWeek(subWeeks(today, i), { weekStartsOn: 1 });
      const weekEnd = addDays(weekStart, 6);
      options.push({
        value: weekStart.toISOString(),
        label: `Semana de ${format(weekStart, 'dd/MM', { locale: ptBR })} a ${format(weekEnd, 'dd/MM', { locale: ptBR })}`
      });
    }
    
    // Semana atual
    const currentWeekStart = startOfWeek(today, { weekStartsOn: 1 });
    const currentWeekEnd = addDays(currentWeekStart, 6);
    options.push({
      value: currentWeekStart.toISOString(),
      label: `Semana atual (${format(currentWeekStart, 'dd/MM', { locale: ptBR })} a ${format(currentWeekEnd, 'dd/MM', { locale: ptBR })})`
    });
    
    // 8 semanas para frente
    for (let i = 1; i <= 8; i++) {
      const weekStart = startOfWeek(addWeeks(today, i), { weekStartsOn: 1 });
      const weekEnd = addDays(weekStart, 6);
      options.push({
        value: weekStart.toISOString(),
        label: `Semana de ${format(weekStart, 'dd/MM', { locale: ptBR })} a ${format(weekEnd, 'dd/MM', { locale: ptBR })}`
      });
    }
    
    return options;
  };
  
  const weekOptions = getWeekOptions();
  
  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Filtrar treinos por categoria se selecionada
      let filteredTrainings = trainings;
      if (selectedCategory && selectedCategory !== 'all') {
        const categoryName = categories.find(c => c.id.toString() === selectedCategory)?.name;
        filteredTrainings = trainings.filter(t => t.category_name === categoryName);
      }
      
      // Gerar nome do arquivo
      const weekStr = format(selectedWeek, 'dd-MM-yyyy', { locale: ptBR });
      const categoryStr = (selectedCategory && selectedCategory !== 'all') ? `-${categories.find(c => c.id.toString() === selectedCategory)?.name}` : '';
      const filename = `grade-treinos-${weekStr}${categoryStr}.pdf`;
      
      // Gerar PDF
      await generateTrainingSchedulePDF(
        { trainings: filteredTrainings },
        {
          weekStartDate: selectedWeek,
          categoryFilter: (selectedCategory && selectedCategory !== 'all') ? categories.find(c => c.id.toString() === selectedCategory)?.name : undefined,
          clubInfo
        },
        filename
      );
      
      toast({
        title: "Grade exportada com sucesso!",
        description: `O arquivo ${filename} foi baixado para seu computador.`,
      });
      
      onOpenChange(false);
    } catch (error) {
      console.error('Erro ao exportar grade:', error);
      toast({
        title: "Erro ao exportar grade",
        description: "Ocorreu um erro ao gerar o PDF. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Exportar Grade de Treinos
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="week-select">Semana</Label>
            <Select
              value={selectedWeek.toISOString()}
              onValueChange={(value) => setSelectedWeek(new Date(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione a semana" />
              </SelectTrigger>
              <SelectContent>
                {weekOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category-select">Categoria (opcional)</Label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger>
                <SelectValue placeholder="Todas as categorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as categorias</SelectItem>
                {categories
                  .filter(category => category.id && category.name && category.name.trim() !== '')
                  .map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="bg-muted p-3 rounded-md">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <Filter className="h-4 w-4" />
              Prévia da exportação:
            </div>
            <div className="text-sm">
              <div><strong>Período:</strong> {format(selectedWeek, 'dd/MM/yyyy', { locale: ptBR })} a {format(addDays(selectedWeek, 6), 'dd/MM/yyyy', { locale: ptBR })}</div>
              <div><strong>Categoria:</strong> {(selectedCategory && selectedCategory !== 'all') ? categories.find(c => c.id.toString() === selectedCategory)?.name : 'Todas'}</div>
              <div><strong>Formato:</strong> PDF Paisagem (A4)</div>
              <div><strong>Conteúdo:</strong> Data, Local, Horário, Manhã/Tarde</div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exportando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Exportar PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}