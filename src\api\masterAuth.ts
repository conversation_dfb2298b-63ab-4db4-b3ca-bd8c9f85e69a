import { supabase } from "@/integrations/supabase/client";

export interface MasterUser {
  id: string;
  organization_id: number | null;
  name: string;
  email: string;
  role: 'super_admin' | 'admin' | 'support' | 'viewer';
  permissions: Record<string, any>;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface MasterOrganization {
  id: number;
  name: string;
  email: string;
  phone?: string;
  document?: string;
  address: Record<string, any>;
  logo_url?: string;
  website?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MasterAuthResponse {
  user: any;
  masterUser: MasterUser;
  organization: MasterOrganization | null;
}

/**
 * Faz login de usuário master
 */
export const masterSignIn = async (email: string, password: string): Promise<MasterAuthResponse> => {
  try {
    // Data validation
    if (!email || email.trim().length === 0) {
      throw new Error('Email é obrigatório');
    }
    if (!password || password.trim().length === 0) {
      throw new Error('Senha é obrigatória');
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error('Email deve ter um formato válido');
    }

    console.log('🔐 Iniciando login master para:', email);
    
    // 1. Fazer login no Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      console.error('❌ Erro de autenticação:', authError);
      if (authError.message.includes('Invalid login credentials')) {
        throw new Error('Email ou senha incorretos');
      }
      if (authError.message.includes('Email not confirmed')) {
        throw new Error('Email não confirmado. Verifique sua caixa de entrada.');
      }
      throw new Error(`Erro de autenticação: ${authError.message}`);
    }

    if (!authData.user) {
      console.error('❌ Usuário não encontrado');
      throw new Error('Usuário não encontrado');
    }

    console.log('✅ Login no Auth realizado com sucesso. User ID:', authData.user.id);

    // 2. Buscar dados do usuário master
    const { data: masterUser, error: masterError } = await supabase
      .from('master_users')
      .select('*')
      .eq('id', authData.user.id)
      .eq('is_active', true)
      .single();

    if (masterError) {
      console.error('❌ Erro na consulta do usuário master:', masterError);
      await supabase.auth.signOut();
      if (masterError.code === 'PGRST116') {
        throw new Error('Erro: Tabela master_users não encontrada. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao verificar permissões: ${masterError.message}`);
    }

    if (!masterUser) {
      console.error('❌ Usuário não é master');
      await supabase.auth.signOut();
      throw new Error('Usuário não autorizado para área master');
    }

    console.log('✅ Usuário master encontrado:', masterUser);

    // 3. Buscar dados da organização (se existir)
    let organization = null;
    if (masterUser.organization_id) {
      // Confirmar sessão ativa antes de consultar a organização
      const { data: authSession } = await supabase.auth.getUser();
      if (!authSession.user) {
        throw new Error('Sessão de usuário não encontrada');
      }

      const { data: orgData, error: orgError } = await supabase
        .from('master_organizations')
        .select('*')
        .eq('id', masterUser.organization_id)
        .single();

      if (orgError) {
        console.error('❌ Erro ao buscar organização:', orgError);
        await supabase.auth.signOut();
        if (orgError.code === 'PGRST116') {
          throw new Error('Erro: Tabela master_organizations não encontrada. Verifique se a migração foi aplicada.');
        }
        throw new Error(`Erro ao buscar organização: ${orgError.message}`);
      }

      organization = orgData;
       // Atualiza metadata com organization_id para RLS
       const { error: metaError } = await supabase.auth.updateUser({
        data: { organization_id: masterUser.organization_id }
      });
      if (metaError) {
        console.warn('⚠️ Erro ao atualizar organization_id no token:', metaError);
      }
    }

    console.log('✅ Organização encontrada:', organization);

    // 4. Atualizar último login
    supabase
      .from('master_users')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', authData.user.id)
      .then(({ error }) => {
        if (error) {
          console.warn('⚠️ Erro ao atualizar last_login_at:', error);
        } else {
          console.log('✅ Last login atualizado');
        }
      });

    console.log('✅ Login master concluído com sucesso!');

    return {
      user: authData.user,
      masterUser: masterUser as MasterUser,
      organization: organization as MasterOrganization | null
    };
  } catch (error: any) {
    console.error('💥 Erro no login master:', error);
    throw new Error(error.message || 'Erro ao fazer login');
  }
};

/**
 * Faz logout do usuário master
 */
export const masterSignOut = async (): Promise<void> => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    localStorage.removeItem('masterUser');
    localStorage.removeItem('masterOrganization');
  } catch (error: any) {
    console.error('Erro no logout master:', error);
    throw new Error(error.message || 'Erro ao fazer logout');
  }
};

/**
 * Obtém o usuário master atual
 */
export const getCurrentMasterUser = async (): Promise<MasterAuthResponse | null> => {
  try {
    console.log('🔍 getCurrentMasterUser: Verificando sessão do usuário...');
    
    const { data: { user }, error: sessionError } = await supabase.auth.getUser();

    if (sessionError) {
      console.log('❌ getCurrentMasterUser: Erro na sessão:', sessionError.message);
      return null;
    }

    if (!user) {
      console.log('ℹ️ getCurrentMasterUser: Nenhum usuário logado');
      return null;
    }

    console.log('✅ getCurrentMasterUser: Usuário encontrado, buscando dados master...');

    const { data: masterUser, error: masterError } = await supabase
      .from('master_users')
      .select(`
        *,
        master_organizations:organization_id (*)
      `)
      .eq('id', user.id)
      .eq('is_active', true)
      .single();

    if (masterError) {
      console.log('❌ getCurrentMasterUser: Erro ao buscar usuário master:', masterError.message);
      // Se for erro de não encontrado, não é um erro crítico
      if (masterError.code === 'PGRST116') {
        console.log('ℹ️ getCurrentMasterUser: Usuário não é master');
        return null;
      }
      return null;
    }

    if (!masterUser) {
      console.log('ℹ️ getCurrentMasterUser: Usuário não é master ou está inativo');
      return null;
    }

    console.log('✅ getCurrentMasterUser: Dados master encontrados');

    const currentOrgId = (user.user_metadata as any)?.organization_id;
    if (masterUser.organization_id && currentOrgId !== masterUser.organization_id) {
      const { error: metaError } = await supabase.auth.updateUser({
        data: { organization_id: masterUser.organization_id }
      });
      if (metaError) {
        console.warn('⚠️ getCurrentMasterUser: erro ao atualizar organization_id no token:', metaError);
      }
    }

    return {
      user,
      masterUser: masterUser as MasterUser,
      organization: (masterUser as any).master_organizations as MasterOrganization
    };
  } catch (error: any) {
    console.error('💥 getCurrentMasterUser: Erro inesperado:', error);
    return null;
  }
};

/**
 * Verifica se o usuário atual tem uma permissão específica
 */
export const hasMasterPermission = (
  permission: string,
  masterUser?: MasterUser
): boolean => {
  if (!masterUser) return false;

  if (masterUser.role === 'super_admin') {
    return true;
  }

  const permissions = masterUser.permissions || {};
  const permissionParts = permission.split('.');
  let currentLevel = permissions;

  for (const part of permissionParts) {
    if (typeof currentLevel !== 'object' || currentLevel === null) {
      return false;
    }
    currentLevel = currentLevel[part];
  }

  return Boolean(currentLevel);
};

/**
 * Verifica se o usuário tem um dos roles especificados
 */
export const hasMasterRole = (
  roles: string[],
  masterUser?: MasterUser
): boolean => {
  if (!masterUser) return false;
  return roles.includes(masterUser.role);
};

/**
 * Atualiza os dados do usuário master
 */
export const updateMasterUser = async (userId: string, updates: Partial<Pick<MasterUser, 'name' | 'email'>>) => {
  try {
    const { data, error } = await supabase
      .from('master_users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data as MasterUser;
  } catch (error: any) {
    console.error('Erro ao atualizar usuário master:', error);
    throw new Error(error.message || 'Erro desconhecido ao atualizar usuário');
  }
};