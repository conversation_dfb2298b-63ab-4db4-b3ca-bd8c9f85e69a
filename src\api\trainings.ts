// Funções relacionadas a Trainings serão migradas para cá a partir do api.ts

import { supabase } from "@/integrations/supabase/client";
import { getCategoryPlayers } from "./categories";
import { isWithinInterval, parseISO, startOfDay, endOfDay } from "date-fns";

// Exporta tipos necessários para re-export em api.ts
export type Training = {
  id: number;
  club_id: number;
  name: string;
  type: string;
  date: string;
  time?: string;
  start_time?: string;
  end_time?: string;
  location: string;
  status: "concluído" | "em andamento" | "agendado";
  progress: number;
  coach: string;
  participants: number;
  description: string;
  category_id?: number;
  category_name?: string;
  images?: TrainingImage[];
  required_materials?: string;
  summary?: string; // Resumo do treino finalizado
};

export type TrainingGoal = {
  id: number;
  club_id: number;
  name: string;
  description: string;
  type: string;
  target_value: number;
  current_value: number;
};

export type TrainingFinalization = {
  id: number;
  training_id: number;
  summary: string;
  description: string;
};

export type TrainingExercise = {
  id: number;
  training_id: number;
  exercise_id: number;
  order_in_training: number;
  notes?: string;
  exercises?: Exercise;
};

export type Exercise = {
  id: number;
  club_id: number;
  name: string;
  description?: string;
  category?: string;
  difficulty?: string;
  created_at?: string;
};

export type PhysicalProgress = {
  id: number;
  club_id: number;
  date: string;
  weight: number;
  height: number;
  body_fat: number;
};

export type TrainingImage = {
  id: number;
  club_id: number;
  training_id: number;
  image_url: string;
  image_order: number;
  created_at?: string;
};

// Funções para Trainings
export async function getTrainings(clubId: number): Promise<Training[]> {
  // Buscar treinos com informações de categoria
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao buscar treinos:", error);
    throw new Error(`Erro ao buscar treinos: ${error.message}`);
  }

  // Buscar as associações de jogadores para todos os treinos
  const { data: trainingPlayersData, error: trainingPlayersError } = await supabase
    .from("training_players")
    .select("training_id, player_id")
    .eq("club_id", clubId);

  if (trainingPlayersError) {
    console.error("Erro ao buscar associações de jogadores:", trainingPlayersError);
    // Não lançamos erro aqui para não impedir a exibição dos treinos
  }

  // Criar um mapa de treino_id -> lista de player_ids
  const trainingPlayersMap: Record<number, string[]> = {};
  if (trainingPlayersData) {
    trainingPlayersData.forEach(tp => {
      if (!trainingPlayersMap[tp.training_id]) {
        trainingPlayersMap[tp.training_id] = [];
      }
      trainingPlayersMap[tp.training_id].push(tp.player_id);
    });
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants:
        trainingPlayersMap[item.id]?.length ?? parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      player_ids: trainingPlayersMap[item.id] || [], // Adicionar os IDs dos jogadores associados
      required_materials: item.required_materials || '',
      summary: notesParts[9] || '' // Resumo do treino finalizado
    } as Training;
  });

  // Não filtrar treinos concluídos automaticamente - deixar que a interface controle a exibição
  // Buscar imagens para cada treino
  const trainingsWithImages = await Promise.all(
    trainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar treinos considerando data e horário completos.
  // Treinos futuros ou em andamento devem aparecer antes dos que já terminaram.
  const now = new Date();

  return trainingsWithImages.sort((a, b) => {
    const startA = new Date(`${a.date}T${a.start_time || "00:00"}`);
    const endA = new Date(`${a.date}T${a.end_time || a.start_time || "23:59"}`);
    const startB = new Date(`${b.date}T${b.start_time || "00:00"}`);
    const endB = new Date(`${b.date}T${b.end_time || b.start_time || "23:59"}`);

    const aUpcoming = endA >= now;
    const bUpcoming = endB >= now;

    if (aUpcoming && !bUpcoming) return -1;
    if (!aUpcoming && bUpcoming) return 1;

    // Se ambos são futuros ou ambos passados, ordenar por início
    if (startA.getTime() !== startB.getTime()) {
      return startA.getTime() - startB.getTime();
    }

    return a.id - b.id;
  });
}

// Nova função para buscar treinos concluídos com filtros de ano e mês
export async function getCompletedTrainings(
  clubId: number,
  startDate?: Date,
  endDate?: Date
): Promise<Training[]> {
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao buscar treinos concluídos:", error);
    throw new Error(`Erro ao buscar treinos concluídos: ${error.message}`);
  }

  // Buscar as associações de jogadores para todos os treinos
  const { data: trainingPlayersData, error: trainingPlayersError } = await supabase
    .from("training_players")
    .select("training_id, player_id")
    .eq("club_id", clubId);

  if (trainingPlayersError) {
    console.error("Erro ao buscar associações de jogadores:", trainingPlayersError);
    // Não lançamos erro aqui para não impedir a exibição dos treinos
  }

  // Criar um mapa de treino_id -> lista de player_ids
  const trainingPlayersMap: Record<number, string[]> = {};
  if (trainingPlayersData) {
    trainingPlayersData.forEach(tp => {
      if (!trainingPlayersMap[tp.training_id]) {
        trainingPlayersMap[tp.training_id] = [];
      }
      trainingPlayersMap[tp.training_id].push(tp.player_id);
    });
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(9).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants:
        trainingPlayersMap[item.id]?.length ?? parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      player_ids: trainingPlayersMap[item.id] || [],
      required_materials: item.required_materials || '',
      summary: notesParts[9] || '' // Resumo do treino finalizado
    } as Training;
  });

  // Filtrar apenas treinos concluídos
  let completedTrainings = trainings.filter(training => training.status === "concluído");

  // Aplicar filtro de intervalo de datas se fornecido
  if (startDate || endDate) {
    const start = startDate ? startOfDay(startDate) : undefined;
    const end = endDate ? endOfDay(endDate) : undefined;
    completedTrainings = completedTrainings.filter(training => {
      const trainingDate = parseISO(training.date);
      return isWithinInterval(trainingDate, {
        start: start || new Date(-8640000000000000),
        end: end || new Date(8640000000000000)
      });
    });
  }

  // Buscar imagens para cada treino concluído
  const completedTrainingsWithImages = await Promise.all(
    completedTrainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar por data (mais recentes primeiro)
  return completedTrainingsWithImages.sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime();
  });
}


export async function createTraining(clubId: number, training: Omit<Training, "id">): Promise<Training> {
  const notes = `${training.name}|${training.type}|${training.time}|${training.location}|${training.status}|${training.progress}|${training.coach}|${training.participants}|${training.description}|${training.summary || ''}`;

  // Inserir o treino com a categoria
  const { data, error } = await supabase
    .from("trainings")
    .insert({
      club_id: clubId,
      date: training.date,
      notes: notes,
      category_id: training.category_id,
      required_materials: training.required_materials || null
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar treino:", error);
    throw new Error(`Erro ao criar treino: ${error.message}`);
  }

  // Variáveis para armazenar dados da categoria
  let categoryName = "";
  let categoryPlayers: any[] = [];
  let playerIdsInCategory: string[] = [];

  // Buscar informações da categoria e jogadores em uma única operação
  if (training.category_id) {
    try {
      // 1. Buscar nome da categoria
      const { data: categoryData } = await supabase
        .from("categories")
        .select("name")
        .eq("id", training.category_id)
        .single();

      if (categoryData) {
        categoryName = categoryData.name;
      }

      // 2. Buscar IDs dos jogadores na categoria
      const { data: playerCategoriesData } = await supabase
        .from("player_categories")
        .select("player_id")
        .eq("club_id", clubId)
        .eq("category_id", training.category_id);

      if (playerCategoriesData && playerCategoriesData.length > 0) {
        playerIdsInCategory = playerCategoriesData.map(item => item.player_id);

        // 3. Buscar detalhes dos jogadores
        const { data: playersData } = await supabase
          .from("players")
          .select("*")
          .in("id", playerIdsInCategory);

        if (playersData) {
          categoryPlayers = playersData;
        }
      }

      // 4. Associar jogadores ao treino
      if (categoryPlayers.length > 0) {
        const trainingPlayersData = categoryPlayers.map(player => ({
          club_id: clubId,
          training_id: data.id,
          player_id: player.id
        }));

        // Inserir em lote
        const { error: batchError } = await supabase
          .from("training_players")
          .insert(trainingPlayersData);

        if (batchError) {
          console.error("Erro ao associar jogadores ao treino:", batchError);
          console.error("Detalhes do erro:", {
            code: batchError.code,
            message: batchError.message,
            details: batchError.details,
            hint: batchError.hint
          });
          // Não lançamos erro aqui para não impedir a criação do treino
        } else {
          console.log(`✅ Treino ${data.id}: ${trainingPlayersData.length} jogadores associados com sucesso`);
        }
      }

      // 5. Criar notificações para os jogadores com contas
      const { data: playersWithAccounts } = await supabase
        .from("players")
        .select("id, name, user_id")
        .eq("club_id", clubId)
        .not("user_id", "is", null)
        .in("id", playerIdsInCategory);

      if (playersWithAccounts && playersWithAccounts.length > 0) {
        // Criar notificações para cada jogador
        for (const player of playersWithAccounts) {
          if (player.user_id) {
            await supabase.from("notifications").insert({
              club_id: clubId,
              user_id: player.user_id,
              title: "Novo treino agendado",
              message: `Um novo treino de ${training.type} foi agendado para ${training.date} às ${training.time.split('-')[0]}.`,
              type: "training",
              reference_id: data.id.toString(),
              reference_type: "training"
            });
          }
        }
      }
    } catch (error) {
      console.error("Erro ao processar categoria e jogadores:", error);
      // Não lançamos erro aqui para não impedir a criação do treino
    }
  }

  return {
    id: data.id,
    club_id: data.club_id,
    name: training.name,
    type: training.type,
    date: data.date,
    time: training.time,
    location: training.location,
    status: training.status,
    progress: training.progress,
    coach: training.coach,
    participants: training.participants,
    description: training.description,
    category_id: training.category_id,
    category_name: categoryName
  } as Training;
}

export async function updateTraining(clubId: number, id: number, training: Partial<Training>): Promise<Training> {
  // Primeiro precisamos obter o treino atual
  const { data: existingTraining, error: fetchError } = await supabase
    .from("trainings")
    .select("*, categories:category_id(id, name)")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (fetchError) {
    console.error("Erro ao buscar treino para atualização:", fetchError);
    throw new Error(`Erro ao buscar treino para atualização: ${fetchError.message}`);
  }

  // Parseamos o notes existente (agora com 10 partes incluindo o resumo)
  const existingNotesParts = existingTraining.notes?.split('|') || Array(10).fill('');

  // Atualizamos apenas os campos fornecidos
  const updatedNotesParts = [...existingNotesParts];
  if (training.name) updatedNotesParts[0] = training.name;
  if (training.type) updatedNotesParts[1] = training.type;

  // Handle time field updates
  if (training.time) {
    // If time is directly provided, use it
    updatedNotesParts[2] = training.time;
  } else if (training.start_time && training.end_time) {
    // If start_time and end_time are provided, combine them
    updatedNotesParts[2] = `${training.start_time}-${training.end_time}`;
  }

  if (training.location) updatedNotesParts[3] = training.location;
  if (training.status) updatedNotesParts[4] = training.status;
  if (training.progress !== undefined) updatedNotesParts[5] = String(training.progress);
  if (training.coach) updatedNotesParts[6] = training.coach;
  if (training.participants !== undefined) updatedNotesParts[7] = String(training.participants);
  if (training.description) updatedNotesParts[8] = training.description;
  if (training.summary !== undefined) updatedNotesParts[9] = training.summary;

  const updatedNotes = updatedNotesParts.join('|');

  // Preparar dados para atualização
  const updateData: any = {
    date: training.date || existingTraining.date,
    notes: updatedNotes
  };

  // Se a categoria foi alterada, atualizar o campo category_id
  if (training.category_id !== undefined) {
    updateData.category_id = training.category_id;
  }

  // Se os materiais necessários foram alterados, atualizar o campo required_materials
  if (training.required_materials !== undefined) {
    updateData.required_materials = training.required_materials;
  }

  // Atualizar o treino
  const { data, error } = await supabase
    .from("trainings")
    .update(updateData)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar treino:", error);
    throw new Error(`Erro ao atualizar treino: ${error.message}`);
  }

  // Se a categoria foi alterada, atualizar os jogadores associados
  let categoryName = existingTraining.categories?.name || "";

  if (training.category_id !== undefined && training.category_id !== existingTraining.category_id) {
    try {
      // Remover associações existentes
      await supabase
        .from("training_players")
        .delete()
        .eq("training_id", id)
        .eq("club_id", clubId);

      // Se a nova categoria não for nula, associar os jogadores da nova categoria
      if (training.category_id) {
        // 1. Buscar nome da categoria
        const { data: categoryData } = await supabase
          .from("categories")
          .select("name")
          .eq("id", training.category_id)
          .single();

        if (categoryData) {
          categoryName = categoryData.name;
        }

        // 2. Buscar IDs dos jogadores na categoria
        const { data: playerCategoriesData } = await supabase
          .from("player_categories")
          .select("player_id")
          .eq("club_id", clubId)
          .eq("category_id", training.category_id);

        if (playerCategoriesData && playerCategoriesData.length > 0) {
          const playerIdsInCategory = playerCategoriesData.map(item => item.player_id);

          // 3. Buscar detalhes dos jogadores
          const { data: playersData } = await supabase
            .from("players")
            .select("*")
            .in("id", playerIdsInCategory);

          if (playersData && playersData.length > 0) {
            // 4. Associar jogadores ao treino
            const trainingPlayersData = playersData.map(player => ({
              club_id: clubId,
              training_id: id,
              player_id: player.id
            }));

            // Inserir em lote
            await supabase
              .from("training_players")
              .insert(trainingPlayersData);
          }
        }
      } else {
        categoryName = "";
      }
    } catch (error) {
      console.error("Erro ao atualizar jogadores do treino:", error);
      // Não lançamos erro aqui para não impedir a atualização do treino
    }
  }

  return {
    id: data.id,
    club_id: data.club_id,
    name: updatedNotesParts[0],
    type: updatedNotesParts[1],
    date: data.date,
    time: updatedNotesParts[2],
    location: updatedNotesParts[3],
    status: updatedNotesParts[4] as "concluído" | "em andamento" | "agendado",
    progress: parseInt(updatedNotesParts[5]),
    coach: updatedNotesParts[6],
    participants: parseInt(updatedNotesParts[7]),
    description: updatedNotesParts[8],
    category_id: training.category_id !== undefined ? training.category_id : existingTraining.category_id,
    category_name: categoryName,
    required_materials: training.required_materials !== undefined ? training.required_materials : existingTraining.required_materials,
    summary: updatedNotesParts[9]
  } as Training;
}

export async function deleteTraining(clubId: number, id: number): Promise<boolean> {
  try {
    // Primeiro, excluir registros relacionados

    // 1. Excluir associações com jogadores
    await supabase
      .from("training_players")
      .delete()
      .eq("training_id", id)
      .eq("club_id", clubId);

    // 2. Excluir imagens do treino
    await supabase
      .from("training_images")
      .delete()
      .eq("training_id", id)
      .eq("club_id", clubId);

    // 3. Excluir exercícios do treino
    await supabase
      .from("training_exercises")
      .delete()
      .eq("training_id", id);

    // Por fim, excluir o treino
    const { error } = await supabase
      .from("trainings")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      console.error("Erro ao deletar treino:", error);
      throw new Error(`Erro ao deletar treino: ${error.message}`);
    }

    return true;
  } catch (error) {
    console.error("Erro ao deletar treino e registros relacionados:", error);
    throw new Error(`Erro ao deletar treino e registros relacionados: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Função para corrigir treinamentos existentes que não têm jogadores associados
export async function fixTrainingsWithoutPlayers(clubId: number): Promise<{ fixed: number; errors: string[] }> {
  console.log(`Iniciando correção de treinamentos sem jogadores para o clube ${clubId}`);

  const errors: string[] = [];
  let fixed = 0;

  try {
    // 1. Buscar todos os treinamentos do clube
    const { data: trainings, error: trainingsError } = await supabase
      .from("trainings")
      .select("id, category_id, notes")
      .eq("club_id", clubId);

    if (trainingsError) {
      throw new Error(`Erro ao buscar treinamentos: ${trainingsError.message}`);
    }

    // 2. Buscar associações existentes
    const { data: existingAssociations, error: associationsError } = await supabase
      .from("training_players")
      .select("training_id")
      .eq("club_id", clubId);

    if (associationsError) {
      throw new Error(`Erro ao buscar associações existentes: ${associationsError.message}`);
    }

    const trainingsWithAssociations = new Set(existingAssociations?.map(ea => ea.training_id) || []);

    // 3. Processar cada treinamento
    for (const training of trainings || []) {
      try {
        // Verificar se o treino já tem jogadores associados
        if (trainingsWithAssociations.has(training.id)) {
          continue;
        }

        // Verificar se o treino está concluído (não associar jogadores a treinos concluídos)
        const notesParts = training.notes?.split('|') || [];
        const status = notesParts[4] || 'agendado';
        if (status === 'concluído') {
          continue;
        }

        // Se tem categoria, associar jogadores
        if (training.category_id) {
          console.log(`Corrigindo treino ID ${training.id} (categoria ${training.category_id})...`);

          // Buscar jogadores da categoria
          const { data: playerCategories, error: playerCategoriesError } = await supabase
            .from("player_categories")
            .select("player_id")
            .eq("club_id", clubId)
            .eq("category_id", training.category_id);

          if (playerCategoriesError) {
            errors.push(`Erro ao buscar jogadores da categoria ${training.category_id}: ${playerCategoriesError.message}`);
            continue;
          }

          if (playerCategories && playerCategories.length > 0) {
            // Criar associações
            const associations = playerCategories.map(pc => ({
              club_id: clubId,
              training_id: training.id,
              player_id: pc.player_id
            }));

            const { error: insertError } = await supabase
              .from("training_players")
              .insert(associations);

            if (insertError) {
              const errorDetails = {
                code: insertError.code,
                message: insertError.message,
                details: insertError.details,
                hint: insertError.hint
              };
              console.error(`Erro ao associar jogadores ao treino ${training.id}:`, errorDetails);

              // Identificar tipo de erro para melhor diagnóstico
              let errorType = "Erro desconhecido";
              if (insertError.code === "42501") {
                errorType = "Erro de permissão (RLS)";
              } else if (insertError.code === "23505") {
                errorType = "Violação de chave única (jogador já associado)";
              } else if (insertError.code === "23503") {
                errorType = "Violação de chave estrangeira (dados inválidos)";
              }

              errors.push(`Treino ${training.id}: ${errorType} - ${insertError.message}`);
            } else {
              console.log(`✅ Treino ${training.id}: ${associations.length} jogadores associados`);
              fixed++;
            }
          }
        }
      } catch (error) {
        errors.push(`Erro ao processar treino ${training.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    }

    console.log(`Correção concluída! ${fixed} treinamentos corrigidos.`);
    return { fixed, errors };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error("Erro durante a correção:", errorMessage);
    throw new Error(`Erro durante a correção: ${errorMessage}`);
  }
}

// Função para testar permissões RLS na tabela training_players
export async function testTrainingPlayersPermissions(clubId: number): Promise<{ canRead: boolean; canWrite: boolean; errors: string[] }> {
  const errors: string[] = [];
  let canRead = false;
  let canWrite = false;

  try {
    // Teste 1: Verificar se consegue ler da tabela training_players
    console.log("Testando permissão de leitura na tabela training_players...");
    const { data: readData, error: readError } = await supabase
      .from("training_players")
      .select("id")
      .eq("club_id", clubId)
      .limit(1);

    if (readError) {
      errors.push(`Erro de leitura: ${readError.message} (código: ${readError.code})`);
      console.error("Erro ao ler training_players:", readError);
    } else {
      canRead = true;
      console.log("✅ Permissão de leitura OK");
    }

    // Teste 2: Verificar se consegue escrever na tabela (teste com dados fictícios)
    console.log("Testando permissão de escrita na tabela training_players...");

    // Primeiro, buscar um treino e jogador válidos para teste
    const { data: testTraining } = await supabase
      .from("trainings")
      .select("id")
      .eq("club_id", clubId)
      .limit(1)
      .single();

    const { data: testPlayer } = await supabase
      .from("players")
      .select("id")
      .eq("club_id", clubId)
      .limit(1)
      .single();

    if (testTraining && testPlayer) {
      // Verificar se a associação já existe
      const { data: existingAssociation } = await supabase
        .from("training_players")
        .select("id")
        .eq("club_id", clubId)
        .eq("training_id", testTraining.id)
        .eq("player_id", testPlayer.id)
        .single();

      if (!existingAssociation) {
        // Tentar inserir uma associação de teste
        const { data: insertData, error: insertError } = await supabase
          .from("training_players")
          .insert({
            club_id: clubId,
            training_id: testTraining.id,
            player_id: testPlayer.id
          })
          .select()
          .single();

        if (insertError) {
          errors.push(`Erro de escrita: ${insertError.message} (código: ${insertError.code})`);
          console.error("Erro ao escrever em training_players:", insertError);
        } else {
          canWrite = true;
          console.log("✅ Permissão de escrita OK");

          // Limpar o teste - remover a associação criada
          await supabase
            .from("training_players")
            .delete()
            .eq("id", insertData.id);
        }
      } else {
        canWrite = true; // Se já existe, assumimos que a escrita funciona
        console.log("✅ Permissão de escrita OK (associação já existe)");
      }
    } else {
      errors.push("Não foi possível encontrar treino ou jogador para teste de escrita");
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    errors.push(`Erro durante teste de permissões: ${errorMessage}`);
    console.error("Erro durante teste de permissões:", error);
  }

  return { canRead, canWrite, errors };
}

// Função para diagnosticar problemas específicos com treinamentos de um jogador
export async function debugPlayerTrainings(clubId: number, playerId: string): Promise<{
  playerExists: boolean;
  playerCategories: any[];
  allTrainings: any[];
  playerTrainingAssociations: any[];
  processedTrainings: any[];
  filteredTrainings: any[];
  errors: string[];
}> {
  const errors: string[] = [];

  try {
    console.log(`🔍 Iniciando diagnóstico para jogador ${playerId} no clube ${clubId}`);

    // 1. Verificar se o jogador existe
    const { data: player, error: playerError } = await supabase
      .from("players")
      .select("id, name, status")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (playerError) {
      errors.push(`Erro ao buscar jogador: ${playerError.message}`);
      return {
        playerExists: false,
        playerCategories: [],
        allTrainings: [],
        playerTrainingAssociations: [],
        processedTrainings: [],
        filteredTrainings: [],
        errors
      };
    }

    console.log(`✅ Jogador encontrado: ${player.name} (Status: ${player.status})`);

    // 2. Buscar categorias do jogador
    const { data: playerCategories, error: categoriesError } = await supabase
      .from("player_categories")
      .select("category_id, categories(id, name)")
      .eq("club_id", clubId)
      .eq("player_id", playerId);

    if (categoriesError) {
      errors.push(`Erro ao buscar categorias do jogador: ${categoriesError.message}`);
    }

    console.log(`📋 Categorias do jogador:`, playerCategories);

    // 3. Buscar todos os treinamentos do clube
    const { data: allTrainings, error: trainingsError } = await supabase
      .from("trainings")
      .select(`
        id,
        date,
        notes,
        category_id,
        categories:category_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId)
      .order("date", { ascending: false });

    if (trainingsError) {
      errors.push(`Erro ao buscar treinamentos: ${trainingsError.message}`);
    }

    console.log(`🏃 Total de treinamentos no clube: ${allTrainings?.length || 0}`);

    // 4. Buscar associações do jogador com treinamentos
    const { data: playerTrainingAssociations, error: associationsError } = await supabase
      .from("training_players")
      .select("training_id, created_at")
      .eq("club_id", clubId)
      .eq("player_id", playerId);

    if (associationsError) {
      errors.push(`Erro ao buscar associações: ${associationsError.message}`);
    }

    console.log(`🔗 Associações do jogador: ${playerTrainingAssociations?.length || 0}`);

    // 5. Simular o processamento da função getTrainings
    const trainingPlayersMap: { [key: number]: string[] } = {};

    // Buscar todas as associações para simular getTrainings
    const { data: allAssociations } = await supabase
      .from("training_players")
      .select("training_id, player_id")
      .eq("club_id", clubId);

    allAssociations?.forEach(tp => {
      if (!trainingPlayersMap[tp.training_id]) {
        trainingPlayersMap[tp.training_id] = [];
      }
      trainingPlayersMap[tp.training_id].push(tp.player_id);
    });

    const processedTrainings = allTrainings?.map(item => {
      const notesParts = item.notes?.split('|') || Array(10).fill('');
      return {
        id: item.id,
        name: notesParts[0] || 'Treino',
        date: item.date,
        start_time: notesParts[2]?.split('-')[0]?.trim() || '10:00',
        status: notesParts[4] || 'agendado',
        category_id: item.category_id,
        category_name: item.categories?.name,
        player_ids: trainingPlayersMap[item.id] || [],
        hasPlayerAssociated: (trainingPlayersMap[item.id] || []).includes(playerId)
      };
    }) || [];

    console.log(`⚙️ Treinamentos processados: ${processedTrainings.length}`);

    // 6. Aplicar filtros do perfil do jogador
    const now = new Date();
    const filteredTrainings = processedTrainings.filter(t => {
      // Verificar se o jogador está associado ao treino
      if (!t.player_ids || !Array.isArray(t.player_ids) || !t.player_ids.includes(playerId)) {
        console.log(`❌ Treino ${t.id} (${t.name}): Jogador não associado. Player IDs: [${t.player_ids.join(', ')}]`);
        return false;
      }

      // Filtrar apenas treinos futuros
      const trainingDateTime = new Date(`${t.date}T${t.start_time || '00:00'}`);
      const isFuture = trainingDateTime >= now;
      const isNotCompleted = t.status !== "concluído";

      console.log(`🔍 Treino ${t.id} (${t.name}): Data: ${t.date}, Hora: ${t.start_time}, Status: ${t.status}`);
      console.log(`   - Data/Hora completa: ${trainingDateTime.toISOString()}`);
      console.log(`   - Agora: ${now.toISOString()}`);
      console.log(`   - É futuro: ${isFuture}, Não concluído: ${isNotCompleted}`);

      const shouldInclude = isFuture && isNotCompleted;
      console.log(`   - ${shouldInclude ? '✅ INCLUÍDO' : '❌ EXCLUÍDO'}`);

      return shouldInclude;
    });

    console.log(`🎯 Treinamentos filtrados para o jogador: ${filteredTrainings.length}`);

    return {
      playerExists: true,
      playerCategories: playerCategories || [],
      allTrainings: allTrainings || [],
      playerTrainingAssociations: playerTrainingAssociations || [],
      processedTrainings,
      filteredTrainings,
      errors
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    errors.push(`Erro durante diagnóstico: ${errorMessage}`);
    console.error("Erro durante diagnóstico:", error);

    return {
      playerExists: false,
      playerCategories: [],
      allTrainings: [],
      playerTrainingAssociations: [],
      processedTrainings: [],
      filteredTrainings: [],
      errors
    };
  }
}

// Função para corrigir datas inválidas nos treinamentos
export async function fixInvalidTrainingDates(clubId: number): Promise<{ fixed: number; errors: string[] }> {
  console.log(`Iniciando correção de datas inválidas para o clube ${clubId}`);

  const errors: string[] = [];
  let fixed = 0;

  try {
    // Buscar todos os treinamentos do clube
    const { data: trainings, error: trainingsError } = await supabase
      .from("trainings")
      .select("id, date, notes")
      .eq("club_id", clubId);

    if (trainingsError) {
      throw new Error(`Erro ao buscar treinamentos: ${trainingsError.message}`);
    }

    const currentYear = new Date().getFullYear();

    for (const training of trainings || []) {
      try {
        const trainingDate = new Date(training.date);
        const year = trainingDate.getFullYear();

        // Verificar se o ano está muito no passado (antes de 2020) ou muito no futuro (depois de 2030)
        if (year < 2020 || year > 2030) {
          console.log(`🔧 Corrigindo data inválida do treino ${training.id}: ${training.date} (ano ${year})`);

          // Tentar corrigir a data assumindo que foi um erro de digitação
          let correctedDate = training.date;

          if (year < 2020) {
            // Se o ano está muito no passado, assumir que deveria ser o ano atual
            correctedDate = training.date.replace(/^\d{4}/, currentYear.toString());
          } else if (year > 2030) {
            // Se o ano está muito no futuro, assumir que deveria ser o ano atual
            correctedDate = training.date.replace(/^\d{4}/, currentYear.toString());
          }

          // Verificar se a data corrigida é válida
          const correctedDateObj = new Date(correctedDate);
          if (!isNaN(correctedDateObj.getTime())) {
            // Atualizar no banco de dados
            const { error: updateError } = await supabase
              .from("trainings")
              .update({ date: correctedDate })
              .eq("id", training.id)
              .eq("club_id", clubId);

            if (updateError) {
              errors.push(`Erro ao corrigir data do treino ${training.id}: ${updateError.message}`);
            } else {
              console.log(`✅ Data do treino ${training.id} corrigida: ${training.date} → ${correctedDate}`);
              fixed++;
            }
          } else {
            errors.push(`Data corrigida inválida para treino ${training.id}: ${correctedDate}`);
          }
        }
      } catch (error) {
        errors.push(`Erro ao processar treino ${training.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    }

    console.log(`Correção de datas concluída! ${fixed} treinamentos corrigidos.`);
    return { fixed, errors };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error("Erro durante a correção de datas:", errorMessage);
    throw new Error(`Erro durante a correção de datas: ${errorMessage}`);
  }
}

// Função alternativa para buscar treinamentos do jogador - IGNORA A LÓGICA ATUAL
export async function getPlayerTrainingsAlternative(clubId: number, playerId: string): Promise<Training[]> {
  console.log(`🔄 Buscando treinamentos alternativos para jogador ${playerId}`);

  try {
    // Buscar diretamente da tabela training_players
    const { data: playerTrainings, error: playerTrainingsError } = await supabase
      .from("training_players")
      .select(`
        training_id,
        trainings:training_id (
          id,
          club_id,
          date,
          notes,
          category_id,
          required_materials,
          categories:category_id (
            id,
            name
          )
        )
      `)
      .eq("club_id", clubId)
      .eq("player_id", playerId);

    if (playerTrainingsError) {
      console.error("Erro ao buscar treinamentos do jogador:", playerTrainingsError);
      throw new Error(`Erro ao buscar treinamentos do jogador: ${playerTrainingsError.message}`);
    }

    console.log(`📊 Associações encontradas: ${playerTrainings?.length || 0}`);

    if (!playerTrainings || playerTrainings.length === 0) {
      return [];
    }

    // Processar os treinamentos
    const now = new Date();
    const processedTrainings = playerTrainings
      .filter(pt => pt.trainings) // Garantir que o treino existe
      .map(pt => {
        const training = pt.trainings;
        const notesParts = training.notes?.split('|') || Array(10).fill('');
        const timeStr = notesParts[2] || '10:00';
        const timeComponents = timeStr.split('-');
        const startTime = timeComponents[0]?.trim() || '10:00';
        const endTime = timeComponents.length > 1 ? timeComponents[1]?.trim() : '';

        return {
          id: training.id,
          club_id: training.club_id,
          name: notesParts[0] || 'Treino',
          type: notesParts[1] || 'geral',
          date: training.date,
          time: timeStr,
          start_time: startTime,
          end_time: endTime,
          location: notesParts[3] || 'Campo Principal',
          status: notesParts[4] || 'agendado',
          progress: parseInt(notesParts[5] || '0'),
          coach: notesParts[6] || 'Técnico',
          participants: parseInt(notesParts[7] || '0'),
          description: notesParts[8] || '',
          category_id: training.category_id,
          category_name: training.categories?.name,
          player_ids: [playerId], // Sabemos que este jogador está associado
          required_materials: training.required_materials || '',
          summary: notesParts[9] || ''
        } as Training;
      })
      .filter(training => {
        // Filtrar apenas treinos futuros e não concluídos
        const trainingDateTime = new Date(`${training.date}T${training.start_time}`);
        
        if (isNaN(trainingDateTime.getTime())) {
          console.warn(
            `⚠️ Ignorando treino ${training.id} com data/hora inválida: ${training.date} ${training.start_time}`
          );
          return false;
        }

        const isFuture = trainingDateTime >= now;
        const isNotCompleted = training.status !== "concluído";

        console.log(`🔍 Treino ${training.id} (${training.name}):`);
        console.log(`   - Data: ${training.date}, Hora: ${training.start_time}`);
        console.log(`   - Data/Hora: ${trainingDateTime.toISOString()}`);
        console.log(`   - Agora: ${now.toISOString()}`);
        console.log(`   - É futuro: ${isFuture}, Não concluído: ${isNotCompleted}`);
        console.log(`   - Status: ${training.status}`);

        return isFuture && isNotCompleted;
      })
      .sort((a, b) => {
        // Ordenar por data (mais próximos primeiro)
        const dateA = new Date(`${a.date}T${a.start_time}`);
        const dateB = new Date(`${b.date}T${b.start_time}`);
        return dateA.getTime() - dateB.getTime();
      });

    console.log(`✅ Treinamentos futuros encontrados: ${processedTrainings.length}`);

    return processedTrainings;

  } catch (error) {
    console.error("Erro na função alternativa:", error);
    throw error;
  }
}

export async function finalizeTraining(clubId: number, id: number, summary: string, description: string): Promise<boolean> {
  // Primeiro precisamos obter o treino atual
  const { data: existingTraining, error: fetchError } = await supabase
    .from("trainings")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (fetchError) {
    console.error("Erro ao buscar treino para finalização:", fetchError);
    throw new Error(`Erro ao buscar treino para finalização: ${fetchError.message}`);
  }

  const existingNotesParts = existingTraining.notes?.split('|') || Array(10).fill('');
  existingNotesParts[4] = "concluído";
  existingNotesParts[5] = "100";
  existingNotesParts[8] = description;
  existingNotesParts[9] = summary; // Adicionar o resumo
  const updatedNotes = existingNotesParts.join('|');

  const { error } = await supabase
    .from("trainings")
    .update({ notes: updatedNotes } as any)
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao finalizar treino:", error);
    throw new Error(`Erro ao finalizar treino: ${error.message}`);
  }

  return true;
}

// Funções para Exercícios do Treino

// Funções para Exercícios
export async function getExercises(clubId: number): Promise<Exercise[]> {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) throw new Error("Usuário não autenticado");

  const { data, error } = await supabase
    .from("exercises")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as Exercise[];
}

export async function createExercise(clubId: number, exercise: { name: string; description?: string; category: string; difficulty: string }) {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) throw new Error("Usuário não autenticado");

  const { data, error } = await supabase
    .from("exercises")
    .insert({
      club_id: clubId,
      name: exercise.name,
      description: exercise.description,
      category: exercise.category,
      difficulty: exercise.difficulty,
    })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data;
}

// Funções para Objetivos da Semana
export async function getTrainingGoals(clubId: number): Promise<TrainingGoal[]> {
  const { data, error } = await supabase
    .from("training_goals")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as TrainingGoal[];
}

export async function createTrainingGoal(clubId: number, goal: Omit<TrainingGoal, "id">): Promise<TrainingGoal> {
  const { data, error } = await supabase
    .from("training_goals")
    .insert({ ...goal, club_id: clubId })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingGoal;
}

export async function updateTrainingGoal(clubId: number, id: number, goal: Partial<TrainingGoal>): Promise<TrainingGoal> {
  const { data, error } = await supabase
    .from("training_goals")
    .update({ ...goal })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingGoal;
}

export async function deleteTrainingGoal(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("training_goals")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) throw new Error(error.message);
  return true;
}

// Funções para Progresso Físico
export async function getPhysicalProgress(clubId: number): Promise<PhysicalProgress[]> {
  const { data, error } = await supabase
    .from("physical_progress")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return (data || []) as PhysicalProgress[];
}

export async function createPhysicalProgress(clubId: number, progress: Omit<PhysicalProgress, "id">): Promise<PhysicalProgress> {
  const { data, error } = await supabase
    .from("physical_progress")
    .insert({ ...progress, club_id: clubId })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as PhysicalProgress;
}

// Funções para Exercícios do Treino
export async function getTrainingExercises(trainingId: number): Promise<TrainingExercise[]> {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) throw new Error("Usuário não autenticado");

  const { data, error } = await supabase
    .from("training_exercises")
    .select("* , exercises(*)")
    .eq("training_id", trainingId);
  if (error) throw new Error(error.message);
  return (data || []) as TrainingExercise[];
}

export async function addExerciseToTraining(trainingId: number, exerciseId: number, order?: number, notes?: string): Promise<TrainingExercise> {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) throw new Error("Usuário não autenticado");

  const { data, error } = await supabase
    .from("training_exercises")
    .insert({ training_id: trainingId, exercise_id: exerciseId, order_in_training: order, notes })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as TrainingExercise;
}

export async function removeExerciseFromTraining(trainingExerciseId: number): Promise<boolean> {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) throw new Error("Usuário não autenticado");
  const { error } = await supabase
    .from("training_exercises")
    .delete()
    .eq("id", trainingExerciseId);
  if (error) throw new Error(error.message);
  return true;
}

// Funções para gerenciar imagens de treino
export async function getTrainingImages(trainingId: number): Promise<TrainingImage[]> {
  const { data, error } = await supabase
    .from("training_images")
    .select("*")
    .eq("training_id", trainingId)
    .order("image_order");

  if (error) {
    console.error("Erro ao buscar imagens do treino:", error);
    throw new Error(`Erro ao buscar imagens do treino: ${error.message}`);
  }

  return data as TrainingImage[];
}

export async function addTrainingImage(
  clubId: number,
  trainingId: number,
  imageUrl: string,
  imageOrder: number
): Promise<TrainingImage> {
  const { data, error } = await supabase
    .from("training_images")
    .insert({
      club_id: clubId,
      training_id: trainingId,
      image_url: imageUrl,
      image_order: imageOrder
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao adicionar imagem ao treino:", error);
    throw new Error(`Erro ao adicionar imagem ao treino: ${error.message}`);
  }

  return data as TrainingImage;
}

export async function deleteTrainingImage(imageId: number): Promise<boolean> {
  const { error } = await supabase
    .from("training_images")
    .delete()
    .eq("id", imageId);

  if (error) {
    console.error("Erro ao excluir imagem do treino:", error);
    throw new Error(`Erro ao excluir imagem do treino: ${error.message}`);
  }

  return true;
}

// Funções para gerenciar jogadores em treinos
export async function getTrainingPlayers(trainingId: number): Promise<any[]> {
  const { data, error } = await supabase
    .from("training_players")
    .select(`
      *,
      players:player_id (
        id,
        name,
        position,
        number,
        image
      )
    `)
    .eq("training_id", trainingId);

  if (error) {
    console.error("Erro ao buscar jogadores do treino:", error);
    throw new Error(`Erro ao buscar jogadores do treino: ${error.message}`);
  }

  return data.map(item => item.players);
}

// Função para buscar treinos por categoria
export async function getTrainingsByCategory(clubId: number, categoryId: number): Promise<Training[]> {
  const { data, error } = await supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .eq("category_id", categoryId);

  if (error) {
    console.error("Erro ao buscar treinos por categoria:", error);
    throw new Error(`Erro ao buscar treinos por categoria: ${error.message}`);
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(10).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      required_materials: item.required_materials || '',
      summary: notesParts[9] || ''
    } as Training;
  });

  // Ordenar treinos: primeiro os agendados por data (mais próximos primeiro)
  const today = new Date();

  return trainings.sort((a, b) => {
    // Primeiro, separar por status
    if (a.status !== b.status) {
      if (a.status === "agendado") return -1;
      if (b.status === "agendado") return 1;
      if (a.status === "em andamento") return -1;
      if (b.status === "em andamento") return 1;
    }

    // Para treinos agendados, ordenar por data (mais próximos primeiro)
    if (a.status === "agendado") {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      // Se ambas as datas são futuras ou ambas são passadas
      if ((dateA >= today && dateB >= today) || (dateA < today && dateB < today)) {
        return dateA.getTime() - dateB.getTime();
      }

      // Datas futuras vêm antes de datas passadas
      return dateA >= today ? -1 : 1;
    }

    // Para treinos concluídos, ordenar por data (mais recentes primeiro)
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB.getTime() - dateA.getTime();
  });
}

// Função para buscar treinos por temporada e categoria (para o dashboard)
export async function getUpcomingTrainingsBySeasonAndCategory(clubId: number, seasonId: number, categoryId?: number): Promise<Training[]> {
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0]; // Formato YYYY-MM-DD

  // Construir a query base
  let query = supabase
    .from("trainings")
    .select(`
      *,
      categories:category_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .gte("date", todayStr); // Apenas treinos futuros

  // Adicionar filtro por categoria se fornecido
  if (categoryId) {
    query = query.eq("category_id", categoryId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar treinos por temporada e categoria:", error);
    throw new Error(`Erro ao buscar treinos por temporada e categoria: ${error.message}`);
  }

  // Converter os dados do banco para o formato esperado pelo app
  const trainings = data.map(item => {
    const notesParts = item.notes?.split('|') || Array(10).fill('');
    const timeStr = notesParts[2] || '10:00';
    const timeComponents = timeStr.split('-');
    const startTime = timeComponents[0].trim();
    const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

    return {
      id: item.id,
      club_id: item.club_id,
      name: notesParts[0] || 'Treino',
      type: notesParts[1] || 'geral',
      date: item.date,
      time: timeStr,
      start_time: startTime,
      end_time: endTime,
      location: notesParts[3] || 'Campo Principal',
      status: notesParts[4] || 'agendado',
      progress: parseInt(notesParts[5] || '0'),
      coach: notesParts[6] || 'Técnico',
      participants: parseInt(notesParts[7] || '0'),
      description: notesParts[8] || '',
      category_id: item.category_id,
      category_name: item.categories?.name,
      required_materials: item.required_materials || '',
      summary: notesParts[9] || ''
    } as Training;
  });

  // Ordenar por data (mais próximos primeiro)
  return trainings.sort((a, b) => {
    const dateTimeA = new Date(`${a.date}T${a.start_time || '00:00'}`);
    const dateTimeB = new Date(`${b.date}T${b.start_time || '00:00'}`);
    return dateTimeA.getTime() - dateTimeB.getTime();
  });
}

// Função para buscar treinos futuros de um jogador
export async function getPlayerUpcomingTrainings(clubId: number, playerId: string): Promise<Training[]> {
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0]; // Formato YYYY-MM-DD
  const now = today.getTime();

  // Buscar treinos onde o jogador está associado
  const { data, error } = await supabase
    .from("training_players")
    .select(`
      training_id,
      trainings:training_id (
        *,
        categories:category_id (
          id,
          name
        )
      )
    `)
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (error) {
    console.error("Erro ao buscar treinos do jogador:", error);
    throw new Error(`Erro ao buscar treinos do jogador: ${error.message}`);
  }

  // Filtrar apenas treinos futuros e converter para o formato esperado
  const trainings = data
    .filter(item => {
      const training = item.trainings;
      if (!training) return false;

      const notesParts = training.notes?.split('|') || Array(10).fill('');
      const timeStr = notesParts[2] || '10:00';
      const timeComponents = timeStr.split('-');
      const startTime = timeComponents[0].trim();

      // Criar um objeto Date combinando a data do treino com o horário de início
      const trainingDateTime = new Date(`${training.date}T${startTime}`);

      return trainingDateTime.getTime() >= now &&
             (notesParts[4] === 'agendado' || notesParts[4] === 'em andamento');
    })
    .map(item => {
      const training = item.trainings;
      const notesParts = training.notes?.split('|') || Array(10).fill('');
      const timeStr = notesParts[2] || '10:00';
      const timeComponents = timeStr.split('-');
      const startTime = timeComponents[0].trim();
      const endTime = timeComponents.length > 1 ? timeComponents[1].trim() : '';

      return {
        id: training.id,
        club_id: training.club_id,
        name: notesParts[0] || 'Treino',
        type: notesParts[1] || 'geral',
        date: training.date,
        time: timeStr,
        start_time: startTime,
        end_time: endTime,
        location: notesParts[3] || 'Campo Principal',
        status: notesParts[4] || 'agendado',
        progress: parseInt(notesParts[5] || '0'),
        coach: notesParts[6] || 'Técnico',
        participants: parseInt(notesParts[7] || '0'),
        description: notesParts[8] || '',
        category_id: training.category_id,
        category_name: training.categories?.name,
        player_ids: [playerId], // Incluir o ID do jogador atual
        required_materials: training.required_materials || '',
        summary: notesParts[9] || ''
      } as Training;
    });

  // Buscar imagens para cada treino
  const trainingsWithImages = await Promise.all(
    trainings.map(async (training) => {
      try {
        const images = await getTrainingImages(training.id);
        return {
          ...training,
          images
        };
      } catch (error) {
        console.error(`Erro ao buscar imagens do treino ${training.id}:`, error);
        return {
          ...training,
          images: []
        };
      }
    })
  );

  // Ordenar por data (mais próximos primeiro)
  return trainingsWithImages.sort((a, b) => {
    const dateTimeA = new Date(`${a.date}T${a.start_time || '00:00'}`);
    const dateTimeB = new Date(`${b.date}T${b.start_time || '00:00'}`);
    return dateTimeA.getTime() - dateTimeB.getTime();
  });
}
