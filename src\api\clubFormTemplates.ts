import { supabase, Database } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { PostgrestError, PostgrestSingleResponse } from "@supabase/supabase-js";

// Default templates content
export const DEFAULT_TEMPLATES = {
  pre_registration: {
    name: "Ficha de Pré-cadastro Padrão",
    description: "Ficha padrão para coleta de dados básicos do atleta durante o pré-cadastro",
    content: `<h2 style="text-align: center;">FICHA DE PRÉ-CADASTRO</h2>
<br>
<p><strong>Nome completo:</strong> _________________________________</p>
<p><strong>Data de nascimento:</strong> ___/___/______</p>
<p><strong>CPF:</strong> _________________________________</p>
<p><strong>RG:</strong> _________________________________</p>
<p><strong>Endereço:</strong> _________________________________</p>
<p><strong>Telefone:</strong> _________________________________</p>
<p><strong>Email:</strong> _________________________________</p>
<p><strong>Posição preferida:</strong> _________________________________</p>
<p><strong>Experiência anterior:</strong> _________________________________</p>
<p><strong>Nome do responsável (se menor):</strong> _________________________________</p>
<p><strong>Telefone do responsável:</strong> _________________________________</p>
<br>
<p>Declaro que as informações acima são verdadeiras e estou ciente dos procedimentos de pré-cadastro do clube.</p>
<br>
<p>Itapira, ___/___/______</p>
<br>
<p>_________________________________</p>
<p>Assinatura do atleta</p>
<br>
<p>_________________________________</p>
<p>Assinatura do responsável (se menor)</p>`
  },
  liability_waiver_minor: {
    name: "Termo de Isenção de Responsabilidade para Menor de 18 anos",
    description: "Termo padrão de isenção de responsabilidade para atletas menores de idade",
    content: `<h2 style="text-align: center;">Termo de Isenção de Responsabilidade para Menor de 18 anos</h2>
<p><strong>Nome do Atleta:</strong> _______________________________________</p>
<p><strong>Categoria:</strong> _______</p>
<br>
<p><strong>1.</strong> Jogadores menores de idade devem apresentar RG, CPF, certidão de nascimento, atestado médico, declaração escolar, cópia do documento do responsável legal, ficha e termo de isenção de responsabilidade (autorização de moradia, caso fiquem alojados). <strong>TODOS AUTENTICADOS</strong></p>
<br>
<p><strong>2. Responsabilidade:</strong> O responsável e/ou atleta declara estar ciente de que o treinamento envolve atividades físicas, técnicas e testes físicos, incluindo treinos com bola e atividades em grupo, e assume total responsabilidade pelos documentos apresentados, tanto em termos de autenticidade quanto de validade civil e criminal.</p>
<br>
<p><strong>3. Condição física:</strong> O responsável e/ou atleta declara possuir documentação original devidamente regularizada e estar apto(a) para participar das atividades esportivas, não possuindo nenhuma doença ou limitação física que impeça sua participação nos treinos.</p>
<br>
<p><strong>4. Lesões e acidentes:</strong> O responsável e/ou atleta está ciente de que durante os treinos e jogos podem ocorrer lesões e acidentes, e isenta o clube de qualquer responsabilidade por tais incidentes.</p>
<br>
<p><strong>5. Contratação:</strong> O responsável e/ou atleta está ciente de que a participação nos treinos não garante a contratação imediata pelo clube, sendo necessário passar por avaliações e aprovação posterior.</p>
<br>
<p><strong>6. Extravio de documentos ou pertences:</strong> O responsável e/ou atleta está ciente de que o clube não se responsabiliza por extravio de documentos, materiais ou pertences pessoais do atleta durante sua participação no clube.</p>
<br>
<p><strong>7. Vínculos e empresários:</strong> O responsável e/ou atleta declara não possuir vínculo com equipes ou entidades filiadas a federações ou confederações de futebol, assim como não possuir vínculo com empresários, procuradores ou similares, isentando o clube de qualquer responsabilidade nesse sentido.</p>
<br>
<p><strong>8. Desistência:</strong> O responsável e/ou atleta está ciente de que em caso de desistência por parte do atleta, o clube não irá reembolsar o pagamento da inscrição, viagens, campeonatos, taxa de avaliação ou qualquer outro custo relacionado.</p>
<br>
<p><strong>9. Direito à imagem:</strong> O responsável e/ou atleta estão cientes de que o atleta poderá ser fotografado e filmado durante sua participação no clube, incluindo treinos, amistosos, campeonatos, viagens, entrevistas, entre outros, e autorizam o clube a utilizar tais imagens livremente, com ou sem fins comerciais, publicitários e promocionais, pelo período de 5 anos a partir da data da assinatura deste termo.</p>
<br>
<p><strong>10. Transporte em caso de acidente:</strong> O responsável e/ou atleta autoriza o clube a transportar o atleta acidentado para uma Unidade de Saúde, caso possua plano de saúde informado previamente.</p>
<br>
<p><strong>11. Código Brasileiro de Justiça Desportiva:</strong> Art. 65: As provas fotográficas, fonográficas, cinematográficas, de vídeo, de vídeo tape e as imagens fixadas por qualquer meio ou processo eletrônico serão apreciadas com a devida cautela, incumbindo à parte que desejar produzi-las o pagamento das despesas com as providências que o órgão judicante determinar.</p>
<br>
<p>Itapira ______de________________de________</p>
<br>
<p>__________________________________&nbsp;&nbsp;&nbsp;&nbsp;________________________________</p>
<p>Assinatura do Atleta&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Assinatura do Responsável</p>
<br>
<p>Nome por extenso, Conferido Por: ____________________________</p>
<p>Data:___/____/____</p>`
  },
  liability_waiver_adult: {
    name: "Termo de Isenção de Responsabilidade para Maior de 18 anos",
    description: "Termo padrão de isenção de responsabilidade para atletas maiores de idade",
    content: `<h2 style="text-align: center;">Termo de Isenção de Responsabilidade para Maior de 18 anos</h2>
<p><strong>Nome do Atleta:</strong> _______________________________________</p>
<p><strong>Categoria:</strong> _______</p>
<br>
<p><strong>1.</strong> Jogadores devem apresentar RG, CPF, certidão de nascimento, atestado médico, ficha e termo de isenção de responsabilidade (autorização de moradia, caso fiquem alojados). <strong>TODOS AUTENTICADOS</strong></p>
<br>
<p><strong>2. Responsabilidade:</strong> O atleta declara estar ciente de que o treinamento envolve atividades físicas, técnicas e testes físicos, incluindo treinos com bola e atividades em grupo, e assume total responsabilidade pelos documentos apresentados, tanto em termos de autenticidade quanto de validade civil e criminal.</p>
<br>
<p><strong>3. Condição física:</strong> O atleta declara possuir documentação original devidamente regularizada e estar apto(a) para participar das atividades esportivas, não possuindo nenhuma doença ou limitação física que impeça sua participação nos treinos.</p>
<br>
<p><strong>4. Lesões e acidentes:</strong> O atleta está ciente de que durante os treinos e jogos podem ocorrer lesões e acidentes, e isenta o clube de qualquer responsabilidade por tais incidentes.</p>
<br>
<p><strong>5. Contratação:</strong> O atleta está ciente de que a participação nos treinos não garante a contratação imediata pelo clube, sendo necessário passar por avaliações e aprovação posterior.</p>
<br>
<p><strong>6. Extravio de documentos ou pertences:</strong> O atleta está ciente de que o clube não se responsabiliza por extravio de documentos, materiais ou pertences pessoais durante sua participação no clube.</p>
<br>
<p><strong>7. Vínculos e empresários:</strong> O atleta declara não possuir vínculo com equipes ou entidades filiadas a federações ou confederações de futebol, assim como não possuir vínculo com empresários, procuradores ou similares, isentando o clube de qualquer responsabilidade nesse sentido.</p>
<br>
<p><strong>8. Desistência:</strong> O atleta está ciente de que em caso de desistência, o clube não irá reembolsar o pagamento da inscrição, viagens, campeonatos, taxa de avaliação ou qualquer outro custo relacionado.</p>
<br>
<p><strong>9. Direito à imagem:</strong> O atleta está ciente de que poderá ser fotografado e filmado durante sua participação no clube, incluindo treinos, amistosos, campeonatos, viagens, entrevistas, entre outros, e autoriza o clube a utilizar tais imagens livremente, com ou sem fins comerciais, publicitários e promocionais, pelo período de 5 anos a partir da data da assinatura deste termo.</p>
<br>
<p><strong>10. Transporte em caso de acidente:</strong> O atleta autoriza o clube a transportá-lo para uma Unidade de Saúde, caso possua plano de saúde informado previamente.</p>
<br>
<p><strong>11. Código Brasileiro de Justiça Desportiva:</strong> Art. 65: As provas fotográficas, fonográficas, cinematográficas, de vídeo, de vídeo tape e as imagens fixadas por qualquer meio ou processo eletrônico serão apreciadas com a devida cautela, incumbindo à parte que desejar produzi-las o pagamento das despesas com as providências que o órgão judicante determinar.</p>
<br>
<p>Itapira ______de________________de________</p>
<br>
<p>__________________________________</p>
<p>Assinatura do Atleta</p>
<br>
<p>Nome por extenso: ____________________________</p>
<p>Data:___/____/____</p>`
  },
  housing: {
    name: "Autorização de Moradia",
    description: "Autorização padrão para residência em alojamento do clube",
    content: `<h2 style="text-align: center;">Autorização de Moradia</h2>
<br>
<p>Eu,________________________________________________________________________________, portador do CPF nº___________________________________ e RG nº__________________________, residente à ________________________________________________________________ na qualidade de responsável legal do(a) menor ______________________________________________________________________________ nascido(a) _____/_____/_____, autorizo o referido(a) atleta a permanecer alojado(a) nas instalações da Sociedade Esportiva Itapirense para desenvolvimento de suas atividades esportivas e demais atividades complementares promovidas pelo clube.</p>
<br>
<p>Por meio deste documento, estou ciente e autorizo:</p>
<br>
<p><strong>1. Viagens e Deslocamentos:</strong></p>
<p>a) Que o(a) atleta participe de viagens nacionais e internacionais acompanhando o clube, sempre que necessário para campeonatos, amistosos e demais compromissos oficiais.</p>
<p>b) Que o(a) atleta realize viagens de final de semana para sua residência, sem a necessidade de acompanhamento de um responsável, caso haja permissão prévia do clube.</p>
<br>
<p><strong>2. Educação e Acompanhamento Escolar:</strong> Autorizo que o clube matricule o(a) atleta em instituição de ensino e realize o acompanhamento do seu desenvolvimento escolar, garantindo que suas atividades acadêmicas sejam mantidas e supervisionadas conforme as diretrizes da instituição educacional.</p>
<br>
<p><strong>3. Participação em Atividades Culturais e Projetos Sociais:</strong> Permito que o(a) atleta participe de atividades culturais externas, bem como de projetos de ação social e programas de psicologia organizados pelo clube, entendendo a importância de tais atividades para o desenvolvimento social e emocional do(a) atleta.</p>
<br>
<p><strong>4. Término de Alojamento:</strong> Declaro-me ciente de que, ao término da participação do(a) atleta no clube, ele(a) deverá desocupar as instalações de alojamento imediatamente, tão logo seja comunicado o fim de sua permanência, não cabendo ao clube a responsabilidade de providenciar a passagem de retorno do(a) atleta à sua cidade de origem.</p>
<br>
<p>Estou ciente e de acordo com as condições expressas neste documento e assumo total responsabilidade pela concordância com os termos estabelecidos.</p>
<br>
<p>Itapira,________ de ___________ de _________</p>
<br>
<p>_________________________________________</p>
<p>Assinatura do Responsável Legal:</p>
<br>
<p>_________________________________</p>
<p>Assinatura de Testemunha</p>
<br>
<p>_______________________________</p>
<p>Assinatura do Atleta:</p>
<br>
<p>______________________________</p>
<p>Assinatura do Clube</p>
<br>
<p><strong>OBS:</strong> Esta ficha tem que ser reconhecida em cartório assinatura Responsável Legal.</p>`
  }
};

// Types
export type FormType = 'pre_registration' | 'housing' | 'liability_waiver' | 'liability_waiver_minor' | 'liability_waiver_adult' | 'custom';

export type ClubFormTemplate = {
  id: number;
  club_id: number;
  name: string;
  description?: string;
  content: string;
  form_type: FormType;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
};

export type FormTemplateInput = Omit<ClubFormTemplate, "id" | "created_at" | "updated_at">;

// Permissions
export const FORM_TEMPLATE_PERMISSIONS = {
  VIEW: "form_templates.view",
  CREATE: "form_templates.create",
  UPDATE: "form_templates.update",
  DELETE: "form_templates.delete",
  MANAGE: "form_templates.manage"
};

// Get all form templates for a club
export async function getClubFormTemplates(
  clubId: number,
  userId: string,
  formType?: string
): Promise<ClubFormTemplate[]> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.VIEW,
    async () => {
      let query = supabase
        .from("club_form_templates")
        .select("*")
        .eq("club_id", clubId)
        .order("created_at", { ascending: false });

      if (formType) {
        query = query.eq("form_type", formType);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching form templates:", error);
        throw new Error(`Error fetching form templates: ${error.message}`);
      }

      return data || [];
    }
  );
}

// Generate default template for a specific form type
export function generateDefaultTemplate(
  clubId: number,
  formType: 'pre_registration' | 'liability_waiver' | 'liability_waiver_minor' | 'liability_waiver_adult' | 'housing',
  clubName?: string
): ClubFormTemplate {
  const defaultTemplate = DEFAULT_TEMPLATES[formType];

  // Replace "Sociedade Esportiva Itapirense" with actual club name if provided
  let content = defaultTemplate.content;
  if (clubName && formType === 'housing') {
    content = content.replace('Sociedade Esportiva Itapirense', clubName);
  }

  // Replace "Itapira" with club name in all templates if provided
  if (clubName) {
    content = content.replace(/Itapira/g, clubName);
  }

  return {
    id: -1, // Negative ID to indicate it's a default template
    club_id: clubId,
    name: defaultTemplate.name,
    description: defaultTemplate.description,
    content: content,
    form_type: formType,
    is_active: true,
    created_by: 'system',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

// Type helper to safely access database types
type TableRow<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
type TableInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
type TableUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Get active form templates for a club (for public use)
// Now includes fallback to default templates when no custom templates exist
export async function getActiveFormTemplates(
  clubId: number,
  formType?: string | string[]
): Promise<ClubFormTemplate[]> {
  try {
    // Build the query
    let query = supabase
      .from('club_form_templates')
      .select('*')
      .eq('club_id', clubId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    // Add form type filter if provided
    if (formType) {
      if (Array.isArray(formType) && formType.length > 0) {
        query = query.in('form_type', formType);
      } else if (typeof formType === 'string') {
        query = query.eq('form_type', formType);
      }
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching active form templates:', error);
      throw new Error(`Error fetching active form templates: ${error.message}`);
    }

    // Type assertion for the data returned by Supabase
    const customTemplates = (data || []) as unknown as ClubFormTemplate[];

    // If no custom templates found and a specific form type is requested,
    // return default template for that type
    if (customTemplates.length === 0 && formType) {
      // Handle array of form types (e.g., for liability waivers)
      if (Array.isArray(formType)) {
        const defaultTemplates: ClubFormTemplate[] = [];
        
        for (const type of formType) {
          if (type === 'pre_registration' || type === 'housing' || 
              type === 'liability_waiver' || type === 'liability_waiver_minor' || type === 'liability_waiver_adult') {
            try {
              const { data: clubData, error: clubError } = await supabase
                .from("club_info")
                .select("name")
                .eq("id", clubId)
                .single();

              const clubName = clubData?.name;
              const defaultTemplate = generateDefaultTemplate(
                clubId, 
                type as any, // Type assertion needed due to union type
                clubName
              );
              defaultTemplates.push(defaultTemplate);
            } catch (err) {
              console.warn(`Error generating default template for ${type}:`, err);
              // Still try to generate default template without club name
              const defaultTemplate = generateDefaultTemplate(clubId, type as any);
              defaultTemplates.push(defaultTemplate);
            }
          }
        }
        
        return defaultTemplates;
      } 
      // Handle single form type
      else if (formType === 'pre_registration' || formType === 'housing' || 
               formType === 'liability_waiver' || formType === 'liability_waiver_minor' || formType === 'liability_waiver_adult') {
        try {
          // Try to get club name for personalization
          const { data: clubData, error: clubError } = await supabase
            .from("club_info")
            .select("name")
            .eq("id", clubId)
            .single();

          const clubName = clubData?.name;
          const defaultTemplate = generateDefaultTemplate(clubId, formType, clubName);
          return [defaultTemplate];
        } catch (err) {
          console.warn("Error getting club info for default template:", err);
          // Still return default template without club name
          const defaultTemplate = generateDefaultTemplate(clubId, formType);
          return [defaultTemplate];
        }
      }
    }

    return customTemplates;
  } catch (error) {
    console.error('Unexpected error in getActiveFormTemplates:', error);
    throw error;
  }
}

// Get form template by ID
export async function getFormTemplateById(
  clubId: number,
  templateId: number,
  userId: string
): Promise<ClubFormTemplate> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.VIEW,
    async () => {
      const { data, error } = await supabase
        .from("club_form_templates")
        .select("*")
        .eq("club_id", clubId)
        .eq("id", templateId)
        .single();

      if (error) {
        console.error("Error fetching form template:", error);
        throw new Error(`Error fetching form template: ${error.message}`);
      }

      return data;
    }
  );
}

// Create form template
export async function createFormTemplate(
  clubId: number,
  template: Omit<FormTemplateInput, "club_id">,
  userId: string
): Promise<ClubFormTemplate> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.CREATE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "form_template.create",
        { name: template.name, form_type: template.form_type },
        async () => {
          const { data, error } = await supabase
            .from("club_form_templates")
            .insert({
              club_id: clubId,
              name: template.name,
              description: template.description,
              content: template.content,
              form_type: template.form_type,
              is_active: template.is_active,
              created_by: userId
            })
            .select()
            .eq("club_id", clubId)
            .single();

          if (error) {
            console.error("Error creating form template:", error);
            throw new Error(`Error creating form template: ${error.message}`);
          }

          return data;
        }
      );
    }
  );
}

// Update form template
export async function updateFormTemplate(
  clubId: number,
  templateId: number,
  updates: Partial<Omit<FormTemplateInput, "club_id" | "created_by">>,
  userId: string
): Promise<ClubFormTemplate> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.UPDATE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "form_template.update",
        { template_id: templateId, updates },
        async () => {
          const { data, error } = await supabase
            .from("club_form_templates")
            .update({
              ...updates,
              updated_at: new Date().toISOString()
            })
            .eq("club_id", clubId)
            .eq("id", templateId)
            .select()
            .single();

          if (error) {
            console.error("Error updating form template:", error);
            throw new Error(`Error updating form template: ${error.message}`);
          }

          return data;
        }
      );
    }
  );
}

// Delete form template
export async function deleteFormTemplate(
  clubId: number,
  templateId: number,
  userId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "form_template.delete",
        { template_id: templateId },
        async () => {
          const { error } = await supabase
            .from("club_form_templates")
            .delete()
            .eq("club_id", clubId)
            .eq("id", templateId);

          if (error) {
            console.error("Error deleting form template:", error);
            throw new Error(`Error deleting form template: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}

// Toggle template active status
export async function toggleTemplateStatus(
  clubId: number,
  templateId: number,
  userId: string
): Promise<ClubFormTemplate> {
  return withPermission(
    clubId,
    userId,
    FORM_TEMPLATE_PERMISSIONS.UPDATE,
    async () => {
      // First get current status
      const { data: currentData, error: fetchError } = await supabase
        .from("club_form_templates")
        .select("is_active")
        .eq("club_id", clubId)
        .eq("id", templateId)
        .single();

      if (fetchError) {
        throw new Error(`Error fetching template status: ${fetchError.message}`);
      }

      const newStatus = !currentData.is_active;

      return withAuditLog(
        clubId,
        userId,
        "form_template.toggle_status",
        { template_id: templateId, new_status: newStatus },
        async () => {
          const { data, error } = await supabase
            .from("club_form_templates")
            .update({
              is_active: newStatus,
              updated_at: new Date().toISOString()
            })
            .eq("club_id", clubId)
            .eq("id", templateId)
            .select()
            .single();

          if (error) {
            console.error("Error toggling template status:", error);
            throw new Error(`Error toggling template status: ${error.message}`);
          }

          return data;
        }
      );
    }
  );
}

// Interface para informações do clube usadas na geração do PDF
interface ClubInfoForPDF {
  name?: string;
  logo_url?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export async function generateFormTemplatePDF(
  template: ClubFormTemplate,
  clubInfo: ClubInfoForPDF = {}
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    try {
      const doc = new jsPDF();
      let initialY = 20;

      // Function to add header and content
      const addHeaderAndContent = () => {
        try {
          // Add club name
          if (clubInfo.name) {
            doc.setFontSize(14);
            doc.setFont('helvetica', 'bold');
            doc.text(clubInfo.name, 105, initialY, { align: 'center' as const });
            initialY += 10;
          }

          // Add content
          doc.setFontSize(12);
          doc.setFont('helvetica', 'normal');
          
          // Split content into lines that fit the PDF width
          const splitContent = doc.splitTextToSize(
            template.content.replace(/<[^>]*>?/gm, ''), // Remove HTML tags
            180
          );
          
          // Add each line of content
          splitContent.forEach((line: string) => {
            if (initialY > 270) { // Near bottom of page
              doc.addPage();
              initialY = 20;
            }
            doc.text(line, 15, initialY);
            initialY += 7; // Line height
          });

          // Generate the PDF blob and resolve the promise
          const pdfBlob = doc.output('blob');
          resolve(pdfBlob);
        } catch (error) {
          reject(error);
        }
      };

      // Add logo if available
      if (clubInfo.logo_url) {
        try {
          const img = new Image();
          img.crossOrigin = 'Anonymous';
          img.onload = function() {
            try {
              // Calculate dimensions to maintain aspect ratio
              const maxWidth = 40;
              const maxHeight = 40;
              let width = img.width;
              let height = img.height;

              if (width > maxWidth) {
                const ratio = maxWidth / width;
                width = maxWidth;
                height = height * ratio;
              }
              if (height > maxHeight) {
                const ratio = maxHeight / height;
                height = maxHeight;
                width = width * ratio;
              }

              // Add image to PDF
              doc.addImage(
                img,
                'JPEG',
                15,
                initialY,
                width,
                height
              );
              
              // Adjust Y position for the rest of the content
              const imgHeight = height + 10;
              initialY += imgHeight + 10;

              addHeaderAndContent();
            } catch (err) {
              console.warn('Error processing logo image:', err);
              addHeaderAndContent();
            }
          };
          img.onerror = function() {
            console.warn('Error loading logo image');
            addHeaderAndContent();
          };
          img.src = clubInfo.logo_url;
        } catch (err) {
          console.warn('Error with logo:', err);
          addHeaderAndContent();
        }
      } else {
        addHeaderAndContent();
      }
    } catch (error) {
      reject(error);
    }
  });
}

// Nova geração de PDF usando html2canvas para preservar formatação HTML
export async function generateFormTemplatePDFHtml(
  template: ClubFormTemplate,
  clubInfo: ClubInfoForPDF = {}
): Promise<Blob> {
  try {
    // Criar container temporário com o conteúdo da ficha
    const container = document.createElement('div');
    container.style.padding = '40px';
    container.style.backgroundColor = 'white';
    container.style.color = 'black';
    container.style.width = '794px'; // Aproximadamente largura A4 em px @96dpi
    container.style.fontFamily = 'Helvetica, Arial, sans-serif';

    // Cabeçalho com logo e nome do clube
    container.innerHTML = `
      <div style="text-align:center; margin-bottom:20px;">
        ${clubInfo.logo_url ? `<img src="${clubInfo.logo_url}" style="max-height:80px; margin-bottom:10px;" />` : ''}
        ${clubInfo.name ? `<h1 style="margin:0; font-size:20px;">${clubInfo.name}</h1>` : ''}
      </div>
      <div class="prose" style="max-width:none;">
        ${template.content}
      </div>
    `;

    document.body.appendChild(container);

    // Capturar o container como imagem
    const canvas = await html2canvas(container, {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
      allowTaint: true
    });

    const pdf = new jsPDF({ format: 'a4', orientation: 'portrait', unit: 'mm' });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = 210; // mm
    const pageHeight = 297; // mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    while (heightLeft > 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    const blob = pdf.output('blob');
    document.body.removeChild(container);
    return blob;
  } catch (err) {
    if (typeof document !== 'undefined') {
      const existing = document.querySelector('#temp-pdf-container');
      if (existing) existing.remove();
    }
    throw err;
  }
}