import { supabase } from "@/integrations/supabase/client";
import { getPlayerMatchStatistics } from "./playerStatistics";
import { updatePlayer } from "./players";

/**
 * Synchronize player aggregated statistics from match statistics
 * This function recalculates and updates the stats column in the players table
 * based on all match statistics for each player
 */
export async function syncPlayerAggregatedStats(clubId: number, playerId?: string): Promise<void> {
  try {
    // Get all players or specific player
    let playersQuery = supabase
      .from("players")
      .select("id, name, stats")
      .eq("club_id", clubId);

    if (playerId) {
      playersQuery = playersQuery.eq("id", playerId);
    }

    const { data: players, error: playersError } = await playersQuery;

    if (playersError) {
      throw new Error(`Error fetching players: ${playersError.message}`);
    }

    if (!players || players.length === 0) {
      return;
    }

    // Process each player
    for (const player of players) {
      try {
        // Get all match statistics for this player
        const { data: matchStats, error: statsError } = await supabase
          .from("player_match_statistics")
          .select("*")
          .eq("club_id", clubId)
          .eq("player_id", player.id);

        if (statsError) {
          console.error(`Error fetching match stats for player ${player.id}:`, statsError);
          continue;
        }

        // Calculate aggregated statistics
        const aggregatedStats = {
          games: matchStats?.length || 0,
          goals: matchStats?.reduce((sum, stat) => sum + (stat.goals || 0), 0) || 0,
          assists: matchStats?.reduce((sum, stat) => sum + (stat.assists || 0), 0) || 0,
          minutes: matchStats?.reduce((sum, stat) => sum + (stat.minutes_played || 0), 0) || 0,
          yellowCards: matchStats?.reduce((sum, stat) => sum + (stat.yellow_cards || 0), 0) || 0,
          redCards: matchStats?.reduce((sum, stat) => sum + (stat.red_cards || 0), 0) || 0,
          shots: matchStats?.reduce((sum, stat) => sum + (stat.shots || 0), 0) || 0,
          shotsOnTarget: matchStats?.reduce((sum, stat) => sum + (stat.shots_on_target || 0), 0) || 0,
          passes: matchStats?.reduce((sum, stat) => sum + (stat.passes || 0), 0) || 0,
          passesCompleted: matchStats?.reduce((sum, stat) => sum + (stat.passes_completed || 0), 0) || 0,
          tackles: matchStats?.reduce((sum, stat) => sum + (stat.tackles || 0), 0) || 0,
          interceptions: matchStats?.reduce((sum, stat) => sum + (stat.interceptions || 0), 0) || 0,
          foulsCommitted: matchStats?.reduce((sum, stat) => sum + (stat.fouls_committed || 0), 0) || 0,
          foulsReceived: matchStats?.reduce((sum, stat) => sum + (stat.fouls_suffered || 0), 0) || 0,
        };

        // Update player stats (without userId to avoid permission checks)
        await updatePlayer(clubId, player.id, {
          stats: aggregatedStats
        });

        console.log(`Updated stats for player ${player.name}:`, aggregatedStats);
        
        // Verificar se a atualização foi bem-sucedida
        const { data: updatedPlayer, error: verifyError } = await supabase
          .from("players")
          .select("stats")
          .eq("id", player.id)
          .eq("club_id", clubId)
          .single();
          
        if (verifyError) {
          console.error(`Error verifying stats update for player ${player.id}:`, verifyError);
        } else {
          console.log(`Verified stats for ${player.name}:`, updatedPlayer.stats);
        }
      } catch (error) {
        console.error(`Error updating stats for player ${player.id}:`, error);
      }
    }
  } catch (error) {
    console.error("Error syncing player aggregated stats:", error);
    throw error;
  }
}

/**
 * Update player statistics based on match events (goals, cards, etc.)
 * This function is called when match data is updated to ensure player stats are current
 */
export async function updatePlayerStatsFromMatch(
  clubId: number,
  matchId: string,
  goals: Array<{ jogador: string; assist?: string }>,
  cards: Array<{ jogador: string; tipo: "amarelo" | "vermelho" }>,
  lineup: string[],
  userId: string
): Promise<void> {
  try {
    // Get all players
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name")
      .eq("club_id", clubId);

    if (playersError) {
      throw new Error(`Error fetching players: ${playersError.message}`);
    }

    if (!players) return;

    // Create player name to ID mapping
    const playerMap = new Map(players.map(p => [p.name, p.id]));

    // Count statistics per player
    const playerStats: Record<string, {
      goals: number;
      assists: number;
      yellowCards: number;
      redCards: number;
      minutesPlayed: number;
    }> = {};

    // Initialize stats for all involved players
    const allInvolvedPlayers = new Set([
      ...goals.map(g => g.jogador),
      ...goals.filter(g => g.assist).map(g => g.assist!),
      ...cards.map(c => c.jogador),
      ...lineup
    ]);

    allInvolvedPlayers.forEach(playerName => {
      playerStats[playerName] = {
        goals: 0,
        assists: 0,
        yellowCards: 0,
        redCards: 0,
        minutesPlayed: lineup.includes(playerName) ? 90 : 0
      };
    });

    // Count goals
    goals.forEach(goal => {
      if (playerStats[goal.jogador]) {
        playerStats[goal.jogador].goals++;
      }
      if (goal.assist && playerStats[goal.assist]) {
        playerStats[goal.assist].assists++;
      }
    });

    // Count cards
    cards.forEach(card => {
      if (playerStats[card.jogador]) {
        if (card.tipo === "amarelo") {
          playerStats[card.jogador].yellowCards++;
        } else {
          playerStats[card.jogador].redCards++;
        }
      }
    });

    // Save individual match statistics for each player
    for (const [playerName, stats] of Object.entries(playerStats)) {
      const playerId = playerMap.get(playerName);
      if (!playerId) continue;

      // Check if statistics already exist for this player and match
      const { data: existingStats } = await supabase
        .from("player_match_statistics")
        .select("id")
        .eq("club_id", clubId)
        .eq("match_id", matchId)
        .eq("player_id", playerId)
        .single();

      const matchStatsData = {
        club_id: clubId,
        match_id: matchId,
        player_id: playerId,
        minutes_played: stats.minutesPlayed,
        goals: stats.goals,
        assists: stats.assists,
        yellow_cards: stats.yellowCards,
        red_cards: stats.redCards,
        shots: 0, // These would need to be provided separately
        shots_on_target: 0,
        passes: 0,
        passes_completed: 0,
        key_passes: 0,
        tackles: 0,
        interceptions: 0,
        fouls_committed: 0,
        fouls_suffered: 0,
      };

      if (existingStats) {
        // Update existing statistics
        const { error } = await supabase
          .from("player_match_statistics")
          .update({
            ...matchStatsData,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingStats.id);

        if (error) {
          console.error(`Error updating match statistics for player ${playerName}:`, error);
        }
      } else {
        // Create new statistics
        const { error } = await supabase
          .from("player_match_statistics")
          .insert(matchStatsData);

        if (error) {
          console.error(`Error creating match statistics for player ${playerName}:`, error);
        }
      }
    }

    // Sync aggregated statistics for all involved players
    for (const playerName of allInvolvedPlayers) {
      const playerId = playerMap.get(playerName);
      if (playerId) {
        await syncPlayerAggregatedStats(clubId, playerId);
      }
    }

  } catch (error) {
    console.error("Error updating player stats from match:", error);
    throw error;
  }
}

/**
 * Initialize player stats for players who don't have any
 */
export async function initializePlayerStats(clubId: number): Promise<void> {
  try {
    const { data: players, error } = await supabase
      .from("players")
      .select("id, name, stats")
      .eq("club_id", clubId)
      .is("stats", null);

    if (error) {
      throw new Error(`Error fetching players: ${error.message}`);
    }

    if (!players || players.length === 0) {
      return;
    }

    const defaultStats = {
      games: 0,
      goals: 0,
      assists: 0,
      minutes: 0,
      yellowCards: 0,
      redCards: 0,
      shots: 0,
      shotsOnTarget: 0,
      passes: 0,
      passesCompleted: 0,
      tackles: 0,
      interceptions: 0,
      foulsCommitted: 0,
      foulsReceived: 0,
    };

    for (const player of players) {
      await updatePlayer(clubId, player.id, {
        stats: defaultStats
      });
    }

    console.log(`Initialized stats for ${players.length} players`);
  } catch (error) {
    console.error("Error initializing player stats:", error);
    throw error;
  }
}
