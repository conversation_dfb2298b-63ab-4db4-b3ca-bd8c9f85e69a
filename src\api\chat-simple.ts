import { getSupabaseClientWithClubId } from '@/integrations/supabase/clientWithClubId';
import type { ChatRoom, ChatMessage, UserPresence } from '@/types/chat';

export const chatApi = {
  // Buscar salas do clube
  async getRooms(clubId: string): Promise<ChatRoom[]> {
    const supabase = getSupabaseClientWithClubId(Number(clubId));
    const { data, error } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('club_id', clubId)
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Buscar mensagens de uma sala
  async getMessages(roomId: string, clubId: string, limit = 50): Promise<ChatMessage[]> {
    const supabase = getSupabaseClientWithClubId(Number(clubId));
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('room_id', roomId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) throw error;
    return data || [];
  },

  // Buscar usuários online do clube
  async getOnlineUsers(clubId: string): Promise<UserPresence[]> {
    const supabase = getSupabaseClientWithClubId(Number(clubId));
    const { data, error } = await supabase
      .from('user_presence')
      .select('*')
      .eq('club_id', clubId)
      .neq('status', 'offline');

    if (error) throw error;
    return data || [];
  },

  // Marcar mensagens como lidas
  async markAsRead(roomId: string): Promise<void> {
    const supabase = getSupabaseClientWithClubId();
    const { error } = await supabase
      .from('chat_room_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', (await supabase.auth.getUser()).data.user?.id);

    if (error) throw error;
  },

  // Editar mensagem
  async editMessage(messageId: string, content: string): Promise<void> {
    const supabase = getSupabaseClientWithClubId();
    const { error } = await supabase
      .from('chat_messages')
      .update({
        content,
        edited_at: new Date().toISOString()
      })
      .eq('id', messageId);

    if (error) throw error;
  },

  // Deletar mensagem
  async deleteMessage(messageId: string): Promise<void> {
    const supabase = getSupabaseClientWithClubId();
    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('id', messageId);

    if (error) throw error;
  },

  // Sair da sala
  async leaveRoom(roomId: string): Promise<void> {
    const supabase = getSupabaseClientWithClubId();
    const { error } = await supabase
      .from('chat_room_participants')
      .delete()
      .eq('room_id', roomId)
      .eq('user_id', (await supabase.auth.getUser()).data.user?.id);

    if (error) throw error;
  }
};