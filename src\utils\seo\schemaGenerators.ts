// Geradores de Schema Markup para diferentes tipos de conteúdo

export interface ArticleData {
  headline: string;
  description: string;
  image: string;
  url: string;
  datePublished: string;
  dateModified: string;
  author?: string;
  section?: string;
  keywords?: string[];
  wordCount?: number;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface VideoData {
  name: string;
  description: string;
  thumbnailUrl: string;
  uploadDate: string;
  duration: string; // ISO 8601 format (PT4M10S)
  contentUrl?: string;
  embedUrl?: string;
  chapters?: Array<{
    name: string;
    startOffset: number;
    endOffset: number;
  }>;
}

export interface HowToStep {
  name: string;
  text: string;
  image?: string;
}

export interface HowToData {
  name: string;
  description: string;
  image: string;
  totalTime: string; // ISO 8601 format
  estimatedCost?: {
    currency: string;
    value: string;
  };
  steps: HowToStep[];
}

// Schema para artigos de blog
export function generateArticleSchema(data: ArticleData) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": data.headline,
    "description": data.description,
    "image": data.image,
    "author": {
      "@type": "Organization",
      "name": data.author || "Game Day Nexus",
      "url": "https://gamedaynexus.com/sobre"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Game Day Nexus",
      "logo": {
        "@type": "ImageObject",
        "url": "https://gamedaynexus.com/logo.png"
      }
    },
    "datePublished": data.datePublished,
    "dateModified": data.dateModified,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": data.url
    },
    ...(data.wordCount && { "wordCount": data.wordCount }),
    ...(data.section && { "articleSection": data.section }),
    ...(data.keywords && { "keywords": data.keywords })
  };
}

// Schema para páginas de FAQ
export function generateFAQSchema(faqs: FAQItem[]) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// Schema para vídeos
export function generateVideoSchema(data: VideoData) {
  const schema: any = {
    "@context": "https://schema.org",
    "@type": "VideoObject",
    "name": data.name,
    "description": data.description,
    "thumbnailUrl": data.thumbnailUrl,
    "uploadDate": data.uploadDate,
    "duration": data.duration
  };

  if (data.contentUrl) schema.contentUrl = data.contentUrl;
  if (data.embedUrl) schema.embedUrl = data.embedUrl;
  
  if (data.chapters && data.chapters.length > 0) {
    schema.hasPart = data.chapters.map(chapter => ({
      "@type": "Clip",
      "name": chapter.name,
      "startOffset": chapter.startOffset,
      "endOffset": chapter.endOffset
    }));
  }

  return schema;
}

// Schema para tutoriais (HowTo)
export function generateHowToSchema(data: HowToData) {
  return {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": data.name,
    "description": data.description,
    "image": data.image,
    "totalTime": data.totalTime,
    ...(data.estimatedCost && {
      "estimatedCost": {
        "@type": "MonetaryAmount",
        "currency": data.estimatedCost.currency,
        "value": data.estimatedCost.value
      }
    }),
    "step": data.steps.map((step, index) => ({
      "@type": "HowToStep",
      "name": step.name,
      "text": step.text,
      ...(step.image && { "image": step.image })
    }))
  };
}

// Schema para breadcrumbs
export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
}

// Schema da organização (site-wide)
export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Game Day Nexus",
    "alternateName": "GDN Platform",
    "url": "https://gamedaynexus.com",
    "logo": "https://gamedaynexus.com/logo.png",
    "description": "Plataforma completa de gestão esportiva para clubes de futebol",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+55-11-99999-9999",
      "contactType": "customer service",
      "availableLanguage": "Portuguese"
    },
    "sameAs": [
      "https://linkedin.com/company/gamedaynexus",
      "https://youtube.com/@gamedaynexus",
      "https://instagram.com/gamedaynexus"
    ],
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "BR",
      "addressRegion": "SP"
    }
  };
}

// Schema para website com busca
export function generateWebSiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Game Day Nexus Platform",
    "url": "https://gamedaynexus.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://gamedaynexus.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };
}