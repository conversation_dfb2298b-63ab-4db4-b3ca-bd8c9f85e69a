import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorBoundary, withErrorBoundary } from '../ErrorBoundary';

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error message');
  }
  return <div>No error</div>;
};

// Component for testing the HOC
const TestComponent = ({ message }: { message: string }) => (
  <div>{message}</div>
);

describe('ErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = vi.fn();
  });
  
  afterAll(() => {
    console.error = originalError;
  });

  it('should render children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('should render error UI when child component throws', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Erro na Análise')).toBeInTheDocument();
    expect(screen.getByText(/Ocorreu um erro ao carregar a análise tática/)).toBeInTheDocument();
    expect(screen.getByText('Tentar Novamente')).toBeInTheDocument();
  });

  it('should show error details when expanded', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    const detailsButton = screen.getByText('Detalhes técnicos');
    fireEvent.click(detailsButton);
    
    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  it('should call onError callback when error occurs', () => {
    const onError = vi.fn();
    
    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    );
  });

  it('should render custom fallback when provided', () => {
    const customFallback = <div>Custom error message</div>;
    
    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Custom error message')).toBeInTheDocument();
    expect(screen.queryByText('Erro na Análise')).not.toBeInTheDocument();
  });

  it('should reset error state when retry button is clicked', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Erro na Análise')).toBeInTheDocument();
    
    const retryButton = screen.getByText('Tentar Novamente');
    fireEvent.click(retryButton);
    
    // Rerender with no error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('No error')).toBeInTheDocument();
    expect(screen.queryByText('Erro na Análise')).not.toBeInTheDocument();
  });

  describe('withErrorBoundary HOC', () => {
    it('should wrap component with ErrorBoundary', () => {
      const WrappedComponent = withErrorBoundary(TestComponent);
      
      render(<WrappedComponent message="Test message" />);
      
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    it('should handle errors in wrapped component', () => {
      const ErrorComponent = ({ shouldThrow }: { shouldThrow: boolean }) => {
        if (shouldThrow) throw new Error('HOC test error');
        return <div>HOC no error</div>;
      };
      
      const WrappedErrorComponent = withErrorBoundary(ErrorComponent);
      
      render(<WrappedErrorComponent shouldThrow={true} />);
      
      expect(screen.getByText('Erro na Análise')).toBeInTheDocument();
    });

    it('should use custom fallback in HOC', () => {
      const customFallback = <div>HOC custom fallback</div>;
      const ErrorComponent = () => {
        throw new Error('HOC test error');
      };
      
      const WrappedComponent = withErrorBoundary(ErrorComponent, customFallback);
      
      render(<WrappedComponent />);
      
      expect(screen.getByText('HOC custom fallback')).toBeInTheDocument();
    });

    it('should call onError callback in HOC', () => {
      const onError = vi.fn();
      const ErrorComponent = () => {
        throw new Error('HOC test error');
      };
      
      const WrappedComponent = withErrorBoundary(ErrorComponent, undefined, onError);
      
      render(<WrappedComponent />);
      
      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });
  });
});