import { ReactNode, useState, useEffect } from "react";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Users, UserPlus, CreditCard, Mail } from "lucide-react";

interface LimitGuardProps {
  limitType: 'users' | 'players';
  children: ReactNode;
  fallback?: ReactNode;
  showWarningAt?: number; // Porcentagem para mostrar aviso (ex: 80)
}

interface LimitInfo {
  current: number;
  limit: number | null; // null = ilimitado
  canAdd: boolean;
  warningThreshold: boolean;
  percentage: number;
}

// Componente de bloqueio por limite
const LimitBlockedScreen = ({ limitType, limitInfo, planName }: { 
  limitType: 'users' | 'players'; 
  limitInfo: LimitInfo;
  planName?: string;
}) => {
  const isUsers = limitType === 'users';
  const icon = isUsers ? <Users className="w-16 h-16 text-red-500 mx-auto mb-4" /> : <UserPlus className="w-16 h-16 text-red-500 mx-auto mb-4" />;
  const title = isUsers ? 'Limite de Usuários Atingido' : 'Limite de Jogadores Atingido';
  const description = isUsers 
    ? `Você atingiu o limite de ${limitInfo.limit} usuários do seu plano.`
    : `Você atingiu o limite de ${limitInfo.limit} jogadores do seu plano.`;

  return (
    <div className="min-h-[40vh] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          {icon}
          <CardTitle className="text-2xl font-bold text-gray-900">
            {title}
          </CardTitle>
          <CardDescription className="text-lg">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-red-800 font-semibold">
              {limitInfo.current} / {limitInfo.limit} {isUsers ? 'usuários' : 'jogadores'}
            </p>
          </div>
          
          <p className="text-gray-600">
            Para adicionar mais {isUsers ? 'usuários' : 'jogadores'}, faça upgrade para um plano superior.
          </p>
          
          <div className="space-y-2">
            <Button className="w-full" onClick={() => window.open('mailto:<EMAIL>?subject=Upgrade de Plano')}>
              <CreditCard className="w-4 h-4 mr-2" />
              Fazer Upgrade
            </Button>
            <Button variant="outline" className="w-full" onClick={() => window.open('mailto:<EMAIL>')}>
              <Mail className="w-4 h-4 mr-2" />
              Falar com Suporte
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-gray-500">
              Plano atual: <span className="font-medium">{planName || 'Não definido'}</span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Componente de aviso de limite próximo
const LimitWarningBanner = ({ limitType, limitInfo, planName }: { 
  limitType: 'users' | 'players'; 
  limitInfo: LimitInfo;
  planName?: string;
}) => {
  if (!limitInfo.warningThreshold) return null;

  const isUsers = limitType === 'users';
  const remaining = limitInfo.limit! - limitInfo.current;

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3 flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-yellow-800">
                Limite de {isUsers ? 'usuários' : 'jogadores'} próximo do máximo
              </p>
              <p className="text-xs text-yellow-700 mt-1">
                {limitInfo.current} / {limitInfo.limit} utilizados ({limitInfo.percentage.toFixed(0)}%)
                <br />
                Restam apenas {remaining} {isUsers ? 'usuários' : 'jogadores'}
              </p>
            </div>
            <Button 
              size="sm" 
              variant="outline"
              className="ml-4 text-yellow-800 border-yellow-300 hover:bg-yellow-100"
              onClick={() => window.open('mailto:<EMAIL>?subject=Upgrade de Plano')}
            >
              Upgrade
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export function LimitGuard({ limitType, children, fallback, showWarningAt = 80 }: LimitGuardProps) {
  const { user } = useUser();
  const [limitInfo, setLimitInfo] = useState<LimitInfo | null>(null);
  const [loading, setLoading] = useState(true);

  const checkLimits = async () => {
    if (!user?.club_info || !user.club_id) {
      setLoading(false);
      return;
    }

    const clubInfo = user.club_info;
    const plan = clubInfo.master_plans;

    if (!plan) {
      setLoading(false);
      return;
    }

    try {
      let current = 0;
      let limit: number | null = null;

      if (limitType === 'users') {
        limit = plan.max_users;
        
        // Contar usuários ativos do clube
        const { count } = await supabase
          .from('club_members')
          .select('*', { count: 'exact', head: true })
          .eq('club_id', user.club_id);
        
        current = count || 0;
      } else if (limitType === 'players') {
        limit = plan.max_players;
        
        // Contar jogadores ativos do clube
        const { count } = await supabase
          .from('players')
          .select('*', { count: 'exact', head: true })
          .eq('club_id', user.club_id)
          .neq('status', 'inativo');
        
        current = count || 0;
      }

      // Se limite é null, significa ilimitado
      if (limit === null) {
        setLimitInfo({
          current,
          limit: null,
          canAdd: true,
          warningThreshold: false,
          percentage: 0
        });
        setLoading(false);
        return;
      }

      const percentage = (current / limit) * 100;
      const canAdd = current < limit;
      const warningThreshold = percentage >= showWarningAt;

      setLimitInfo({
        current,
        limit,
        canAdd,
        warningThreshold,
        percentage
      });

    } catch (error) {
      console.error('Erro ao verificar limites:', error);
    }

    setLoading(false);
  };

  useEffect(() => {
    checkLimits();
  }, [user, limitType]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-team-blue"></div>
      </div>
    );
  }

  if (!limitInfo) {
    return <>{children}</>;
  }

  // Se não pode adicionar mais, mostrar tela de bloqueio
  if (!limitInfo.canAdd) {
    return fallback || <LimitBlockedScreen 
      limitType={limitType} 
      limitInfo={limitInfo} 
      planName={user?.club_info?.master_plans?.name} 
    />;
  }

  return (
    <>
      <LimitWarningBanner 
        limitType={limitType} 
        limitInfo={limitInfo} 
        planName={user?.club_info?.master_plans?.name} 
      />
      {children}
    </>
  );
}

// Hook para verificar limites
export function useLimits(limitType: 'users' | 'players') {
  const { user } = useUser();
  const [limitInfo, setLimitInfo] = useState<LimitInfo | null>(null);
  const [loading, setLoading] = useState(true);

  const checkLimits = async () => {
    if (!user?.club_info || !user.club_id) {
      setLoading(false);
      return;
    }

    const plan = user.club_info.master_plans;
    if (!plan) {
      setLoading(false);
      return;
    }

    try {
      let current = 0;
      let limit: number | null = null;

      if (limitType === 'users') {
        limit = plan.max_users;
        const { count } = await supabase
          .from('club_members')
          .select('*', { count: 'exact', head: true })
          .eq('club_id', user.club_id);
        current = count || 0;
      } else if (limitType === 'players') {
        limit = plan.max_players;
        
        const { count } = await supabase
          .from('players')
          .select('*', { count: 'exact', head: true })
          .eq('club_id', user.club_id)
          .neq('status', 'inativo');
        
        current = count || 0;
      }

      const percentage = limit ? (current / limit) * 100 : 0;
      const canAdd = limit === null || current < limit;

      setLimitInfo({
        current,
        limit,
        canAdd,
        warningThreshold: percentage >= 80,
        percentage
      });

    } catch (error) {
      console.error('Erro ao verificar limites:', error);
    }

    setLoading(false);
  };

  useEffect(() => {
    checkLimits();
  }, [user, limitType]);

  return { limitInfo, loading, refreshLimits: checkLimits };
}