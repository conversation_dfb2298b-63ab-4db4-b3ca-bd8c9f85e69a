import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';
import { useChat } from '@/hooks/useChat';

export function ChatStatus() {
  const { isConnected } = useChat();

  if (isConnected === undefined) {
    return (
      <Badge variant="secondary" className="gap-1">
        <Loader2 className="h-3 w-3 animate-spin" />
        Conectando...
      </Badge>
    );
  }

  return (
    <Badge variant={isConnected ? "default" : "destructive"} className="gap-1">
      {isConnected ? (
        <>
          <Wifi className="h-3 w-3" />
          Online
        </>
      ) : (
        <>
          <WifiOff className="h-3 w-3" />
          Offline
        </>
      )}
    </Badge>
  );
}