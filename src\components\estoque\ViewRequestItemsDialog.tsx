import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { InventoryRequest, InventoryRequestItem } from "@/api/api";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import React from "react";

interface ViewRequestItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  request: InventoryRequest | null;
  items: InventoryRequestItem[];
  loading?: boolean;
}

export function ViewRequestItemsDialog({ open, onOpenChange, request, items, loading = false }: ViewRequestItemsDialogProps) {
  if (!request) return null;

  const statusColor =
    request.status === "pending"
      ? "bg-yellow-100 text-yellow-800"
      : request.status === "approved"
      ? "bg-blue-100 text-blue-800"
      : request.status === "rejected"
      ? "bg-red-100 text-red-800"
      : "bg-green-100 text-green-800";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Itens da Solicitação #{request.id}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-col gap-2">
              <div>
                <p className="text-sm font-medium">Solicitante</p>
                <p className="text-sm">{request.requester_name}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Categoria</p>
                <p className="text-sm">{request.category}</p>
              </div>
            </div>
            <Badge className={statusColor}>
              {request.status === "pending"
                ? "Pendente"
                : request.status === "approved"
                ? "Aprovado"
                : request.status === "rejected"
                ? "Rejeitado"
                : "Concluído"}
            </Badge>
          </div>

          <div>
            <p className="text-sm font-medium">Data de Retirada</p>
            <p className="text-sm">{new Date(request.withdrawal_date).toLocaleDateString()}</p>
          </div>

          <div className="border rounded-md overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produto</TableHead>
                  <TableHead>Departamento</TableHead>
                  <TableHead>Quantidade</TableHead>
                  <TableHead>Devolvido</TableHead>
                  <TableHead>Disponível</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      Carregando itens...
                    </TableCell>
                  </TableRow>
                ) : items.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      Nenhum item encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell>{item.product_department}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.returned_quantity}</TableCell>
                      <TableCell>{item.available_quantity}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}