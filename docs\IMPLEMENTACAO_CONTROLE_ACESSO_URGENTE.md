# 🚨 IMPLEMENTAÇÃO URGENTE: CONTROLE DE ACESSO POR PLANOS

## ❌ PROBLEMA CRÍTICO IDENTIFICADO

**O sistema de controle de planos existe no backend, MAS NÃO ESTÁ SENDO APLICADO no frontend!**

Qualquer clube pode acessar qualquer módulo, independente do plano contratado ou status de pagamento.

---

## 🎯 IMPLEMENTAÇÕES NECESSÁRIAS

### 1. **ATIVAR MIDDLEWARE DE ACESSO**

#### Atualizar UserContext para incluir dados do clube:
```typescript
// src/context/UserContext.tsx
export interface UserData {
  id: string;
  name: string;
  email: string;
  club_id: number;
  club_info?: {
    subscription_status: 'active' | 'suspended' | 'cancelled' | 'trial';
    master_plan_id: number;
    master_plans?: {
      name: string;
      modules: Record<string, boolean>;
      features: Record<string, any>;
      max_users: number;
      max_players: number;
    };
    is_trial: boolean;
    trial_end_date?: string;
  };
}
```

#### Modificar fetchUser para buscar dados do clube:
```typescript
const { data, error } = await supabase
  .from("users")
  .select(`
    id, name, email, first_login, profile_image, club_id,
    club_info:club_id (
      subscription_status,
      master_plan_id,
      is_trial,
      trial_end_date,
      master_plans:master_plan_id (
        name, modules, features, max_users, max_players
      )
    )
  `)
  .eq("id", userId)
  .single();
```

### 2. **CRIAR COMPONENTE DE PROTEÇÃO GLOBAL**

#### ClubAccessProvider:
```typescript
// src/context/ClubAccessContext.tsx
export const ClubAccessProvider = ({ children }) => {
  const { user } = useUser();
  const [accessInfo, setAccessInfo] = useState(null);

  useEffect(() => {
    if (user?.club_info) {
      const clubInfo = user.club_info;
      
      // Verificar se clube está suspenso
      if (clubInfo.subscription_status === 'suspended') {
        setAccessInfo({
          hasAccess: false,
          message: 'Acesso suspenso por falta de pagamento'
        });
        return;
      }

      // Verificar se trial expirou
      if (clubInfo.is_trial && clubInfo.trial_end_date) {
        const trialEnd = new Date(clubInfo.trial_end_date);
        if (trialEnd < new Date()) {
          setAccessInfo({
            hasAccess: false,
            message: 'Período de teste expirado'
          });
          return;
        }
      }

      setAccessInfo({ hasAccess: true });
    }
  }, [user]);

  if (!accessInfo?.hasAccess) {
    return <AccessBlockedScreen message={accessInfo?.message} />;
  }

  return <>{children}</>;
};
```

### 3. **PROTEGER ROTAS POR MÓDULO**

#### Componente ModuleGuard:
```typescript
// src/components/guards/ModuleGuard.tsx
export const ModuleGuard = ({ module, children, fallback }) => {
  const { user } = useUser();
  
  const hasModuleAccess = () => {
    const plan = user?.club_info?.master_plans;
    if (!plan?.modules) return false;
    return plan.modules[module] === true;
  };

  if (!hasModuleAccess()) {
    return fallback || <ModuleBlockedScreen module={module} />;
  }

  return <>{children}</>;
};
```

#### Uso nas páginas:
```typescript
// src/pages/Medical.tsx
export default function Medical() {
  return (
    <ModuleGuard module="medical">
      {/* Conteúdo médico */}
    </ModuleGuard>
  );
}
```

### 4. **VERIFICAR LIMITES DE USUÁRIOS**

#### Hook para verificar limites:
```typescript
// src/hooks/useClubLimits.ts
export const useClubLimits = () => {
  const { user } = useUser();
  
  const checkUserLimit = async () => {
    const plan = user?.club_info?.master_plans;
    if (!plan?.max_users) return { canAdd: true }; // Ilimitado
    
    const { count } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('club_id', user.club_id);
    
    return {
      canAdd: count < plan.max_users,
      current: count,
      limit: plan.max_users
    };
  };

  return { checkUserLimit };
};
```

### 5. **ATUALIZAR APP.tsx COM PROTEÇÃO**

```typescript
// src/App.tsx
function App() {
  return (
    <UserProvider>
      <ClubAccessProvider>
        <Routes>
          {/* Rotas protegidas */}
        </Routes>
      </ClubAccessProvider>
    </UserProvider>
  );
}
```

---

## 🚀 IMPLEMENTAÇÃO IMEDIATA

### Prioridade 1 (CRÍTICO):
1. ✅ Atualizar UserContext para buscar dados do clube
2. ✅ Criar ClubAccessProvider para bloquear clubes suspensos
3. ✅ Implementar ModuleGuard nas páginas principais

### Prioridade 2 (IMPORTANTE):
4. ✅ Verificar limites de usuários/jogadores
5. ✅ Adicionar avisos de trial expirando
6. ✅ Implementar verificação de storage/API calls

### Prioridade 3 (MELHORIAS):
7. ✅ Adicionar componentes visuais de upgrade
8. ✅ Implementar notificações de limite atingido
9. ✅ Criar dashboard de uso por clube

---

## 📋 CHECKLIST DE IMPLEMENTAÇÃO

- [ ] Modificar UserContext para buscar dados do clube
- [ ] Criar ClubAccessProvider
- [ ] Implementar ModuleGuard
- [ ] Proteger páginas principais (Medical, Financial, etc.)
- [ ] Verificar limites de usuários
- [ ] Testar bloqueio de clubes suspensos
- [ ] Testar expiração de trial
- [ ] Implementar avisos visuais
- [ ] Documentar uso dos componentes

---

## ⚠️ IMPACTO ATUAL

**SEM essas implementações:**
- Qualquer clube acessa qualquer módulo
- Clubes suspensos continuam usando o sistema
- Trials expirados não são bloqueados
- Limites de usuários não são respeitados
- **Você não tem controle real sobre o acesso!**

**COM as implementações:**
- Controle total sobre acesso por plano
- Bloqueio automático de clubes suspensos
- Respeito aos limites contratados
- Monetização efetiva do SaaS