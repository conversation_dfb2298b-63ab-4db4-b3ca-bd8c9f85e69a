# Mapeamento de Funções: Escalação → Convocação

## Como Funciona a Sincronização

Quando você salva uma escalação, o sistema **automaticamente** sincroniza o staff com a convocação da partida (se existir uma). Aqui está como funciona o mapeamento das funções:

## 🎯 **Prioridade do Mapeamento**

### 1. **Função Personalizada (custom_role)** - PRIORIDADE MÁXIMA
Se o membro tem uma `custom_role` definida na escalação:

#### ✅ **Função Exata** (Mapeamento Direto)
Se a função personalizada for **exatamente igual** a uma função válida da convocação:
```
Escalação: "Técnico" → Convocação: "Técnico"
Escalação: "Preparador Físico" → Convocação: "Preparador Físico"  
Escalação: "Roupeiro" → Convocação: "Roupeiro"
Escalação: "Diretor" → Convocação: "Diretor"
```

#### 🔄 **Função Personalizada** (Mapeamento por Categoria)
Se a função personalizada não for exata, o sistema categoriza automaticamente:

**Comissão Técnica:**
```
Escalação: "Técnico Principal" → Convocação: "Técnico"
Escalação: "Auxiliar Técnico" → Convocação: "Técnico"
Escalação: "Preparador de Goleiros" → Convocação: "Técnico"
```

**Staff Operacional:**
```
Escalação: "Roupeiro Principal" → Convocação: "Roupeiro"
Escalação: "Massagista" → Convocação: "Roupeiro"
Escalação: "Segurança" → Convocação: "Roupeiro"
```

**Diretoria:**
```
Escalação: "Diretor de Futebol" → Convocação: "Diretor"
Escalação: "Presidente" → Convocação: "Diretor"
Escalação: "Vice-Presidente" → Convocação: "Diretor"
```

### 2. **Categoria do Squad (role)** - FALLBACK
Se não houver `custom_role`, usa a categoria do squad:
```
technical_staff → "Técnico"
staff → "Roupeiro"  
executive → "Diretor"
```

## 📋 **Funções Válidas na Convocação**

### **Comissão Técnica:**
- Técnico
- Auxiliar Técnico
- Preparador Físico
- Médico
- Preparador de goleiro
- Psicólogo

### **Staff Operacional:**
- Roupeiro
- Supervisor
- Fisioterapeuta
- Nutricionista
- Fisiologista
- Segurança
- Motorista
- Analista de desempenho
- Assessor de Imprensa
- Massagista
- Comunicação
- Fotógrafo

### **Diretoria Executiva:**
- Diretor
- Presidente

## 🔍 **Exemplos Práticos**

### **Cenário 1: Função Exata**
```
Escalação: João Silva - "Preparador Físico" (custom_role)
Convocação: João Silva - "Preparador Físico" ✅
```

### **Cenário 2: Função Personalizada**
```
Escalação: Maria Santos - "Técnica Principal" (custom_role)
Convocação: Maria Santos - "Técnico" ✅ (mapeado por categoria)
```

### **Cenário 3: Sem Função Personalizada**
```
Escalação: Pedro Costa - technical_staff (sem custom_role)
Convocação: Pedro Costa - "Técnico" ✅ (fallback)
```

### **Cenário 4: Função Não Reconhecida**
```
Escalação: Ana Lima - "Coordenadora Especial" (custom_role)
Convocação: Ana Lima - "Roupeiro" ✅ (fallback para staff)
```

## ⚙️ **Lógica de Categorização**

O sistema usa a função `categorizeRole()` que analisa:

1. **Palavras-chave técnicas**: "técnic", "prepar", "médic", "fisio", etc.
2. **Palavras-chave executivas**: "diretor", "president", "vice", "gerente", etc.
3. **Palavras-chave de staff**: "roupeiro", "massag", "seguranç", etc.
4. **Fallback**: Se não reconhecer, categoriza como "staff"

## 🚀 **Benefícios**

✅ **Flexibilidade Total**: Use qualquer função na escalação  
✅ **Mapeamento Inteligente**: Sistema reconhece automaticamente  
✅ **Compatibilidade**: Funciona com funções existentes  
✅ **Sincronização Automática**: Não precisa fazer nada manual  
✅ **Fallback Seguro**: Sempre mapeia para alguma função válida  

## 🔧 **Para Desenvolvedores**

### **Função Principal:**
```typescript
function getCallupRoleFromSquadMember(member: MatchSquadMember): string
```

### **Fluxo:**
1. Verifica se tem `custom_role`
2. Se sim, tenta mapeamento direto
3. Se não encontrar, categoriza por palavras-chave
4. Se falhar, usa fallback baseado no `role` do squad
5. Retorna função válida para convocação

### **Localização:**
- Arquivo: `src/api/matchLineups.ts`
- Função: `getCallupRoleFromSquadMember()`
- Chamada: `syncWithCallup()` → `saveMatchLineup()`

## ✨ **Resultado Final**

Agora você pode:
- ✅ Usar **qualquer função** na escalação
- ✅ Sistema **mapeia automaticamente** para convocação
- ✅ **Funções exatas** são preservadas
- ✅ **Funções personalizadas** são categorizadas inteligentemente
- ✅ **Sincronização transparente** entre escalação e convocação