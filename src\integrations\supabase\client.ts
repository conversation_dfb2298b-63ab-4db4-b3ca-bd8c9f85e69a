
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://qoujacltecwxvymynbsh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdWphY2x0ZWN3eHZ5bXluYnNoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NDAxNTYsImV4cCI6MjA2MDUxNjE1Nn0.YKsYHPtM7VaMNUge_bEt-RIszA_n8ZHBO0T3ahjyyeI";
const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:8080";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce',
      redirectTo: SITE_URL
    }
  }
);
