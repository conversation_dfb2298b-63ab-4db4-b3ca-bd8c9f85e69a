import { supabase } from "@/integrations/supabase/client";
import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { v4 as uuidv4 } from "uuid";

// Tipos para o sistema de transferências
export interface GlobalPlayer {
  id: string;
  cpf_number: string;
  name: string;
  birthdate?: string;
  birthplace?: string;
  nationality?: string;
  rg_number?: string;
  father_name?: string;
  mother_name?: string;
  phone?: string;
  email?: string;
  height?: number;
  weight?: number;
  created_at: string;
  updated_at: string;
}

export interface GlobalPlayerDocument {
  id: number;
  document_type: string;
  file_url: string;
  original_club_id: number;
  original_club_name?: string;
  uploaded_at: string;
  file_size?: number;
  file_name?: string;
}

export interface PlayerTransfer {
  id: number;
  global_player_id: string;
  from_club_id?: number;
  to_club_id: number;
  transfer_date: string;
  transfer_type: 'transfer' | 'loan' | 'return';
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  requested_by: string;
  approved_by?: string;
  notes?: string;
  created_at: string;
  completed_at?: string;
}

export interface PlayerSearchResult {
  found: boolean;
  global_player_id?: string;
  name?: string;
  birthdate?: string;
  birthplace?: string;
  nationality?: string;
  rg_number?: string;
  father_name?: string;
  mother_name?: string;
  phone?: string;
  email?: string;
  height?: number;
  weight?: number;
  last_club_id?: number;
  last_club_name?: string;
  current_status?: string;
  is_active_elsewhere: boolean;
  active_club_name?: string;
  documents_count: number;
}

export interface TransferResult {
  success: boolean;
  player_id?: string;
  transfer_id?: number;
  global_player_id?: string;
  message: string;
}

/**
 * Busca um jogador por CPF no sistema global
 * @param cpf CPF do jogador (com ou sem formatação)
 * @returns Resultado da busca com dados do jogador
 */
export async function searchPlayerByCPF(cpf: string): Promise<PlayerSearchResult> {
  try {
    // Limpar CPF (remover formatação)
    const cleanCPF = cpf.replace(/\D/g, '');
    
    // Chamar função RPC do Supabase
    const { data, error } = await supabase.rpc('search_player_by_cpf', {
      p_cpf: cleanCPF
    });

    if (error) {
      console.error('Erro ao buscar jogador por CPF:', error);
      throw new Error(`Erro ao buscar jogador: ${error.message}`);
    }

    // A função RPC retorna um array, pegar o primeiro resultado
    const result = data && data.length > 0 ? data[0] : null;
    
    if (!result) {
      return {
        found: false,
        is_active_elsewhere: false,
        documents_count: 0
      };
    }

    return {
      found: result.found,
      global_player_id: result.global_player_id_result, // Nome correto do campo
      name: result.name,
      birthdate: result.birthdate,
      birthplace: result.birthplace,
      nationality: result.nationality,
      rg_number: result.rg_number,
      father_name: result.father_name,
      mother_name: result.mother_name,
      phone: result.phone,
      email: result.email,
      height: result.height,
      weight: result.weight,
      last_club_id: result.last_club_id,
      last_club_name: result.last_club_name,
      current_status: result.current_status,
      is_active_elsewhere: result.is_active_elsewhere || false,
      active_club_name: result.active_club_name,
      documents_count: result.documents_count || 0
    };
  } catch (error) {
    console.error('Erro na busca por CPF:', error);
    throw error;
  }
}

/**
 * Obtém os documentos de um jogador global
 * @param globalPlayerId ID do jogador global
 * @returns Lista de documentos
 */
export async function getGlobalPlayerDocuments(globalPlayerId: string): Promise<GlobalPlayerDocument[]> {
  try {
    const { data, error } = await supabase.rpc('get_global_player_documents', {
      p_global_player_id: globalPlayerId
    });

    if (error) {
      console.error('Erro ao buscar documentos do jogador global:', error);
      throw new Error(`Erro ao buscar documentos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao obter documentos globais:', error);
    throw error;
  }
}

/**
 * Inicia o processo de transferência de um jogador
 * @param cpf CPF do jogador
 * @param toClubId ID do clube de destino
 * @param playerData Dados específicos do jogador para o novo clube
 * @param userId ID do usuário que está fazendo a transferência
 * @returns Resultado da transferência
 */
export async function initiatePlayerTransfer(
  cpf: string,
  toClubId: number,
  playerData: any,
  userId: string
): Promise<TransferResult> {
  try {
    // Limpar CPF
    const cleanCPF = cpf.replace(/\D/g, '');
    
    // Chamar função RPC do Supabase
    const { data, error } = await supabase.rpc('initiate_player_transfer', {
      p_cpf: cleanCPF,
      p_to_club_id: toClubId,
      p_player_data: playerData,
      p_requested_by: userId
    });

    if (error) {
      console.error('Erro ao iniciar transferência:', error);
      throw new Error(`Erro na transferência: ${error.message}`);
    }

    // A função RPC retorna um array, pegar o primeiro resultado
    const result = data && data.length > 0 ? data[0] : null;
    
    if (!result) {
      throw new Error('Nenhum resultado retornado da transferência');
    }

    return {
      success: result.success,
      player_id: result.player_id,
      transfer_id: result.transfer_id,
      global_player_id: result.global_player_id,
      message: result.message
    };
  } catch (error) {
    console.error('Erro na transferência:', error);
    throw error;
  }
}

/**
 * Copia documentos de um jogador global para um novo jogador no clube
 * @param globalPlayerId ID do jogador global
 * @param newPlayerId ID do novo jogador no clube
 * @param newClubId ID do novo clube
 * @param documents Lista de documentos para copiar
 */
export async function copyPlayerDocuments(
  globalPlayerId: string,
  newPlayerId: string,
  newClubId: number,
  documents: GlobalPlayerDocument[]
): Promise<void> {
  try {
    for (const doc of documents) {
      // Copiar arquivo no storage para o novo clube
      const newFileUrl = await copyDocumentToNewClub(
        doc.file_url,
        newClubId,
        newPlayerId,
        doc.document_type
      );
      
      // Registrar documento para o novo jogador
      const { error } = await supabase
        .from('player_documents')
        .insert({
          club_id: newClubId,
          player_id: newPlayerId,
          document_type: doc.document_type,
          file_url: newFileUrl,
          status: 'verified' // Manter como verificado se já estava
        });

      if (error) {
        console.error(`Erro ao registrar documento ${doc.document_type}:`, error);
        // Continuar com os outros documentos mesmo se um falhar
      }
    }
  } catch (error) {
    console.error('Erro ao copiar documentos:', error);
    throw error;
  }
}

/**
 * Copia um documento para o storage do novo clube
 * @param originalUrl URL original do documento
 * @param newClubId ID do novo clube
 * @param newPlayerId ID do novo jogador
 * @param documentType Tipo do documento
 * @returns Nova URL do documento
 */
async function copyDocumentToNewClub(
  originalUrl: string,
  newClubId: number,
  newPlayerId: string,
  documentType: string
): Promise<string> {
  try {
    // Baixar arquivo original
    const response = await fetch(originalUrl);
    if (!response.ok) {
      throw new Error(`Erro ao baixar documento: ${response.statusText}`);
    }
    
    const blob = await response.blob();
    
    // Gerar novo caminho
    const fileExt = originalUrl.split('.').pop() || 'pdf';
    const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
    const newFilePath = `${newClubId}/${newPlayerId}/${fileName}`;
    
    // Upload para novo local
    const { error } = await supabase.storage
      .from('playerdocuments')
      .upload(newFilePath, blob, {
        cacheControl: '3600',
        upsert: true,
        contentType: blob.type
      });
      
    if (error) {
      throw new Error(`Erro ao fazer upload do documento: ${error.message}`);
    }
    
    // Retornar nova URL
    const { data: { publicUrl } } = supabase.storage
      .from('playerdocuments')
      .getPublicUrl(newFilePath);
      
    return publicUrl;
  } catch (error) {
    console.error('Erro ao copiar documento:', error);
    throw error;
  }
}

/**
 * Migra um jogador existente para o sistema global
 * @param playerId ID do jogador existente
 * @returns ID do jogador global criado/encontrado
 */
export async function migratePlayerToGlobal(playerId: string): Promise<string> {
  try {
    const { data, error } = await supabase.rpc('migrate_player_to_global', {
      p_player_id: playerId
    });

    if (error) {
      console.error('Erro ao migrar jogador:', error);
      throw new Error(`Erro na migração: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Erro na migração:', error);
    throw error;
  }
}

/**
 * Obtém o histórico de transferências de um jogador
 * @param globalPlayerId ID do jogador global
 * @returns Lista de transferências
 */
export async function getPlayerTransferHistory(globalPlayerId: string): Promise<PlayerTransfer[]> {
  try {
    const { data, error } = await supabase
      .from('player_transfers')
      .select(`
        *,
        from_club:from_club_id(name),
        to_club:to_club_id(name),
        requested_by_user:requested_by(name),
        approved_by_user:approved_by(name)
      `)
      .eq('global_player_id', globalPlayerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar histórico de transferências:', error);
      throw new Error(`Erro ao buscar histórico: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao obter histórico de transferências:', error);
    throw error;
  }
}

/**
 * Valida se um CPF é válido
 * @param cpf CPF a ser validado
 * @returns true se válido
 */
export function validateCPF(cpf: string): boolean {
  // Remover caracteres não numéricos
  const cleanCPF = cpf.replace(/\D/g, '');
  
  // Verificar se tem 11 dígitos
  if (cleanCPF.length !== 11) {
    return false;
  }
  
  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(cleanCPF)) {
    return false;
  }
  
  // Validar dígitos verificadores
  let sum = 0;
  let remainder;
  
  // Primeiro dígito verificador
  for (let i = 1; i <= 9; i++) {
    sum += parseInt(cleanCPF.substring(i - 1, i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) {
    remainder = 0;
  }
  if (remainder !== parseInt(cleanCPF.substring(9, 10))) {
    return false;
  }
  
  // Segundo dígito verificador
  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum += parseInt(cleanCPF.substring(i - 1, i)) * (12 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) {
    remainder = 0;
  }
  if (remainder !== parseInt(cleanCPF.substring(10, 11))) {
    return false;
  }
  
  return true;
}

/**
 * Formata um CPF adicionando a máscara
 * @param cpf CPF a ser formatado
 * @returns CPF formatado
 */
export function formatCPF(cpf: string): string {
  // Remover caracteres não numéricos
  const cleanCPF = cpf.replace(/\D/g, '');
  
  // Aplicar máscara
  return cleanCPF.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4');
}