-- Adici<PERSON>r campo slug para URLs personalizadas dos clubes
ALTER TABLE club_info ADD COLUMN slug VARCHAR(100) UNIQUE;

-- Criar índice para performance
CREATE INDEX idx_club_info_slug ON club_info(slug);

-- <PERSON><PERSON><PERSON><PERSON> coment<PERSON>rio explicativo
COMMENT ON COLUMN club_info.slug IS 'URL slug personalizada do clube (ex: corinthians, palmeiras, flamengo)';

-- Habilitar extensão unaccent se disponível, senão usar função alternativa
DO $$
BEGIN
  CREATE EXTENSION IF NOT EXISTS unaccent;
EXCEPTION
  WHEN OTHERS THEN
    -- Se não conseguir criar a extensão, criar função alternativa
    NULL;
END
$$;

-- Função para remover acentos manualmente (fallback)
CREATE OR REPLACE FUNCTION remove_accents(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN TRANSLATE(
    input_text,
    'áàâãäåāăąçćĉċčđďèéêëēĕėęěĝğġģĥħìíîïĩīĭįıĵķĸĺļľŀłñńņňŉŋòóôõöøōŏőœĝğġģŕŗřśŝşšţťŧùúûüũūŭůűųŵýÿŷźżžÁÀÂÃÄÅĀĂĄÇĆĈĊČĐĎÈÉÊËĒĔĖĘĚĜĞĠĢĤĦÌÍÎÏĨĪĬĮİĴĶĸĹĻĽĿŁÑŃŅŇŉŊÒÓÔÕÖØŌŎŐŒĜĞĠĢŔŖŘŚŜŞŠŢŤŦÙÚÛÜŨŪŬŮŰŲŴÝŸŶŹŻŽ',
    'aaaaaaaaacccccddeeeeeeeeegggghhhiiiiiiiijkklllllnnnnnooooooooooggggrrrssssttttuuuuuuuuuuwyyyzzaaaaaaaacccccddeeeeeeeeegggghhhiiiiiiiijkklllllnnnnnooooooooooggggrrrssssttttuuuuuuuuuuwyyyzzz'
  );
END;
$$ LANGUAGE plpgsql;

-- Função para gerar slug automaticamente baseado no nome
CREATE OR REPLACE FUNCTION generate_club_slug(club_name TEXT)
RETURNS TEXT AS $$
DECLARE
  clean_name TEXT;
BEGIN
  -- Tentar usar unaccent se disponível, senão usar função manual
  BEGIN
    clean_name := UNACCENT(club_name);
  EXCEPTION
    WHEN OTHERS THEN
      clean_name := remove_accents(club_name);
  END;
  
  RETURN LOWER(
    REGEXP_REPLACE(
      REGEXP_REPLACE(
        clean_name, 
        '[^a-zA-Z0-9\s-]', '', 'g'
      ), 
      '\s+', '-', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Atualizar clubes existentes com slugs baseados no nome
UPDATE club_info 
SET slug = generate_club_slug(name)
WHERE slug IS NULL;

-- Trigger para gerar slug automaticamente em novos clubes (se não fornecido)
CREATE OR REPLACE FUNCTION set_club_slug()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := generate_club_slug(NEW.name);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_club_slug
  BEFORE INSERT OR UPDATE ON club_info
  FOR EACH ROW
  EXECUTE FUNCTION set_club_slug();