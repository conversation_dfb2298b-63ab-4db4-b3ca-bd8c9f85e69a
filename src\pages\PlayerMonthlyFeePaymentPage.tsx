import { useParams } from 'react-router-dom';
import { PlayerMonthlyFeesPortal } from '@/components/monthly-fees/PlayerMonthlyFeesPortal';
import { useUser } from '@/context/UserContext';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

export default function PlayerMonthlyFeePaymentPage() {
  const { monthlyFeeId } = useParams();
  const { user } = useUser();

  if (!user?.id) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
            <p>Você precisa estar logado para acessar esta página</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Pagamento de Mensalidade</h1>
        <p className="text-muted-foreground">
          Visualize os detalhes da sua mensalidade e faça o pagamento
        </p>
      </div>
      
      <PlayerMonthlyFeesPortal userId={user.id} />
    </div>
  );
}