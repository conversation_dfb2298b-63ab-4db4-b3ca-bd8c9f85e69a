import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { 
            headers: corsHeaders,
            status: 200 
        })
    }

    try {
        // Create Supabase client with service role key
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL') ?? '',
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
            {
                auth: {
                    autoRefreshToken: false,
                    persistSession: false
                }
            }
        )

        const { clubId, email, clubName, password } = await req.json()

        // Generate temporary password if not provided
        const generatePassword = () => {
            const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
            let password = ''
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length))
            }
            return password
        }

        const temporaryPassword = password || generatePassword()

        // 1. Create user in Supabase Auth
        const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
            email: email,
            password: temporaryPassword,
            email_confirm: true,
            user_metadata: {
                name: 'Presidente',
                club_name: clubName,
                role: 'president'
            }
        })

        if (authError) {
            throw new Error(`Erro ao criar usuário no auth: ${authError.message}`)
        }

        if (!authUser.user) {
            throw new Error('Usuário não foi criado no auth')
        }

        // 2. Create record in users table
        const { error: userError } = await supabaseAdmin
            .from('users')
            .insert({
                id: authUser.user.id,
                name: 'Presidente',
                email: email,
                first_login: true
            })

        if (userError) {
            throw new Error(`Erro ao criar usuário na tabela: ${userError.message}`)
        }

        // 3. Add user as club member with president role
        const { error: memberError } = await supabaseAdmin
            .from('club_members')
            .insert({
                user_id: authUser.user.id,
                club_id: clubId,
                role: 'president',
                status: 'ativo',
                permissions: {
                    "users": { "view": true, "create": true, "edit": true, "delete": true },
                    "players": { "view": true, "create": true, "edit": true, "delete": true },
                    "matches": { "view": true, "create": true, "edit": true, "delete": true },
                    "trainings": { "view": true, "create": true, "edit": true, "delete": true },
                    "medical": { "view": true, "create": true, "edit": true, "delete": true },
                    "finances": { "view": true, "create": true, "edit": true, "delete": true },
                    "administrative": { "view": true, "create": true, "edit": true, "delete": true },
                    "settings": { "view": true, "edit": true }
                }
            })

        if (memberError) {
            throw new Error(`Erro ao adicionar usuário ao clube: ${memberError.message}`)
        }

        return new Response(
            JSON.stringify({
                success: true,
                email: email,
                password: temporaryPassword
            }),
            {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 200,
            },
        )

    } catch (error) {
        return new Response(
            JSON.stringify({
                success: false,
                error: error.message
            }),
            {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 400,
            },
        )
    }
})