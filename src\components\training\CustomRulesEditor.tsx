import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Trash2,
  Edit,
  Save,
  Copy,
  Share2,
  Download,
  Upload,
  Play,
  Pause,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Zap,
  Target,
  Timer,
  Users,
  MapPin,
  Activity
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export interface CustomRule {
  id: string;
  name: string;
  description: string;
  category: 'movement' | 'interaction' | 'timing' | 'spatial' | 'conditional';
  priority: number;
  enabled: boolean;
  conditions: RuleCondition[];
  actions: RuleAction[];
  validation: RuleValidation;
  createdAt: Date;
  updatedAt: Date;
}

export interface RuleCondition {
  id: string;
  type: 'element_position' | 'element_distance' | 'time_elapsed' | 'player_action' | 'ball_position' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'within_range';
  value: any;
  target?: string; // ID do elemento alvo
  logicalOperator?: 'AND' | 'OR';
}

export interface RuleAction {
  id: string;
  type: 'move_element' | 'highlight_element' | 'show_message' | 'play_sound' | 'pause_animation' | 'trigger_event';
  parameters: Record<string, any>;
  delay?: number;
  duration?: number;
}

export interface RuleValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface CustomRulesEditorProps {
  rules: CustomRule[];
  onRulesChange: (rules: CustomRule[]) => void;
  availableElements: Array<{ id: string; name: string; type: string }>;
  onTestRule?: (rule: CustomRule) => void;
}

export function CustomRulesEditor({
  rules,
  onRulesChange,
  availableElements,
  onTestRule
}: CustomRulesEditorProps) {
  const { toast } = useToast();
  const [selectedRule, setSelectedRule] = useState<CustomRule | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [editingRule, setEditingRule] = useState<CustomRule | null>(null);

  // Predefined rule templates
  const ruleTemplates: Partial<CustomRule>[] = [
    {
      name: "Proximidade entre jogadores",
      description: "Ação quando dois jogadores ficam muito próximos",
      category: "spatial",
      conditions: [
        {
          id: "cond_1",
          type: "element_distance",
          operator: "less_than",
          value: 5,
          logicalOperator: "AND"
        }
      ],
      actions: [
        {
          id: "act_1",
          type: "highlight_element",
          parameters: { color: "#ff0000", duration: 2000 }
        }
      ]
    },
    {
      name: "Tempo limite de exercício",
      description: "Pausa automática após tempo determinado",
      category: "timing",
      conditions: [
        {
          id: "cond_1",
          type: "time_elapsed",
          operator: "greater_than",
          value: 300
        }
      ],
      actions: [
        {
          id: "act_1",
          type: "pause_animation",
          parameters: {}
        },
        {
          id: "act_2",
          type: "show_message",
          parameters: { message: "Tempo limite atingido!", type: "warning" }
        }
      ]
    },
    {
      name: "Bola fora de campo",
      description: "Ação quando a bola sai dos limites do campo",
      category: "spatial",
      conditions: [
        {
          id: "cond_1",
          type: "ball_position",
          operator: "not_equals",
          value: "within_field"
        }
      ],
      actions: [
        {
          id: "act_1",
          type: "show_message",
          parameters: { message: "Bola fora de campo!", type: "info" }
        },
        {
          id: "act_2",
          type: "play_sound",
          parameters: { sound: "whistle" }
        }
      ]
    }
  ];

  const createNewRule = useCallback(() => {
    const newRule: CustomRule = {
      id: `rule_${Date.now()}`,
      name: "Nova Regra",
      description: "",
      category: "movement",
      priority: 1,
      enabled: true,
      conditions: [],
      actions: [],
      validation: { isValid: false, errors: [], warnings: [] },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setEditingRule(newRule);
    setIsEditing(true);
  }, []);

  const validateRule = useCallback((rule: CustomRule): RuleValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validação básica
    if (!rule.name.trim()) {
      errors.push("Nome da regra é obrigatório");
    }

    if (rule.conditions.length === 0) {
      errors.push("Pelo menos uma condição é necessária");
    }

    if (rule.actions.length === 0) {
      errors.push("Pelo menos uma ação é necessária");
    }

    // Validação de condições
    rule.conditions.forEach((condition, index) => {
      if (!condition.type) {
        errors.push(`Condição ${index + 1}: Tipo é obrigatório`);
      }
      if (!condition.operator) {
        errors.push(`Condição ${index + 1}: Operador é obrigatório`);
      }
      if (condition.value === undefined || condition.value === null) {
        errors.push(`Condição ${index + 1}: Valor é obrigatório`);
      }
    });

    // Validação de ações
    rule.actions.forEach((action, index) => {
      if (!action.type) {
        errors.push(`Ação ${index + 1}: Tipo é obrigatório`);
      }
    });

    // Avisos
    if (rule.priority > 10) {
      warnings.push("Prioridade muito alta pode afetar performance");
    }

    if (rule.conditions.length > 5) {
      warnings.push("Muitas condições podem tornar a regra complexa");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, []);

  const saveRule = useCallback(() => {
    if (!editingRule) return;

    const validation = validateRule(editingRule);
    const updatedRule = {
      ...editingRule,
      validation,
      updatedAt: new Date()
    };

    if (!validation.isValid) {
      toast({
        title: "Erro de Validação",
        description: validation.errors.join(", "),
        variant: "destructive"
      });
      return;
    }

    const existingIndex = rules.findIndex(r => r.id === updatedRule.id);
    let newRules;

    if (existingIndex >= 0) {
      newRules = [...rules];
      newRules[existingIndex] = updatedRule;
    } else {
      newRules = [...rules, updatedRule];
    }

    onRulesChange(newRules);
    setIsEditing(false);
    setEditingRule(null);

    toast({
      title: "Regra Salva",
      description: `Regra "${updatedRule.name}" foi salva com sucesso!`
    });
  }, [editingRule, rules, onRulesChange, validateRule, toast]);

  const deleteRule = useCallback((ruleId: string) => {
    const newRules = rules.filter(r => r.id !== ruleId);
    onRulesChange(newRules);
    
    toast({
      title: "Regra Removida",
      description: "A regra foi removida com sucesso!"
    });
  }, [rules, onRulesChange, toast]);

  const duplicateRule = useCallback((rule: CustomRule) => {
    const duplicatedRule: CustomRule = {
      ...rule,
      id: `rule_${Date.now()}`,
      name: `${rule.name} (Cópia)`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    onRulesChange([...rules, duplicatedRule]);
    
    toast({
      title: "Regra Duplicada",
      description: `Regra "${duplicatedRule.name}" foi criada!`
    });
  }, [rules, onRulesChange, toast]);

  const addCondition = useCallback(() => {
    if (!editingRule) return;

    const newCondition: RuleCondition = {
      id: `cond_${Date.now()}`,
      type: 'element_position',
      operator: 'equals',
      value: '',
      logicalOperator: 'AND'
    };

    setEditingRule({
      ...editingRule,
      conditions: [...editingRule.conditions, newCondition]
    });
  }, [editingRule]);

  const addAction = useCallback(() => {
    if (!editingRule) return;

    const newAction: RuleAction = {
      id: `act_${Date.now()}`,
      type: 'highlight_element',
      parameters: {}
    };

    setEditingRule({
      ...editingRule,
      actions: [...editingRule.actions, newAction]
    });
  }, [editingRule]);

  const updateCondition = useCallback((conditionId: string, updates: Partial<RuleCondition>) => {
    if (!editingRule) return;

    setEditingRule({
      ...editingRule,
      conditions: editingRule.conditions.map(c =>
        c.id === conditionId ? { ...c, ...updates } : c
      )
    });
  }, [editingRule]);

  const updateAction = useCallback((actionId: string, updates: Partial<RuleAction>) => {
    if (!editingRule) return;

    setEditingRule({
      ...editingRule,
      actions: editingRule.actions.map(a =>
        a.id === actionId ? { ...a, ...updates } : a
      )
    });
  }, [editingRule]);

  const removeCondition = useCallback((conditionId: string) => {
    if (!editingRule) return;

    setEditingRule({
      ...editingRule,
      conditions: editingRule.conditions.filter(c => c.id !== conditionId)
    });
  }, [editingRule]);

  const removeAction = useCallback((actionId: string) => {
    if (!editingRule) return;

    setEditingRule({
      ...editingRule,
      actions: editingRule.actions.filter(a => a.id !== actionId)
    });
  }, [editingRule]);

  const applyTemplate = useCallback((template: Partial<CustomRule>) => {
    const newRule: CustomRule = {
      id: `rule_${Date.now()}`,
      name: template.name || "Nova Regra",
      description: template.description || "",
      category: template.category || "movement",
      priority: template.priority || 1,
      enabled: true,
      conditions: template.conditions || [],
      actions: template.actions || [],
      validation: { isValid: false, errors: [], warnings: [] },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setEditingRule(newRule);
    setIsEditing(true);
    setShowTemplates(false);
  }, []);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'movement': return <Activity className="h-4 w-4" />;
      case 'interaction': return <Users className="h-4 w-4" />;
      case 'timing': return <Timer className="h-4 w-4" />;
      case 'spatial': return <MapPin className="h-4 w-4" />;
      case 'conditional': return <Zap className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'movement': return 'bg-blue-100 text-blue-800';
      case 'interaction': return 'bg-green-100 text-green-800';
      case 'timing': return 'bg-yellow-100 text-yellow-800';
      case 'spatial': return 'bg-purple-100 text-purple-800';
      case 'conditional': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Regras Personalizadas</h3>
          <p className="text-sm text-muted-foreground">
            Configure regras customizadas para controlar o comportamento dos drills
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowTemplates(true)}>
            <Target className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button size="sm" onClick={createNewRule}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Regra
          </Button>
        </div>
      </div>

      {/* Rules List */}
      <div className="grid gap-4">
        {rules.map((rule) => (
          <Card key={rule.id} className={`${!rule.enabled ? 'opacity-60' : ''}`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(rule.category)}
                    <Badge className={getCategoryColor(rule.category)}>
                      {rule.category}
                    </Badge>
                  </div>
                  <div>
                    <CardTitle className="text-base">{rule.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {rule.description}
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    {rule.validation.isValid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    {rule.validation.warnings.length > 0 && (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <Switch
                    checked={rule.enabled}
                    onCheckedChange={(enabled) => {
                      const updatedRules = rules.map(r =>
                        r.id === rule.id ? { ...r, enabled } : r
                      );
                      onRulesChange(updatedRules);
                    }}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onTestRule?.(rule)}
                    disabled={!rule.validation.isValid}
                  >
                    <Play className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => duplicateRule(rule)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      setEditingRule(rule);
                      setIsEditing(true);
                    }}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                        <AlertDialogDescription>
                          Tem certeza que deseja excluir a regra "{rule.name}"? Esta ação não pode ser desfeita.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction onClick={() => deleteRule(rule.id)}>
                          Excluir
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-xs font-medium text-muted-foreground">
                    Condições ({rule.conditions.length})
                  </Label>
                  <div className="mt-1 space-y-1">
                    {rule.conditions.slice(0, 2).map((condition, index) => (
                      <div key={condition.id} className="text-xs text-muted-foreground">
                        {condition.type} {condition.operator} {String(condition.value)}
                      </div>
                    ))}
                    {rule.conditions.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{rule.conditions.length - 2} mais...
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <Label className="text-xs font-medium text-muted-foreground">
                    Ações ({rule.actions.length})
                  </Label>
                  <div className="mt-1 space-y-1">
                    {rule.actions.slice(0, 2).map((action, index) => (
                      <div key={action.id} className="text-xs text-muted-foreground">
                        {action.type}
                      </div>
                    ))}
                    {rule.actions.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{rule.actions.length - 2} mais...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {rules.length === 0 && (
          <Card className="p-8 text-center">
            <div className="flex flex-col items-center gap-4">
              <Target className="h-12 w-12 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-medium">Nenhuma regra criada</h3>
                <p className="text-sm text-muted-foreground">
                  Crie sua primeira regra personalizada para controlar o comportamento dos drills
                </p>
              </div>
              <Button onClick={createNewRule}>
                <Plus className="h-4 w-4 mr-2" />
                Criar Primeira Regra
              </Button>
            </div>
          </Card>
        )}
      </div>

      {/* Rule Editor Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              {editingRule?.id.startsWith('rule_') ? 'Nova Regra' : 'Editar Regra'}
            </DialogTitle>
            <DialogDescription>
              Configure as condições e ações para sua regra personalizada
            </DialogDescription>
          </DialogHeader>

          {editingRule && (
            <div className="flex-1 overflow-hidden">
              <Tabs defaultValue="basic" className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Básico</TabsTrigger>
                  <TabsTrigger value="conditions">Condições</TabsTrigger>
                  <TabsTrigger value="actions">Ações</TabsTrigger>
                </TabsList>

                <div className="flex-1 overflow-hidden">
                  <TabsContent value="basic" className="space-y-4 h-full">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Nome da Regra</Label>
                        <Input
                          value={editingRule.name}
                          onChange={(e) => setEditingRule({
                            ...editingRule,
                            name: e.target.value
                          })}
                          placeholder="Digite o nome da regra"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Categoria</Label>
                        <Select
                          value={editingRule.category}
                          onValueChange={(value: any) => setEditingRule({
                            ...editingRule,
                            category: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="movement">Movimento</SelectItem>
                            <SelectItem value="interaction">Interação</SelectItem>
                            <SelectItem value="timing">Timing</SelectItem>
                            <SelectItem value="spatial">Espacial</SelectItem>
                            <SelectItem value="conditional">Condicional</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Descrição</Label>
                      <Textarea
                        value={editingRule.description}
                        onChange={(e) => setEditingRule({
                          ...editingRule,
                          description: e.target.value
                        })}
                        placeholder="Descreva o que esta regra faz"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Prioridade (1-10)</Label>
                        <Input
                          type="number"
                          min="1"
                          max="10"
                          value={editingRule.priority}
                          onChange={(e) => setEditingRule({
                            ...editingRule,
                            priority: parseInt(e.target.value) || 1
                          })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Status</Label>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={editingRule.enabled}
                            onCheckedChange={(enabled) => setEditingRule({
                              ...editingRule,
                              enabled
                            })}
                          />
                          <Label className="text-sm">
                            {editingRule.enabled ? 'Ativada' : 'Desativada'}
                          </Label>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="conditions" className="space-y-4 h-full">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Condições</Label>
                      <Button size="sm" onClick={addCondition}>
                        <Plus className="h-4 w-4 mr-2" />
                        Adicionar Condição
                      </Button>
                    </div>

                    <ScrollArea className="h-[400px]">
                      <div className="space-y-4">
                        {editingRule.conditions.map((condition, index) => (
                          <Card key={condition.id} className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <Label className="text-sm font-medium">
                                Condição {index + 1}
                              </Label>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => removeCondition(condition.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-3 gap-3">
                              <div className="space-y-2">
                                <Label className="text-xs">Tipo</Label>
                                <Select
                                  value={condition.type}
                                  onValueChange={(value: any) => updateCondition(condition.id, { type: value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="element_position">Posição do Elemento</SelectItem>
                                    <SelectItem value="element_distance">Distância entre Elementos</SelectItem>
                                    <SelectItem value="time_elapsed">Tempo Decorrido</SelectItem>
                                    <SelectItem value="player_action">Ação do Jogador</SelectItem>
                                    <SelectItem value="ball_position">Posição da Bola</SelectItem>
                                    <SelectItem value="custom">Personalizada</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-2">
                                <Label className="text-xs">Operador</Label>
                                <Select
                                  value={condition.operator}
                                  onValueChange={(value: any) => updateCondition(condition.id, { operator: value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="equals">Igual a</SelectItem>
                                    <SelectItem value="not_equals">Diferente de</SelectItem>
                                    <SelectItem value="greater_than">Maior que</SelectItem>
                                    <SelectItem value="less_than">Menor que</SelectItem>
                                    <SelectItem value="contains">Contém</SelectItem>
                                    <SelectItem value="within_range">Dentro do intervalo</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-2">
                                <Label className="text-xs">Valor</Label>
                                <Input
                                  value={condition.value}
                                  onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
                                  placeholder="Valor da condição"
                                />
                              </div>
                            </div>

                            {index < editingRule.conditions.length - 1 && (
                              <div className="mt-3 pt-3 border-t">
                                <Select
                                  value={condition.logicalOperator || 'AND'}
                                  onValueChange={(value: any) => updateCondition(condition.id, { logicalOperator: value })}
                                >
                                  <SelectTrigger className="w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="AND">E</SelectItem>
                                    <SelectItem value="OR">OU</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                          </Card>
                        ))}

                        {editingRule.conditions.length === 0 && (
                          <div className="text-center py-8 text-muted-foreground">
                            <Info className="h-8 w-8 mx-auto mb-2" />
                            <p>Nenhuma condição adicionada</p>
                            <p className="text-sm">Clique em "Adicionar Condição" para começar</p>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="actions" className="space-y-4 h-full">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Ações</Label>
                      <Button size="sm" onClick={addAction}>
                        <Plus className="h-4 w-4 mr-2" />
                        Adicionar Ação
                      </Button>
                    </div>

                    <ScrollArea className="h-[400px]">
                      <div className="space-y-4">
                        {editingRule.actions.map((action, index) => (
                          <Card key={action.id} className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <Label className="text-sm font-medium">
                                Ação {index + 1}
                              </Label>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => removeAction(action.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-3">
                              <div className="space-y-2">
                                <Label className="text-xs">Tipo de Ação</Label>
                                <Select
                                  value={action.type}
                                  onValueChange={(value: any) => updateAction(action.id, { type: value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="move_element">Mover Elemento</SelectItem>
                                    <SelectItem value="highlight_element">Destacar Elemento</SelectItem>
                                    <SelectItem value="show_message">Mostrar Mensagem</SelectItem>
                                    <SelectItem value="play_sound">Tocar Som</SelectItem>
                                    <SelectItem value="pause_animation">Pausar Animação</SelectItem>
                                    <SelectItem value="trigger_event">Disparar Evento</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-2">
                                <Label className="text-xs">Atraso (ms)</Label>
                                <Input
                                  type="number"
                                  value={action.delay || 0}
                                  onChange={(e) => updateAction(action.id, { delay: parseInt(e.target.value) || 0 })}
                                  placeholder="0"
                                />
                              </div>
                            </div>

                            {/* Action-specific parameters */}
                            <div className="mt-3 space-y-2">
                              <Label className="text-xs">Parâmetros</Label>
                              <Textarea
                                value={JSON.stringify(action.parameters, null, 2)}
                                onChange={(e) => {
                                  try {
                                    const params = JSON.parse(e.target.value);
                                    updateAction(action.id, { parameters: params });
                                  } catch (error) {
                                    // Invalid JSON, ignore
                                  }
                                }}
                                placeholder='{"key": "value"}'
                                rows={3}
                                className="font-mono text-xs"
                              />
                            </div>
                          </Card>
                        ))}

                        {editingRule.actions.length === 0 && (
                          <div className="text-center py-8 text-muted-foreground">
                            <Zap className="h-8 w-8 mx-auto mb-2" />
                            <p>Nenhuma ação adicionada</p>
                            <p className="text-sm">Clique em "Adicionar Ação" para começar</p>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <div className="flex items-center gap-2">
              {editingRule?.validation && !editingRule.validation.isValid && (
                <div className="flex items-center gap-1 text-red-600">
                  <XCircle className="h-4 w-4" />
                  <span className="text-sm">
                    {editingRule.validation.errors.length} erro(s)
                  </span>
                </div>
              )}
              {editingRule?.validation && editingRule.validation.warnings.length > 0 && (
                <div className="flex items-center gap-1 text-yellow-600">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">
                    {editingRule.validation.warnings.length} aviso(s)
                  </span>
                </div>
              )}
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancelar
              </Button>
              <Button onClick={saveRule}>
                <Save className="h-4 w-4 mr-2" />
                Salvar Regra
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Templates Dialog */}
      <Dialog open={showTemplates} onOpenChange={setShowTemplates}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Templates de Regras</DialogTitle>
            <DialogDescription>
              Escolha um template para começar rapidamente
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4">
            {ruleTemplates.map((template, index) => (
              <Card key={index} className="cursor-pointer hover:bg-muted/50" onClick={() => applyTemplate(template)}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    {getCategoryIcon(template.category || 'movement')}
                    <div>
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {template.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{template.conditions?.length || 0} condições</span>
                    <span>{template.actions?.length || 0} ações</span>
                    <Badge className={getCategoryColor(template.category || 'movement')}>
                      {template.category}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}