import { createClient, type SupabaseClient } from '@supabase/supabase-js';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://qoujacltecwxvymynbsh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdWphY2x0ZWN3eHZ5bXluYnNoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NDAxNTYsImV4cCI6MjA2MDUxNjE1Nn0.YKsYHPtM7VaMNUge_bEt-RIszA_n8ZHBO0T3ahjyyeI";
const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";

/**
 * Cria um cliente Supabase com o club_id configurado nos headers
 * @param clubId ID do clube a ser incluído nos headers
 * @returns Cliente Supabase configurado
 */
const clients: Record<string, SupabaseClient> = {};

export function getSupabaseClientWithClubId(clubId?: number) {
  let resolvedClubId = clubId;

  // Se não foi passado explicitamente, tentar obter do localStorage
  if (!resolvedClubId) {
    const stored = typeof localStorage !== 'undefined' ? localStorage.getItem('clubId') : null;
    if (stored) {
      resolvedClubId = Number(stored);
    }
  }

  console.log(`[DEBUG] Criando cliente Supabase com club_id: ${resolvedClubId}`);

  // Garantir que o clubId seja um número válido
  if (!resolvedClubId || isNaN(Number(resolvedClubId)) || Number(resolvedClubId) <= 0) {
    console.error(`[ERROR] club_id inválido: ${resolvedClubId}`);
    throw new Error(`Invalid club_id: ${resolvedClubId}`);
  }

  const clubIdStr = resolvedClubId.toString();
  console.log(`[DEBUG] club_id como string: ${clubIdStr}`);

  if (clients[clubIdStr]) {
    return clients[clubIdStr];
  }

  const client = createClient(
    SUPABASE_URL,
    SUPABASE_PUBLISHABLE_KEY,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        redirectTo: SITE_URL
      },
      global: {
        headers: {
          'x-club-id': clubIdStr
        }
      }
    }
  );

  // Adicionar interceptor para verificar se o header está sendo enviado
  // Bind ao fetch original para preservar o contexto e headers padrão
  const originalFetch = client.rest.fetch.bind(client.rest);
  client.rest.fetch = async (url, options = {}) => {
    console.log(`[DEBUG] Enviando requisição para ${url}`);

    const baseHeaders = client.rest.headers as Record<string, string>;
    const mergedHeaders = {
      ...baseHeaders,
      ...(options.headers ? (options.headers as Record<string, string>) : {}),
    };

    if (!mergedHeaders.apikey && baseHeaders.apikey) {
      mergedHeaders.apikey = baseHeaders.apikey;
    }

    if (!mergedHeaders.Authorization && baseHeaders.Authorization) {
      mergedHeaders.Authorization = baseHeaders.Authorization;
    }

    console.log(`[DEBUG] Headers finais:`, mergedHeaders);

    return originalFetch(url, { ...options, headers: mergedHeaders });
  };

  clients[clubIdStr] = client;
  return client;
}