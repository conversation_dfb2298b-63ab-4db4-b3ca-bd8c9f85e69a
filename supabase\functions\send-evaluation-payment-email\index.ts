import { serve } from "https://deno.land/std@0.177.0/http/server.ts"

const BREVO_API_KEY = Deno.env.get('BREVO_API_KEY') ?? ''
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email'
const SENDER_NAME = 'GameDayNexus'
const SENDER_EMAIL = '<EMAIL>'

interface EmailRequest {
    to: string
    playerName: string
    amount: number
    periodDescription: string
    pixKey: string
    pixCode: string
    qrCodeData: string
    receiptUploadUrl: string
    clubName: string
}

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    if (req.method !== 'POST') {
        return new Response('Method not allowed', {
            status: 405,
            headers: corsHeaders
        })
    }

    try {
        const {
            to,
            playerName,
            amount,
            periodDescription,
            pixKey,
            pixCode,
            qrCodeData,
            receiptUploadUrl,
            clubName
        }: EmailRequest = await req.json()

        if (!BREVO_API_KEY) {
            console.log('Missing BREVO_API_KEY')
            return new Response(
                JSON.stringify({ error: 'Configuração de email ausente' }),
                {
                    status: 500,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                }
            )
        }

        // Format amount in Brazilian currency
        const formattedAmount = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(amount)

        // Create email HTML content
        const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pagamento de Avaliação - ${clubName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .content { background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
          .payment-details { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .pix-section { background-color: #f1f8e9; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .qr-code { text-align: center; margin: 20px 0; }
          .qr-code img { max-width: 200px; border: 1px solid #ddd; border-radius: 8px; }
          .pix-code { background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all; font-size: 12px; }
          .upload-section { background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .button { display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Pagamento de Avaliação</h1>
            <p><strong>${clubName}</strong></p>
          </div>
          
          <div class="content">
            <p>Olá, <strong>${playerName}</strong>!</p>
            
            <p>Recebemos com sucesso sua inscrição. Para que possamos agendar sua apresentação, é necessário efetuar o pagamento da taxa no valor de <strong>${formattedAmount}</strong>, referente à alimentação e hospedagem no período de <strong>${periodDescription}</strong>.</p>
            
            <div class="payment-details">
              <h3>Detalhes do Pagamento</h3>
              <p><strong>Valor:</strong> ${formattedAmount}</p>
              <p><strong>Período:</strong> ${periodDescription}</p>
              <p><strong>Chave PIX:</strong> ${pixKey}</p>
            </div>
            
            <div class="pix-section">
              <h3>Pagamento via PIX</h3>
              <p>Você pode pagar de duas formas:</p>
              
              <h4>1. Escaneando o QR Code:</h4>
              <div class="qr-code">
                <img src="${qrCodeData}" alt="QR Code PIX" />
              </div>
              
              <h4>2. Copiando o código PIX:</h4>
              <div class="pix-code">${pixCode}</div>
              <p><small>Copie este código e cole no seu aplicativo bancário na opção "Pix Copia e Cola"</small></p>
            </div>
            
            <div class="upload-section">
              <h3>⚠️ Importante - Envio do Comprovante</h3>
              <p>Após realizar o pagamento, é <strong>obrigatório</strong> anexar o comprovante para dar continuidade ao processo.</p>
              
              <p style="text-align: center;">
                <a href="${receiptUploadUrl}" class="button">📎 Enviar Comprovante de Pagamento</a>
              </p>
              
              <p><small>Clique no botão acima ou acesse: ${receiptUploadUrl}</small></p>
            </div>
            
            <p>Em caso de dúvidas, estamos à disposição.</p>
            
            <p>Atenciosamente,<br>
            <strong>${clubName}</strong></p>
          </div>
          
          <div class="footer">
            <p>Este é um email automático, não responda a esta mensagem.</p>
            <p>Para dúvidas, entre em contato diretamente com o clube.</p>
          </div>
        </div>
      </body>
      </html>
    `

        // Create plain text version

        // Send email via Brevo
        const emailData = {
            sender: {
                name: SENDER_NAME,
                email: SENDER_EMAIL
            },
            to: [
                {
                    email: to,
                    name: playerName
                }
            ],
            subject: `Pagamento de Avaliação - ${clubName}`,
            htmlContent: htmlContent
        }

        const response = await fetch(BREVO_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'api-key': BREVO_API_KEY
            },
            body: JSON.stringify(emailData)
        })

        if (!response.ok) {
            const errorData = await response.text()
            console.log('Brevo error', errorData)
            throw new Error(`Erro ao enviar email: ${response.status}`)
        }

        const result = await response.json()

        return new Response(
            JSON.stringify({ success: true, messageId: result.messageId }),
            {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
        )

    } catch (error) {
        console.error('Error sending evaluation payment email:', error)

        return new Response(
            JSON.stringify({ error: error.message }),
            {
                status: 500,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
        )
    }
})