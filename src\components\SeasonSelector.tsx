import { useEffect, useState } from "react";
import { useSeasonStore } from "@/store/useSeasonStore";
import { getSeasons, deleteSeason } from "@/api/seasonApi";
import { useCurrentClubId } from "@/context/ClubContext";
import { SeasonDialog } from "@/components/modals/SeasonDialog";
import { SeasonManagerDialog } from "@/components/modals/SeasonManagerDialog";
import { Pencil, Trash2, Plus, Settings } from "lucide-react";
import { toast } from "react-toastify";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

export function SeasonSelector() {
  const clubId = useCurrentClubId();
  const { seasons, activeSeason, setActiveSeason, fetchSeasons } = useSeasonStore();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editDialog, setEditDialog] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [deleteConfirm, setDeleteConfirm] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [managerOpen, setManagerOpen] = useState(false);

  useEffect(() => {
    if (clubId) fetchSeasons(clubId);
  }, [clubId, fetchSeasons]);

  // Notifica o usuário se a temporada acabou
  useEffect(() => {
    if (activeSeason && activeSeason.end_date) {
      const today = new Date();
      const end = new Date(activeSeason.end_date);
      if (today >= end) {
        toast.info(
          <span>
            A temporada <b>{activeSeason.name}</b> acabou! <br />
            <button
              className="underline text-blue-600 hover:text-blue-800"
              onClick={() => window.location.href = '/estatisticas'}
            >Ver estatísticas totais</button>
          </span>,
          { autoClose: false, toastId: `season-ended-${activeSeason.id}` }
        );
      }
    }
  }, [activeSeason]);

  return (
    <div className="flex items-center gap-2">
      <Select
        value={activeSeason?.id?.toString() || ""}
        onValueChange={(value) => {
          const selected = seasons.find((s) => s.id.toString() === value);
          if (selected) setActiveSeason(selected);
        }}
      >
        <SelectTrigger className="bg-white/90 hover:bg-white text-black border-gray-300 min-w-[100px] md:min-w-[120px] h-8 text-xs sm:text-sm">
          <SelectValue placeholder="Temp." />
        </SelectTrigger>
        <SelectContent>
          {seasons.length > 0 ? (
            seasons.map((season) => (
              <SelectItem key={season.id} value={season.id.toString()}>
                {season.name}
              </SelectItem>
            ))
          ) : (
            <p className="p-4 text-sm text-gray-500">Nenhuma temporada encontrada.</p>
          )}
        </SelectContent>
      </Select>

      <Button variant="outline" size="sm" className="h-8 px-2 hidden lg:flex" onClick={() => setManagerOpen(true)}>
        <Settings size={14} className="mr-1" /> 
        <span className="hidden xl:inline">Gerenciar</span>
      </Button>

      {/* Dialogs remain unchanged */}
      <SeasonDialog open={dialogOpen} onOpenChange={setDialogOpen} clubId={clubId} />
      {editDialog.open && (
        <SeasonDialog
          open={editDialog.open}
          onOpenChange={(open) => setEditDialog({ open, season: open ? editDialog.season : null })}
          clubId={clubId}
          season={{
            ...editDialog.season,
            club_id: (editDialog.season && 'club_id' in editDialog.season)
              ? editDialog.season.club_id
              : clubId
          }}
        />
      )}
      {deleteConfirm.open && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
          <div className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3">
            <h2 className="font-semibold text-lg mb-2">Excluir Temporada</h2>
            <p>Tem certeza que deseja excluir a temporada <b>{deleteConfirm.season.name}</b>?</p>
            <div className="flex gap-2 mt-2">
              <Button variant="outline" onClick={() => setDeleteConfirm({ open: false, season: null })}>
                Cancelar
              </Button>
              <Button variant="destructive"
                onClick={async () => {
                  try {
                    if (deleteConfirm.season) {
                      const response = await fetch('/api/seasons/' + deleteConfirm.season.id, { method: 'DELETE' });
                      if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Erro ao excluir temporada');
                      }
                      toast.success('Temporada excluída com sucesso');
                      if (activeSeason && activeSeason.id === deleteConfirm.season.id) {
                        setActiveSeason(null);
                      }
                      await fetchSeasons(clubId);
                    }
                  } catch (error: any) {
                    toast.error(error.message || 'Erro ao excluir temporada');
                  } finally {
                    setDeleteConfirm({ open: false, season: null });
                  }
                }}>
                Excluir
              </Button>
            </div>
          </div>
        </div>
      )}
      <SeasonManagerDialog open={managerOpen} onOpenChange={setManagerOpen} clubId={clubId} />
    </div>
  );
}
