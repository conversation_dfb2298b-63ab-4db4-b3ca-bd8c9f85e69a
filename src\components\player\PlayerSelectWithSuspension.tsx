import React, { useEffect, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SuspensionIndicator } from './SuspensionIndicator';
import { isPlayerSuspendedForMatch } from '@/api/cardSuspensions';
import type { Player } from '@/api/api';

interface PlayerSelectWithSuspensionProps {
  players: Player[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  clubId: number;
  disabled?: boolean;
  className?: string;
  competitionId?: string;
  categoryId?: number;
}

interface PlayerSuspensionInfo {
  [playerId: string]: {
    isSuspended: boolean;
    reason?: string;
    matchesRemaining?: number;
  };
}

export function PlayerSelectWithSuspension({
  players,
  value,
  onValueChange,
  placeholder = "Selecione um jogador",
  clubId,
  disabled = false,
  className = "",
  competitionId,
  categoryId
}: PlayerSelectWithSuspensionProps) {
  const [suspensionInfo, setSuspensionInfo] = useState<PlayerSuspensionInfo>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSuspensionInfo = async () => {
      if (!clubId || players.length === 0) {
        setLoading(false);
        return;
      }

      const info: PlayerSuspensionInfo = {};
      
      try {
        await Promise.all(
          players.map(async (player) => {
            const suspensionData = await isPlayerSuspendedForMatch(clubId, player.id, competitionId, categoryId);
            info[player.id] = suspensionData;
          })
        );
        
        setSuspensionInfo(info);
      } catch (error) {
        console.error('Erro ao carregar informações de suspensão:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSuspensionInfo();
  }, [clubId, players, competitionId, categoryId]);

  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Carregando..." />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {players.map((player) => {
          const suspension = suspensionInfo[player.id];
          const isSuspended = suspension?.isSuspended || false;
          
          return (
            <SelectItem 
              key={player.id} 
              value={player.id}
              disabled={isSuspended}
              className={isSuspended ? "opacity-50" : ""}
            >
              <div className="flex items-center justify-between w-full">
                <span>
                  {player.name} ({player.position})
                </span>
                {isSuspended && (
                  <SuspensionIndicator
                    isSuspended={true}
                    reason={suspension.reason}
                    matchesRemaining={suspension.matchesRemaining}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}