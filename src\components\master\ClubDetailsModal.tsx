import React, { useState, useEffect } from 'react';
import { 
  X, 
  Building2, 
  Edit, 
  Users, 
  UserCheck, 
  Calendar,
  CreditCard,
  BarChart3,
  <PERSON><PERSON>s,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle
} from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { MasterClub, getClubUsageStats } from '@/api/masterClubs';

interface ClubDetailsModalProps {
  club: MasterClub;
  onClose: () => void;
  onEdit: (club: MasterClub) => void;
}

interface UsageStats {
  usage: {
    users: number;
    players: number;
    matches: number;
  };
  limits: {
    max_users: number;
    max_players: number;
    max_matches_per_month: number;
  };
  percentages: {
    users: number;
    players: number;
  };
}

export const ClubDetailsModal: React.FC<ClubDetailsModalProps> = ({
  club,
  onClose,
  onEdit
}) => {
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(true);

  useEffect(() => {
    loadUsageStats();
  }, [club.id]);

  const loadUsageStats = async () => {
    try {
      setLoadingStats(true);
      const stats = await getClubUsageStats(club.id);
      setUsageStats(stats);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case 'trial':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspenso</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'current':
        return <Badge className="bg-green-100 text-green-800">Em Dia</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Em Atraso</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800">Cancelado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Building2 className="w-8 h-8 text-blue-600" />
            <div>
              <h2 className="text-2xl font-semibold">{club.name}</h2>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusBadge(club.subscription_status)}
                {club.is_trial && <Badge variant="outline">Trial</Badge>}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => onEdit(club)}>
              <Edit className="w-4 h-4 mr-2" />
              Editar
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="p-6">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Visão Geral</TabsTrigger>
              <TabsTrigger value="plan">Plano & Cobrança</TabsTrigger>
              <TabsTrigger value="usage">Uso & Limites</TabsTrigger>
              <TabsTrigger value="history">Histórico</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Informações básicas */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Informações do Clube</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Nome</label>
                        <p className="text-sm">{club.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Email</label>
                        <p className="text-sm">{club.email || '-'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Telefone</label>
                        <p className="text-sm">{club.phone || '-'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">CNPJ</label>
                        <p className="text-sm">{club.document || '-'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Criado em</label>
                        <p className="text-sm">{formatDate(club.created_at)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Última atualização</label>
                        <p className="text-sm">{formatDate(club.updated_at)}</p>
                      </div>
                    </div>

                    {club.notes && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Observações</label>
                        <p className="text-sm bg-gray-50 p-3 rounded-lg">{club.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Status atual */}
                <Card>
                  <CardHeader>
                    <CardTitle>Status Atual</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Assinatura</span>
                      {getStatusBadge(club.subscription_status)}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Pagamento</span>
                      {getPaymentStatusBadge(club.payment_status)}
                    </div>

                    {club.is_trial && (
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Calendar className="w-4 h-4 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-blue-900">Período Trial</span>
                        </div>
                        <p className="text-xs text-blue-700">
                          {club.trial_start_date && `Início: ${formatDate(club.trial_start_date)}`}
                        </p>
                        <p className="text-xs text-blue-700">
                          {club.trial_end_date && `Fim: ${formatDate(club.trial_end_date)}`}
                        </p>
                      </div>
                    )}

                    {club.subscription_status === 'suspended' && (
                      <div className="p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                          <span className="text-sm font-medium text-red-900">Suspenso</span>
                        </div>
                        {club.suspended_at && (
                          <p className="text-xs text-red-700">
                            Em: {formatDate(club.suspended_at)}
                          </p>
                        )}
                        {club.suspension_reason && (
                          <p className="text-xs text-red-700 mt-1">
                            Motivo: {club.suspension_reason}
                          </p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Estatísticas rápidas */}
              {!loadingStats && usageStats && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Usuários</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {usageStats.usage.users}
                            {usageStats.limits.max_users > 0 && (
                              <span className="text-sm text-gray-500">
                                /{usageStats.limits.max_users}
                              </span>
                            )}
                          </p>
                        </div>
                        <Users className="w-8 h-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Jogadores</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {usageStats.usage.players}
                            {usageStats.limits.max_players > 0 && (
                              <span className="text-sm text-gray-500">
                                /{usageStats.limits.max_players}
                              </span>
                            )}
                          </p>
                        </div>
                        <UserCheck className="w-8 h-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Partidas</p>
                          <p className="text-2xl font-bold text-gray-900">{usageStats.usage.matches}</p>
                        </div>
                        <BarChart3 className="w-8 h-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            <TabsContent value="plan" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Plano atual */}
                <Card>
                  <CardHeader>
                    <CardTitle>Plano Atual</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {club.master_plans ? (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold">{club.master_plans.name}</h3>
                          <Badge variant={club.master_plans.is_trial ? 'outline' : 'secondary'}>
                            {club.master_plans.is_trial ? 'Trial' : 'Pago'}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600">{club.master_plans.description}</p>
                        
                        <div className="text-2xl font-bold text-blue-600">
                          {formatCurrency(club.master_plans.price)}
                          <span className="text-sm text-gray-500">
                            /{club.master_plans.billing_cycle === 'yearly' ? 'ano' : 'mês'}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Usuários:</span> {' '}
                            {club.master_plans.max_users === -1 ? 'Ilimitado' : club.master_plans.max_users}
                          </div>
                          <div>
                            <span className="font-medium">Jogadores:</span> {' '}
                            {club.master_plans.max_players === -1 ? 'Ilimitado' : club.master_plans.max_players}
                          </div>
                          <div>
                            <span className="font-medium">Armazenamento:</span> {' '}
                            {club.master_plans.max_storage_gb}GB
                          </div>
                          <div>
                            <span className="font-medium">Cobrança:</span> {' '}
                            {club.master_plans.billing_cycle === 'yearly' ? 'Anual' : 'Mensal'}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">Nenhum plano definido</p>
                    )}
                  </CardContent>
                </Card>

                {/* Informações de cobrança */}
                <Card>
                  <CardHeader>
                    <CardTitle>Cobrança</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Status do Pagamento</span>
                      {getPaymentStatusBadge(club.payment_status)}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Último Pagamento</span>
                      <span className="text-sm">
                        {club.last_payment_date ? formatDate(club.last_payment_date) : 'Nunca'}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Próximo Vencimento</span>
                      <span className="text-sm">
                        {club.next_payment_date ? formatDate(club.next_payment_date) : '-'}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Período de Graça</span>
                      <span className="text-sm">{club.grace_period_days} dias</span>
                    </div>

                    {club.payment_status === 'overdue' && (
                      <div className="p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center">
                          <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                          <span className="text-sm font-medium text-red-900">
                            Pagamento em Atraso
                          </span>
                        </div>
                        <p className="text-xs text-red-700 mt-1">
                          O clube pode ser suspenso após o período de graça
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="usage" className="space-y-6">
              {loadingStats ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : usageStats ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Uso de Usuários</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Usuários Ativos</span>
                          <span className="text-sm font-medium">
                            {usageStats.usage.users}
                            {usageStats.limits.max_users > 0 && ` / ${usageStats.limits.max_users}`}
                          </span>
                        </div>
                        {usageStats.limits.max_users > 0 && (
                          <Progress 
                            value={usageStats.percentages.users} 
                            className="h-2"
                          />
                        )}
                        <p className="text-xs text-gray-500">
                          {usageStats.limits.max_users === -1 
                            ? 'Usuários ilimitados neste plano'
                            : `${Math.round(usageStats.percentages.users)}% do limite utilizado`
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Uso de Jogadores</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Jogadores Cadastrados</span>
                          <span className="text-sm font-medium">
                            {usageStats.usage.players}
                            {usageStats.limits.max_players > 0 && ` / ${usageStats.limits.max_players}`}
                          </span>
                        </div>
                        {usageStats.limits.max_players > 0 && (
                          <Progress 
                            value={usageStats.percentages.players} 
                            className="h-2"
                          />
                        )}
                        <p className="text-xs text-gray-500">
                          {usageStats.limits.max_players === -1 
                            ? 'Jogadores ilimitados neste plano'
                            : `${Math.round(usageStats.percentages.players)}% do limite utilizado`
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Partidas</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Total de Partidas</span>
                          <span className="text-sm font-medium">{usageStats.usage.matches}</span>
                        </div>
                        <p className="text-xs text-gray-500">
                          Todas as partidas cadastradas no sistema
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Armazenamento</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Limite</span>
                          <span className="text-sm font-medium">
                            {club.master_plans?.max_storage_gb || 0}GB
                          </span>
                        </div>
                        <p className="text-xs text-gray-500">
                          Espaço disponível para documentos e imagens
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Erro ao carregar estatísticas de uso</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Histórico de Atividades</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Histórico de atividades em desenvolvimento</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};