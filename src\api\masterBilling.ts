import { supabase } from "@/integrations/supabase/client";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";

export interface MasterPayment {
  id: number;
  club_id: number;
  plan_id: number;
  amount: number;
  due_date: string;
  paid_date?: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  payment_method?: string;
  transaction_id?: string;
  notes?: string;
  created_at: string;
  updated_at?: string;
  // Dados relacionados
  club?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  plan?: {
    id: number;
    name: string;
    price: number;
    billing_cycle: string;
  };
}

export interface CreatePaymentData {
  club_id: number;
  plan_id: number;
  amount: number;
  due_date: string;
  notes?: string;
}

export interface PaymentFilters {
  status?: 'pending' | 'paid' | 'overdue' | 'cancelled';
  club_id?: number;
  plan_id?: number;
  overdue_only?: boolean;
  start_date?: string;
  end_date?: string;
  search?: string;
}

export interface BillingStats {
  totalPending: number;
  totalOverdue: number;
  totalPaid: number;
  monthlyRevenue: number;
  overdueAmount: number;
  defaultRate: number;
  totalClubs: number;
  activeClubs: number;
}

// Buscar pagamentos com filtros
export const getMasterPayments = async (filters?: PaymentFilters): Promise<MasterPayment[]> => {
  try {
    await ensureAuthenticated();
    // Data validation
    if (filters?.start_date && filters?.end_date && filters.start_date > filters.end_date) {
      throw new Error('Data inicial deve ser anterior à data final');
    }
    if (filters?.club_id && filters.club_id <= 0) {
      throw new Error('ID do clube deve ser um número positivo');
    }
    if (filters?.plan_id && filters.plan_id <= 0) {
      throw new Error('ID do plano deve ser um número positivo');
    }

    let query = supabase
      .from('master_payments')
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `);

    // Aplicar filtros
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.club_id) {
      query = query.eq('club_id', filters.club_id);
    }

    if (filters?.plan_id) {
      query = query.eq('plan_id', filters.plan_id);
    }

    if (filters?.overdue_only) {
      query = query.eq('status', 'overdue');
    }

    if (filters?.start_date) {
      query = query.gte('due_date', filters.start_date);
    }

    if (filters?.end_date) {
      query = query.lte('due_date', filters.end_date);
    }

    if (filters?.search) {
      // Buscar por nome do clube
      query = query.or(`club_info.name.ilike.%${filters.search}%`);
    }

    const { data, error } = await query.order('due_date', { ascending: false });

    if (error) {
      console.error('Database error in getMasterPayments:', error);
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados nas tabelas. Verifique se a migração foi aplicada.');
      }
      throw new Error(`Erro ao buscar pagamentos: ${error.message}`);
    }
    
    return data || [];
  } catch (error: any) {
    console.error('Erro ao buscar pagamentos:', error);
    if (error.message.includes('deve ser') || error.message.includes('Campos não encontrados')) {
      throw error; // Re-throw specific validation errors
    }
    throw new Error(error.message || 'Erro ao buscar pagamentos');
  }
};

// Buscar estatísticas de cobrança
export const getBillingStats = async (): Promise<BillingStats> => {
  try {
    await ensureAuthenticated();
    // Buscar estatísticas de pagamentos
    const { data: payments, error: paymentsError } = await supabase
      .from('master_payments')
      .select('status, amount, due_date');

    if (paymentsError) throw paymentsError;

    // Buscar estatísticas de clubes
    const { data: clubs, error: clubsError } = await supabase
      .from('club_info')
      .select('subscription_status');

    if (clubsError) throw clubsError;

    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Calcular estatísticas
    const totalPending = payments?.filter(p => p.status === 'pending').length || 0;
    const totalOverdue = payments?.filter(p => p.status === 'overdue').length || 0;
    const totalPaid = payments?.filter(p => p.status === 'paid').length || 0;

    const monthlyRevenue = payments
      ?.filter(p => {
        const paidDate = p.paid_date ? new Date(p.paid_date) : null;
        return p.status === 'paid' && 
               paidDate && 
               paidDate.getMonth() === currentMonth && 
               paidDate.getFullYear() === currentYear;
      })
      .reduce((sum, p) => sum + p.amount, 0) || 0;

    const overdueAmount = payments
      ?.filter(p => p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0) || 0;

    const totalClubs = clubs?.length || 0;
    const activeClubs = clubs?.filter(c => c.subscription_status === 'active').length || 0;

    const defaultRate = totalClubs > 0 ? (totalOverdue / totalClubs) * 100 : 0;

    return {
      totalPending,
      totalOverdue,
      totalPaid,
      monthlyRevenue,
      overdueAmount,
      defaultRate: Math.round(defaultRate * 100) / 100,
      totalClubs,
      activeClubs
    };
  } catch (error) {
    console.error('Erro ao buscar estatísticas de cobrança:', error);
    throw error;
  }
};

// Criar novo pagamento
export const createMasterPayment = async (paymentData: CreatePaymentData): Promise<MasterPayment> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('master_payments')
      .insert({
        ...paymentData,
        status: 'pending',
        created_at: new Date().toISOString()
      })
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao criar pagamento:', error);
    throw error;
  }
};

// Marcar pagamento como pago
export const markPaymentAsPaid = async (
  paymentId: number,
  paymentMethod?: string,
  transactionId?: string
): Promise<MasterPayment> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('master_payments')
      .update({
        status: 'paid',
        paid_date: new Date().toISOString(),
        payment_method: paymentMethod,
        transaction_id: transactionId,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .single();

    if (error) throw error;

    // Atualizar status do clube se necessário
    if (data) {
      await supabase
        .from('club_info')
        .update({
          payment_status: 'current',
          last_payment_date: new Date().toISOString().split('T')[0]
        })
        .eq('id', data.club_id);
    }

    return data;
  } catch (error) {
    console.error('Erro ao marcar pagamento como pago:', error);
    throw error;
  }
};

// Cancelar pagamento
export const cancelPayment = async (paymentId: number, reason?: string): Promise<MasterPayment> => {
  try {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('master_payments')
      .update({
        status: 'cancelled',
        notes: reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao cancelar pagamento:', error);
    throw error;
  }
};

// Gerar cobranças mensais automaticamente
export const generateMonthlyPayments = async (): Promise<{ success: boolean; count: number; message: string }> => {
  try {
    const { data, error } = await supabase.rpc('generate_monthly_payments');

    if (error) throw error;

    return {
      success: true,
      count: data?.count || 0,
      message: `${data?.count || 0} cobranças geradas com sucesso`
    };
  } catch (error: any) {
    console.error('Erro ao gerar cobranças mensais:', error);
    return {
      success: false,
      count: 0,
      message: error.message || 'Erro ao gerar cobranças mensais'
    };
  }
};

// Enviar lembrete de pagamento
export const sendPaymentReminder = async (paymentId: number): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('send_payment_reminder', {
      payment_id: paymentId
    });

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Erro ao enviar lembrete:', error);
    throw error;
  }
};

// Suspender acesso do clube por falta de pagamento
export const suspendClubAccess = async (clubId: number, reason?: string): Promise<boolean> => {
  try {
    await ensureAuthenticated();
    const { error } = await supabase
      .from('club_info')
      .update({
        subscription_status: 'suspended',
        payment_status: 'overdue',
        updated_at: new Date().toISOString()
      })
      .eq('id', clubId);

    if (error) throw error;

    // Registrar log de auditoria (requer autenticação)
    const {
      data: { user }
    } = await supabase.auth.getUser();
    if (!user) throw new Error('Usuário não autenticado');

    await supabase
      .from('master_audit_logs')
      .insert({
        user_id: user.id,
        action: 'club_suspended',
        entity_type: 'club',
        entity_id: clubId,
        details: { reason: reason || 'Pagamento em atraso' },
        created_at: new Date().toISOString()
      });

    return true;
  } catch (error) {
    console.error('Erro ao suspender clube:', error);
    throw error;
  }
};

// Reativar acesso do clube
export const reactivateClubAccess = async (clubId: number): Promise<boolean> => {
  try {
    await ensureAuthenticated();
    const { error } = await supabase
      .from('club_info')
      .update({
        subscription_status: 'active',
        payment_status: 'current',
        updated_at: new Date().toISOString()
      })
      .eq('id', clubId);

    if (error) throw error;

    // Registrar log de auditoria (requer autenticação)
    const {
      data: { user }
    } = await supabase.auth.getUser();
    if (!user) throw new Error('Usuário não autenticado');

    await supabase
      .from('master_audit_logs')
      .insert({
        user_id: user.id,
        action: 'club_reactivated',
        entity_type: 'club',
        entity_id: clubId,
        details: { reason: 'Pagamento regularizado' },
        created_at: new Date().toISOString()
      });

    return true;
  } catch (error) {
    console.error('Erro ao reativar clube:', error);
    throw error;
  }
};

// Buscar histórico de pagamentos de um clube
export const getClubPaymentHistory = async (clubId: number): Promise<MasterPayment[]> => {
  try {
    const { data, error } = await supabase
      .from('master_payments')
      .select(`
        *,
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .eq('club_id', clubId)
      .order('due_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar histórico de pagamentos:', error);
    throw error;
  }
};

// Atualizar data de vencimento
export const updatePaymentDueDate = async (paymentId: number, newDueDate: string): Promise<MasterPayment> => {
  try {
    const { data, error } = await supabase
      .from('master_payments')
      .update({
        due_date: newDueDate,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Erro ao atualizar data de vencimento:', error);
    throw error;
  }
};

// Buscar pagamentos em atraso para notificação
export const getOverduePayments = async (daysSinceOverdue: number = 0): Promise<MasterPayment[]> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysSinceOverdue);

    const { data, error } = await supabase
      .from('master_payments')
      .select(`
        *,
        club_info:club_id (
          id,
          name,
          email,
          phone
        ),
        master_plans:plan_id (
          id,
          name,
          price,
          billing_cycle
        )
      `)
      .eq('status', 'overdue')
      .lte('due_date', cutoffDate.toISOString().split('T')[0])
      .order('due_date', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar pagamentos em atraso:', error);
    throw error;
  }
};