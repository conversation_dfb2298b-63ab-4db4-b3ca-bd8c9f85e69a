import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings,
  DollarSign,
  FileText,
  BarChart3,
  Calendar,
  Users
} from 'lucide-react';
import { MonthlyFeeSettingsManager } from './MonthlyFeeSettingsManager';
import { MonthlyFeeDashboard } from './MonthlyFeeDashboard';
import { MonthlyFeeReceiptsManager } from './MonthlyFeeReceiptsManager';

export function MonthlyFeesManager() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sistema de Mensalidades</h1>
          <p className="text-muted-foreground">
            Gerencie mensalidades, configurações e comprovantes de pagamento
          </p>
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Configurações
          </TabsTrigger>
          <TabsTrigger value="receipts" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Comprovantes
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6 mt-6">
          <MonthlyFeeDashboard />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6 mt-6">
          <MonthlyFeeSettingsManager />
        </TabsContent>

        <TabsContent value="receipts" className="space-y-6 mt-6">
          <MonthlyFeeReceiptsManager />
        </TabsContent>
      </Tabs>
    </div>
  );
}