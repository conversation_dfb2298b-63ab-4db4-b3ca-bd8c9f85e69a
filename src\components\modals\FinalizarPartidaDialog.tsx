import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { createMatchHistory, deleteUpcomingMatch, getPlayerById, updatePlayer, insertGols } from "@/api/api";
import { updatePlayerStatsFromMatch } from "@/api/playerStatsSync";
import type { UpcomingMatch, Gol, Cartao, MatchStats } from "@/api/api";
import { toast } from "@/hooks/use-toast";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useSeasonStore } from "@/store/useSeasonStore";
import { CriarPartidaVoltaDialog } from "./CriarPartidaVoltaDialog";
import { useGamesStore } from "@/store/useGamesStore";
import { useMatchHistoryStore } from "@/store/useMatchHistoryStore";

interface FinalizarPartidaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  match: UpcomingMatch;
  clubId: number;
}

export function FinalizarPartidaDialog({ open, onOpenChange, match, clubId }: FinalizarPartidaDialogProps) {
  const [result, setResult] = useState<"win" | "loss" | "draw">("win");
  const [stats, setStats] = useState<Partial<MatchStats>>({});
  const [goals, setGoals] = useState<Gol[]>([]);
  const [cards, setCards] = useState<Cartao[]>([]);
  const [lineup, setLineup] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [finalScoreHome, setFinalScoreHome] = useState("");
  const [finalScoreAway, setFinalScoreAway] = useState("");

  // Estados para submodais de gol/cartão
  const [openGoalModal, setOpenGoalModal] = useState(false);
  const [openCardModal, setOpenCardModal] = useState(false);
  // Gol
  const [selectedGoalPlayer, setSelectedGoalPlayer] = useState(""); // id do jogador
  const [selectedGoalMinute, setSelectedGoalMinute] = useState("");
  const [selectedGoalAssist, setSelectedGoalAssist] = useState("");
  // Cartão
  const [selectedCardPlayer, setSelectedCardPlayer] = useState("");
  const [selectedCardColor, setSelectedCardColor] = useState<"amarelo"|"vermelho">("amarelo");
  const [selectedCardMinute, setSelectedCardMinute] = useState("");

  // Buscar jogadores do store Zustand
  const { players, fetchPlayers, loading: loadingPlayers } = usePlayersStore();
  const { activeSeason } = useSeasonStore();

  // Stores para sincronização
  const { syncGames } = useGamesStore();
  const { syncMatchHistory } = useMatchHistoryStore();

  const [openVoltaModal, setOpenVoltaModal] = useState(false);
  const [voltaDefault, setVoltaDefault] = useState<{ adversario: string; competition: string; seasonId: number } | null>(null);

  useEffect(() => {
    if (players.length === 0) {
      fetchPlayers(clubId);
    }
  }, [players.length, fetchPlayers, clubId]);

  const handleSave = async () => {
    if (!activeSeason) {
      setError("Selecione uma temporada antes de finalizar a partida.");
      return;
    }
    if (!result) {
      setError("O resultado é obrigatório.");
      return;
    }
    // Validação dos campos de placar
    if (finalScoreHome === "" || finalScoreAway === "") {
      setError("Informe o placar final do jogo.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      await deleteUpcomingMatch(clubId, match.id);
      let score_home = Number(finalScoreHome);
      let score_away = Number(finalScoreAway);
      const matchHistory = await createMatchHistory(clubId, {
        ...match,
        club_id: clubId,
        season_id: activeSeason.id,
        result,
        score_home,
        score_away,
        type: match.type, // preserva o tipo original do match
        competition: match.competition,
        location: match.location,
        summary: '',
        // Apenas para exibição no frontend
        estatisticas: {
          chutes: Number(stats.chutes) || 0,
          chutesNoGol: Number(stats.chutesNoGol) || 0,
          escanteios: Number(stats.escanteios) || 0,
          faltas: Number(stats.faltas) || 0,
          impedimentos: Number(stats.impedimentos) || 0,
          posse: 0
        },
        // CAMPOS DO BANCO (fonte de verdade)
        shots: Number(stats.chutes) || 0,
        shots_on_target: Number(stats.chutesNoGol) || 0,
        corners: Number(stats.escanteios) || 0,
        fouls: Number(stats.faltas) || 0,
        offsides: Number(stats.impedimentos) || 0,
        gols: goals.map(g => ({ minuto: g.minuto, jogador: g.jogador })),
        cartoes: cards,
        escalacao: lineup.split(',').map(s => s.trim()).filter(Boolean),
        formation: match.formation || '4-4-2',
      });

      // Salvar gols na tabela gols do banco
      try {
        await insertGols(clubId, matchHistory.id, goals.map(g => ({
          minuto: g.minuto,
          player_id: g.jogadorId // id correto para o banco
        })));
      } catch (e) {
        // Se der erro, apenas loga, não impede o fluxo
        console.error("Erro ao inserir gols na tabela gols:", e);
      }

      // Atualizar stats dos jogadores usando a nova função de sincronização
      try {
        const lineupPlayers = lineup.split(',').map(s => s.trim()).filter(Boolean);
        await updatePlayerStatsFromMatch(
          clubId,
          matchHistory.id,
          goals,
          cards,
          lineupPlayers,
          "system" // Usar "system" como userId para finalização automática
        );
      } catch (error) {
        console.error("Erro ao atualizar estatísticas dos jogadores:", error);
        // Não impede o fluxo, apenas loga o erro
      }

      // Processar suspensões após a partida
      console.log('🏁 [DEBUG] MATCH OBJECT:', match);
      console.log('🏁 [DEBUG] COMPETITION_ID:', match.competition_id);
      console.log('🏁 [DEBUG] CATEGORY_ID:', match.category_id);
      console.log('🏁 [DEBUG] NOTES:', match.notes);
      
      try {
        console.log('🏁 [FINALIZAR PARTIDA] Dados completos da partida:', match);
        console.log('🏁 [FINALIZAR PARTIDA] Iniciando processamento de suspensões:', {
          clubId,
          matchId: match.id,
          competitionId: match.competition_id,
          categoryId: match.category_id
        });
        
        const { processSuspensionAfterMatch } = await import('@/api/cardSuspensions');
        await processSuspensionAfterMatch(clubId, match.competition_id, match.category_id);
        
        console.log('✅ [FINALIZAR PARTIDA] Processamento de suspensões concluído');
      } catch (suspensionError) {
        console.error("❌ [FINALIZAR PARTIDA] Erro ao processar suspensões após partida:", suspensionError);
        // Não impede o fluxo, apenas loga o erro
      }

      // Após finalizar, sincronize stores
      await syncMatchHistory(clubId, activeSeason.id);
      await syncGames(clubId, activeSeason.id);

      // Se for ida/volta, abrir modal para criar partida de volta
      if (match.ida_volta) {
        setVoltaDefault({ adversario: match.opponent, competition: match.competition, seasonId: activeSeason.id });
        setOpenVoltaModal(true);
      } else {
        toast({ title: "Partida finalizada! Stats dos jogadores atualizadas.", variant: "default" });
        onOpenChange(false);
      }
    } catch (e) {
      setError("Erro ao finalizar partida.");
      toast({ title: "Erro ao finalizar partida.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Finalizar Partida</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Resultado fixo */}
            <select className="w-full border rounded px-2 py-1" value={result} onChange={e => setResult(e.target.value as "win" | "loss" | "draw")}>
              <option value="win">Vitória</option>
              <option value="draw">Empate</option>
              <option value="loss">Derrota</option>
            </select>
            <div className="flex space-x-2">
              <Input placeholder="Gols feitos" value={finalScoreHome} onChange={e => setFinalScoreHome(e.target.value.replace(/\D/g, ""))} />
              <Input placeholder="Gols sofridos" value={finalScoreAway} onChange={e => setFinalScoreAway(e.target.value.replace(/\D/g, ""))} />
            </div>
            <Input placeholder="Finalizações" value={stats.chutes === undefined ? "" : String(stats.chutes)} onChange={e => setStats(s => ({ ...s, chutes: Number(e.target.value) }))} />
            <Input placeholder="Finalizações no gol" value={stats.chutesNoGol === undefined ? "" : String(stats.chutesNoGol)} onChange={e => setStats(s => ({ ...s, chutesNoGol: Number(e.target.value) }))} />
            <Input placeholder="Escanteios" value={stats.escanteios === undefined ? "" : String(stats.escanteios)} onChange={e => setStats(s => ({ ...s, escanteios: Number(e.target.value) }))} />
            <Input placeholder="Faltas" value={stats.faltas === undefined ? "" : String(stats.faltas)} onChange={e => setStats(s => ({ ...s, faltas: Number(e.target.value) }))} />
            <Input placeholder="Impedimentos" value={stats.impedimentos === undefined ? "" : String(stats.impedimentos)} onChange={e => setStats(s => ({ ...s, impedimentos: Number(e.target.value) }))} />

            {/* Gols */}
            <div>
              <div className="flex items-center justify-between">
                <span className="font-semibold">Gols</span>
                <Button variant="outline" size="sm" onClick={() => setOpenGoalModal(true)}>Adicionar Gol</Button>
              </div>
              <ul className="mt-2 list-disc pl-5">
                {goals.map((g, i) => <li key={i}>{g.minuto}' - {g.jogador} {g.assist && <span className="text-gray-500"> (assistência de {g.assist})</span>} <Button size="sm" variant="ghost" onClick={() => setGoals(gs => gs.filter((_, idx) => idx !== i))}>Remover</Button></li>)}
              </ul>
            </div>

            {/* Submodal Gol */}
            <Dialog open={openGoalModal} onOpenChange={setOpenGoalModal}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Adicionar Gol</DialogTitle>
                </DialogHeader>
                <div className="space-y-2">
                  <label>Jogador</label>
                  <select className="w-full border rounded px-2 py-1" value={selectedGoalPlayer} onChange={e => setSelectedGoalPlayer(e.target.value)} disabled={loadingPlayers || players.length === 0}>
                    <option value="">Selecione o jogador</option>
                    {players.map(p => (
                      <option key={p.id} value={p.id}>{p.name}</option>
                    ))}
                  </select>
                  <label>Minuto</label>
                  <Input placeholder="Minuto" type="number" value={selectedGoalMinute} onChange={e => setSelectedGoalMinute(e.target.value)} />
                  <label>Assistência (opcional)</label>
                  <select className="w-full border rounded px-2 py-1" value={selectedGoalAssist} onChange={e => setSelectedGoalAssist(e.target.value)} disabled={loadingPlayers || players.length === 0}>
                    <option value="">Sem assistência</option>
                    {players.map(p => (
                      <option key={p.id} value={p.name}>{p.name}</option>
                    ))}
                  </select>
                </div>
                <DialogFooter>
                  <Button onClick={() => {
                    if (!selectedGoalPlayer) return;
                    const player = players.find(p => p.id === selectedGoalPlayer);
                    if (!player) return;
                    setGoals(gs => [...gs, {
                      minuto: Number(selectedGoalMinute),
                      jogador: player.name,
                      jogadorId: player.id,
                      ...(selectedGoalAssist ? { assist: selectedGoalAssist } : {})
                    }]);
                    setSelectedGoalPlayer(""); setSelectedGoalMinute(""); setSelectedGoalAssist(""); setOpenGoalModal(false);
                  }}>Adicionar</Button>
                  <Button variant="ghost" onClick={() => setOpenGoalModal(false)}>Cancelar</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Cartões */}
            <div>
              <div className="flex items-center justify-between">
                <span className="font-semibold">Cartões</span>
                <Button variant="outline" size="sm" onClick={() => setOpenCardModal(true)}>Adicionar Cartão</Button>
              </div>
              <ul className="mt-2 list-disc pl-5">
                {cards.map((c, i) => <li key={i}>' - {c.jogador} ({c.tipo})  <Button size="sm" variant="ghost" onClick={() => setCards(cs => cs.filter((_, idx) => idx !== i))}>Remover</Button></li>)}
              </ul>
            </div>

            {/* Submodal Cartão */}
            <Dialog open={openCardModal} onOpenChange={setOpenCardModal}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Adicionar Cartão</DialogTitle>
                </DialogHeader>
                <div className="space-y-2">
                  <label>Jogador</label>
                  <select className="w-full border rounded px-2 py-1" value={selectedCardPlayer} onChange={e => setSelectedCardPlayer(e.target.value)} disabled={loadingPlayers || players.length === 0}>
                    <option value="">Selecione o jogador</option>
                    {players.map(p => (
                      <option key={p.id} value={p.name}>{p.name}</option>
                    ))}
                  </select>
                  <label>Cor</label>
                  <select className="w-full border rounded px-2 py-1" value={selectedCardColor} onChange={e => setSelectedCardColor(e.target.value as "amarelo"|"vermelho")}>
                    <option value="amarelo">Amarelo</option>
                    <option value="vermelho">Vermelho</option>
                  </select>
                </div>
                <DialogFooter>
                  <Button onClick={() => {
                    if(selectedCardPlayer && selectedCardColor) {
                      setCards(cs => [...cs, { jogador: selectedCardPlayer, tipo: selectedCardColor }]);
                      setSelectedCardPlayer(""); setSelectedCardColor("amarelo"); setOpenCardModal(false);
                    }
                  }}>Adicionar</Button>
                  <Button variant="ghost" onClick={() => setOpenCardModal(false)}>Cancelar</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Input placeholder="Escalação (separe por vírgula)" value={lineup} onChange={e => setLineup(e.target.value)} />
            {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? <span className="loader mr-2" /> : null}
              Finalizar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {voltaDefault && (
        <CriarPartidaVoltaDialog
          open={openVoltaModal}
          onOpenChange={open => {
            setOpenVoltaModal(open);
            if (!open) {
              setVoltaDefault(null);
              onOpenChange(false);
            }
          }}
          clubId={clubId}
          adversario={voltaDefault.adversario}
          competition={voltaDefault.competition}
          seasonId={voltaDefault.seasonId}
        />
      )}
    </>
  );
}
