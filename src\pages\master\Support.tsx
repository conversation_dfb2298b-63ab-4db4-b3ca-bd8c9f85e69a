import React, { useState, useEffect, useMemo } from 'react';
import { 
  HelpCircle, 
  MessageSquare, 
  Phone, 
  Mail,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  User,
  Building2,
  Search,
  Filter,
  Plus,
  Send,
  Paperclip,
  MoreHorizontal,
  Star,
  TrendingUp,
  Users
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from "@/integrations/supabase/client";
import { toast } from 'sonner';
import { format, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface SupportTicket {
  id: number;
  club_id: number;
  club_name: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'feature' | 'bug' | 'other';
  assigned_to?: string;
  assigned_name?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  customer_email: string;
  customer_phone?: string;
  messages: SupportMessage[];
}

interface SupportMessage {
  id: number;
  ticket_id: number;
  sender_type: 'customer' | 'support';
  sender_name: string;
  message: string;
  attachments?: string[];
  created_at: string;
}

interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedToday: number;
  averageResponseTime: number;
  satisfactionRate: number;
  ticketsByCategory: Array<{ category: string; count: number }>;
  ticketsByPriority: Array<{ priority: string; count: number }>;
}

export const Support: React.FC = () => {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [stats, setStats] = useState<SupportStats>({
    totalTickets: 0,
    openTickets: 0,
    resolvedToday: 0,
    averageResponseTime: 0,
    satisfactionRate: 0,
    ticketsByCategory: [],
    ticketsByPriority: []
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [clubFilter, setClubFilter] = useState<string>('all');
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [activeTab, setActiveTab] = useState('tickets');

  useEffect(() => {
    loadTickets();
    loadStats();
  }, []);

  const loadTickets = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('master_support_tickets')
        .select(`
          *,
          club_info:club_id (name),
          master_users:assigned_to (name),
          master_support_messages (
            id,
            sender_type,
            sender_name,
            sender_email,
            message,
            attachments,
            created_at
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const processedTickets: SupportTicket[] = data?.map(ticket => ({
        id: ticket.id,
        club_id: ticket.club_id,
        club_name: ticket.club_info?.name || 'Clube Desconhecido',
        title: ticket.title,
        description: ticket.description,
        status: ticket.status as any,
        priority: ticket.priority as any,
        category: ticket.category as any,
        assigned_to: ticket.assigned_to,
        assigned_name: ticket.master_users?.name,
        customer_email: ticket.customer_email,
        customer_phone: ticket.customer_phone,
        created_at: ticket.created_at,
        updated_at: ticket.updated_at,
        resolved_at: ticket.resolved_at,
        messages: ticket.master_support_messages?.map((msg: any) => ({
          id: msg.id,
          ticket_id: ticket.id,
          sender_type: msg.sender_type,
          sender_name: msg.sender_name,
          message: msg.message,
          attachments: msg.attachments,
          created_at: msg.created_at
        })) || []
      })) || [];

      setTickets(processedTickets);
    } catch (error: any) {
      console.error('Erro ao carregar tickets:', error);
      toast.error('Erro ao carregar tickets de suporte');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const totalTickets = tickets.length;
      const openTickets = tickets.filter(t => t.status === 'open' || t.status === 'in_progress').length;
      const resolvedToday = tickets.filter(t => 
        t.resolved_at && 
        new Date(t.resolved_at).toDateString() === new Date().toDateString()
      ).length;

      // Calcular tempo médio de resposta (mock)
      const averageResponseTime = 2.5; // horas

      // Taxa de satisfação (mock)
      const satisfactionRate = 4.2; // de 5

      // Tickets por categoria
      const categoryCount: Record<string, number> = {};
      tickets.forEach(ticket => {
        categoryCount[ticket.category] = (categoryCount[ticket.category] || 0) + 1;
      });
      const ticketsByCategory = Object.entries(categoryCount).map(([category, count]) => ({
        category,
        count
      }));

      // Tickets por prioridade
      const priorityCount: Record<string, number> = {};
      tickets.forEach(ticket => {
        priorityCount[ticket.priority] = (priorityCount[ticket.priority] || 0) + 1;
      });
      const ticketsByPriority = Object.entries(priorityCount).map(([priority, count]) => ({
        priority,
        count
      }));

      setStats({
        totalTickets,
        openTickets,
        resolvedToday,
        averageResponseTime,
        satisfactionRate,
        ticketsByCategory,
        ticketsByPriority
      });
    } catch (error: any) {
      console.error('Erro ao calcular estatísticas:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!selectedTicket || !newMessage.trim()) return;

    try {
      // Inserir mensagem no banco
      const { data, error } = await supabase
        .from('master_support_messages')
        .insert({
          ticket_id: selectedTicket.id,
          sender_type: 'support',
          sender_name: 'Suporte Master',
          sender_email: '<EMAIL>',
          message: newMessage
        })
        .select()
        .single();

      if (error) throw error;

      // Atualizar ticket no banco
      const { error: updateError } = await supabase
        .from('master_support_tickets')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', selectedTicket.id);

      if (updateError) throw updateError;

      const newMsg: SupportMessage = {
        id: data.id,
        ticket_id: selectedTicket.id,
        sender_type: 'support',
        sender_name: 'Suporte Master',
        message: newMessage,
        created_at: data.created_at
      };

      // Atualizar estado local
      const updatedTicket = {
        ...selectedTicket,
        messages: [...selectedTicket.messages, newMsg],
        updated_at: new Date().toISOString()
      };

      setSelectedTicket(updatedTicket);
      setTickets(prev => prev.map(t => t.id === selectedTicket.id ? updatedTicket : t));
      setNewMessage('');
      
      toast.success('Mensagem enviada com sucesso!');
    } catch (error: any) {
      console.error('Erro ao enviar mensagem:', error);
      toast.error('Erro ao enviar mensagem');
    }
  };

  const handleUpdateTicketStatus = async (ticketId: number, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('master_support_tickets')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId);

      if (error) throw error;

      const updatedTickets = tickets.map(ticket => {
        if (ticket.id === ticketId) {
          const updatedTicket = {
            ...ticket,
            status: newStatus as any,
            updated_at: new Date().toISOString(),
            resolved_at: newStatus === 'resolved' ? new Date().toISOString() : ticket.resolved_at
          };
          
          if (selectedTicket?.id === ticketId) {
            setSelectedTicket(updatedTicket);
          }
          
          return updatedTicket;
        }
        return ticket;
      });

      setTickets(updatedTickets);
      toast.success('Status do ticket atualizado!');
    } catch (error: any) {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status do ticket');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'in_progress':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'resolved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'closed':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <HelpCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      open: { variant: 'destructive' as const, label: 'Aberto' },
      in_progress: { variant: 'secondary' as const, label: 'Em Andamento' },
      resolved: { variant: 'default' as const, label: 'Resolvido' },
      closed: { variant: 'outline' as const, label: 'Fechado' }
    };

    const config = variants[status as keyof typeof variants] || variants.open;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      urgent: { variant: 'destructive' as const, label: 'Urgente' },
      high: { variant: 'destructive' as const, label: 'Alta' },
      medium: { variant: 'secondary' as const, label: 'Média' },
      low: { variant: 'outline' as const, label: 'Baixa' }
    };

    const config = variants[priority as keyof typeof variants] || variants.medium;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      technical: 'Técnico',
      billing: 'Cobrança',
      feature: 'Funcionalidade',
      bug: 'Bug',
      other: 'Outros'
    };
    return labels[category as keyof typeof labels] || category;
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.club_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.customer_email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'all' || ticket.category === categoryFilter;
    const matchesClub = clubFilter === 'all' || ticket.club_id.toString() === clubFilter;

    return matchesSearch && matchesStatus && matchesPriority && matchesCategory && matchesClub;
  });

  const clubOptions = useMemo(() => {
    const map = new Map<number, string>();
    tickets.forEach(t => map.set(t.club_id, t.club_name));
    return Array.from(map.entries());
  }, [tickets]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando tickets de suporte...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Suporte</h1>
          <p className="text-gray-600 mt-1">
            Gerencie tickets e solicitações de suporte dos clubes
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="tickets">Tickets</TabsTrigger>
          <TabsTrigger value="knowledge">Base de Conhecimento</TabsTrigger>
          <TabsTrigger value="contacts">Contatos</TabsTrigger>
        </TabsList>

        <TabsContent value="tickets" className="space-y-6">
          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total de Tickets</p>
                    <p className="text-2xl font-bold">{stats.totalTickets}</p>
                  </div>
                  <MessageSquare className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Tickets Abertos</p>
                    <p className="text-2xl font-bold text-red-600">{stats.openTickets}</p>
                  </div>
                  <AlertCircle className="w-8 h-8 text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Resolvidos Hoje</p>
                    <p className="text-2xl font-bold text-green-600">{stats.resolvedToday}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Tempo Médio</p>
                    <p className="text-2xl font-bold">{stats.averageResponseTime}h</p>
                    <p className="text-xs text-gray-500">resposta</p>
                  </div>
                  <Clock className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Satisfação</p>
                    <p className="text-2xl font-bold">{stats.satisfactionRate}/5</p>
                    <div className="flex items-center mt-1">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`w-3 h-3 ${i < Math.floor(stats.satisfactionRate) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                  </div>
                  <TrendingUp className="w-8 h-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtros */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Buscar tickets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="open">Aberto</SelectItem>
                  <SelectItem value="in_progress">Em Andamento</SelectItem>
                  <SelectItem value="resolved">Resolvido</SelectItem>
                  <SelectItem value="closed">Fechado</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as prioridades</SelectItem>
                  <SelectItem value="urgent">Urgente</SelectItem>
                  <SelectItem value="high">Alta</SelectItem>
                  <SelectItem value="medium">Média</SelectItem>
                  <SelectItem value="low">Baixa</SelectItem>
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as categorias</SelectItem>
                  <SelectItem value="technical">Técnico</SelectItem>
                  <SelectItem value="billing">Cobrança</SelectItem>
                  <SelectItem value="feature">Funcionalidade</SelectItem>
                  <SelectItem value="bug">Bug</SelectItem>
                  <SelectItem value="other">Outros</SelectItem>
                </SelectContent>
              </Select>
              <Select value={clubFilter} onValueChange={setClubFilter}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Todos os clubes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os clubes</SelectItem>
                  {clubOptions.map(([id, name]) => (
                    <SelectItem key={id} value={id.toString()}>{name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

          {/* Lista de Tickets */}
          <Card>
            <CardHeader>
              <CardTitle>Tickets de Suporte ({filteredTickets.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTickets.map((ticket) => (
                  <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(ticket.status)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{ticket.title}</h3>
                          <Badge variant="outline" className="text-xs">
                            #{ticket.id}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {ticket.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Building2 className="w-3 h-3" />
                            <span>{ticket.club_name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            <span>{ticket.customer_email}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{format(new Date(ticket.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}</span>
                          </div>
                          {ticket.assigned_name && (
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              <span>{ticket.assigned_name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {getPriorityBadge(ticket.priority)}
                      {getStatusBadge(ticket.status)}
                      <Badge variant="outline" className="text-xs">
                        {getCategoryLabel(ticket.category)}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setSelectedTicket(ticket);
                            setIsTicketModalOpen(true);
                          }}>
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Ver Conversa
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'in_progress')}>
                            <Clock className="w-4 h-4 mr-2" />
                            Marcar em Andamento
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'resolved')}>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Marcar como Resolvido
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}

                {filteredTickets.length === 0 && (
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Nenhum ticket encontrado</p>
                    <p className="text-sm text-gray-500">Ajuste os filtros para ver mais tickets</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Base de Conhecimento</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Base de conhecimento em desenvolvimento</p>
                <p className="text-sm text-gray-500">Artigos e tutoriais serão adicionados em breve</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contatos de Suporte</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <Mail className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-medium">Email de Suporte</h3>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                      <p className="text-xs text-gray-500">Resposta em até 24h</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <Phone className="w-8 h-8 text-green-600" />
                    <div>
                      <h3 className="font-medium">Telefone de Suporte</h3>
                      <p className="text-sm text-gray-600">+55 11 99999-9999</p>
                      <p className="text-xs text-gray-500">Seg-Sex: 8h às 18h</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <MessageSquare className="w-8 h-8 text-purple-600" />
                    <div>
                      <h3 className="font-medium">Chat Online</h3>
                      <p className="text-sm text-gray-600">Disponível no sistema</p>
                      <p className="text-xs text-gray-500">Resposta imediata</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <Users className="w-8 h-8 text-orange-600" />
                    <div>
                      <h3 className="font-medium">Suporte Técnico</h3>
                      <p className="text-sm text-gray-600">Para problemas críticos</p>
                      <p className="text-xs text-gray-500">Disponível 24/7</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de Ticket */}
      <Dialog open={isTicketModalOpen} onOpenChange={setIsTicketModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              {selectedTicket?.title}
              <Badge variant="outline">#{selectedTicket?.id}</Badge>
            </DialogTitle>
          </DialogHeader>
          
          {selectedTicket && (
            <div className="space-y-4">
              {/* Informações do Ticket */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium">Cliente</p>
                  <p className="text-sm text-gray-600">{selectedTicket.club_name}</p>
                  <p className="text-xs text-gray-500">{selectedTicket.customer_email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusBadge(selectedTicket.status)}
                    {getPriorityBadge(selectedTicket.priority)}
                  </div>
                </div>
              </div>

              {/* Mensagens */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {selectedTicket.messages.map((message) => (
                  <div key={message.id} className={`flex gap-3 ${message.sender_type === 'support' ? 'flex-row-reverse' : ''}`}>
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${message.sender_name}`} />
                      <AvatarFallback>
                        {message.sender_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`flex-1 ${message.sender_type === 'support' ? 'text-right' : ''}`}>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium">{message.sender_name}</span>
                        <span className="text-xs text-gray-500">
                          {format(new Date(message.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </span>
                      </div>
                      <div className={`p-3 rounded-lg ${message.sender_type === 'support' ? 'bg-blue-100 ml-8' : 'bg-gray-100 mr-8'}`}>
                        <p className="text-sm">{message.message}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Nova Mensagem */}
              <div className="border-t pt-4">
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Digite sua resposta..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    className="flex-1"
                    rows={3}
                  />
                  <div className="flex flex-col gap-2">
                    <Button onClick={handleSendMessage} disabled={!newMessage.trim()}>
                      <Send className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Paperclip className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};