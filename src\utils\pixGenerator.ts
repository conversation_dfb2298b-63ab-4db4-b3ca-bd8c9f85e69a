import { createStaticPix } from 'pix-utils';
import QRCode from 'qrcode';

// Função para preparar dados PIX respeitando os limites da biblioteca
const preparePixData = (data: PixData) => {
    // Limites da biblioteca pix-utils
    const merchantName = (data.merchantName || 'CLUBE DE FUTEBOL').substring(0, 25);
    const merchantCity = (data.merchantCity || 'SAO PAULO').substring(0, 15);
    const infoAdicional = data.description?.substring(0, 72) || '';
    
    return {
        merchantName,
        merchantCity,
        pixKey: data.pixKey.trim(),
        infoAdicional,
        transactionAmount: data.amount
    };
};

// Função para verificar se o objeto PIX é válido ou um erro
const isPixError = (pixData: any): boolean => {
    return pixData && pixData.error === true;
};

// Função para validar diferentes tipos de chave PIX
const validatePixKey = (pixKey: string): { isValid: boolean; type: string; error?: string } => {
    const cleanKey = pixKey.trim();
    
    if (!cleanKey) {
        return { isValid: false, type: 'empty', error: 'Chave PIX vazia' };
    }

    // CPF (11 dígitos)
    const cpfRegex = /^\d{11}$/;
    if (cpfRegex.test(cleanKey.replace(/\D/g, ''))) {
        return { isValid: true, type: 'cpf' };
    }

    // CNPJ (14 dígitos)
    const cnpjRegex = /^\d{14}$/;
    if (cnpjRegex.test(cleanKey.replace(/\D/g, ''))) {
        return { isValid: true, type: 'cnpj' };
    }

    // Email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(cleanKey)) {
        return { isValid: true, type: 'email' };
    }

    // Telefone (formato brasileiro)
    const phoneRegex = /^\+55\d{10,11}$|^\d{10,11}$/;
    if (phoneRegex.test(cleanKey.replace(/\D/g, ''))) {
        return { isValid: true, type: 'phone' };
    }

    // Chave aleatória (UUID format)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanKey)) {
        return { isValid: true, type: 'random' };
    }

    // Se chegou até aqui, pode ser uma chave válida mas em formato não reconhecido
    // Vamos tentar processar mesmo assim
    return { isValid: true, type: 'unknown', error: 'Formato de chave não reconhecido, mas tentando processar' };
};

export interface PixData {
    pixKey: string;
    amount: number;
    description: string;
    merchantName?: string;
    merchantCity?: string;
}

export const generatePixQRCode = async (data: PixData): Promise<string> => {
    try {
        // Validações básicas
        if (!data.pixKey || data.pixKey.trim() === '') {
            throw new Error('Chave PIX não pode estar vazia');
        }

        if (!data.amount || data.amount <= 0) {
            throw new Error('Valor deve ser maior que zero');
        }

        // Validar formato da chave PIX
        const pixValidation = validatePixKey(data.pixKey);
        
        if (!pixValidation.isValid) {
            throw new Error(`Chave PIX inválida: ${pixValidation.error}`);
        }

        const pixConfig = preparePixData(data);

        let pixData;
        try {
            pixData = createStaticPix(pixConfig);
        } catch (pixError) {
            throw new Error(`Erro ao processar chave PIX: ${pixError.message}`);
        }

        // Verificar se o objeto é um erro
        if (isPixError(pixData)) {
            throw new Error(`Erro na biblioteca PIX: ${pixData.message}`);
        }

        // Verificar se o método existe antes de chamar
        if (typeof pixData.toBRCode !== 'function') {
            throw new Error('Erro interno: método toBRCode não disponível');
        }

        const brCode = pixData.toBRCode();

        const qrCodeDataURL = await QRCode.toDataURL(brCode, {
            width: 300,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });

        return qrCodeDataURL;
    } catch (error) {
        throw new Error(`Não foi possível gerar o QR Code PIX: ${error.message}`);
    }
};

// Função para testar a biblioteca pix-utils
export const testPixLibrary = (): { success: boolean; error?: string } => {
    try {
        const testData = {
            merchantName: 'TESTE',
            merchantCity: 'SAO PAULO',
            pixKey: '<EMAIL>',
            transactionAmount: 1.00,
            infoAdicional: 'Teste'
        };

        const pixData = createStaticPix(testData);
        
        if (typeof pixData.toBRCode === 'function') {
            pixData.toBRCode();
            return { success: true };
        } else {
            return { success: false, error: 'Método toBRCode não encontrado' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
};

export const generatePixString = (data: PixData): string => {
    try {
        // Validações básicas
        if (!data.pixKey || data.pixKey.trim() === '') {
            throw new Error('Chave PIX não pode estar vazia');
        }

        if (!data.amount || data.amount <= 0) {
            throw new Error('Valor deve ser maior que zero');
        }

        // Validar formato da chave PIX
        const pixValidation = validatePixKey(data.pixKey);
        
        if (!pixValidation.isValid) {
            throw new Error(`Chave PIX inválida: ${pixValidation.error}`);
        }

        const pixConfig = preparePixData(data);

        let pixData;
        try {
            pixData = createStaticPix(pixConfig);
        } catch (pixError) {
            throw new Error(`Erro ao processar chave PIX: ${pixError.message}`);
        }

        // Verificar se o objeto é um erro
        if (isPixError(pixData)) {
            throw new Error(`Erro na biblioteca PIX: ${pixData.message}`);
        }

        // Verificar se o método existe antes de chamar
        if (typeof pixData.toBRCode !== 'function') {
            throw new Error('Erro interno: método toBRCode não disponível');
        }

        const brCode = pixData.toBRCode();
        return brCode;
    } catch (error) {
        throw new Error(`Não foi possível gerar a string PIX: ${error.message}`);
    }
};