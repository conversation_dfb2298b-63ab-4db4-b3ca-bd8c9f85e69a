import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ListChecks, Calendar, Clock, MapPin, User, FileText, Package, Trophy } from "lucide-react";
import type { Training, TrainingExercise } from "@/api/api";
import { getTrainingExercises } from "@/api";
import { formatDate } from "@/lib/utils";

interface TrainingDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  training: Training | null;
}

export function TrainingDetailsDialog({ open, onOpenChange, training }: TrainingDetailsDialogProps) {
  const [exercises, setExercises] = useState<TrainingExercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!open || !training) return;
    setLoading(true);
    setError(null);
    getTrainingExercises(training.id)
      .then(setExercises)
      .catch((e) => setError(e.message))
      .finally(() => setLoading(false));
  }, [open, training]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concluído':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'em andamento':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'agendado':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'físico':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'tático':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'técnico':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Detalhes do Treino
          </DialogTitle>
        </DialogHeader>

        {training && (
          <div className="space-y-6">
            {/* Cabeçalho do treino */}
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-bold">{training.name}</h2>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge className={getTypeColor(training.type)}>
                      {training.type}
                    </Badge>
                    <Badge className={getStatusColor(training.status)}>
                      {training.status}
                    </Badge>
                    {training.category_name && (
                      <Badge variant="outline">
                        {training.category_name}
                      </Badge>
                    )}
                  </div>
                </div>
                {training.progress !== undefined && (
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">Progresso</div>
                    <div className="text-2xl font-bold">{training.progress}%</div>
                  </div>
                )}
              </div>

              {/* Informações básicas */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Data</div>
                    <div className="font-medium">{formatDate(training.date)}</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Horário</div>
                    <div className="font-medium">
                      {training.start_time && training.end_time
                        ? `${training.start_time} - ${training.end_time}`
                        : training.time || 'Não definido'
                      }
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Local</div>
                    <div className="font-medium">{training.location}</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">Treinador</div>
                    <div className="font-medium">{training.coach}</div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Imagens do treino */}
            {training.images && training.images.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Imagens do Treino</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {training.images.map((image, index) => (
                    <div key={image.id} className="relative border rounded-md overflow-hidden h-32">
                      <img
                        src={image.image_url}
                        alt={`Imagem ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Descrição */}
            {training.description && (
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Descrição
                </h3>
                <div className="bg-muted p-4 rounded-md">
                  <p className="whitespace-pre-wrap">{training.description}</p>
                </div>
              </div>
            )}

            {/* Resumo (para treinos concluídos) */}
            {training.status === 'concluído' && training.summary && (
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Resumo do Treino
                </h3>
                <div className="bg-green-50 border border-green-200 p-4 rounded-md">
                  <p className="whitespace-pre-wrap">{training.summary}</p>
                </div>
              </div>
            )}

            {/* Materiais necessários */}
            {training.required_materials && (
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Materiais Necessários
                </h3>
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-md">
                  <p className="whitespace-pre-wrap">{training.required_materials}</p>
                </div>
              </div>
            )}

            {/* Exercícios */}
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <ListChecks className="h-5 w-5" />
                Exercícios do Treino
              </h3>
              {loading ? (
                <div className="text-center text-muted-foreground py-8">Carregando exercícios...</div>
              ) : error ? (
                <div className="text-center text-red-500 py-8">{error}</div>
              ) : exercises.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">Nenhum exercício associado a este treino.</div>
              ) : (
                <div className="space-y-2">
                  {exercises
                    .sort((a, b) => a.order_in_training - b.order_in_training)
                    .map((ex, idx) => (
                      <div key={ex.id} className="border rounded-md p-4 bg-card">
                        <div className="flex items-start gap-3">
                          <div className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                            {idx + 1}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{ex.exercises?.name}</h4>
                            {ex.exercises?.description && (
                              <p className="text-sm text-muted-foreground mt-1">{ex.exercises.description}</p>
                            )}
                            {ex.notes && (
                              <div className="mt-2 p-2 bg-muted rounded text-sm">
                                <strong>Observações:</strong> {ex.notes}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
