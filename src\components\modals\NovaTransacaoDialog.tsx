import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFinancialStore } from "@/store/useFinancialStore";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { Upload, Settings } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useFinancialCategoriesStore } from "@/store/useFinancialCategoriesStore";
import { ManageFinancialCategoriesDialog } from "@/components/financeiro/ManageFinancialCategoriesDialog";

interface NovaTransacaoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function NovaTransacaoDialog({ open, onOpenChange, clubId }: NovaTransacaoDialogProps) {
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [category, setCategory] = useState("salários");
  const [type, setType] = useState("despesa");
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentStatus, setPaymentStatus] = useState("pending");
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const [uploadingReceipt, setUploadingReceipt] = useState(false);
  const { addTransaction, loading } = useFinancialStore();
  const { categories, fetchCategories } = useFinancialCategoriesStore();
  const [manageCategoriesOpen, setManageCategoriesOpen] = useState(false);

  const defaultCategories = [
    "Salários",
    "Transferências",
    "Ingressos",
    "Patrocínios",
    "Operacional",
    "Vendas Camisas",
    "outros",
    "Aluguel",
    "Contas de Água",
    "Conta Telefone",
    "Conta de Energia",
    "Alimentação",
    "Combustível",
    "Papelaria",
    "Hotel",
    "Vale",
    "Imposto",
    "Despesa Federação",
    "Despesa CBF",
    "Taxa de Inscrição",
    "Exame Médico",
    "Parcelamento Dívida",
    "Fornecedor",
    "Despesas Fixas",
  ];

  useEffect(() => {
    if (open) {
      fetchCategories(clubId);
    }
  }, [open, clubId, fetchCategories]);

  const allCategories = Array.from(new Set([...defaultCategories, ...categories.map(c => c.name)]));
  // Função para fazer upload do comprovante
  const uploadReceipt = async (transactionId: number, file: File): Promise<string | null> => {
    try {
      setUploadingReceipt(true);

      // Gerar nome único para o arquivo
      const fileExt = file.name.split('.').pop();
      const fileName = `${clubId}_${transactionId}_${Date.now()}.${fileExt}`;
      const filePath = `financial_receipts/${fileName}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from("playerdocuments")
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error("Erro no upload:", uploadError);
        toast({
          title: "Erro",
          description: "Erro ao fazer upload do comprovante.",
          variant: "destructive"
        });
        return null;
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from("playerdocuments")
        .getPublicUrl(filePath);

      console.log("URL do comprovante gerada:", urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error("Erro no upload:", error);
      toast({
        title: "Erro",
        description: "Erro ao fazer upload do comprovante.",
        variant: "destructive"
      });
      return null;
    } finally {
      setUploadingReceipt(false);
    }
  };

  const handleSave = async () => {
    if (!description || !amount || !date) {
      return;
    }

    // Converte o valor para número
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount)) {
      return;
    }

    try {
      console.log("Adicionando transação:", {
        description,
        amount: numericAmount,
        category,
        type,
        date,
        club_id: clubId,
        payment_status: type === 'receita' ? 'paid' : paymentStatus
      });

      // Criar a transação primeiro
      const transaction = await addTransaction(clubId, {
        description,
        amount: numericAmount,
        category,
        type,
        date,
        club_id: clubId,
        payment_status: type === 'receita' ? 'paid' : paymentStatus
      });

      // Se o status for "pago" e há um arquivo, fazer upload
      if (paymentStatus === "paid" && receiptFile && transaction?.id) {
        const receiptUrl = await uploadReceipt(transaction.id, receiptFile);

        // Atualizar a transação com a URL do comprovante
        if (receiptUrl) {
          const { error: updateError } = await supabase
            .from("financial_transactions")
            .update({ receipt_url: receiptUrl } as any)
            .eq("id", transaction.id as any)
            .eq("club_id", clubId as any);

          if (updateError) {
            console.error("Erro ao atualizar URL do comprovante:", updateError);
            toast({
              title: "Aviso",
              description: "Transação criada, mas houve erro ao salvar o comprovante.",
              variant: "destructive"
            });
          } else {
            toast({
              title: "Sucesso",
              description: "Transação criada com comprovante anexado.",
            });
          }
        }
      } else {
        toast({
          title: "Sucesso",
          description: "Transação criada com sucesso.",
        });
      }

      // Reset form
      setDescription("");
      setAmount("");
      setCategory("salários");
      setType("despesa");
      setDate(new Date().toISOString().split('T')[0]);
      setPaymentStatus("pending");
      setReceiptFile(null);
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao adicionar transação:", error);
      toast({
        title: "Erro",
        description: "Erro ao criar transação.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Nova Transação Financeira</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Input id="description" value={description} onChange={e => setDescription(e.target.value)} placeholder="Descrição da transação" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Valor</Label>
              <Input
                id="amount"
                value={amount}
                onChange={e => setAmount(e.target.value)}
                placeholder="0,00"
                type="number"
                step="0.01"
              />
            </div>

            <div>
              <Label htmlFor="date">Data</Label>
              <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Categoria</Label>
              <div className="flex gap-2">
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    {allCategories.map((cat) => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon" onClick={() => setManageCategoriesOpen(true)}>
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="type">Tipo</Label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="receita">Receita</SelectItem>
                  <SelectItem value="despesa">Despesa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {type === 'despesa' && (
            <div>
              <Label htmlFor="payment_status">Status de Pagamento</Label>
              <Select value={paymentStatus} onValueChange={setPaymentStatus}>
                <SelectTrigger id="payment_status">
                  <SelectValue placeholder="Status de Pagamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pendente</SelectItem>
                  <SelectItem value="paid">Pago</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {type === 'despesa' && paymentStatus === 'paid' && (
            <div>
              <Label htmlFor="receipt">Comprovante de Pagamento</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="receipt"
                  type="file"
                  onChange={(e) => setReceiptFile(e.target.files?.[0] || null)}
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                {uploadingReceipt && (
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Upload className="h-4 w-4 animate-spin" />
                    Enviando...
                  </div>
                )}
              </div>
              {receiptFile && (
                <p className="text-sm text-muted-foreground mt-1">
                  Arquivo selecionado: {receiptFile.name} ({(receiptFile.size / 1024).toFixed(2)} KB)
                </p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Formatos aceitos: PDF, JPG, JPEG, PNG (máx. 5MB)
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!description || !amount || !date || loading}>
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
      <ManageFinancialCategoriesDialog
        open={manageCategoriesOpen}
        onOpenChange={setManageCategoriesOpen}
        clubId={clubId}
      />
    </Dialog>
  );
}
