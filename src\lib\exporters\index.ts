// Export all exporters and utilities
export { PDFExporter } from './PDFExporter';
export { ImageExporter } from './ImageExporter';
export { VideoExporter } from './VideoExporter';
export { DataExporter } from './DataExporter';

// Re-export main engine types
export type {
  ExportFormat,
  ExportOptions,
  PDFExportOptions,
  ImageExportOptions,
  VideoExportOptions,
  JSONExportOptions,
  ExportResult,
  ExportProgress,
  ExportJob,
  BaseExporter
} from '../ExportEngine';

export { ExportEngine, defaultExportEngine, createExportEngine } from '../ExportEngine';

// Utility function to register all exporters
import { ExportEngine } from '../ExportEngine';
import { PDFExporter } from './PDFExporter';
import { ImageExporter } from './ImageExporter';
import { VideoExporter } from './VideoExporter';
import { DataExporter } from './DataExporter';

export function createConfiguredExportEngine(): ExportEngine {
  const engine = new ExportEngine();
  
  // Register all exporters
  engine.registerExporter('pdf', new PDFExporter());
  engine.registerExporter('image', new ImageExporter());
  engine.registerExporter('video', new VideoExporter());
  engine.registerExporter('json', new DataExporter());
  
  return engine;
}

// Configure the default export engine
import { defaultExportEngine } from '../ExportEngine';

export function configureDefaultExportEngine(): void {
  // Register all exporters with the default engine
  defaultExportEngine.registerExporter('pdf', new PDFExporter());
  defaultExportEngine.registerExporter('image', new ImageExporter());
  defaultExportEngine.registerExporter('video', new VideoExporter());
  defaultExportEngine.registerExporter('json', new DataExporter());
}

// Auto-configure on import
configureDefaultExportEngine();