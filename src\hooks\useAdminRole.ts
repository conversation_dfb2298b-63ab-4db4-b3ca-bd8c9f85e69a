import { useUser } from '@/context/UserContext';
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { ensureAuthenticated } from '@/integrations/supabase/ensureAuth';

export function useAdminRole() {
  const { user } = useUser();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminRole = async () => {
      if (!user?.id || !user?.club_id) {
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      try {
        await ensureAuthenticated();
        // Primeiro, verificar se é o president do clube (campo president na club_info)
        const { data: clubInfo, error: clubError } = await supabase
          .from('club_info')
          .select('president')
          .eq('id', user.club_id)
          .single();

        if (!clubError && clubInfo?.president) {
          // Verificar se o nome do usuário corresponde ao president
          if (user.name === clubInfo.president) {
            setIsAdmin(true);
            setLoading(false);
            return;
          }
        }

        // Se não é president, verificar se tem role administrativo na tabela club_members
        const { data: memberData, error: memberError } = await supabase
          .from('club_members')
          .select('role')
          .eq('user_id', user.id)
          .eq('club_id', user.club_id)
          .single();

        if (!memberError && memberData?.role) {
          const adminRoles = ['admin', 'owner', 'president', 'director', 'manager'];
          const hasAdminRole = adminRoles.includes(memberData.role.toLowerCase());
          
          if (hasAdminRole) {
            setIsAdmin(true);
            setLoading(false);
            return;
          }
        }

        // Por último, verificar na tabela collaborators (caso exista)
        const { data: collaborator, error: collabError } = await supabase
          .from('collaborators')
          .select(`
            id,
            role,
            collaborator_roles(
              name,
              role_type
            )
          `)
          .eq('user_id', user.id)
          .eq('club_id', user.club_id)
          .single();

        if (!collabError && collaborator?.collaborator_roles?.name) {
          const adminRoles = [
            'Presidente',
            'Vice presidente', 
            'CEO',
            'Diretor',
            'Gerente administrativo',
            'Gerente de operações',
            'Coordenador'
          ];

          const hasAdminRole = adminRoles.includes(collaborator.collaborator_roles.name);
          setIsAdmin(hasAdminRole);
        } else {
          setIsAdmin(false);
        }

        setLoading(false);
      } catch (error) {
        console.error('Erro ao verificar role administrativo:', error);
        setIsAdmin(false);
        setLoading(false);
      }
    };

    checkAdminRole();
  }, [user?.id, user?.club_id, user?.name]);

  return { isAdmin, loading };
}