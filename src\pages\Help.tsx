import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, BookOpen, Sparkles, ArrowRight, ChevronDown, ChevronUp, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { helpModules, searchFeatures, getAllFeatures, type HelpModule, type HelpFeature } from '@/data/helpData';
import { VideoModal } from '@/components/help/VideoModal';
import { Link } from 'react-router-dom';

export default function Help() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedModule, setSelectedModule] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<'all' | 'Core' | 'Avançada'>('all');
  const [expandedFeatures, setExpandedFeatures] = useState<Set<string>>(new Set());
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<HelpFeature | null>(null);

  const filteredFeatures = useMemo(() => {
    let features = searchFeatures(searchQuery, selectedType === 'all' ? undefined : selectedType);
    
    if (selectedModule !== 'all') {
      features = features.filter(feature => feature.category === helpModules.find(m => m.id === selectedModule)?.name);
    }
    
    return features;
  }, [searchQuery, selectedModule, selectedType]);

  const toggleFeatureExpansion = (featureId: string) => {
    const newExpanded = new Set(expandedFeatures);
    if (newExpanded.has(featureId)) {
      newExpanded.delete(featureId);
    } else {
      newExpanded.add(featureId);
    }
    setExpandedFeatures(newExpanded);
  };

  const openVideoModal = (feature: HelpFeature) => {
    setSelectedFeature(feature);
    setVideoModalOpen(true);
  };

  const closeVideoModal = () => {
    setVideoModalOpen(false);
    setSelectedFeature(null);
  };

  const FeatureCard = ({ feature }: { feature: HelpFeature }) => {
    const isExpanded = expandedFeatures.has(feature.id);
    
    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/20 hover:border-l-primary hover:scale-[1.02] relative overflow-hidden">
          {/* Gradient overlay on hover */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          <CardHeader className="pb-3 relative z-10">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <motion.div
                  className="text-2xl bg-gray-50 group-hover:bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center transition-colors duration-300"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  {feature.icon}
                </motion.div>
                <div className="flex-1">
                  <CardTitle className="text-lg group-hover:text-primary transition-colors flex items-center gap-2">
                    {feature.title}
                    {feature.type === 'Avançada' && (
                      <Sparkles className="h-4 w-4 text-yellow-500" />
                    )}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {feature.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={feature.type === 'Core' ? 'default' : 'secondary'}
                  className={feature.type === 'Avançada' ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' : ''}
                >
                  {feature.type}
                </Badge>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openVideoModal(feature)}
                  className="h-8 px-3 text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-500 text-white border-none hover:from-blue-600 hover:to-purple-600 shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Demo
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleFeatureExpansion(feature.id)}
                  className="h-8 w-8 p-0 hover:bg-primary/10"
                >
                  <motion.div
                    animate={{ rotate: isExpanded ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </motion.div>
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <CardContent className="pt-0 relative z-10">
                  <div className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200 relative overflow-hidden"
                    >
                      <div className="absolute top-0 right-0 w-20 h-20 bg-blue-100 rounded-full -translate-y-10 translate-x-10 opacity-50" />
                      <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                        🎯 Problema que resolve:
                      </h4>
                      <p className="text-blue-800 text-sm relative z-10">{feature.problem}</p>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        ⚡ Funcionalidades:
                      </h4>
                      <div className="grid gap-2">
                        {feature.functionalities.map((func, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group"
                          >
                            <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/60 rounded-full mt-2 flex-shrink-0 group-hover:scale-125 transition-transform" />
                            <span className="text-sm text-gray-700 group-hover:text-gray-900 transition-colors">{func}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>

                    {feature.tags.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          🏷️ Tags:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {feature.tags.map((tag, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.5 + index * 0.05 }}
                            >
                              <Badge
                                variant="outline"
                                className="text-xs hover:bg-primary hover:text-white transition-colors cursor-pointer"
                                onClick={() => setSearchQuery(tag)}
                              >
                                {tag}
                              </Badge>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 scroll-smooth">
      {/* Header */}
      <div className="bg-white border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center gap-3">
              <img src="/logo-branca.png" alt="Game Day Nexus" className="h-10 w-auto" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Game Day Nexus</h1>
                <p className="text-sm text-gray-500">Central de Ajuda</p>
              </div>
            </Link>
            
            <div className="flex items-center gap-4">
              <Link to="/login">
                <Button variant="outline">Entrar</Button>
              </Link>
              <Link to="/">
                <Button>Voltar ao Site</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-primary to-primary/80 text-white py-20 overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1993&q=80')`
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/95 to-primary/85" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-primary/20" />
        </div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl"
          />
          <motion.div
            animate={{
              x: [0, -80, 0],
              y: [0, 60, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-32 right-20 w-24 h-24 bg-white/10 rounded-full blur-lg"
          />
          <motion.div
            animate={{
              x: [0, 60, 0],
              y: [0, -40, 0],
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/3 rounded-full blur-2xl"
          />

          {/* Floating particles */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              animate={{
                y: [0, -20, 0],
                x: [0, 10, 0],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 4 + i,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 0.5,
              }}
              className={`absolute w-2 h-2 bg-white/40 rounded-full`}
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + i * 10}%`,
              }}
            />
          ))}
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="relative"
          >
            <motion.div
              animate={{
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="inline-block"
            >
              <BookOpen className="h-20 w-20 mx-auto mb-8 opacity-90 drop-shadow-lg" />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent"
            >
              Central de Ajuda
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative"
            >
              <p className="text-xl md:text-2xl text-white/95 mb-8 max-w-4xl mx-auto leading-relaxed font-light">
                Descubra todas as funcionalidades do <span className="font-semibold text-white">Game Day Nexus Platform</span>.
                <br className="hidden md:block" />
                Mais de <span className="font-bold text-yellow-300">200 recursos</span> para transformar a gestão do seu clube esportivo.
              </p>

              {/* Decorative elements */}
              <div className="absolute -top-4 -left-4 w-8 h-8 border-l-2 border-t-2 border-white/30 rounded-tl-lg" />
              <div className="absolute -bottom-4 -right-4 w-8 h-8 border-r-2 border-b-2 border-white/30 rounded-br-lg" />
            </motion.div>

            {/* Estatísticas */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-12"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white/15 backdrop-blur-md rounded-2xl p-8 text-center border border-white/20 shadow-2xl relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10">
                  <motion.div
                    className="text-5xl font-bold text-white mb-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 1 }}
                  >
                    {helpModules.length}
                  </motion.div>
                  <div className="text-white/90 font-medium text-lg">Módulos Principais</div>
                  <div className="text-white/70 text-sm mt-2">Sistemas integrados</div>
                </div>
                <div className="absolute top-4 right-4 text-white/20 text-2xl">🏗️</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white/15 backdrop-blur-md rounded-2xl p-8 text-center border border-white/20 shadow-2xl relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10">
                  <motion.div
                    className="text-5xl font-bold text-white mb-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 1.1 }}
                  >
                    {getAllFeatures().length}+
                  </motion.div>
                  <div className="text-white/90 font-medium text-lg">Funcionalidades</div>
                  <div className="text-white/70 text-sm mt-2">Recursos disponíveis</div>
                </div>
                <div className="absolute top-4 right-4 text-white/20 text-2xl">⚡</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.9 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white/15 backdrop-blur-md rounded-2xl p-8 text-center border border-white/20 shadow-2xl relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10">
                  <motion.div
                    className="text-5xl font-bold text-white mb-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 1.2 }}
                  >
                    100%
                  </motion.div>
                  <div className="text-white/90 font-medium text-lg">Integrado</div>
                  <div className="text-white/70 text-sm mt-2">Sistema unificado</div>
                </div>
                <div className="absolute top-4 right-4 text-white/20 text-2xl">🔗</div>
              </motion.div>
            </motion.div>

            <div className="flex flex-col items-center gap-4">
              <div className="flex items-center justify-center gap-2 text-white/80">
                <Sparkles className="h-5 w-5" />
                <span>Sistema ERP completo para clubes de futebol</span>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <a
                  href="https://wa.me/5519987111198?text=Olá! Vi a central de ajuda e gostaria de conhecer o Game Day Nexus. Quando posso começar?"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    size="lg"
                    className="bg-white text-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    🚀 Começar Teste Grátis
                  </Button>
                </a>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg border p-6 mb-8 relative overflow-hidden"
        >
          {/* Animated background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-purple-50/30 to-pink-50/50 opacity-60" />
          <motion.div
            animate={{
              background: [
                'linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))',
                'linear-gradient(45deg, rgba(147, 51, 234, 0.1), rgba(236, 72, 153, 0.1))',
                'linear-gradient(45deg, rgba(236, 72, 153, 0.1), rgba(59, 130, 246, 0.1))',
              ]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0"
          />

          <div className="relative z-10">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Buscar funcionalidades..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-4">
              <Select value={selectedModule} onValueChange={setSelectedModule}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Todos os módulos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os módulos</SelectItem>
                  {helpModules.map((module) => (
                    <SelectItem key={module.id} value={module.id}>
                      {module.icon} {module.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedType} onValueChange={(value: any) => setSelectedType(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os tipos</SelectItem>
                  <SelectItem value="Core">Core</SelectItem>
                  <SelectItem value="Avançada">Avançada</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="mt-4 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Filter className="h-4 w-4" />
              <span>
                {filteredFeatures.length} funcionalidade{filteredFeatures.length !== 1 ? 's' : ''} encontrada{filteredFeatures.length !== 1 ? 's' : ''}
              </span>
              {searchQuery && (
                <Badge variant="outline" className="ml-2">
                  Busca: "{searchQuery}"
                </Badge>
              )}
              {selectedModule !== 'all' && (
                <Badge variant="outline" className="ml-2">
                  Módulo: {helpModules.find(m => m.id === selectedModule)?.name}
                </Badge>
              )}
              {selectedType !== 'all' && (
                <Badge variant="outline" className="ml-2">
                  Tipo: {selectedType}
                </Badge>
              )}
            </div>

            {(searchQuery || selectedModule !== 'all' || selectedType !== 'all') && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedModule('all');
                  setSelectedType('all');
                }}
                className="flex items-center gap-2"
              >
                Limpar filtros
              </Button>
            )}
          </div>

          {/* Popular Searches */}
          {searchQuery === '' && selectedModule === 'all' && selectedType === 'all' && (
            <div className="mt-4 pt-4 border-t">
              <p className="text-sm text-gray-600 mb-2">Buscas populares:</p>
              <div className="flex flex-wrap gap-2">
                {['escalação', 'financeiro', 'médico', 'relatórios', 'PIX', 'convocação', 'treinamento'].map((term) => (
                  <Button
                    key={term}
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery(term)}
                    className="text-xs h-7 px-3 bg-gray-100 hover:bg-gray-200 text-gray-700"
                  >
                    {term}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </motion.div>

        {/* Quick Navigation */}
        {selectedModule === 'all' && searchQuery === '' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-8"
          >
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Navegação Rápida</h3>
              <div className="flex flex-wrap gap-2">
                {helpModules.map((module) => (
                  <Button
                    key={module.id}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedModule(module.id)}
                    className="flex items-center gap-2 hover:bg-primary hover:text-white transition-colors"
                  >
                    <span>{module.icon}</span>
                    <span>{module.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {/* Module Overview */}
        {selectedModule === 'all' && searchQuery === '' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-12"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Módulos do Sistema</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {helpModules.map((module, index) => (
                <motion.div
                  key={module.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                >
                  <Card
                    className="group hover:shadow-2xl transition-all duration-500 cursor-pointer h-full relative overflow-hidden border-2 hover:border-primary/20 hover:scale-105 hover:-translate-y-2"
                    onClick={() => setSelectedModule(module.id)}
                  >
                    {/* Hover overlay effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                    {/* Animated border effect */}
                    <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 via-purple-500/20 to-primary/20 blur-sm" />
                    </div>
                    <CardHeader className="relative z-10">
                      <motion.div
                        className={`w-12 h-12 rounded-lg bg-gradient-to-r ${module.color} flex items-center justify-center text-white text-2xl mb-3 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        {module.icon}
                      </motion.div>
                      <CardTitle className="group-hover:text-primary transition-colors duration-300 text-xl font-bold">
                        {module.name}
                      </CardTitle>
                      <CardDescription className="group-hover:text-gray-700 transition-colors duration-300">
                        {module.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="relative z-10">
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="outline"
                          className="group-hover:bg-primary group-hover:text-white transition-colors duration-300 border-gray-300 group-hover:border-primary"
                        >
                          {module.features.length} funcionalidades
                        </Badge>
                        <motion.div
                          whileHover={{ x: 5 }}
                          transition={{ duration: 0.2 }}
                        >
                          <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-primary transition-colors duration-300" />
                        </motion.div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Selected Module Header */}
        {selectedModule !== 'all' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-8"
          >
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {(() => {
                    const module = helpModules.find(m => m.id === selectedModule);
                    return module ? (
                      <>
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${module.color} flex items-center justify-center text-white text-2xl`}>
                          {module.icon}
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">{module.name}</h2>
                          <p className="text-gray-600">{module.description}</p>
                        </div>
                      </>
                    ) : null;
                  })()}
                </div>
                <Button
                  variant="outline"
                  onClick={() => setSelectedModule('all')}
                  className="flex items-center gap-2"
                >
                  ← Voltar para todos os módulos
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Features List */}
        <div className="space-y-6">
          <AnimatePresence mode="wait">
            {filteredFeatures.map((feature) => (
              <FeatureCard key={feature.id} feature={feature} />
            ))}
          </AnimatePresence>
          
          {filteredFeatures.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="text-gray-400 text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhuma funcionalidade encontrada
              </h3>
              <p className="text-gray-600">
                Tente ajustar os filtros ou usar termos diferentes na busca.
              </p>
            </motion.div>
          )}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-16 bg-white rounded-xl shadow-sm border p-8"
        >
          <div className="text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Precisa de mais ajuda?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Nossa equipe está sempre pronta para ajudar você a aproveitar ao máximo
              todas as funcionalidades do Game Day Nexus Platform.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg"
              >
                <div className="text-3xl mb-3">💬</div>
                <h4 className="font-semibold text-gray-900 mb-2">Suporte via WhatsApp</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Fale diretamente com nossa equipe
                </p>
                <a
                  href="https://wa.me/5519987111198?text=Olá! Preciso de ajuda com o Game Day Nexus."
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="sm" className="w-full">
                    Abrir WhatsApp
                  </Button>
                </a>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg"
              >
                <div className="text-3xl mb-3">🚀</div>
                <h4 className="font-semibold text-gray-900 mb-2">Teste Grátis</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Experimente todas as funcionalidades
                </p>
                <a
                  href="https://wa.me/5519987111198?text=Olá! Quero começar o teste grátis do Game Day Nexus."
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="sm" variant="outline" className="w-full">
                    Começar Teste
                  </Button>
                </a>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg"
              >
                <div className="text-3xl mb-3">📚</div>
                <h4 className="font-semibold text-gray-900 mb-2">Blog e Recursos</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Dicas e tutoriais detalhados
                </p>
                <Link to="/blog">
                  <Button size="sm" variant="outline" className="w-full">
                    Ver Blog
                  </Button>
                </Link>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Video Modal */}
      <VideoModal
        isOpen={videoModalOpen}
        onClose={closeVideoModal}
        feature={selectedFeature}
      />
    </div>
  );
}
