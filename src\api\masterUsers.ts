import { supabase } from "@/integrations/supabase/client";

const ensureSession = async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
        console.error('Erro ao verificar sessão:', error);
        throw new Error('Erro ao verificar sessão de usuário');
    }
    if (!session) {
        throw new Error('Usuário não está autenticado');
    }
    return session;
};

export interface MasterUser {
    id: string;
    organization_id?: number;
    name: string;
    email: string;
    role: 'super_admin' | 'admin' | 'support' | 'viewer';
    permissions: Record<string, any>;
    is_active: boolean;
    last_login_at?: string;
    created_at: string;
    updated_at: string;
}

export interface CreateMasterUserData {
    name: string;
    email: string;
    password: string;
    role: 'super_admin' | 'admin' | 'support' | 'viewer';
    permissions: Record<string, any>;
    is_active: boolean;
    organization_id?: number;
}

export interface UpdateMasterUserData {
    name?: string;
    role?: 'super_admin' | 'admin' | 'support' | 'viewer';
    permissions?: Record<string, any>;
    is_active?: boolean;
    organization_id?: number;
}

/**
 * Busca todos os usuários master
 */
export const getMasterUsers = async (): Promise<MasterUser[]> => {
    try {
        await ensureSession();
        const { data, error } = await supabase
            .from('master_users')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Erro ao buscar usuários master:', error);
            throw new Error(`Erro ao buscar usuários: ${error.message}`);
        }

        return data || [];
    } catch (error: any) {
        console.error('Erro ao buscar usuários master:', error);
        throw error;
    }
};

/**
 * Busca um usuário master por ID
 */
export const getMasterUserById = async (userId: string): Promise<MasterUser | null> => {
    try {
        await ensureSession();
        const { data, error } = await supabase
            .from('master_users')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return null;
            }
            throw new Error(`Erro ao buscar usuário: ${error.message}`);
        }

        return data;
    } catch (error: any) {
        console.error('Erro ao buscar usuário master:', error);
        throw error;
    }
};

/**
 * Cria um novo usuário master
 */
export const createMasterUser = async (userData: CreateMasterUserData): Promise<MasterUser> => {
    try {
        await ensureSession();
        // Chamar Edge Function para criar usuário
        const { data, error } = await supabase.functions.invoke('create-master-user', {
            body: {
                name: userData.name,
                email: userData.email,
                password: userData.password,
                role: userData.role,
                permissions: userData.permissions,
                is_active: userData.is_active,
                organization_id: userData.organization_id
            }
        });

        if (error) {
            console.error('Erro na Edge Function:', error);
            throw new Error(`Erro ao criar usuário: ${error.message}`);
        }

        if (!data.success) {
            throw new Error(`Erro ao criar usuário: ${data.error}`);
        }

        return data.user;
    } catch (error: any) {
        console.error('Erro ao criar usuário master:', error);
        throw error;
    }
};

/**
 * Atualiza um usuário master
 */
export const updateMasterUser = async (userId: string, userData: UpdateMasterUserData): Promise<MasterUser> => {
    try {
        await ensureSession();
        const { data, error } = await supabase
            .from('master_users')
            .update({
                ...userData,
                updated_at: new Date().toISOString()
            })
            .eq('id', userId)
            .select()
            .single();

        if (error) {
            console.error('Erro ao atualizar usuário:', error);
            throw new Error(`Erro ao atualizar usuário: ${error.message}`);
        }

        return data;
    } catch (error: any) {
        console.error('Erro ao atualizar usuário master:', error);
        throw error;
    }
};

/**
 * Exclui um usuário master
 */
export const deleteMasterUser = async (userId: string): Promise<void> => {
    try {
        await ensureSession();
        // Chamar Edge Function para excluir usuário (remove do auth e da tabela)
        const { data, error } = await supabase.functions.invoke('delete-master-user', {
            body: { userId }
        });

        if (error) {
            console.error('Erro na Edge Function:', error);
            throw new Error(`Erro ao excluir usuário: ${error.message}`);
        }

        if (!data.success) {
            throw new Error(`Erro ao excluir usuário: ${data.error}`);
        }
    } catch (error: any) {
        console.error('Erro ao excluir usuário master:', error);
        throw error;
    }
};

/**
 * Alterna o status ativo/inativo de um usuário master
 */
export const toggleMasterUserStatus = async (userId: string, isActive: boolean): Promise<MasterUser> => {
    try {
        await ensureSession();
        const { data, error } = await supabase
            .from('master_users')
            .update({
                is_active: isActive,
                updated_at: new Date().toISOString()
            })
            .eq('id', userId)
            .select()
            .single();

        if (error) {
            console.error('Erro ao alterar status:', error);
            throw new Error(`Erro ao alterar status: ${error.message}`);
        }

        return data;
    } catch (error: any) {
        console.error('Erro ao alterar status do usuário master:', error);
        throw error;
    }
};

/**
 * Redefine a senha de um usuário master
 */
export const resetMasterUserPassword = async (userId: string, newPassword: string): Promise<void> => {
    try {
        await ensureSession();
        const { data, error } = await supabase.functions.invoke('reset-master-user-password', {
            body: {
                userId,
                newPassword
            }
        });

        if (error) {
            console.error('Erro na Edge Function:', error);
            throw new Error(`Erro ao redefinir senha: ${error.message}`);
        }

        if (!data.success) {
            throw new Error(`Erro ao redefinir senha: ${data.error}`);
        }
    } catch (error: any) {
        console.error('Erro ao redefinir senha:', error);
        throw error;
    }
};

/**
 * Gera uma senha temporária segura
 */
export const generateTemporaryPassword = (): string => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
};