import { supabase } from "@/integrations/supabase/client";
import { getMasterClubs } from "./masterClubs";
import { getMasterPayments } from "./masterBilling";

// TypeScript interfaces for dashboard data structures
export interface MasterDashboardStats {
  totalClubs: number;
  activeClubs: number;
  trialClubs: number;
  suspendedClubs: number;
  monthlyRevenue: number;
  overduePayments: number;
  newClubsThisMonth: number;
  revenueGrowth?: number; // calculated on frontend
}

export interface MasterActivity {
  id: string;
  action: string;
  entityType: string;
  entityId: number;
  userName: string;
  timestamp: string;
  description: string;
  details?: Record<string, any>;
}

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
let statsCache: { data: MasterDashboardStats; timestamp: number } | null = null;
let activitiesCache: { data: MasterActivity[]; timestamp: number } | null = null;

// Fallback calculation using direct table queries
const calculateStatsFallback = async (): Promise<MasterDashboardStats> => {
    const [clubs, payments] = await Promise.all([
      getMasterClubs(),
      getMasterPayments()
    ]);
  
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
  
    const totalClubs = clubs.length;
    const activeClubs = clubs.filter(c => c.subscription_status === 'active').length;
    const trialClubs = clubs.filter(c => c.subscription_status === 'trial' || c.is_trial).length;
    const suspendedClubs = clubs.filter(c => c.subscription_status === 'suspended').length;
  
    const monthlyRevenue = payments
      .filter(p => {
        if (p.status !== 'paid' || !p.paid_date) return false;
        const date = new Date(p.paid_date);
        return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
      })
      .reduce((sum, p) => sum + p.amount, 0);
  
    const overduePayments = payments.filter(p => p.status === 'overdue').length;
  
    return {
      totalClubs,
      activeClubs,
      trialClubs,
      suspendedClubs,
      monthlyRevenue,
      overduePayments,
      newClubsThisMonth: 0
    };
  };  
  
/**
 * Get master dashboard statistics with caching
 */
export const getMasterDashboardStats = async (useCache: boolean = true): Promise<MasterDashboardStats> => {
  try {
    // Check cache first
    if (useCache && statsCache && Date.now() - statsCache.timestamp < CACHE_DURATION) {
      return statsCache.data;
    }

    // Call the database function
    const { data, error } = await supabase.rpc('get_master_dashboard_stats');
    console.log('RPC dashboard stats data:', data);

    if (error) {
      console.error('Database error in getMasterDashboardStats:', error);
      
      // Handle specific error cases
      if (error.code === 'PGRST202') {
        throw new Error('Função get_master_dashboard_stats não encontrada. Verifique se a migração foi aplicada.');
      }
      
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados nas tabelas. Verifique se a migração foi aplicada.');
      }
      
      throw new Error(`Erro ao buscar estatísticas do dashboard: ${error.message}`);
    }

    if (!data || data.length === 0) {
      const fallback = await calculateStatsFallback();
      if (useCache) {
        statsCache = { data: fallback, timestamp: Date.now() };
      }
      return fallback;
    }

    // Transform the data to match our interface
    const stats: MasterDashboardStats = {
      totalClubs: data[0]?.total_clubs || 0,
      activeClubs: data[0]?.active_clubs || 0,
      trialClubs: data[0]?.trial_clubs || 0,
      suspendedClubs: data[0]?.suspended_clubs || 0,
      monthlyRevenue: parseFloat(data[0]?.monthly_revenue || '0'),
      overduePayments: data[0]?.overdue_payments || 0,
      newClubsThisMonth: data[0]?.new_clubs_this_month || 0
    };

    const allZero =
      stats.totalClubs === 0 &&
      stats.activeClubs === 0 &&
      stats.trialClubs === 0 &&
      stats.suspendedClubs === 0 &&
      stats.monthlyRevenue === 0 &&
      stats.overduePayments === 0 &&
      stats.newClubsThisMonth === 0;

    if (allZero) {
      console.log('RPC returned zero stats, using fallback calculation');
      const fallback = await calculateStatsFallback();
      if (useCache) {
        statsCache = { data: fallback, timestamp: Date.now() };
      }
      return fallback;
    }

    // Cache the results
    if (useCache) {
      statsCache = { data: stats, timestamp: Date.now() };
    }

    return stats;
  } catch (error: any) {
    console.error('Error in getMasterDashboardStats:', error);

    // If it's a specific database error, re-throw it
    if (error.message.includes('não encontrada') || error.message.includes('Campos não encontrados')) {
      throw error;
    }

    // Attempt fallback calculation before failing completely
    try {
      const fallback = await calculateStatsFallback();
      if (useCache) {
        statsCache = { data: fallback, timestamp: Date.now() };
      }
      return fallback;
    } catch (fallbackErr) {
      console.error('Fallback calculation failed:', fallbackErr);
    }

    // For other errors, provide a generic message
    throw new Error(error.message || 'Erro ao buscar estatísticas do dashboard');
  }
};

/**
 * Get master recent activities with proper error handling
 */
export const getMasterRecentActivities = async (limit: number = 10, useCache: boolean = true): Promise<MasterActivity[]> => {
  try {
    // Check cache first
    if (useCache && activitiesCache && Date.now() - activitiesCache.timestamp < CACHE_DURATION) {
      return activitiesCache.data.slice(0, limit);
    }

    // Call the database function
    const { data, error } = await supabase.rpc('get_master_recent_activities', { 
      limit_count: limit,
      offset_count: 0
    });

    if (error) {
      console.error('Database error in getMasterRecentActivities:', error);
      
      // Handle specific error cases
      if (error.code === 'PGRST202') {
        throw new Error('Função get_master_recent_activities não encontrada. Verifique se a migração foi aplicada.');
      }
      
      if (error.code === 'PGRST116') {
        throw new Error('Erro: Campos não encontrados na tabela master_audit_logs. Verifique se a migração foi aplicada.');
      }
      
      throw new Error(`Erro ao buscar atividades recentes: ${error.message}`);
    }

    if (!data || data.length === 0) {
      // Return empty array if no activities
      const emptyActivities: MasterActivity[] = [];
      
      // Cache the empty result
      if (useCache) {
        activitiesCache = { data: emptyActivities, timestamp: Date.now() };
      }
      
      return emptyActivities;
    }

    // Transform the data to match our interface
    const activities: MasterActivity[] = data.map((activity: any) => ({
      id: activity.id?.toString() || '',
      action: activity.action || 'Ação desconhecida',
      entityType: activity.entity_type || 'unknown',
      entityId: activity.entity_id || 0,
      userName: activity.user_name || 'Usuário desconhecido',
      timestamp: activity.created_at || new Date().toISOString(),
      description: activity.action || 'Ação realizada',
      details: activity.details || {}
    }));

    // Cache the results
    if (useCache) {
      activitiesCache = { data: activities, timestamp: Date.now() };
    }

    return activities;
  } catch (error: any) {
    console.error('Error in getMasterRecentActivities:', error);
    
    // If it's a specific database error, re-throw it
    if (error.message.includes('não encontrada') || error.message.includes('Campos não encontrados')) {
      throw error;
    }
    
    // For other errors, provide a generic message
    throw new Error(error.message || 'Erro ao buscar atividades recentes');
  }
};

/**
 * Retry logic wrapper for API calls
 */
const withRetry = async <T>(
  fn: () => Promise<T>, 
  maxRetries: number = 3, 
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;
      
      // Don't retry on specific database errors (missing functions/fields)
      if (error.message.includes('não encontrada') || error.message.includes('Campos não encontrados')) {
        throw error;
      }
      
      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
};

/**
 * Get dashboard stats with retry logic
 */
export const getMasterDashboardStatsWithRetry = async (useCache: boolean = true): Promise<MasterDashboardStats> => {
  return withRetry(() => getMasterDashboardStats(useCache));
};

/**
 * Get recent activities with retry logic
 */
export const getMasterRecentActivitiesWithRetry = async (limit: number = 10, useCache: boolean = true): Promise<MasterActivity[]> => {
  return withRetry(() => getMasterRecentActivities(limit, useCache));
};

/**
 * Clear cache manually
 */
export const clearMasterDashboardCache = (): void => {
  statsCache = null;
  activitiesCache = null;
};

/**
 * Get cache status
 */
export const getCacheStatus = () => {
  return {
    statsCache: {
      exists: !!statsCache,
      age: statsCache ? Date.now() - statsCache.timestamp : 0,
      expired: statsCache ? Date.now() - statsCache.timestamp > CACHE_DURATION : true
    },
    activitiesCache: {
      exists: !!activitiesCache,
      age: activitiesCache ? Date.now() - activitiesCache.timestamp : 0,
      expired: activitiesCache ? Date.now() - activitiesCache.timestamp > CACHE_DURATION : true
    }
  };
};