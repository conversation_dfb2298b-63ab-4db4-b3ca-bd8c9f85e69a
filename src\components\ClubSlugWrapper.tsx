import { ReactNode, Suspense } from 'react';
import { useClubSlug } from '@/hooks/useClubSlug';
import { useSecurityMonitor, useURLTamperingDetection } from '@/hooks/useSecurityMonitor';
import { ClubProvider } from '@/context/ClubContext';
import { ClubAccessProvider } from '@/context/ClubAccessContext';
import { ClubInfoLoader } from '@/components/ClubInfoLoader';
import { FastLoadingSpinner, PageLoadingSpinner } from '@/components/FastLoadingSpinner';
import { UnauthorizedAccess } from '@/components/UnauthorizedAccess';

interface ClubSlugWrapperProps {
  children: ReactNode;
}

function ErrorDisplay({ error }: { error: string }) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-500 to-red-600">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <h2 className="text-2xl font-bold mb-4 text-red-600">Clube não encontrado</h2>
        <p className="mb-4 text-gray-600">{error}</p>
        <a 
          href="/login" 
          className="inline-block bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Voltar ao Login
        </a>
      </div>
    </div>
  );
}

export function ClubSlugWrapper({ children }: ClubSlugWrapperProps) {
  const { club, loading, error, accessDenied } = useClubSlug();
  
  // Ativar monitoramento de segurança
  useSecurityMonitor();
  useURLTamperingDetection();

  // PRIORIDADE MÁXIMA: Mostrar erro de acesso não autorizado
  if (accessDenied.denied) {
    return (
      <UnauthorizedAccess 
        attemptedClub={accessDenied.clubSlug}
        message="Você tentou acessar um clube ao qual não pertence. Esta ação foi registrada por motivos de segurança."
      />
    );
  }

  // Se temos um clube, renderizar imediatamente (mesmo se loading for true)
  if (club) {
    return (
      <ClubProvider clubId={club.id}>
        <ClubAccessProvider>
          <Suspense fallback={<FastLoadingSpinner />}>
            <ClubInfoLoader />
            {children}
          </Suspense>
        </ClubAccessProvider>
      </ClubProvider>
    );
  }

  // Mostrar loading apenas se realmente está carregando e não há clube
  if (loading) {
    return <PageLoadingSpinner />;
  }

  // Mostrar erro se houver erro
  if (error) {
    return <ErrorDisplay error={error} />;
  }

  // Se não há clube e não há loading nem erro, mostrar erro genérico
  return <ErrorDisplay error="O clube que você está tentando acessar não foi encontrado." />;
}