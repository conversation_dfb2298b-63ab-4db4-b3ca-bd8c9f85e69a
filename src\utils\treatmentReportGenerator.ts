import { jsPDF } from "jspdf";
import "jspdf-autotable";
import autoTable from "jspdf-autotable";
import { ClubInfo, Player } from "@/api/api";
import { generateDigitalSignatureText } from "@/utils/digitalSignature";

type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

export interface TreatmentReportData {
  patient: Player;
  startDate: string;
  endDate: string;
  injury: string;
  dischargeReason: string;
  treatmentUsed: string;
  recommendation: string;
  doctorName: string;
  doctorRole?: string;
  treatmentDescription?: string[];
}

export function parseRecordDescription(description: string) {
  const map: Record<string, string> = {};
  description.split("\n").forEach(line => {
    const [key, ...rest] = line.split(":");
    if (!key || rest.length === 0) return;
    const normalized = key
      .trim()
      .normalize("NFD")
      .replace(/\p{Diacritic}/gu, "");
    map[normalized] = rest.join(":").trim();
  });
  return {
    startDate: map["Data inicio"] || "",
    endDate: map["Data encerramento"] || "",
    injury: map["Lesao"] || "",
    dischargeReason: map["Motivo da alta"] || "",
    treatmentUsed: map["Tratamento utilizado"] || "",
    recommendation: map["Recomendacao"] || "",
  };
}

export async function generateTreatmentReport(
  data: TreatmentReportData,
  clubInfo: ClubInfo,
  filename = 'relatorio-tratamento.pdf'
): Promise<void> {
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  const title = 'Relatório de Tratamento';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  
  if (clubInfo.logo_url) {
    try {
      const img = new Image();
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });
      img.src = clubInfo.logo_url;
      await loadImage;
    } catch (err) {
      console.error('Erro ao adicionar logo ao PDF:', err);
    }
  }

  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, 170, 50, { align: 'right' });

  doc.setFontSize(14);
  doc.text('Informações do Paciente', 14, 60);

  const patientTable = [
    ['Nome', data.patient.name || '-'],
    ['Data de Nascimento', data.patient.birthdate ? new Date(data.patient.birthdate).toLocaleDateString('pt-BR') : '-'],
    ['Categoria', (data.patient as any).category || '-'],
    ['Posição', (data.patient as any).position || '-'],
    ['Registro', (data.patient as any).registration_number || '-'],
  ];

  autoTable(doc, {
    startY: 65,
    body: patientTable,
    theme: 'grid',
    styles: { fontSize: 10 },
    columnStyles: { 0: { fontStyle: 'bold', width: 40 } },
  });

  const docWithTable = doc as jsPDFWithAutoTable;
  let y = docWithTable.lastAutoTable.finalY + 15;

  doc.setFontSize(14);
  doc.text('Informações de Tratamento', 14, y);
  y += 5;

  const treatmentTable = [
    ['Data de Início', data.startDate],
    ['Data de Encerramento', data.endDate],
    ['Lesão', data.injury],
    ['Motivo da Alta', data.dischargeReason],
    ['Tratamento Utilizado', data.treatmentUsed],
    ['Recomendação', data.recommendation],
  ];

  autoTable(doc, {
    startY: y,
    body: treatmentTable,
    theme: 'grid',
    styles: { fontSize: 10 },
    columnStyles: { 0: { fontStyle: 'bold', width: 55 } },
    rowPageBreak: 'avoid',
  });

  y = docWithTable.lastAutoTable.finalY + 10;

  if (data.treatmentDescription && data.treatmentDescription.length > 0) {
    doc.setFontSize(12);
    doc.text('Descrições das Sessões', 14, y);
    y += 5;
    autoTable(doc, {
      startY: y,
      body: data.treatmentDescription.map(d => [d]),
      theme: 'grid',
      styles: { fontSize: 10 },
      columnStyles: { 0: { width: 180 } },
    });
    y = docWithTable.lastAutoTable.finalY + 10;
  }

  if (data.doctorName) {
    doc.setFontSize(12);
    const doctorRole = data.doctorRole || 'Médico';
    doc.text(`Médico Responsável: ${data.doctorName} - ${doctorRole}`, 14, y);
    y += 10;
    
    const signatureLines = generateDigitalSignatureText(
      data.doctorName,
      data.doctorRole || 'medical'
    ).split('\n');
    doc.setFontSize(10);
    signatureLines.forEach((line, idx) => {
      doc.text(line, 14, y + idx * 5);
    });
    y += signatureLines.length * 5;
  }

  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${currentDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  doc.save(filename);
}