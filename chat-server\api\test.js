export default function handler(req, res) {
  // Configurar CORS
  const origin = req.headers.origin;
  const allowedOrigins = [
    'https://www.gamedaynexus.com.br',
    'https://gamedaynexus.com.br',
    'http://localhost:5173',
    'http://localhost:3000'
  ];

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.status(200).end(); 
  }

  res.status(200).json({
    message: 'Socket endpoint is working',
    method: req.method,
    url: req.url,
    origin: req.headers.origin,
    timestamp: new Date().toISOString()
  });
}