import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getPendingDocuments, PlayerDocument, DOCUMENT_LABELS } from "@/api/api";
import { PlayerDocumentViewer } from "@/components/player/PlayerDocumentViewer";
import { CollaboratorDocumentViewer } from "@/components/administrativo/CollaboratorDocumentViewer";
import { AlertCircle, FileText, Eye, User, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";

// Tipo para documentos de colaborador
interface CollaboratorDocument {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: string;
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  collaborator_name: string;
}

export function PendingDocuments() {
  const clubId = useCurrentClubId();
  const [playerDocuments, setPlayerDocuments] = useState<PlayerDocument[]>([]);
  const [collaboratorDocuments, setCollaboratorDocuments] = useState<CollaboratorDocument[]>([]);
  const [loadingPlayers, setLoadingPlayers] = useState(true);
  const [loadingCollaborators, setLoadingCollaborators] = useState(true);
  const [isPlayerViewerOpen, setIsPlayerViewerOpen] = useState(false);
  const [isCollaboratorViewerOpen, setIsCollaboratorViewerOpen] = useState(false);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string | null>(null);
  const [selectedCollaboratorId, setSelectedCollaboratorId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("players");

  // Carregar documentos pendentes
  useEffect(() => {
    const fetchPlayerDocuments = async () => {
      try {
        setLoadingPlayers(true);
        const data = await getPendingDocuments(clubId);
        setPlayerDocuments(data);
      } catch (err: any) {
        console.error("Erro ao carregar documentos pendentes de jogadores:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os documentos pendentes de jogadores",
          variant: "destructive",
        });
      } finally {
        setLoadingPlayers(false);
      }
    };

    const fetchCollaboratorDocuments = async () => {
      try {
        setLoadingCollaborators(true);

        const { data, error } = await supabase
          .from('collaborator_documents')
          .select('id, club_id, collaborator_id, document_type, file_url, status, uploaded_at, verified_at, verified_by, rejection_reason, collaborators(full_name)')
          .eq('club_id', clubId)
          .eq('status', 'pending')
          .order('uploaded_at');

        if (error) {
          throw new Error(`Erro ao buscar documentos pendentes de colaboradores: ${error.message}`);
        }

        const docs = (data || []).map((doc: any) => ({
          ...doc,
          collaborator_name: doc.collaborators?.full_name || ''
        }));

        setCollaboratorDocuments(docs);
      } catch (err: any) {
        console.error("Erro ao carregar documentos pendentes de colaboradores:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os documentos pendentes de colaboradores",
          variant: "destructive",
        });
      } finally {
        setLoadingCollaborators(false);
      }
    };

    fetchPlayerDocuments();
    fetchCollaboratorDocuments();
  }, [clubId]);

  // Função para abrir o visualizador de documentos de jogadores
  const handleViewPlayerDocuments = (playerId: string) => {
    setSelectedPlayerId(playerId);
    setIsPlayerViewerOpen(true);
  };

  // Função para abrir o visualizador de documentos de colaboradores
  const handleViewCollaboratorDocuments = (collaboratorId: number) => {
    setSelectedCollaboratorId(collaboratorId);
    setIsCollaboratorViewerOpen(true);
  };

  // Agrupar documentos de jogadores por jogador
  const documentsByPlayer: Record<string, { playerId: string, playerName: string, count: number }> = {};

  playerDocuments.forEach((doc) => {
    if (!documentsByPlayer[doc.player_id]) {
      documentsByPlayer[doc.player_id] = {
        playerId: doc.player_id,
        playerName: doc.player_name || "Jogador desconhecido",
        count: 0,
      };
    }
    documentsByPlayer[doc.player_id].count++;
  });

  // Agrupar documentos de colaboradores por colaborador
  const documentsByCollaborator: Record<number, { collaboratorId: number, collaboratorName: string, count: number }> = {};

  collaboratorDocuments.forEach((doc) => {
    if (!documentsByCollaborator[doc.collaborator_id]) {
      documentsByCollaborator[doc.collaborator_id] = {
        collaboratorId: doc.collaborator_id,
        collaboratorName: doc.collaborator_name || "Colaborador desconhecido",
        count: 0,
      };
    }
    documentsByCollaborator[doc.collaborator_id].count++;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-yellow-500" />
          Documentos Pendentes
        </CardTitle>
        <CardDescription>
          Documentos de jogadores e colaboradores que precisam de verificação
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="players" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Jogadores
            </TabsTrigger>
            <TabsTrigger value="collaborators" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Colaboradores
            </TabsTrigger>
          </TabsList>

          <TabsContent value="players">
            {loadingPlayers ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : Object.keys(documentsByPlayer).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum documento pendente de verificação para jogadores
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Jogador</TableHead>
                    <TableHead>Documentos Pendentes</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.values(documentsByPlayer).map((item) => (
                    <TableRow key={item.playerId}>
                      <TableCell className="font-medium">{item.playerName}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                          {item.count} {item.count === 1 ? "documento" : "documentos"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewPlayerDocuments(item.playerId)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Verificar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </TabsContent>

          <TabsContent value="collaborators">
            {loadingCollaborators ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : Object.keys(documentsByCollaborator).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum documento pendente de verificação para colaboradores
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Colaborador</TableHead>
                    <TableHead>Documentos Pendentes</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.values(documentsByCollaborator).map((item) => (
                    <TableRow key={item.collaboratorId}>
                      <TableCell className="font-medium">{item.collaboratorName}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                          {item.count} {item.count === 1 ? "documento" : "documentos"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewCollaboratorDocuments(item.collaboratorId)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Verificar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </TabsContent>
        </Tabs>

        {selectedPlayerId && (
          <PlayerDocumentViewer
            open={isPlayerViewerOpen}
            onOpenChange={setIsPlayerViewerOpen}
            playerId={selectedPlayerId}
            canVerify={true}
          />
        )}

        {selectedCollaboratorId && (
          <CollaboratorDocumentViewer
            open={isCollaboratorViewerOpen}
            onOpenChange={setIsCollaboratorViewerOpen}
            collaboratorId={selectedCollaboratorId}
            clubId={clubId}
            canVerify={true}
          />
        )}
      </CardContent>
    </Card>
  );
}