import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Footer, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCurrentClubId } from '@/context/ClubContext';
import { useUser } from '@/context/UserContext';
import {
  createGameOperationStaff,
  updateGameOperationStaff,
  GameOperationStaff,
  getOperationRoles
} from '@/api/gameOperations';
import { toast } from '@/components/ui/use-toast';
import { OperationRoleForm } from './OperationRoleForm';

interface OperationStaffFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff?: GameOperationStaff;
  onSuccess?: () => void;
}

export function OperationStaffForm({ open, onOpenChange, staff, onSuccess }: OperationStaffFormProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [name, setName] = useState('');
  const [role, setRole] = useState('');
  const [birth, setBirth] = useState('');
  const [cpf, setCpf] = useState('');
  const [pixKey, setPixKey] = useState('');
  const [roles, setRoles] = useState<string[]>([]);
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    async function fetchRoles() {
      try {
        const data = await getOperationRoles(clubId);
        setRoles(data.map(r => r.name));
      } catch (err) {
        console.error(err);
      }
    }
    if (open) fetchRoles();
  }, [open, clubId]);

  useEffect(() => {
    if (staff) {
      setName(staff.name);
      setRole(staff.role);
      setBirth(staff.birth_date || '');
      setCpf(staff.cpf || '');
      setPixKey(staff.pix_key || '');
    } else {
      setName('');
      setRole('');
      setBirth('');
      setCpf('');
      setPixKey('');
    }
  }, [staff, open]);

  const handleSave = async () => {
    if (!name.trim() || !role.trim()) return;
    try {
      setLoading(true);
      if (staff) {
        await updateGameOperationStaff(clubId, user?.id || '', staff.id, { name, role, birth_date: birth || null, cpf: cpf || null, pix_key: pixKey || null });
        toast({ title: 'Sucesso', description: 'Membro atualizado' });
      } else {
        await createGameOperationStaff(clubId, user?.id || '', { name, role, birth_date: birth || null, cpf: cpf || null, pix_key: pixKey || null });
        toast({ title: 'Sucesso', description: 'Membro criado' });
      }
      onOpenChange(false);
      onSuccess?.();
    } catch (err) {
      console.error(err);
      toast({ title: 'Erro', description: 'Não foi possível salvar', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  const handleRoleCreated = (newRole: string) => {
    setRoles(prev => [...prev, newRole]);
    setRole(newRole);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{staff ? 'Editar Membro' : 'Novo Membro'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="op-name">Nome</Label>
              <Input id="op-name" value={name} onChange={e => setName(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="op-role">Função</Label>
              <div className="flex gap-2">
                <select id="op-role" className="flex-1 p-2 border rounded-md" value={role} onChange={e => setRole(e.target.value)}>
                  <option value="">Selecione</option>
                  {roles.map(r => (
                    <option key={r} value={r}>{r}</option>
                  ))}
                </select>
                <Button variant="outline" onClick={() => setRoleDialogOpen(true)}>+</Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="op-birth">Data de Nascimento</Label>
              <Input id="op-birth" type="date" value={birth} onChange={e => setBirth(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="op-cpf">CPF</Label>
              <Input id="op-cpf" value={cpf} onChange={e => setCpf(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="op-pix">Chave PIX / Conta</Label>
              <Input id="op-pix" value={pixKey} onChange={e => setPixKey(e.target.value)} />
            </div>          
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
            <Button onClick={handleSave} disabled={!name.trim() || !role.trim() || loading}>Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <OperationRoleForm open={roleDialogOpen} onOpenChange={setRoleDialogOpen} onCreated={handleRoleCreated} />
    </>
  );
}
