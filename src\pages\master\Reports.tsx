import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter,
  TrendingUp,
  Building2,
  DollarSign,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { supabase } from "@/integrations/supabase/client";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";
import { toast } from 'sonner';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { DateRange } from 'react-day-picker';

interface ReportData {
  id: string;
  name: string;
  description: string;
  type: 'financial' | 'clubs' | 'users' | 'system';
  status: 'ready' | 'generating' | 'error';
  createdAt: string;
  size?: string;
  downloadUrl?: string;
}

interface ReportMetrics {
  totalRevenue: number;
  totalClubs: number;
  activeClubs: number;
  suspendedClubs: number;
  overduePayments: number;
  newClubsThisMonth: number;
  churnRate: number;
  averageRevenue: number;
}

export const Reports: React.FC = () => {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [metrics, setMetrics] = useState<ReportMetrics>({
    totalRevenue: 0,
    totalClubs: 0,
    activeClubs: 0,
    suspendedClubs: 0,
    overduePayments: 0,
    newClubsThisMonth: 0,
    churnRate: 0,
    averageRevenue: 0
  });
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date())
  });
  const [reportType, setReportType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadReports();
    loadMetrics();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('master_reports')
        .select(`
          id,
          name,
          description,
          type,
          status,
          file_size,
          created_at,
          completed_at,
          error_message
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const processedReports: ReportData[] = data?.map(report => ({
        id: report.id.toString(),
        name: report.name,
        description: report.description,
        type: report.type as any,
        status: report.status as any,
        createdAt: report.created_at,
        size: report.file_size ? formatFileSize(report.file_size) : undefined,
        downloadUrl: report.status === 'ready' ? `/api/reports/${report.id}/download` : undefined
      })) || [];

      setReports(processedReports);
    } catch (error: any) {
      console.error('Erro ao carregar relatórios:', error);
      toast.error('Erro ao carregar relatórios');
    } finally {
      setLoading(false);
    }
  };

  const loadMetrics = async () => {
    try {
      // Buscar métricas reais do banco
      await ensureAuthenticated();
      const { data: clubs } = await supabase
        .from('club_info')
        .select('subscription_status, created_at');

      const { data: payments } = await supabase
        .from('master_payments')
        .select('amount, status, due_date');

      const totalClubs = clubs?.length || 0;
      const activeClubs = clubs?.filter(c => c.subscription_status === 'active').length || 0;
      const suspendedClubs = clubs?.filter(c => c.subscription_status === 'suspended').length || 0;
      
      const currentMonth = new Date();
      const newClubsThisMonth = clubs?.filter(c => {
        const created = new Date(c.created_at);
        return created.getMonth() === currentMonth.getMonth() && 
               created.getFullYear() === currentMonth.getFullYear();
      }).length || 0;

      const totalRevenue = payments?.filter(p => p.status === 'paid')
        .reduce((sum, p) => sum + p.amount, 0) || 0;
      
      const overduePayments = payments?.filter(p => 
        p.status === 'overdue' || 
        (p.status === 'pending' && new Date(p.due_date) < new Date())
      ).length || 0;

      const averageRevenue = activeClubs > 0 ? totalRevenue / activeClubs : 0;
      const cancelledClubs = clubs?.filter(c => c.subscription_status === 'cancelled').length || 0;
      const churnRate = totalClubs > 0 ? (cancelledClubs / totalClubs) * 100 : 0;

      setMetrics({
        totalRevenue,
        totalClubs,
        activeClubs,
        suspendedClubs,
        overduePayments,
        newClubsThisMonth,
        churnRate,
        averageRevenue
      });
    } catch (error: any) {
      console.error('Erro ao carregar métricas:', error);
    }
  };

  const generateReport = async (type: string) => {
    try {
      setGenerating(type);
      
      // Criar registro do relatório no banco
      const { data, error } = await supabase
        .from('master_reports')
        .insert({
          name: getReportName(type),
          description: getReportDescription(type),
          type: type,
          status: 'generating',
          parameters: { generated_at: new Date().toISOString() }
        })
        .select()
        .single();

      if (error) throw error;

      // Simular processamento do relatório
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Atualizar status para ready
      const { error: updateError } = await supabase
        .from('master_reports')
        .update({
          status: 'ready',
          file_size: Math.floor(Math.random() * 5000000) + 1000000, // 1-5MB
          completed_at: new Date().toISOString()
        })
        .eq('id', data.id);

      if (updateError) throw updateError;

      toast.success('Relatório gerado com sucesso!');
      loadReports(); // Recarregar lista
    } catch (error: any) {
      console.error('Erro ao gerar relatório:', error);
      toast.error('Erro ao gerar relatório');
    } finally {
      setGenerating(null);
    }
  };

  const getReportName = (type: string) => {
    const names = {
      financial: 'Relatório Financeiro',
      clubs: 'Relatório de Clubes',
      users: 'Relatório de Usuários',
      system: 'Relatório do Sistema'
    };
    return names[type as keyof typeof names] || 'Relatório Personalizado';
  };

  const getReportDescription = (type: string) => {
    const descriptions = {
      financial: 'Análise completa de receitas, pagamentos e inadimplência',
      clubs: 'Status, performance e métricas dos clubes cadastrados',
      users: 'Atividade e permissões dos usuários do sistema master',
      system: 'Logs de auditoria e atividades do sistema'
    };
    return descriptions[type as keyof typeof descriptions] || 'Relatório personalizado';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'generating':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      ready: { variant: 'default' as const, label: 'Pronto' },
      generating: { variant: 'secondary' as const, label: 'Gerando' },
      error: { variant: 'destructive' as const, label: 'Erro' }
    };

    const config = variants[status as keyof typeof variants] || variants.ready;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'financial':
        return <DollarSign className="w-4 h-4 text-green-600" />;
      case 'clubs':
        return <Building2 className="w-4 h-4 text-blue-600" />;
      case 'users':
        return <Users className="w-4 h-4 text-purple-600" />;
      case 'system':
        return <FileText className="w-4 h-4 text-gray-600" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = reportType === 'all' || report.type === reportType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando relatórios...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Relatórios</h1>
          <p className="text-gray-600 mt-1">
            Gere e gerencie relatórios detalhados do sistema
          </p>
        </div>
      </div>

      {/* Métricas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Receita Total</p>
                <p className="text-2xl font-bold">{formatCurrency(metrics.totalRevenue)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Clubes Ativos</p>
                <p className="text-2xl font-bold">{metrics.activeClubs}</p>
                <p className="text-xs text-gray-500">de {metrics.totalClubs} total</p>
              </div>
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pagamentos em Atraso</p>
                <p className="text-2xl font-bold text-red-600">{metrics.overduePayments}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Novos Clubes</p>
                <p className="text-2xl font-bold">{metrics.newClubsThisMonth}</p>
                <p className="text-xs text-gray-500">este mês</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Geração de Relatórios */}
      <Card>
        <CardHeader>
          <CardTitle>Gerar Novo Relatório</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button
              onClick={() => generateReport('financial')}
              disabled={generating === 'financial'}
              className="flex items-center gap-2 h-20 flex-col"
            >
              <DollarSign className="w-6 h-6" />
              <span>Financeiro</span>
              {generating === 'financial' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
            </Button>

            <Button
              onClick={() => generateReport('clubs')}
              disabled={generating === 'clubs'}
              className="flex items-center gap-2 h-20 flex-col"
              variant="outline"
            >
              <Building2 className="w-6 h-6" />
              <span>Clubes</span>
              {generating === 'clubs' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>}
            </Button>

            <Button
              onClick={() => generateReport('users')}
              disabled={generating === 'users'}
              className="flex items-center gap-2 h-20 flex-col"
              variant="outline"
            >
              <Users className="w-6 h-6" />
              <span>Usuários</span>
              {generating === 'users' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>}
            </Button>

            <Button
              onClick={() => generateReport('system')}
              disabled={generating === 'system'}
              className="flex items-center gap-2 h-20 flex-col"
              variant="outline"
            >
              <FileText className="w-6 h-6" />
              <span>Sistema</span>
              {generating === 'system' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Buscar relatórios..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                <SelectItem value="financial">Financeiro</SelectItem>
                <SelectItem value="clubs">Clubes</SelectItem>
                <SelectItem value="users">Usuários</SelectItem>
                <SelectItem value="system">Sistema</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Relatórios */}
      <Card>
        <CardHeader>
          <CardTitle>Relatórios Disponíveis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center gap-4">
                  {getTypeIcon(report.type)}
                  <div>
                    <h3 className="font-medium">{report.name}</h3>
                    <p className="text-sm text-gray-600">{report.description}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {format(new Date(report.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                      </span>
                      {report.size && (
                        <>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">{report.size}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(report.status)}
                    {getStatusBadge(report.status)}
                  </div>
                  {report.status === 'ready' && report.downloadUrl && (
                    <Button size="sm" className="flex items-center gap-2">
                      <Download className="w-4 h-4" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
            ))}

            {filteredReports.length === 0 && (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Nenhum relatório encontrado</p>
                <p className="text-sm text-gray-500">Gere um novo relatório ou ajuste os filtros</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};