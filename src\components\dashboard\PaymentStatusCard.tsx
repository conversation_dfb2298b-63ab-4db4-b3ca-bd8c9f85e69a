import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, AlertTriangle, CheckCircle, Clock, Calendar } from 'lucide-react';
import { usePaymentStatus } from '@/hooks/usePaymentStatus';
import { useAdminRole } from '@/hooks/useAdminRole';
import { useUser } from '@/context/UserContext';

export function PaymentStatusCard() {
  const { user } = useUser();
  const { paymentStatus, loading } = usePaymentStatus();
  const { isAdmin, loading: adminLoading } = useAdminRole();

  // Só mostrar para admins
  if (adminLoading || !isAdmin || !user?.club_info) {
    return null;
  }

  const clubInfo = user.club_info;

  // Não mostrar para trials
  if (clubInfo.is_trial) {
    return null;
  }

  // Não mostrar se não há status de pagamento
  if (!paymentStatus?.showCard) {
    return null;
  }

  const getStatusIcon = () => {
    if (!paymentStatus) return <CreditCard className="w-5 h-5 text-gray-500" />;

    switch (paymentStatus.type) {
      case 'payment_overdue':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'payment_due_soon':
        return paymentStatus.severity === 'error'
          ? <AlertTriangle className="w-5 h-5 text-red-500" />
          : <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
  };

  const getStatusBadge = () => {
    if (!paymentStatus) {
      return <Badge variant="secondary">Sem informações</Badge>;
    }

    switch (paymentStatus.type) {
      case 'payment_overdue':
        return <Badge variant="destructive">Em Atraso</Badge>;
      case 'payment_due_soon':
        return paymentStatus.severity === 'error'
          ? <Badge variant="destructive">Vence Hoje</Badge>
          : <Badge variant="outline" className="border-yellow-500 text-yellow-700">Vence em Breve</Badge>;
      default:
        return <Badge variant="default" className="bg-green-100 text-green-800">Em Dia</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Status do Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon()}
              Status do Pagamento
            </CardTitle>
            <CardDescription>
              Informações sobre a mensalidade do clube
            </CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Informações do Plano */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600">Plano Atual</p>
            <p className="text-lg font-semibold">{clubInfo.master_plans?.name || 'Não definido'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Valor Mensal</p>
            <p className="text-lg font-semibold">
              {clubInfo.master_plans?.price ? formatPrice(clubInfo.master_plans.price) : 'N/A'}
            </p>
          </div>
        </div>

        {/* Data do Próximo Pagamento */}
        {clubInfo.next_payment_date && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-800">Próximo Vencimento</span>
            </div>
            <p className="text-sm text-gray-700">
              {formatDate(clubInfo.next_payment_date)}
            </p>
            {paymentStatus && (
              <p className="text-xs text-gray-600 mt-1">
                {paymentStatus.message}
              </p>
            )}
          </div>
        )}

        {/* Aviso de Pagamento */}
        {paymentStatus?.type === 'payment_overdue' && (
          <div className="p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-center gap-2 mb-1">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">Ação Necessária</span>
            </div>
            <p className="text-sm text-red-700">
              Pagamento em atraso. Regularize para evitar suspensão do acesso.
            </p>
          </div>
        )}

        {paymentStatus?.type === 'payment_due_soon' && paymentStatus.severity === 'error' && (
          <div className="p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="w-4 h-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">Vence Hoje</span>
            </div>
            <p className="text-sm text-red-700">
              O pagamento vence hoje. Efetue o pagamento para manter o acesso.
            </p>
          </div>
        )}

        {paymentStatus?.type === 'payment_due_soon' && paymentStatus.severity === 'warning' && (
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">Vencimento Próximo</span>
            </div>
            <p className="text-sm text-yellow-700">
              Pagamento vence em breve. Prepare-se para efetuar o pagamento.
            </p>
          </div>
        )}

        {paymentStatus?.type === 'payment_current' && (
          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-1">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Situação Regular</span>
            </div>
            <p className="text-sm text-green-700">
              Seu pagamento está em dia. Próximo vencimento em {paymentStatus.daysUntilDue} dias.
            </p>
          </div>
        )}

        {/* Ações */}
        <div className="flex gap-2">
          <Button
            className="flex-1"
            onClick={() => window.open('mailto:<EMAIL>?subject=Pagamento de Mensalidade')}
          >
            <CreditCard className="w-4 h-4 mr-2" />
            Pagar Agora
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('mailto:<EMAIL>?subject=Dúvidas sobre Pagamento')}
          >
            Suporte
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}