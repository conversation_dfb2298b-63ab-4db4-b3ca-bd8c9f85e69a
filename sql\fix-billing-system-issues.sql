-- Correções para o sistema de cobrança

-- 1. <PERSON><PERSON><PERSON><PERSON> que as colunas PIX existam nas tabelas
ALTER TABLE players 
ADD COLUMN IF NOT EXISTS bank_pix_key VARCHAR(255);

-- Para colaboradores, vamos garantir que bank_info seja JSONB
ALTER TABLE collaborators 
ADD COLUMN IF NOT EXISTS bank_info JSONB DEFAULT '{}';

-- 2. Verificar se as tabelas de cobrança existem
CREATE TABLE IF NOT EXISTS clients (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  pix_key VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS billing_transactions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('cobranca', 'recebimento')),
  entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('player', 'collaborator', 'client')),
  entity_id INTEGER,
  entity_name VARCHAR(255) NOT NULL,
  pix_key VARCHAR(255) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'pago')),
  due_date DATE,
  paid_at TIMESTAMP WITH TIME ZONE,
  qr_code_data TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 3. Habilitar RLS
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_transactions ENABLE ROW LEVEL SECURITY;

-- 4. Remover políticas existentes se houver
DO $$ 
BEGIN
    -- Remover políticas de clients
    DROP POLICY IF EXISTS "Users can view clients from their club" ON clients;
    DROP POLICY IF EXISTS "Users can insert clients in their club" ON clients;
    DROP POLICY IF EXISTS "Users can update clients from their club" ON clients;
    DROP POLICY IF EXISTS "Users can delete clients from their club" ON clients;
    
    -- Remover políticas de billing_transactions
    DROP POLICY IF EXISTS "Users can view billing transactions from their club" ON billing_transactions;
    DROP POLICY IF EXISTS "Users can insert billing transactions in their club" ON billing_transactions;
    DROP POLICY IF EXISTS "Users can update billing transactions from their club" ON billing_transactions;
    DROP POLICY IF EXISTS "Users can delete billing transactions from their club" ON billing_transactions;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignorar erros se as políticas não existirem
        NULL;
END $$;

-- 5. Criar políticas RLS mais permissivas para clients
CREATE POLICY "Enable read access for club members" ON clients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable insert for club members" ON clients
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable update for club members" ON clients
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable delete for club members" ON clients
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = clients.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

-- 6. Criar políticas RLS para billing_transactions
CREATE POLICY "Enable read access for club members" ON billing_transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable insert for club members" ON billing_transactions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable update for club members" ON billing_transactions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

CREATE POLICY "Enable delete for club members" ON billing_transactions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = billing_transactions.club_id 
            AND club_members.user_id = auth.uid() 
            AND club_members.status = 'active'
        )
    );

-- 7. Criar índices se não existirem
CREATE INDEX IF NOT EXISTS idx_clients_club_id ON clients(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_club_id ON billing_transactions(club_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_status ON billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_entity ON billing_transactions(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_due_date ON billing_transactions(due_date);

-- 8. Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. Triggers para updated_at
DROP TRIGGER IF EXISTS update_clients_updated_at ON clients;
DROP TRIGGER IF EXISTS update_billing_transactions_updated_at ON billing_transactions;

CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_transactions_updated_at 
    BEFORE UPDATE ON billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Comentários
COMMENT ON TABLE clients IS 'Tabela de clientes do clube para sistema de cobrança';
COMMENT ON TABLE billing_transactions IS 'Tabela de transações de cobrança e recebimento';
COMMENT ON COLUMN billing_transactions.type IS 'Tipo: cobranca (clube cobra) ou recebimento (clube recebe)';
COMMENT ON COLUMN billing_transactions.entity_type IS 'Tipo de entidade: player, collaborator ou client';
COMMENT ON COLUMN billing_transactions.entity_id IS 'ID da entidade (pode ser null para clientes sem cadastro)';
COMMENT ON COLUMN billing_transactions.entity_name IS 'Nome da entidade para exibição';
COMMENT ON COLUMN billing_transactions.pix_key IS 'Chave PIX a ser usada na transação';
COMMENT ON COLUMN billing_transactions.qr_code_data IS 'Dados do QR Code PIX gerado';