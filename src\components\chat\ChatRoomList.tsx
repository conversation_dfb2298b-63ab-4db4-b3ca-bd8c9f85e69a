import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Plus, Hash, Users, Search } from 'lucide-react';
import { useChatStore } from '@/store/useChatStore';
import { useUser } from '@/context/UserContext';
import { useChat } from '@/hooks/useChat';
import { CreateRoomDialog } from './CreateRoomDialog';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import type { ChatRoom } from '@/types/chat';
import { getRoomDisplayName } from '@/utils/chat';

export function ChatRoomList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  
  const { rooms, setCurrentRoom, loadMessages } = useChatStore();
  const { markAsRead } = useChat();
  const { user } = useUser();
  const clubId = user?.club_id || localStorage.getItem('clubId');

  const currentUserName = user?.name || user?.email;
  const filteredRooms = rooms.filter(room =>
    getRoomDisplayName(room, currentUserName)
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  const handleRoomClick = async (room: ChatRoom) => {
    setCurrentRoom(room);
    await loadMessages(room.id, room.club_id);
    await markAsRead(room.id);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search and Create */}
      <div className="p-3 border-b space-y-2">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar salas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        
        <Button
          onClick={() => setShowCreateDialog(true)}
          className="w-full"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nova Sala
        </Button>
      </div>

      {/* Rooms List */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {filteredRooms.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Hash className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">
                {searchTerm ? 'Nenhuma sala encontrada' : 'Nenhuma sala disponível'}
              </p>
            </div>
          ) : (
            filteredRooms.map((room) => (
              <div
                key={room.id}
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                onClick={() => handleRoomClick(room)}
              >
                <div className="flex-shrink-0">
                  {room.is_general ? (
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Hash className="h-4 w-4 text-primary" />
                    </div>
                  ) : (
                    <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center">
                      <Users className="h-4 w-4 text-secondary-foreground" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm truncate">
                      {getRoomDisplayName(room, currentUserName)}
                    </h4>
                    {room.unread_count && room.unread_count > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {room.unread_count > 99 ? '99+' : room.unread_count}
                      </Badge>
                    )}
                  </div>
                  
                  {room.last_message && (
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-xs text-muted-foreground truncate">
                        <span className="font-medium">
                          {room.last_message.user?.name}:
                        </span>{' '}
                        {room.last_message.content}
                      </p>
                      <span className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                        {formatDistanceToNow(new Date(room.last_message.created_at), {
                          addSuffix: true,
                          locale: ptBR
                        })}
                      </span>
                    </div>
                  )}
                  
                  {room.description && !room.last_message && (
                    <p className="text-xs text-muted-foreground truncate mt-1">
                      {room.description}
                    </p>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Create Room Dialog */}
      <CreateRoomDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        clubId={clubId?.toString() || ''}
      />
    </div>
  );
}