import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useEffect } from "react";
import { updateMatchHistory, getPlayerById, updatePlayer, insertGols } from "@/api/api";
import type { MatchHistory, Gol, Cartao, MatchStats } from "@/api/api";
import { toast } from "@/hooks/use-toast";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useMatchHistoryStore } from "@/store/useMatchHistoryStore";
import { useUser } from "@/context/UserContext";
import { savePlayerMatchStatistics } from "@/api/playerStatistics";
import { updatePlayerStatsFromMatch } from "@/api/playerStatsSync";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PlayerStatsEditor } from "@/components/partidas/PlayerStatsEditor";


interface EditMatchHistoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  match: MatchHistory | null;
  clubId: number;
}

export function EditMatchHistoryDialog({ open, onOpenChange, match, clubId }: EditMatchHistoryDialogProps) {
  const [result, setResult] = useState<"win" | "loss" | "draw">("win");
  const [scoreHome, setScoreHome] = useState("");
  const [scoreAway, setScoreAway] = useState("");
  const [opponent, setOpponent] = useState("");
  const [competition, setCompetition] = useState("");
  const [location, setLocation] = useState("");
  const [date, setDate] = useState("");
  const [summary, setSummary] = useState("");
  const [stats, setStats] = useState<Partial<MatchStats>>({});
  const [goals, setGoals] = useState<Gol[]>([]);
  const [cards, setCards] = useState<Cartao[]>([]);
  const [lineup, setLineup] = useState("");
  const [formation, setFormation] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Modal states for adding goals and cards
  const [openGoalModal, setOpenGoalModal] = useState(false);
  const [openCardModal, setOpenCardModal] = useState(false);
  const [goalPlayer, setGoalPlayer] = useState("");
  const [goalMinute, setGoalMinute] = useState("");
  const [goalAssist, setGoalAssist] = useState("");
  const [cardPlayer, setCardPlayer] = useState("");
  const [cardType, setCardType] = useState<"amarelo" | "vermelho">("amarelo");

  const { players, fetchPlayers } = usePlayersStore();
  const { syncMatchHistory } = useMatchHistoryStore();
  const { user } = useUser();

  // Load players when component mounts
  useEffect(() => {
    if (players.length === 0) {
      fetchPlayers(clubId);
    }
  }, [players.length, fetchPlayers, clubId]);

  // Initialize form when match changes
  useEffect(() => {
    if (match) {
      setResult(match.result);
      setScoreHome(String(match.score_home || 0));
      setScoreAway(String(match.score_away || 0));
      setOpponent(match.opponent || "");
      setCompetition(match.competition || "");
      setLocation(match.location || "");
      setDate(match.date || "");
      setSummary(match.summary || "");
      setStats({
        chutes: match.shots || 0,
        chutesNoGol: match.shots_on_target || 0,
        escanteios: match.corners || 0,
        faltas: match.fouls || 0,
        impedimentos: match.offsides || 0,
      });
      setGoals(match.gols || []);
      setCards(match.cartoes || []);
      setLineup(Array.isArray(match.escalacao) ? match.escalacao.join(', ') : '');
      setFormation(match.formation || '4-4-2');
    }
  }, [match]);

  const handleAddGoal = () => {
    if (!goalPlayer.trim() || !goalMinute.trim()) {
      setError("Jogador e minuto são obrigatórios para adicionar gol.");
      return;
    }

    const newGoal: Gol = {
      jogador: goalPlayer,
      minuto: parseInt(goalMinute),
      assist: goalAssist.trim() || undefined,
    };

    setGoals(prev => [...prev, newGoal]);
    setGoalPlayer("");
    setGoalMinute("");
    setGoalAssist("");
    setOpenGoalModal(false);
    setError("");
  };

  const handleAddCard = () => {
    if (!cardPlayer.trim()) {
      setError("Jogador é obrigatório para adicionar cartão.");
      return;
    }

    const newCard: Cartao = {
      jogador: cardPlayer,
      tipo: cardType,
    };

    setCards(prev => [...prev, newCard]);
    setCardPlayer("");
    setCardType("amarelo");
    setOpenCardModal(false);
    setError("");
  };

  const handleSave = async () => {
    if (!match) return;

    setError("");
    setIsLoading(true);

    try {
      // Update match history
      const updatedMatch = await updateMatchHistory(clubId, match.id, {
        result,
        score_home: parseInt(scoreHome) || 0,
        score_away: parseInt(scoreAway) || 0,
        opponent,
        competition,
        location,
        date,
        summary,
        shots: Number(stats.chutes) || 0,
        shots_on_target: Number(stats.chutesNoGol) || 0,
        corners: Number(stats.escanteios) || 0,
        fouls: Number(stats.faltas) || 0,
        offsides: Number(stats.impedimentos) || 0,
        gols: goals,
        cartoes: cards,
        escalacao: lineup.split(',').map(s => s.trim()).filter(Boolean),
        formation: formation || '4-4-2',
      });

      // Update player statistics
      await updatePlayerStatistics();

      // Sync match history store
      await syncMatchHistory(clubId);

      toast({
        title: "Partida atualizada",
        description: "A partida foi atualizada com sucesso.",
      });

      onOpenChange(false);
    } catch (err: any) {
      console.error("Erro ao atualizar partida:", err);
      setError(err.message || "Erro ao atualizar partida");
    } finally {
      setIsLoading(false);
    }
  };

  const updatePlayerStatistics = async () => {
    if (!match || !user?.id) return;

    const lineupPlayers = lineup.split(',').map(s => s.trim()).filter(Boolean);

    try {
      console.log("Atualizando estatísticas dos jogadores:", {
        matchId: match.id,
        goals,
        cards,
        lineupPlayers
      });

      // Use the new sync function to update player statistics
      await updatePlayerStatsFromMatch(
        clubId,
        match.id,
        goals,
        cards,
        lineupPlayers,
        user.id
      );

      console.log("Estatísticas dos jogadores atualizadas com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar estatísticas dos jogadores:", error);
      throw error;
    }
  };

  if (!match) return null;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Partida</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="detalhes" className="mt-4">
            <TabsList className="mb-4">
              <TabsTrigger value="detalhes">Detalhes</TabsTrigger>
              <TabsTrigger value="jogadores">Estatísticas dos Jogadores</TabsTrigger>
            </TabsList>

            <TabsContent value="detalhes">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Basic Information */}
                <div className="space-y-4">
              <div>
                <Label htmlFor="opponent">Adversário</Label>
                <Input
                  id="opponent"
                  value={opponent}
                  onChange={(e) => setOpponent(e.target.value)}
                  placeholder="Nome do adversário"
                />
              </div>

              <div>
                <Label htmlFor="competition">Competição</Label>
                <Input
                  id="competition"
                  value={competition}
                  onChange={(e) => setCompetition(e.target.value)}
                  placeholder="Nome da competição"
                />
              </div>

              <div>
                <Label htmlFor="location">Local</Label>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="Local da partida"
                />
              </div>

              <div>
                <Label htmlFor="date">Data</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="result">Resultado</Label>
                <Select value={result} onValueChange={(value: "win" | "loss" | "draw") => setResult(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="win">Vitória</SelectItem>
                    <SelectItem value="draw">Empate</SelectItem>
                    <SelectItem value="loss">Derrota</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="scoreHome">Gols Casa</Label>
                  <Input
                    id="scoreHome"
                    type="number"
                    value={scoreHome}
                    onChange={(e) => setScoreHome(e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="scoreAway">Gols Fora</Label>
                  <Input
                    id="scoreAway"
                    type="number"
                    value={scoreAway}
                    onChange={(e) => setScoreAway(e.target.value)}
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="space-y-4">
              <h3 className="font-semibold">Estatísticas da Partida</h3>

              <div>
                <Label htmlFor="shots">Finalizações</Label>
                <Input
                  id="shots"
                  type="number"
                  value={stats.chutes || ""}
                  onChange={(e) => setStats(s => ({ ...s, chutes: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>

              <div>
                <Label htmlFor="shotsOnTarget">Finalizações no Gol</Label>
                <Input
                  id="shotsOnTarget"
                  type="number"
                  value={stats.chutesNoGol || ""}
                  onChange={(e) => setStats(s => ({ ...s, chutesNoGol: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>

              <div>
                <Label htmlFor="corners">Escanteios</Label>
                <Input
                  id="corners"
                  type="number"
                  value={stats.escanteios || ""}
                  onChange={(e) => setStats(s => ({ ...s, escanteios: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>

              <div>
                <Label htmlFor="fouls">Faltas</Label>
                <Input
                  id="fouls"
                  type="number"
                  value={stats.faltas || ""}
                  onChange={(e) => setStats(s => ({ ...s, faltas: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>

              <div>
                <Label htmlFor="offsides">Impedimentos</Label>
                <Input
                  id="offsides"
                  type="number"
                  value={stats.impedimentos || ""}
                  onChange={(e) => setStats(s => ({ ...s, impedimentos: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>
            </div>
          </div>

          {/* Goals Section */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Gols</h3>
              <Button variant="outline" size="sm" onClick={() => setOpenGoalModal(true)}>
                Adicionar Gol
              </Button>
            </div>
            <div className="space-y-2">
              {goals.map((goal, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span>
                    {goal.minuto}' - {goal.jogador}
                    {goal.assist && <span className="text-gray-500"> (assistência: {goal.assist})</span>}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setGoals(goals.filter((_, i) => i !== index))}
                  >
                    Remover
                  </Button>
                </div>
              ))}
              {goals.length === 0 && (
                <p className="text-gray-500 text-sm">Nenhum gol registrado</p>
              )}
            </div>
          </div>

          {/* Cards Section */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Cartões</h3>
              <Button variant="outline" size="sm" onClick={() => setOpenCardModal(true)}>
                Adicionar Cartão
              </Button>
            </div>
            <div className="space-y-2">
              {cards.map((card, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span>
                    {card.jogador} - Cartão {card.tipo}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCards(cards.filter((_, i) => i !== index))}
                  >
                    Remover
                  </Button>
                </div>
              ))}
              {cards.length === 0 && (
                <p className="text-gray-500 text-sm">Nenhum cartão registrado</p>
              )}
            </div>
          </div>

          {/* Lineup and Formation */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="lineup">Escalação (separar por vírgula)</Label>
              <Input
                id="lineup"
                value={lineup}
                onChange={(e) => setLineup(e.target.value)}
                placeholder="Jogador 1, Jogador 2, ..."
              />
            </div>
            <div>
              <Label htmlFor="formation">Formação</Label>
              <Input
                id="formation"
                value={formation}
                onChange={(e) => setFormation(e.target.value)}
                placeholder="4-4-2"
              />
            </div>
          </div>

          {/* Summary */}
          <div className="mt-6">
            <Label htmlFor="summary">Resumo da Partida</Label>
            <Input
              id="summary"
              value={summary}
              onChange={(e) => setSummary(e.target.value)}
              placeholder="Resumo opcional da partida"
            />
          </div>

          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "Salvando..." : "Salvar Alterações"}
            </Button>
          </DialogFooter>
          </TabsContent>

            <TabsContent value="jogadores">
              {match && (
                <PlayerStatsEditor
                  clubId={clubId}
                  matchId={match.id}
                  categoryId={match.category_id}
                />
              )}
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Add Goal Modal */}
      <Dialog open={openGoalModal} onOpenChange={setOpenGoalModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Gol</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="goalPlayer">Jogador</Label>
              <Select value={goalPlayer} onValueChange={setGoalPlayer}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.name}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="goalMinute">Minuto</Label>
              <Input
                id="goalMinute"
                type="number"
                value={goalMinute}
                onChange={(e) => setGoalMinute(e.target.value)}
                placeholder="Ex: 45"
              />
            </div>
            <div>
              <Label htmlFor="goalAssist">Assistência (opcional)</Label>
              <Select value={goalAssist} onValueChange={setGoalAssist}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione quem deu a assistência" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Nenhuma assistência</SelectItem>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.name}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenGoalModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleAddGoal}>
              Adicionar Gol
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Card Modal */}
      <Dialog open={openCardModal} onOpenChange={setOpenCardModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Cartão</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="cardPlayer">Jogador</Label>
              <Select value={cardPlayer} onValueChange={setCardPlayer}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o jogador" />
                </SelectTrigger>
                <SelectContent>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.name}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="cardType">Tipo de Cartão</Label>
              <Select value={cardType} onValueChange={(value: "amarelo" | "vermelho") => setCardType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="amarelo">Cartão Amarelo</SelectItem>
                  <SelectItem value="vermelho">Cartão Vermelho</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenCardModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleAddCard}>
              Adicionar Cartão
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
