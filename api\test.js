export default function handler(req, res) {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    console.log('=== API TEST DEBUG ===');
    console.log('Method:', req.method);
    console.log('Headers:', req.headers);
    console.log('Body:', req.body);
    console.log('Environment variables:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- VERCEL:', process.env.VERCEL);
    console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
    console.log('- Has GEMINI_API_KEY:', !!process.env.GEMINI_API_KEY);
    console.log('- GEMINI_API_KEY length:', process.env.GEMINI_API_KEY?.length || 0);

    try {
        res.status(200).json({
            success: true,
            message: 'API de teste funcionando!',
            timestamp: new Date().toISOString(),
            method: req.method,
            environment: {
                nodeEnv: process.env.NODE_ENV,
                vercel: process.env.VERCEL,
                vercelEnv: process.env.VERCEL_ENV,
                hasGeminiKey: !!process.env.GEMINI_API_KEY,
                geminiKeyLength: process.env.GEMINI_API_KEY?.length || 0
            }
        });
    } catch (error) {
        console.error('Erro na API de teste:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            stack: error.stack
        });
    }
}