import React, { useState, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Download,
  FileText,
  Image,
  Video,
  Database,
  Settings,
  X,
  CheckCircle,
  AlertCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { TrainingDrill } from './InteractiveTrainingBuilder';
import { useExportEngine, usePDFExport, useImageExport, useVideoExport, useDataExport } from '@/hooks/useExportEngine';
import { ExportResult, ExportProgress } from '@/lib/exporters';
import { useToast } from '@/components/ui/use-toast';

interface ExportSystemProps {
  drill: TrainingDrill | null;
  drills?: TrainingDrill[];
  embedded?: boolean; // Para quando usado dentro de outro Dialog
}

interface ExportJobStatus {
  id: string;
  name: string;
  format: string;
  progress: ExportProgress;
  result?: ExportResult;
}

export function ExportSystem({ drill, drills = [], embedded = false }: ExportSystemProps) {
  const { toast } = useToast();
  const exportEngine = useExportEngine();
  const pdfExport = usePDFExport();
  const imageExport = useImageExport();
  const videoExport = useVideoExport();
  const dataExport = useDataExport();

  const [showExportDialog, setShowExportDialog] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'image' | 'video' | 'json'>('pdf');
  const [exportOptions, setExportOptions] = useState({
    quality: 'high',
    includeInstructions: true,
    includeTimeline: false,
    includeAnimations: true,
    includeTrajectories: true,
    watermark: false,
    watermarkText: 'Game Day Nexus'
  });
  const [activeExports, setActiveExports] = useState<Map<string, ExportJobStatus>>(new Map());
  const [batchExport, setBatchExport] = useState(false);

  // Handle single drill export
  const handleExport = useCallback(async () => {
    if (!drill) return;

    try {
      let result: ExportResult;
      const options = {
        quality: exportOptions.quality as any,
        includeMetadata: true,
        ...(exportOptions.watermark && {
          watermark: {
            text: exportOptions.watermarkText,
            position: 'bottom-right' as const,
            opacity: 0.3
          }
        })
      };

      switch (selectedFormat) {
        case 'pdf':
          result = await pdfExport.exportToPDF(drill, {
            ...options,
            includeInstructions: exportOptions.includeInstructions,
            includeTimeline: exportOptions.includeTimeline,
            orientation: 'landscape',
            template: 'landscape'
          });
          break;
        case 'image':
          if (drill.steps.length > 1) {
            const results = [] as ExportResult[];
            for (let i = 0; i < drill.steps.length; i++) {
              const stepDrill = { ...drill, steps: [drill.steps[i]], name: `${drill.name} - Passo ${i + 1}` };
              const r = await imageExport.exportToImage(stepDrill, {
                ...options,
                imageFormat: 'png',
                width: 1920,
                height: 1080
              });
              results.push(r);
              if (r.success) exportEngine.downloadResult(r);
            }
            toast({
              title: 'Exportação concluída',
              description: `${results.filter(r => r.success).length}/${drill.steps.length} passos exportados.`
            });
            return;
          } else {
            result = await imageExport.exportToImage(drill, {
              ...options,
              imageFormat: 'png',
              width: 1920,
              height: 1080
            });
          }          break;
        case 'video':
          result = await videoExport.exportToVideo(drill, {
            ...options,
            videoFormat: 'webm',
            fps: 30,
            duration: 10000
          });
          break;
        case 'json':
          result = await dataExport.exportToJSON(drill, {
            ...options,
            includeAnimations: exportOptions.includeAnimations,
            includeTrajectories: exportOptions.includeTrajectories,
            minify: false
          });
          break;
        default:
          throw new Error('Formato não suportado');
      }

      if (result.success) {
        exportEngine.downloadResult(result);
        toast({
          title: "Exportação concluída",
          description: `Drill exportado como ${selectedFormat.toUpperCase()} com sucesso.`,
        });
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (error) {
      toast({
        title: "Erro na exportação",
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: "destructive"
      });
    } finally {
      setShowExportDialog(false);
    }
  }, [drill, selectedFormat, exportOptions, exportEngine, pdfExport, imageExport, videoExport, dataExport, toast]);

  // Handle batch export
  const handleBatchExport = useCallback(async () => {
    if (!drills.length) return;

    try {
      const results = await exportEngine.exportMultiple(drills, selectedFormat, {
        quality: exportOptions.quality as any,
        includeMetadata: true
      });

      const successCount = results.filter(r => r.success).length;
      
      // Download successful exports
      results.forEach(result => {
        if (result.success) {
          exportEngine.downloadResult(result);
        }
      });

      toast({
        title: "Exportação em lote concluída",
        description: `${successCount}/${drills.length} drills exportados com sucesso.`,
      });
    } catch (error) {
      toast({
        title: "Erro na exportação em lote",
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: "destructive"
      });
    } finally {
      setShowExportDialog(false);
    }
  }, [drills, selectedFormat, exportOptions, exportEngine, toast]);

  // Queue export for background processing
  const handleQueueExport = useCallback(() => {
    if (!drill) return;

    const options = {
      format: selectedFormat,
      quality: exportOptions.quality as any,
      includeMetadata: true
    } as any;

    const jobId = exportEngine.queueExport(drill, options, 'normal');
    
    // Track progress
    const jobStatus: ExportJobStatus = {
      id: jobId,
      name: drill.name,
      format: selectedFormat,
      progress: {
        stage: 'preparing',
        progress: 0,
        message: 'Preparando exportação...'
      }
    };

    setActiveExports(prev => new Map(prev.set(jobId, jobStatus)));

    exportEngine.onProgress(jobId, (progress) => {
      setActiveExports(prev => {
        const updated = new Map(prev);
        const status = updated.get(jobId);
        if (status) {
          status.progress = progress;
          updated.set(jobId, status);
        }
        return updated;
      });
    });

    toast({
      title: "Exportação adicionada à fila",
      description: "A exportação será processada em segundo plano.",
    });

    setShowExportDialog(false);
  }, [drill, selectedFormat, exportOptions, exportEngine, toast]);

  // Remove completed export from tracking
  const removeExport = useCallback((jobId: string) => {
    setActiveExports(prev => {
      const updated = new Map(prev);
      updated.delete(jobId);
      return updated;
    });
    exportEngine.offProgress(jobId);
  }, [exportEngine]);

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'json': return <Database className="h-4 w-4" />;
      default: return <Download className="h-4 w-4" />;
    }
  };

  const getProgressColor = (stage: string) => {
    switch (stage) {
      case 'complete': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'processing': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  // Se usado como componente embarcado, renderizar apenas o conteúdo do modal
  if (embedded) {
    return (
      <div className="space-y-4">
        <div className="space-y-4">
          {/* Format Selection */}
          <div>
            <Label className="text-xs">Formato</Label>
            <Select value={selectedFormat} onValueChange={(value: any) => setSelectedFormat(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="image">Imagem</SelectItem>
                <SelectItem value="video">Vídeo</SelectItem>
                <SelectItem value="json">Dados JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Quality */}
          <div>
            <Label className="text-xs">Qualidade</Label>
            <Select value={exportOptions.quality} onValueChange={(value) => 
              setExportOptions(prev => ({ ...prev, quality: value }))
            }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Baixa</SelectItem>
                <SelectItem value="medium">Média</SelectItem>
                <SelectItem value="high">Alta</SelectItem>
                <SelectItem value="ultra">Ultra</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Format-specific options */}
          {selectedFormat === 'pdf' && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeInstructions"
                  checked={exportOptions.includeInstructions}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeInstructions: !!checked }))
                  }
                />
                <Label htmlFor="includeInstructions" className="text-xs">
                  Incluir instruções
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeTimeline"
                  checked={exportOptions.includeTimeline}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeTimeline: !!checked }))
                  }
                />
                <Label htmlFor="includeTimeline" className="text-xs">
                  Incluir timeline
                </Label>
              </div>
            </div>
          )}

          {selectedFormat === 'json' && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeAnimations"
                  checked={exportOptions.includeAnimations}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeAnimations: !!checked }))
                  }
                />
                <Label htmlFor="includeAnimations" className="text-xs">
                  Incluir animações
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeTrajectories"
                  checked={exportOptions.includeTrajectories}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeTrajectories: !!checked }))
                  }
                />
                <Label htmlFor="includeTrajectories" className="text-xs">
                  Incluir trajetórias
                </Label>
              </div>
            </div>
          )}

          {/* Watermark */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="watermark"
                checked={exportOptions.watermark}
                onCheckedChange={(checked) =>
                  setExportOptions(prev => ({ ...prev, watermark: !!checked }))
                }
              />
              <Label htmlFor="watermark" className="text-xs">
                Adicionar marca d'água
              </Label>
            </div>
            {exportOptions.watermark && (
              <Input
                placeholder="Texto da marca d'água"
                value={exportOptions.watermarkText}
                onChange={(e) =>
                  setExportOptions(prev => ({ ...prev, watermarkText: e.target.value }))
                }
                className="text-xs"
              />
            )}
          </div>

          {/* Batch Export */}
          {drills.length > 1 && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="batchExport"
                checked={batchExport}
                onCheckedChange={(checked) => setBatchExport(!!checked)}
              />
              <Label htmlFor="batchExport" className="text-xs">
                Exportar todos os drills ({drills.length})
              </Label>
            </div>
          )}

          {/* Export Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              onClick={batchExport ? handleBatchExport : handleExport}
              disabled={exportEngine.isExporting}
              className="flex-1"
            >
              {exportEngine.isExporting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Exportar
            </Button>
            <Button
              variant="secondary"
              onClick={handleQueueExport}
              disabled={!drill}
            >
              <Clock className="h-4 w-4 mr-2" />
              Fila
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Renderização normal (não embarcada) - removida para evitar aparecer na página
  return null;
}