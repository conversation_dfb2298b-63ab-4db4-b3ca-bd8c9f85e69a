
<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Game Day Nexus - Plataforma de Gerenciamento para Times Esportivos</title>
    <meta name="description" content="Sistema completo para gestão de times esportivos - jogadores, staff, treinamentos, partidas e análises." />
    <meta name="author" content="Game Day Nexus" />

    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Game Day Nexus - Plataforma de Gerenciamento para Times Esportivos" />
    <meta property="og:description" content="Sistema completo para gestão de times esportivos - jogadores, staff, treinamentos, partidas e análises." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/apple-touch-icon.svg" />
    <meta property="og:url" content="https://gamedaynexus.com" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Game Day Nexus - Plataforma de Gerenciamento para Times Esportivos" />
    <meta name="twitter:description" content="Sistema completo para gestão de times esportivos - jogadores, staff, treinamentos, partidas e análises." />
    <meta name="twitter:image" content="/apple-touch-icon.svg" />

    <!-- Fonte -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C6XNFXQXCC"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-C6XNFXQXCC');
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
