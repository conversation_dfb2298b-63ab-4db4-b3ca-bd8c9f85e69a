export interface HelpFeature {
  id: string;
  title: string;
  description: string;
  problem: string;
  functionalities: string[];
  type: 'Core' | 'Avançada';
  icon: string;
  category: string;
  tags: string[];
}

export interface HelpModule {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  features: HelpFeature[];
}

export const helpModules: HelpModule[] = [
  {
    id: 'athletes',
    name: 'Gestão de Atletas',
    description: 'Sistema completo para gerenciar jogadores, documentos e finanças',
    icon: '👥',
    color: 'from-blue-500 to-blue-600',
    features: [
      {
        id: 'athlete-registration',
        title: 'Cadastro e Perfil de Atletas',
        description: 'Sistema completo de cadastro de jogadores com dados pessoais, documentos e fotos',
        problem: 'Centraliza todas as informações dos atletas em um local seguro e organizado',
        functionalities: [
          'Cadastro completo com dados pessoais, contatos e endereços',
          'Gestão de status (ativo, inativo, emprestado, transferido)',
          'Sistema de categorias e mapeamento por temporada',
          'Controle de contratos com datas de vencimento e alertas',
          'Gestão de números de camisa e posições',
          'Upload de fotos e documentos'
        ],
        type: 'Core',
        icon: '📝',
        category: 'Gestão de Atletas',
        tags: ['cadastro', 'perfil', 'documentos', 'contratos']
      },
      {
        id: 'athlete-documents',
        title: 'Documentação de Atletas',
        description: 'Sistema de gestão documental para atletas',
        problem: 'Organiza e controla a documentação obrigatória dos atletas',
        functionalities: [
          'Upload e gestão de documentos (RG, CPF, certidão, etc.)',
          'Sistema de verificação e aprovação de documentos',
          'Assinatura digital para documentos',
          'Controle de documentos pendentes',
          'Alertas de vencimento de documentos'
        ],
        type: 'Core',
        icon: '📄',
        category: 'Gestão de Atletas',
        tags: ['documentos', 'verificação', 'assinatura digital']
      },
      {
        id: 'athlete-finances',
        title: 'Finanças de Atletas',
        description: 'Controle financeiro individual dos atletas',
        problem: 'Gerencia aspectos financeiros relacionados aos atletas',
        functionalities: [
          'Controle de salários e bonificações',
          'Sistema de vales e adiantamentos salariais',
          'Gestão de contas bancárias e chaves PIX',
          'Relatórios financeiros individuais',
          'Controle de pagamentos e recebimentos'
        ],
        type: 'Core',
        icon: '💰',
        category: 'Gestão de Atletas',
        tags: ['finanças', 'salários', 'PIX', 'relatórios']
      },
      {
        id: 'athlete-discipline',
        title: 'Ocorrências e Disciplina',
        description: 'Sistema de controle disciplinar',
        problem: 'Registra e controla questões disciplinares dos atletas',
        functionalities: [
          'Registro de ocorrências disciplinares',
          'Sistema de punições e suspensões',
          'Controle de cartões por competição',
          'Histórico disciplinar completo',
          'Relatórios disciplinares'
        ],
        type: 'Core',
        icon: '⚠️',
        category: 'Gestão de Atletas',
        tags: ['disciplina', 'cartões', 'suspensões', 'ocorrências']
      },
      {
        id: 'athlete-suspensions',
        title: 'Sistema de Suspensões',
        description: 'Controle automático de suspensões por cartões',
        problem: 'Evita escalação de jogadores suspensos',
        functionalities: [
          'Cálculo automático de suspensões por cartões',
          'Alertas visuais para jogadores suspensos',
          'Controle por competição',
          'Histórico de suspensões'
        ],
        type: 'Core',
        icon: '🚫',
        category: 'Gestão de Atletas',
        tags: ['suspensões', 'cartões', 'alertas', 'competições']
      },
      {
        id: 'athlete-evaluation',
        title: 'Avaliação de Atletas',
        description: 'Sistema de pré-cadastro para novos atletas',
        problem: 'Organiza o processo de avaliação de novos talentos',
        functionalities: [
          'Convites para avaliação com links públicos',
          'Controle de status de avaliação (pendente, aprovado, rejeitado)',
          'Formulários de avaliação personalizáveis',
          'Dashboard de estatísticas de avaliações',
          'Sistema de pagamento para avaliações'
        ],
        type: 'Avançada',
        icon: '⭐',
        category: 'Gestão de Atletas',
        tags: ['avaliação', 'talentos', 'formulários', 'pagamento']
      },
      {
        id: 'athlete-statistics',
        title: 'Estatísticas de Atletas',
        description: 'Controle estatístico de desempenho',
        problem: 'Acompanha o desempenho individual dos atletas',
        functionalities: [
          'Controle de estatísticas por partida (gols, assistências, cartões)',
          'Sincronização automática de dados estatísticos',
          'Relatórios de desempenho por temporada',
          'Análise comparativa entre atletas',
          'Gráficos de evolução'
        ],
        type: 'Avançada',
        icon: '📊',
        category: 'Gestão de Atletas',
        tags: ['estatísticas', 'desempenho', 'gráficos', 'análise']
      }
    ]
  },
  {
    id: 'monthly-fees',
    name: 'Mensalidades',
    description: 'Sistema completo de cobrança e gestão de mensalidades',
    icon: '💳',
    color: 'from-green-500 to-green-600',
    features: [
      {
        id: 'fee-configuration',
        title: 'Configuração de Mensalidades',
        description: 'Sistema de configuração de mensalidades por categoria',
        problem: 'Automatiza a cobrança de mensalidades dos atletas',
        functionalities: [
          'Configuração de valores por categoria',
          'Definição de dias de vencimento',
          'Configuração de multas por atraso',
          'Sistema de descontos para pagamento antecipado',
          'Chaves PIX personalizadas por configuração'
        ],
        type: 'Core',
        icon: '⚙️',
        category: 'Mensalidades',
        tags: ['configuração', 'valores', 'vencimento', 'PIX']
      },
      {
        id: 'automatic-generation',
        title: 'Geração Automática de Mensalidades',
        description: 'Criação automática de cobranças mensais',
        problem: 'Elimina trabalho manual de criação de cobranças',
        functionalities: [
          'Geração automática por mês/ano',
          'Aplicação de regras de desconto e multa',
          'Cálculo automático de valores finais',
          'Integração com sistema de cobrança PIX'
        ],
        type: 'Core',
        icon: '🔄',
        category: 'Mensalidades',
        tags: ['automático', 'geração', 'desconto', 'multa']
      },
      {
        id: 'athlete-portal',
        title: 'Portal do Atleta para Mensalidades',
        description: 'Interface pública para pagamento de mensalidades',
        problem: 'Facilita o pagamento pelos atletas e responsáveis',
        functionalities: [
          'Acesso público com link personalizado',
          'Visualização de mensalidades pendentes',
          'Geração de QR Code PIX para pagamento',
          'Histórico de pagamentos',
          'Upload de comprovantes'
        ],
        type: 'Avançada',
        icon: '🌐',
        category: 'Mensalidades',
        tags: ['portal', 'público', 'QR Code', 'comprovantes']
      }
    ]
  },
  {
    id: 'matches',
    name: 'Partidas e Competições',
    description: 'Gestão completa de jogos, escalações e eventos em tempo real',
    icon: '⚽',
    color: 'from-red-500 to-red-600',
    features: [
      {
        id: 'match-management',
        title: 'Gestão de Partidas',
        description: 'Sistema completo de gestão de jogos',
        problem: 'Organiza todas as informações relacionadas às partidas',
        functionalities: [
          'Cadastro de partidas com data, horário e local',
          'Controle de adversários e competições',
          'Gestão de tipo de jogo (casa/fora)',
          'Sistema de ida e volta',
          'Análise de adversários'
        ],
        type: 'Core',
        icon: '📅',
        category: 'Partidas e Competições',
        tags: ['partidas', 'adversários', 'competições', 'análise']
      },
      {
        id: 'tactical-lineup',
        title: 'Escalação Tática',
        description: 'Editor visual de escalação com campo interativo',
        problem: 'Facilita a criação e visualização de escalações',
        functionalities: [
          'Editor visual com campo de futebol',
          'Múltiplas formações táticas (4-4-2, 4-3-3, etc.)',
          'Controle de posições específicas por jogador',
          'Sistema de substituições durante a partida',
          'Drag and drop para posicionamento'
        ],
        type: 'Core',
        icon: '🎯',
        category: 'Partidas e Competições',
        tags: ['escalação', 'tática', 'formação', 'substituições']
      },
      {
        id: 'real-time-events',
        title: 'Eventos de Partida em Tempo Real',
        description: 'Sistema de acompanhamento ao vivo das partidas',
        problem: 'Registra eventos da partida em tempo real',
        functionalities: [
          'Cronômetro integrado com controles de pausa/retomada',
          'Registro de gols, cartões e substituições',
          'Controle de placar em tempo real',
          'Anotações e observações da partida',
          'Sincronização automática de dados'
        ],
        type: 'Core',
        icon: '⏱️',
        category: 'Partidas e Competições',
        tags: ['tempo real', 'cronômetro', 'gols', 'cartões']
      },
      {
        id: 'convocation-system',
        title: 'Sistema de Convocação',
        description: 'Criação de convocações oficiais',
        problem: 'Organiza e formaliza as convocações',
        functionalities: [
          'Criação de convocações por categoria',
          'Design personalizado com logo do clube',
          'Geração automática de PDFs',
          'Controle de alojamentos para convocações',
          'Sistema de papéis (jogadores, staff, dirigentes)',
          'Upload de imagens personalizadas'
        ],
        type: 'Avançada',
        icon: '📋',
        category: 'Partidas e Competições',
        tags: ['convocação', 'PDF', 'alojamentos', 'staff']
      }
    ]
  },
  {
    id: 'training',
    name: 'Treinamentos',
    description: 'Planejamento e gestão completa de treinamentos',
    icon: '🏃',
    color: 'from-purple-500 to-purple-600',
    features: [
      {
        id: 'training-planning',
        title: 'Planejamento de Treinos',
        description: 'Sistema de agendamento e organização de treinamentos',
        problem: 'Organiza a rotina de treinamentos do clube',
        functionalities: [
          'Agenda de treinamentos por categoria',
          'Controle de locais de treino',
          'Gestão de objetivos e metas físicas',
          'Sistema de exercícios e atividades',
          'Controle de duração e intensidade'
        ],
        type: 'Core',
        icon: '📋',
        category: 'Treinamentos',
        tags: ['planejamento', 'agenda', 'objetivos', 'exercícios']
      },
      {
        id: 'interactive-editor',
        title: 'Editor Interativo de Treinos',
        description: 'Ferramenta visual para criação de exercícios',
        problem: 'Facilita a criação e comunicação de exercícios',
        functionalities: [
          'Campo visual para criação de exercícios',
          'Sistema de desenho com cones, setas e anotações',
          'Biblioteca de exercícios pré-definidos',
          'Sequenciador de atividades (drill sequencer)',
          'Animações e trajetórias de movimento',
          'Exportação de exercícios'
        ],
        type: 'Avançada',
        icon: '🎨',
        category: 'Treinamentos',
        tags: ['editor', 'visual', 'exercícios', 'animações']
      },
      {
        id: 'attendance-control',
        title: 'Controle de Presença',
        description: 'Sistema de chamada digital',
        problem: 'Controla a frequência dos atletas nos treinos',
        functionalities: [
          'Lista de presença digital',
          'Controle de faltas e justificativas',
          'Relatórios de frequência por atleta',
          'Estatísticas de participação',
          'Alertas de ausências frequentes'
        ],
        type: 'Core',
        icon: '✅',
        category: 'Treinamentos',
        tags: ['presença', 'frequência', 'faltas', 'relatórios']
      }
    ]
  },
  {
    id: 'medical',
    name: 'Módulo Médico',
    description: 'Sistema completo de saúde e acompanhamento médico',
    icon: '🏥',
    color: 'from-emerald-500 to-emerald-600',
    features: [
      {
        id: 'medical-professionals',
        title: 'Profissionais Médicos',
        description: 'Cadastro e gestão de profissionais de saúde',
        problem: 'Organiza a equipe médica do clube',
        functionalities: [
          'Cadastro de médicos, fisioterapeutas e massagistas',
          'Controle de credenciais e certificados',
          'Gestão de disponibilidade de horários',
          'Sistema de contas para profissionais externos',
          'Controle financeiro de profissionais'
        ],
        type: 'Core',
        icon: '👨‍⚕️',
        category: 'Módulo Médico',
        tags: ['médicos', 'fisioterapeutas', 'credenciais', 'horários']
      },
      {
        id: 'medical-scheduling',
        title: 'Agendamento Médico',
        description: 'Sistema de marcação de consultas',
        problem: 'Organiza a agenda médica do clube',
        functionalities: [
          'Sistema de agendas por profissional',
          'Marcação de consultas e exames',
          'Controle de disponibilidade',
          'Notificações automáticas',
          'Reagendamento de consultas'
        ],
        type: 'Core',
        icon: '📅',
        category: 'Módulo Médico',
        tags: ['agendamento', 'consultas', 'exames', 'notificações']
      },
      {
        id: 'medical-records',
        title: 'Prontuários Médicos',
        description: 'Histórico médico completo dos atletas',
        problem: 'Centraliza informações médicas para melhor atendimento',
        functionalities: [
          'Histórico médico completo por atleta',
          'Registro de consultas e diagnósticos',
          'Controle de medicamentos e prescrições',
          'Sistema de evolução de tratamentos',
          'Assinatura digital em prontuários',
          'Relatórios médicos personalizados'
        ],
        type: 'Core',
        icon: '📋',
        category: 'Módulo Médico',
        tags: ['prontuários', 'diagnósticos', 'medicamentos', 'assinatura digital']
      }
    ]
  },
  {
    id: 'financial',
    name: 'Módulo Financeiro',
    description: 'Controle financeiro completo do clube',
    icon: '💰',
    color: 'from-yellow-500 to-yellow-600',
    features: [
      {
        id: 'transaction-control',
        title: 'Controle de Transações',
        description: 'Sistema de controle financeiro geral',
        problem: 'Centraliza todas as movimentações financeiras',
        functionalities: [
          'Registro de receitas e despesas',
          'Categorização financeira',
          'Upload de comprovantes',
          'Controle de contas a pagar e receber',
          'Conciliação bancária'
        ],
        type: 'Core',
        icon: '💳',
        category: 'Módulo Financeiro',
        tags: ['transações', 'receitas', 'despesas', 'comprovantes']
      },
      {
        id: 'financial-reports',
        title: 'Relatórios Financeiros',
        description: 'Geração de relatórios financeiros',
        problem: 'Fornece visão financeira completa do clube',
        functionalities: [
          'Fluxo de caixa',
          'Demonstrativo de resultados',
          'Relatórios por categoria',
          'Análise de tendências',
          'Comparativos entre períodos'
        ],
        type: 'Core',
        icon: '📊',
        category: 'Módulo Financeiro',
        tags: ['relatórios', 'fluxo de caixa', 'demonstrativo', 'análise']
      },
      {
        id: 'pix-integration',
        title: 'Sistema de Cobrança PIX',
        description: 'Sistema integrado de cobrança PIX',
        problem: 'Facilita recebimentos e cobranças',
        functionalities: [
          'Geração de cobranças PIX',
          'QR Codes para pagamento',
          'Controle de inadimplência',
          'Notificações automáticas',
          'Integração com bancos'
        ],
        type: 'Avançada',
        icon: '📱',
        category: 'Módulo Financeiro',
        tags: ['PIX', 'QR Code', 'cobrança', 'inadimplência']
      }
    ]
  },
  {
    id: 'inventory',
    name: 'Estoque e Inventário',
    description: 'Gestão completa de materiais e equipamentos',
    icon: '📦',
    color: 'from-orange-500 to-orange-600',
    features: [
      {
        id: 'product-control',
        title: 'Controle de Produtos',
        description: 'Sistema de gestão de materiais',
        problem: 'Organiza e controla todos os materiais do clube',
        functionalities: [
          'Cadastro de materiais esportivos',
          'Controle de quantidades',
          'Alertas de estoque baixo',
          'Gestão por departamentos',
          'Upload de imagens de produtos'
        ],
        type: 'Core',
        icon: '📋',
        category: 'Estoque e Inventário',
        tags: ['materiais', 'quantidades', 'alertas', 'departamentos']
      },
      {
        id: 'stock-movement',
        title: 'Movimentação de Estoque',
        description: 'Controle de entradas e saídas',
        problem: 'Rastreia todas as movimentações de materiais',
        functionalities: [
          'Entradas e saídas de materiais',
          'Histórico de transações',
          'Controle de responsáveis',
          'Relatórios de movimentação',
          'Justificativas para movimentações'
        ],
        type: 'Core',
        icon: '🔄',
        category: 'Estoque e Inventário',
        tags: ['movimentação', 'entradas', 'saídas', 'histórico']
      }
    ]
  }
];

export const getAllFeatures = (): HelpFeature[] => {
  return helpModules.flatMap(module => module.features);
};

export const searchFeatures = (query: string, type?: 'Core' | 'Avançada'): HelpFeature[] => {
  const allFeatures = getAllFeatures();
  const filtered = allFeatures.filter(feature => {
    const searchTerm = query.toLowerCase();
    const matchesQuery = query === '' ||
      feature.title.toLowerCase().includes(searchTerm) ||
      feature.description.toLowerCase().includes(searchTerm) ||
      feature.problem.toLowerCase().includes(searchTerm) ||
      feature.category.toLowerCase().includes(searchTerm) ||
      feature.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      feature.functionalities.some(func => func.toLowerCase().includes(searchTerm));

    const matchesType = !type || feature.type === type;

    return matchesQuery && matchesType;
  });

  // Ordenar por relevância (título primeiro, depois descrição, depois tags)
  return filtered.sort((a, b) => {
    const searchTerm = query.toLowerCase();
    if (query === '') return 0;

    const aTitle = a.title.toLowerCase().includes(searchTerm);
    const bTitle = b.title.toLowerCase().includes(searchTerm);

    if (aTitle && !bTitle) return -1;
    if (!aTitle && bTitle) return 1;

    const aDesc = a.description.toLowerCase().includes(searchTerm);
    const bDesc = b.description.toLowerCase().includes(searchTerm);

    if (aDesc && !bDesc) return -1;
    if (!aDesc && bDesc) return 1;

    return 0;
  });
};

export const getModuleById = (id: string): HelpModule | undefined => {
  return helpModules.find(module => module.id === id);
};

export const getFeatureById = (id: string): HelpFeature | undefined => {
  return getAllFeatures().find(feature => feature.id === id);
};
