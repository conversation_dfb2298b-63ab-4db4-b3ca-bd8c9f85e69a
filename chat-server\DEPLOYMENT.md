# Deployment do Chat Server na Vercel

## Configuração das Variáveis de Ambiente

Na Vercel, configure as seguintes variáveis de ambiente:

### Obrigatórias
- `SUPABASE_URL`: URL do seu projeto Supabase
- `SUPABASE_SERVICE_KEY`: Service key do Supabase (com permissões de admin)
- `ALLOWED_ORIGINS`: Lista de origens permitidas separadas por vírgula

### Exemplo de ALLOWED_ORIGINS
```
https://www.gamedaynexus.com.br,https://gamedaynexus.com.br,http://localhost:5173,http://localhost:3000
```

## Configuração do Frontend

No frontend, certifique-se de que a variável `VITE_SOCKET_URL` aponta para o seu chat-server na Vercel:

```env
VITE_SOCKET_URL=https://chat-server-iota-inky.vercel.app
```

## Endpoints Disponíveis

- `/api/health` - Health check e diagnóstico
- `/api/test` - Endpoint de teste simples
- `/api/socket` - Endpoint do Socket.IO

## Testando o Deployment

1. Primeiro teste o health check:
   ```
   https://chat-server-iota-inky.vercel.app/api/health
   ```

2. Teste o endpoint simples:
   ```
   https://chat-server-iota-inky.vercel.app/api/test
   ```

3. Teste a conexão Socket.IO:
   ```
   https://chat-server-iota-inky.vercel.app/api/socket
   ```

## Troubleshooting

### Erro de Build "No Output Directory"
- Certifique-se de que existe um diretório `public/` com pelo menos um arquivo
- O `package.json` deve ter um script de build válido

### Erro de CORS
- Verifique se o domínio do frontend está listado em `ALLOWED_ORIGINS`
- Certifique-se de incluir tanto `https://www.seudominio.com` quanto `https://seudominio.com`
- Para desenvolvimento local, inclua `http://localhost:5173` e `http://localhost:3000`

### Socket.IO não conecta (404 Error)
- Verifique se o path está correto: `/api/socket`
- Certifique-se de que está usando `transports: ['polling']` no frontend para compatibilidade com Vercel
- Teste primeiro os endpoints `/api/health` e `/api/test` para verificar se a API está funcionando
- Verifique os logs da Vercel para erros de inicialização

### Problemas de Autenticação
- Verifique se `SUPABASE_SERVICE_KEY` está configurada corretamente
- Certifique-se de que o token JWT está sendo enviado corretamente do frontend

### Debugging Steps
1. Acesse `/api/health` para verificar se as variáveis de ambiente estão configuradas
2. Acesse `/api/test` para verificar se o CORS está funcionando
3. Verifique os logs da Vercel para erros específicos
4. Teste a conexão Socket.IO com ferramentas como Postman ou curl