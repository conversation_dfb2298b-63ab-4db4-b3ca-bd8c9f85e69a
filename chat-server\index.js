require('dotenv').config();
const { Server } = require('socket.io');
const { createClient } = require('@supabase/supabase-js');

// Configuração Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

const allowedOrigins = process.env.ALLOWED_ORIGINS 
  ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim())
  : [
      'http://localhost:5173',
      'http://localhost:3000',
      'https://www.gamedaynexus.com.br'
    ];

const io = new Server({
  path: '/api/socket',
  cors: {
    origin: allowedOrigins,
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['polling', 'websocket'],
  allowEIO3: true
});

// Cache de usuários conectados
const connectedUsers = new Map();

// Middleware de autenticação
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    const clubId = socket.handshake.auth.clubId;
    if (!token) {
      console.warn('Auth handshake sem token');
      return next(new Error('No token provided'));
    }
    if (!clubId) {
      console.warn('Auth handshake sem clubId');
      return next(new Error('No club provided'));
    }

    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return next(new Error('Authentication failed'));
    }

    socket.userId = user.id;
    socket.userEmail = user.email;
    
    // Obter nome completo do perfil ou usar email como fallback
    try {
      const { data: profile, error: profileErr } = await supabase
        .from('users')
        .select('full_name')
        .eq('id', user.id)
        .maybeSingle();

      if (profileErr) {
        console.warn('Erro buscando perfil do usuário:', profileErr.message);
      }
      socket.userName = profile?.full_name || user.email;
    } catch (err) {
      console.warn('Não foi possível obter nome do usuário:', err);
      socket.userName = user.email;
    }

    socket.clubId = Number(clubId);

    try {
      const { data: clubInfo, error: clubErr } = await supabase
        .from('club_info')
        .select('name')
        .eq('id', socket.clubId)
        .maybeSingle();

      if (clubErr) {
        console.warn('Erro buscando nome do clube:', clubErr.message);
      }
      socket.clubName = clubInfo?.name;
    } catch (err) {
      console.warn('Não foi possível obter informações do clube:', err);
    }

    next();
  } catch (error) {
    console.error('Auth error:', error);
    next(new Error('Authentication failed'));
  }
});

io.on('connection', async (socket) => {
  console.log(`🟢 ${socket.userName} conectado ao clube ${socket.clubName || socket.clubId}`);
  
  // Adicionar ao cache de usuários conectados
  connectedUsers.set(socket.userId, {
    id: socket.userId,
    email: socket.userEmail,
    name: socket.userName,
    clubId: socket.clubId,
    socketId: socket.id,
    status: 'online'
  });

  try {
    // 1. Buscar e entrar nas salas do usuário
    const { data: userRooms } = await supabase
      .from('chat_room_participants')
      .select(`
        room_id,
        chat_rooms(id, name, is_general, club_id)
      `)
      .eq('user_id', socket.userId);

    // Entrar nas salas
    userRooms?.forEach(({ room_id, chat_rooms }) => {
      if (chat_rooms?.club_id === socket.clubId) {
        socket.join(`room:${room_id}`);
        console.log(`📝 ${socket.userName} entrou na sala: ${chat_rooms.name}`);
      }
    });

    // 2. Entrar na sala geral do clube (para presença)
    socket.join(`club:${socket.clubId}`);

    // 3. Atualizar presença no banco
    await supabase
      .from('user_presence')
      .upsert({
        user_id: socket.userId,
        club_id: socket.clubId,
        status: 'online',
        last_seen: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    // 4. Notificar outros usuários que está online
    socket.to(`club:${socket.clubId}`).emit('user-presence-update', {
      user_id: socket.userId,
      email: socket.userEmail,
      name: socket.userName,
      status: 'online'
    });

    // 5. Enviar lista de usuários online para o novo usuário
    const onlineUsers = Array.from(connectedUsers.values())
      .filter(user => user.clubId === socket.clubId);
    
    socket.emit('online-users-list', onlineUsers);

  } catch (error) {
    console.error('Erro ao conectar usuário:', error);
  }

  // EVENTO: Enviar mensagem
  socket.on('send-message', async (data) => {
    try {
      console.log(`💬 ${socket.userName} enviando mensagem para sala ${data.room_id}`);
      
      // Verificar se usuário está na sala
      const { data: participant } = await supabase
        .from('chat_room_participants')
        .select('*')
        .eq('room_id', data.room_id)
        .eq('user_id', socket.userId)
        .single();

      if (!participant) {
        socket.emit('error', { message: 'Você não está nesta sala' });
        return;
      }

      // Salvar mensagem no banco
      const { data: message, error } = await supabase
        .from('chat_messages')
        .insert({
          room_id: data.room_id,
          user_id: socket.userId,
          content: data.content,
          reply_to: data.reply_to || null,
          message_type: data.message_type || 'text'
        })
        .select(`
          *,
          reply_message:chat_messages!reply_to(
            id,
            content,
            user_id
          )
        `)
        .single();

      if (error) {
        console.error('Erro ao salvar mensagem:', error);
        socket.emit('error', { message: 'Erro ao enviar mensagem' });
        return;
      }

      // Adicionar dados do usuário à mensagem
      let replyData;
      if (message.reply_message) {
        try {
          const { data: replyUser, error: replyErr } = await supabase
            .from('users')
            .select('full_name, email')
            .eq('id', message.reply_message.user_id)
            .maybeSingle();

          if (replyErr) {
            console.warn('Erro buscando usuário da mensagem respondida:', replyErr.message);
          }

          replyData = {
            ...message.reply_message,
            user: {
              id: message.reply_message.user_id,
              name: replyUser?.full_name || replyUser?.email || ''
            }
          };
        } catch (err) {
          console.warn('Não foi possível obter dados do usuário da resposta:', err);
          replyData = {
            ...message.reply_message,
            user: { id: message.reply_message.user_id }
          };
        }
      }

      const messageWithUser = {
        ...message,
        user: {
          id: socket.userId,
          email: socket.userEmail,
          name: socket.userName
        },
        reply_message: replyData
      };

      // Emitir para todos na sala
      io.to(`room:${data.room_id}`).emit('new-message', messageWithUser);
      
      console.log(`✅ Mensagem enviada para sala ${data.room_id}`);

    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      socket.emit('error', { message: 'Erro interno do servidor' });
    }
  });

  // EVENTO: Criar sala
  socket.on('create-room', async (data) => {
    try {
      console.log(`🏠 ${socket.userName} criando sala: ${data.name}`);

      // Criar sala no banco
      const { data: room, error } = await supabase
        .from('chat_rooms')
        .insert({
          club_id: socket.clubId,
          name: data.name,
          description: data.description,
          created_by: socket.userId,
          is_general: false
        })
        .select()
        .single();

      if (error) {
        socket.emit('error', { message: 'Erro ao criar sala', details: error.message });
        return;
      }

      // Adicionar criador como participante
      await supabase
        .from('chat_room_participants')
        .insert({
          room_id: room.id,
          user_id: socket.userId,
          is_admin: true
        });

      // Entrar na sala
      socket.join(`room:${room.id}`);

      // Notificar criação da sala para o clube
      socket.to(`club:${socket.clubId}`).emit('room-created', room);
      socket.emit('room-created', room);

      console.log(`✅ Sala ${data.name} criada com sucesso`);

    } catch (error) {
      console.error('Erro ao criar sala:', error);
      socket.emit('error', { message: 'Erro ao criar sala', details: error.message });
    }
  });

  // EVENTO: Entrar em sala
  socket.on('join-room', async (roomId) => {
    try {
      // Verificar se a sala existe e é do mesmo clube
      const { data: room } = await supabase
        .from('chat_rooms')
        .select('*')
        .eq('id', roomId)
        .eq('club_id', socket.clubId)
        .single();

      if (!room) {
        socket.emit('error', { message: 'Sala não encontrada' });
        return;
      }

      // Adicionar como participante
      await supabase
        .from('chat_room_participants')
        .insert({
          room_id: roomId,
          user_id: socket.userId
        })
        .onConflict('room_id,user_id')
        .ignoreDuplicates();

      // Entrar na sala
      socket.join(`room:${roomId}`);

      // Notificar entrada
      socket.to(`room:${roomId}`).emit('user-joined-room', {
        user_id: socket.userId,
        name: socket.userName,
        room_id: roomId
      });

      socket.emit('joined-room', { room_id: roomId });

    } catch (error) {
      console.error('Erro ao entrar na sala:', error);
      socket.emit('error', { message: 'Erro ao entrar na sala' });
    }
  });

  // EVENTO: Criar chat 1x1
  socket.on('create-direct-chat', async (targetUserId) => {
    try {
      console.log(`💬 ${socket.userName} iniciando chat 1x1 com ${targetUserId}`);

      // Verificar se o usuário alvo está no mesmo clube
      const { data: membership, error: membershipErr } = await supabase
        .from('club_members')
        .select('*')
        .eq('user_id', targetUserId)
        .eq('club_id', socket.clubId)
        .maybeSingle();

      const targetOnline = connectedUsers.get(targetUserId);

      if (membershipErr) {
        console.warn('Erro verificando membro do clube:', membershipErr.message);
      }

      if (!membership && (!targetOnline || targetOnline.clubId !== socket.clubId)) {
        socket.emit('error', { message: 'Usuário não encontrado no clube' });
        return;
      }

      // Buscar informações do usuário alvo
      let { data: targetProfile } = await supabase
        .from('users')
        .select('id, email, full_name')
        .eq('id', targetUserId)
        .maybeSingle();

      if (!targetProfile) {
        if (targetOnline) {
          targetProfile = {
            id: targetOnline.id,
            email: targetOnline.email,
            full_name: targetOnline.name
          };
        } else {
          try {
            const { data: { user: authUser }, error: authErr } = await supabase.auth.admin.getUserById(targetUserId);
            if (authErr || !authUser) {
              socket.emit('error', { message: 'Dados do usuário não encontrados' });
              return;
            }
            targetProfile = {
              id: authUser.id,
              email: authUser.email,
              full_name: authUser.user_metadata?.full_name || authUser.email
            };
          } catch (err) {
            socket.emit('error', { message: 'Dados do usuário não encontrados' });
            return;
          }
        }
      }

      // Criar nome da sala (ordenado para evitar duplicatas)
      const roomName = [socket.userId, targetUserId].sort().join('-');

      // Verificar se já existe
      const { data: existingRoom, error: existingRoomErr } = await supabase
        .from('chat_rooms')
        .select('*')
        .eq('club_id', socket.clubId)
        .eq('name', roomName)
        .maybeSingle();

      if (existingRoomErr) {
        console.warn('Erro buscando sala existente:', existingRoomErr.message);
      }

      let room = existingRoom;

      if (!room) {
        // Criar sala privada
        const { data: newRoom } = await supabase
          .from('chat_rooms')
          .insert({
            club_id: socket.clubId,
            name: roomName,
            description: `Chat entre ${socket.userName} e ${targetProfile.full_name}`,
            created_by: socket.userId,
            is_general: false
          })
          .select()
          .single();

        room = newRoom;

        // Adicionar ambos como participantes
        await supabase
          .from('chat_room_participants')
          .insert([
            { room_id: room.id, user_id: socket.userId },
            { room_id: room.id, user_id: targetUserId }
          ]);
      }

      // Entrar na sala
      socket.join(`room:${room.id}`);

      // Se o usuário alvo estiver online, adicionar à sala também
      const targetSocket = Array.from(io.sockets.sockets.values())
        .find(s => s.userId === targetUserId);
      
      if (targetSocket) {
        targetSocket.join(`room:${room.id}`);
        targetSocket.emit('new-direct-chat', {
          room,
          with_user: {
            id: socket.userId,
            name: socket.userName,
            email: socket.userEmail
          }
        });
      }

      socket.emit('direct-chat-created', {
        room,
        with_user: {
          id: targetUserId,
          name: targetProfile.full_name,
          email: targetProfile.email
        }
      });

    } catch (error) {
      console.error('Erro ao criar chat direto:', error);
      socket.emit('error', { message: 'Erro ao criar chat direto', details: error.message });
    }
  });

  // EVENTO: Marcar mensagens como lidas
  socket.on('mark-as-read', async (roomId) => {
    try {
      await supabase
        .from('chat_room_participants')
        .update({ last_read_at: new Date().toISOString() })
        .eq('room_id', roomId)
        .eq('user_id', socket.userId);

      socket.emit('marked-as-read', { room_id: roomId });
    } catch (error) {
      console.error('Erro ao marcar como lido:', error);
    }
  });

  // EVENTO: Desconexão
  socket.on('disconnect', async () => {
    try {
      console.log(`🔴 ${socket.userName} desconectado`);

      // Remover do cache
      connectedUsers.delete(socket.userId);

      // Atualizar presença no banco
      await supabase
        .from('user_presence')
        .update({
          status: 'offline',
          last_seen: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', socket.userId);

      // Notificar outros usuários
      socket.to(`club:${socket.clubId}`).emit('user-presence-update', {
        user_id: socket.userId,
        email: socket.userEmail,
        name: socket.userName,
        status: 'offline'
      });

    } catch (error) {
      console.error('Erro ao desconectar:', error);
    }
  });
});

// Para Vercel, exportar o handler
const SocketHandler = (req, res) => {
  console.log('SocketHandler chamado:', {
    method: req.method,
    origin: req.headers.origin,
    allowedOrigins
  });

  // Configurar CORS
  const origin = req.headers.origin;
  const isAllowedOrigin = allowedOrigins.includes(origin) || 
                         allowedOrigins.includes('*') ||
                         (!origin && req.headers.host); // Para requests do mesmo domínio

  if (origin && !isAllowedOrigin) {
    console.log('Origin não permitida:', origin);
    return res.status(403).json({ error: 'Origin not allowed' });
  }

  // Definir headers CORS
  if (origin && isAllowedOrigin) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else if (allowedOrigins.includes('*')) {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Responder a preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end(); 
  }

  // Inicializar Socket.IO se necessário
  if (!res.socket.server.io) {
    console.log('Iniciando Socket.IO na Vercel...');
    res.socket.server.io = io;
    io.attach(res.socket.server, { 
      path: '/api/socket',
      cors: {
        origin: allowedOrigins,
        methods: ['GET', 'POST'],
        credentials: true
      }
    });
  } else {
    console.log('Socket.IO já está rodando');
  }
  
  res.end();
};

// Para desenvolvimento local
if (process.env.NODE_ENV !== 'production') {
  const port = process.env.PORT || 3001;
  io.listen(port);
  console.log(`🚀 Socket.IO server rodando na porta ${port}`);
}

module.exports = SocketHandler;