// Brevo API configuration - Hardcoded for reliability
const BREVO_API_KEY = '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';
const SENDER_NAME = 'GameDayNexus';
const SENDER_EMAIL = '<EMAIL>';

// Log API key (first 10 characters only for security)
// console.log('Brevo API Key (primeiros 10 caracteres):', BREVO_API_KEY.substring(0, 10) + '...');

// Brevo API endpoint
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';

/**
 * Interface for email data
 */
export interface EmailData {
  to: string;
  subject: string;
  body: string;
}

/**
 * Generates a random password for new users
 * @param length Length of the password (default: 10)
 * @returns Random password
 */
export function generateRandomPassword(length = 10): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

/**
 * Sends an email using Brevo API
 * @param emailData Email data to send
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendEmailWithBrevo(emailData: EmailData): Promise<boolean> {
  try {
    // Log email sending attempt
    console.log('Tentando enviar email via Brevo API:', {
      to: emailData.to,
      subject: emailData.subject,
    });

    // Prepare request data for Brevo API
    const requestData = {
      sender: {
        name: SENDER_NAME,
        email: SENDER_EMAIL
      },
      to: [
        {
          email: emailData.to
        }
      ],
      subject: emailData.subject,
      htmlContent: emailData.body
    };

    // Log request data
    // console.log('Dados da requisição para API Brevo:', {
    //   url: BREVO_API_URL,
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'api-key': BREVO_API_KEY.substring(0, 10) + '...' // Mostrar apenas os primeiros 10 caracteres da chave
    //   },
    //   body: requestData
    // });

    // Send email using Brevo API
    const response = await fetch(BREVO_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': BREVO_API_KEY
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      // console.error('Resposta da API Brevo:', {
      //   status: response.status,
      //   statusText: response.statusText,
      //   data: errorData
      // });
      throw new Error(`Brevo API error: ${JSON.stringify(errorData)}`);
    }

    console.log('Email enviado com sucesso via Brevo API');
    return true;
  } catch (error) {
    console.error('Erro ao enviar email via Brevo API:', error);
    return false;
  }
}

/**
 * Sends a welcome email with login credentials
 * @param email Recipient email
 * @param name Recipient name
 * @param password Generated password
 * @param clubName Club name
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendWelcomeEmail(
  email: string,
  name: string,
  password: string,
  clubName: string
): Promise<boolean> {
  // Use environment variable for site URL instead of window.location.origin
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const loginUrl = `${SITE_URL}/login`;

  // console.log(`Sending welcome email with URL: ${loginUrl}`);

  const emailBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Bem-vindo ao ${clubName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 0; }
        .header { background-color: #4F46E5; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f1f1f1; }
        .credentials { background-color: #fff; padding: 15px; border-left: 4px solid #4F46E5; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Bem-vindo(a) ao ${clubName}</h1>
        </div>
        <div class="content">
          <p>Olá ${name},</p>
          <p>Sua conta foi criada com sucesso na plataforma de gerenciamento do clube.</p>
          <p>Aqui estão suas credenciais de acesso:</p>

          <div class="credentials">
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Senha temporária:</strong> ${password}</p>
          </div>

          <p>Por favor, faça login usando o link abaixo e altere sua senha na primeira vez que acessar o sistema:</p>

          <p style="text-align: center; margin: 30px 0;">
            <a href="${loginUrl}" class="button">Acessar o Sistema</a>
          </p>

          <p>Ou copie e cole o seguinte link no seu navegador:</p>
          <p>${loginUrl}</p>

          <p>Se você tiver alguma dúvida, entre em contato com o administrador do sistema.</p>
        </div>
        <div class="footer">
          <p>Atenciosamente,<br>Equipe ${clubName}</p>
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Bem-vindo(a) ao ${clubName} - Suas Credenciais de Acesso`,
    body: emailBody,
  });
}

/**
 * Sends an invitation email
 * @param email Recipient email
 * @param name Recipient name
 * @param token Invitation token
 * @param clubName Club name
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendInvitationEmail(
  email: string,
  name: string,
  token: string,
  clubName: string
): Promise<boolean> {
  // Use environment variable for site URL instead of window.location.origin
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const inviteUrl = `${SITE_URL}/accept-invitation?token=${token}`;

  // console.log(`Sending invitation email with URL: ${inviteUrl}`);

  const emailBody = `
    <h2>Olá ${name}!</h2>
    <p>Você foi convidado para acessar a plataforma de gerenciamento do clube <strong>${clubName}</strong>.</p>
    <p>Para aceitar o convite e criar sua conta, clique no link abaixo:</p>
    <p><a href="${inviteUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Aceitar Convite</a></p>
    <p>Ou copie e cole o seguinte link no seu navegador:</p>
    <p>${inviteUrl}</p>
    <p>Este convite expira em 7 dias.</p>
    <p>Atenciosamente,<br>Equipe ${clubName}</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Convite para acessar a plataforma do ${clubName}`,
    body: emailBody,
  });
}

/**
 * Sends a password reset email
 * @param email Recipient email
 * @param name Recipient name
 * @param token Reset token
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  token: string
): Promise<boolean> {
  // Use environment variable for site URL instead of window.location.origin
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const resetUrl = `${SITE_URL}/reset-password?token=${token}`;

  // console.log(`Sending password reset email with URL: ${resetUrl}`);

  const emailBody = `
    <h2>Olá ${name}!</h2>
    <p>Recebemos uma solicitação para redefinir sua senha.</p>
    <p>Para redefinir sua senha, clique no link abaixo:</p>
    <p><a href="${resetUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Redefinir Senha</a></p>
    <p>Ou copie e cole o seguinte link no seu navegador:</p>
    <p>${resetUrl}</p>
    <p>Este link expira em 1 hora.</p>
    <p>Se você não solicitou a redefinição de senha, ignore este email.</p>
    <p>Atenciosamente,<br>Equipe de Suporte</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: "Redefinição de Senha",
    body: emailBody,
  });
}

/**
 * Sends an email notifying the player that they have been released
 * from the club.
 * @param email Recipient email
 * @param name Player name
 * @param clubName Club name
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendReleaseEmail(
  email: string,
  name: string,
  clubName: string
): Promise<boolean> {
  const emailBody = `
    <h2>Olá ${name}!</h2>
    <p>Durante o período de avaliação em nosso clube, o atleta ${name} apresentou certa qualidade técnica, caracteristícas importantes no perfil de um jogador competitivo. Observamos empenho nas atividades propostas euma postura respeitosa com a comissão técnica e demais companheiros de equipe.</p>
    <p>Entretanto, após uma análise mais profunda e continua de seu desempenhos nos treinamentos, concluímos que, apesar de suas virtudes, o atleta não se enquadra nas necessidades técnicas e táticas especifícas que o clube busca suprir no momento. Nosso processo de avaliação é sempre pautado em critérios objetivos e alinhados ao planejamento estratégico da equipe.</p>
    <p>Por isso, optamos por não dar continuidade à permanência do atleta em nosso elenco. Reforçamos nosso agradecimento pelo comprometimento durante o período em que esteve conosco e desejamos sucesso em sua trajetória esportiva, certos de que poderá encontrar um ambiente que melhor aproveite seu potencial.</p>
    <p>Atleta está liberado do clube, caso queira mais detalhes da sua liberação, favor acessar app do clube na página "Meu Perfil", aba de "Avaliação" para detalhamento.</p>
    <p>Atenciosamente,<br>Equipe ${clubName}</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Dispensa do ${clubName}`,
    body: emailBody,
  });
}
/**
 * Sends an email notifying the player that they are being monitored
 * and will remain at the club for another week.
 * @param email Recipient email
 * @param name Player name
 * @param clubName Club name
 * @returns Promise that resolves to true if email was sent successfully
 */

export async function sendMonitoredEmail(
  email: string,
  name: string,
  clubName: string
): Promise<boolean> {
  const emailBody = `
    <h2>Olá ${name}!</h2>
    <p>O clube informa que a sua avaliação desta semana foi concluída. Após análise da comissão técnica, você permanecerá no clube por mais uma semana para continuidade do processo de avaliação.</p>
    <p>Durante este período, é importante manter o foco, dedicação e comprometimento nos treinamentos. Nossa equipe técnica continuará acompanhando seu desenvolvimento e desempenho.</p>
    <p>Para dúvidas ou esclarecimentos adicionais, procure a direção do clube. Você também pode acessar o app do clube na página "Meu Perfil", aba de "Avaliação" para mais detalhes sobre sua situação atual.</p>
    <p>Continue se dedicando e dando o seu melhor!</p>
    <p>Atenciosamente,<br>Equipe ${clubName}</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Avaliação Semanal - ${clubName}`,
    body: emailBody,
  });
}

/**
 
* Sends an email notifying the player that they have been approved
 * and are now part of the club's squad.
 * @param email Recipient email
 * @param name Player name
 * @param clubName Club name
 * @returns Promise that resolves to true if email was sent successfully
 */
export async function sendApprovedEmail(
  email: string,
  name: string,
  clubName: string
): Promise<boolean> {
  const emailBody = `
    <h2>🎉 Parabéns ${name}!</h2>
    <p><strong>É com grande satisfação que o ${clubName} informa que você agora faz parte oficial do nosso elenco!</strong></p>
    <p>Após cuidadosa avaliação da nossa comissão técnica, reconhecemos em você as qualidades, o comprometimento e o potencial que buscamos para fortalecer nossa equipe. Sua dedicação durante o período de avaliação foi exemplar e demonstrou que você tem tudo para contribuir significativamente com nossos objetivos.</p>
    <p>A partir de agora, você é oficialmente um atleta do ${clubName}! Estamos ansiosos para vê-lo em ação e confiantes de que esta parceria será muito produtiva e cheia de conquistas.</p>
    <p>Para maiores informações sobre os próximos passos, documentação necessária, cronograma de treinamentos e demais orientações, procure a direção do clube. Você também pode acessar o app do clube na página "Meu Perfil", aba de "Avaliação" para acompanhar sua situação.</p>
    <p><strong>Seja muito bem-vindo à família ${clubName}! Vamos juntos em busca de grandes vitórias!</strong></p>
    <p>Atenciosamente,<br>Equipe ${clubName}</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `🎉 Bem-vindo ao ${clubName}!`,
    body: emailBody,
  });
}