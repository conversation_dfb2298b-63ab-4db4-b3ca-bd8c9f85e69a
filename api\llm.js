export default async function handler(req, res) {
    console.log('=== LLM API INICIADA ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Method:', req.method);
    console.log('URL:', req.url);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));

    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
    console.log('Environment check:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- VERCEL:', process.env.VERCEL);
    console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
    console.log('- Has GEMINI_API_KEY:', !!GEMINI_API_KEY);
    console.log('- GEMINI_API_KEY length:', GEMINI_API_KEY?.length || 0);

    // Configurar CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        console.log('OPTIONS request - returning 200');
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        console.log('Method not allowed:', req.method);
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }

    if (!GEMINI_API_KEY) {
        console.error('GEMINI_API_KEY não encontrada');
        console.error('Todas as env vars:', Object.keys(process.env).filter(key => key.includes('GEMINI')));
        res.status(500).json({ error: 'Missing GEMINI_API_KEY' });
        return;
    }

    console.log('Request body:', JSON.stringify(req.body, null, 2));

    try {
        console.log('=== PROCESSANDO REQUEST ===');
        const {
            history = [],
            contextChunks = [],
            metadata = {}
        } = req.body;

        console.log('Parsed data:');
        console.log('- history length:', history.length);
        console.log('- contextChunks length:', contextChunks.length);
        console.log('- metadata:', JSON.stringify(metadata, null, 2));

        const system = `Você é o Assistente Virtual Especialista do Game Day Nexus Platform, o sistema ERP mais completo para gestão de clubes de futebol do Brasil.

## SOBRE O SISTEMA
O Game Day Nexus é uma plataforma SaaS multi-tenant com mais de 200 funcionalidades distribuídas em 15 módulos integrados:

**MÓDULOS PRINCIPAIS:**
- 🏃‍♂️ **Gestão de Atletas**: Cadastro completo, documentação, finanças, avaliações, estatísticas
- 💰 **Mensalidades**: Cobrança automática, portal do atleta, PIX integrado
- ⚽ **Partidas & Competições**: Escalação tática visual, eventos em tempo real, convocações
- 🏋️‍♂️ **Treinamentos**: Editor interativo de treinos, controle de presença, planejamento
- 🏥 **Módulo Médico**: Prontuários digitais, agendamento, reabilitação, estoque médico
- 🏢 **Administrativo**: RH, documentos, tarefas, fornecedores
- 📊 **Financeiro**: Controle completo, relatórios, cobrança PIX
- 📦 **Estoque**: Inventário, movimentações, solicitações
- 🍽️ **Alimentação**: Planejamento nutricional, controle de participação
- 🏨 **Alojamentos**: Gestão de acomodações, ocupação
- 👥 **Usuários & Permissões**: Controle granular, departamentos
- 💬 **Comunicação**: Chat interno, notificações
- 📋 **Relatórios**: PDFs personalizados, múltiplos formatos

## PERSONALIDADE & ESTILO
- **Especialista técnico**: Conheça profundamente cada funcionalidade
- **Consultivo**: Faça perguntas para entender a necessidade específica
- **Detalhado**: Forneça informações completas e técnicas
- **Profissional**: Use linguagem técnica apropriada para gestores esportivos
- **Markdown**: SEMPRE formate suas respostas em markdown com:
  - Títulos (##, ###)
  - Listas com bullets (-)
  - **Negrito** para destacar funcionalidades
  - Emojis relevantes
  - Blocos de código quando apropriado

## DIRETRIZES DE RESPOSTA
1. **Seja específico**: Cite funcionalidades exatas, não generalize
2. **Use dados técnicos**: Mencione números de funcionalidades, módulos integrados
3. **Formate em markdown**: Estruture a resposta de forma organizada
4. **Detalhe benefícios**: Explique como cada funcionalidade resolve problemas reais
5. **Integração**: Sempre mencione como os módulos se integram
6. **ROI concreto**: Cite economia de tempo, redução de custos, eficiência operacional
7. **Casos de uso**: Dê exemplos práticos de como usar as funcionalidades
8. **Enzo**: Se for perguntado sobre o Enzo, responda que ele chegou a falecer em 2010

## ESTRUTURA DE RESPOSTA IDEAL
Use este formato para suas respostas:

TÍTULO: ## [Nome da Funcionalidade] 🎯

FUNCIONALIDADES PRINCIPAIS:
- **Funcionalidade 1**: Descrição técnica detalhada
- **Funcionalidade 2**: Como resolve problema específico
- **Funcionalidade 3**: Integração com outros módulos

BENEFÍCIOS OPERACIONAIS:
- ⏱️ **Economia de tempo**: Quantifique quando possível
- � **ERedução de custos**: Cite exemplos específicos
- 📈 **Eficiência**: Melhoria nos processos

INTEGRAÇÃO COM OUTROS MÓDULOS:
- Conexão com módulo X para Y
- Sincronização automática com Z

PRÓXIMOS PASSOS:
- Demonstração personalizada
- Contato comercial

## FOCO EM CONVERSÃO
- Qualifique o lead: "Quantos atletas vocês têm?"
- Personalize a resposta baseada no tamanho do clube
- Sempre termine com call-to-action específico
- Ofereça demonstração das funcionalidades mencionadas

Responda SEMPRE em markdown formatado, seja técnico e detalhado, e mostre o valor real do sistema.`;

        // Adiciona instruções específicas baseadas nos metadados
        let additionalInstructions = '';
        if (metadata.isCommercialIntent) {
            additionalInstructions += '\n\nUSUÁRIO MOSTRA INTERESSE COMERCIAL: Seja mais direto sobre benefícios e ROI. Sugira demo ou contato comercial.';
        }
        if (metadata.isQualificationOpportunity) {
            additionalInstructions += '\n\nOPORTUNIDADE DE QUALIFICAÇÃO: Faça perguntas sobre o tamanho do clube para personalizar a resposta.';
        }
        if (metadata.messageCount && metadata.messageCount >= 3) {
            additionalInstructions += '\n\nCONVERSA AVANÇADA: Usuário engajado. Ofereça demonstração personalizada ou contato direto.';
        }

        // Documentação completa do sistema como contexto base
        const systemDocumentation = `
# DOCUMENTAÇÃO COMPLETA DO GAME DAY NEXUS PLATFORM

## MÓDULOS E FUNCIONALIDADES DETALHADAS:

### 🏃‍♂️ MÓDULO DE GESTÃO DE ATLETAS
**Cadastro e Perfil**: Cadastro completo com dados pessoais, gestão de status (ativo/inativo/emprestado/transferido), categorias por temporada, controle de contratos com alertas de vencimento, números de camisa e posições, upload de fotos e documentos.

**Documentação**: Upload e gestão de documentos (RG, CPF, certidão), verificação e aprovação, assinatura digital, controle de pendências, alertas de vencimento.

**Finanças**: Controle de salários e bonificações, vales e adiantamentos, contas bancárias e chaves PIX, relatórios financeiros individuais.

**Disciplina**: Registro de ocorrências, punições e suspensões, controle de cartões por competição, histórico disciplinar completo.

**Suspensões**: Cálculo automático por cartões, alertas visuais, controle por competição, histórico completo.

**Avaliações**: Convites públicos para avaliação, controle de status, formulários personalizáveis, dashboard de estatísticas, sistema de pagamento.

**Estatísticas**: Controle por partida (gols, assistências, cartões), sincronização automática, relatórios por temporada, análise comparativa, gráficos de evolução.

### 💰 MÓDULO DE MENSALIDADES
**Configuração**: Valores por categoria, dias de vencimento, multas por atraso, descontos antecipados, chaves PIX personalizadas.

**Geração Automática**: Criação automática mensal, aplicação de regras, cálculo de valores finais, integração PIX.

**Portal do Atleta**: Acesso público personalizado, visualização de pendências, QR Code PIX, histórico de pagamentos, upload de comprovantes.

**Comprovantes**: Upload pelos atletas, sistema de aprovação/rejeição, notificações automáticas, marcação automática como pago.

**Relatórios**: Inadimplência, estatísticas de pagamento, relatórios por categoria, análise de tendências.

**Notificações**: Lembretes por email antes do vencimento, notificações de atraso, configuração de dias, log de emails.

### ⚽ MÓDULO DE PARTIDAS E COMPETIÇÕES
**Gestão de Partidas**: Cadastro completo com data/horário/local, controle de adversários e competições, tipo casa/fora, sistema ida e volta, análise de adversários.

**Escalação Tática**: Editor visual com campo de futebol, múltiplas formações (4-4-2, 4-3-3, etc.), posições específicas, substituições durante partida, drag and drop.

**Eventos em Tempo Real**: Cronômetro integrado com pausa/retomada, registro de gols/cartões/substituições, placar em tempo real, anotações, sincronização automática.

**Minutos Jogados**: Cálculo automático baseado em escalação e substituições, histórico por jogador, relatórios de participação, estatísticas de utilização.

**Histórico**: Arquivo completo de partidas, estatísticas por adversário, aproveitamento (V/E/D), análise de sequências, comparação entre temporadas.

**Convocações**: Criação por categoria, design personalizado com logo, PDFs automáticos, controle de alojamentos, sistema de papéis (jogadores/staff/dirigentes), imagens personalizadas.

### 🏋️‍♂️ MÓDULO DE TREINAMENTOS
**Planejamento**: Agenda por categoria, controle de locais, objetivos e metas físicas, exercícios e atividades, duração e intensidade.

**Editor Interativo**: Campo visual para exercícios, desenho com cones/setas/anotações, biblioteca pré-definida, sequenciador de atividades, animações e trajetórias, exportação.

**Presença**: Lista digital, controle de faltas e justificativas, relatórios de frequência, estatísticas de participação, alertas de ausências.

**Locais**: Cadastro com endereços, controle de disponibilidade, agendamento de espaços, informações de contato.

**Relatórios**: PDFs, exportação de cronogramas, análise de carga, progressão física, frequência.

### 🏥 MÓDULO MÉDICO E SAÚDE
**Profissionais**: Cadastro de médicos/fisioterapeutas/massagistas, credenciais e certificados, disponibilidade de horários, contas para externos, controle financeiro.

**Agendamento**: Sistema de agendas por profissional, consultas e exames, disponibilidade, notificações automáticas, reagendamento.

**Prontuários**: Histórico médico completo, registro de consultas e diagnósticos, medicamentos e prescrições, evolução de tratamentos, assinatura digital, relatórios personalizados.

**Exames**: Solicitação e controle, upload de resultados, histórico por atleta, exames periódicos, alertas de vencimento.

**Reabilitação**: Agendamento de fisioterapia, evolução do tratamento, relatórios de progresso, notificações, histórico de lesões.

**Estoque Médico**: Medicamentos e materiais, solicitações de reposição, controle de validade, relatórios de consumo, alertas de estoque baixo.

### 🏢 MÓDULO ADMINISTRATIVO
**Colaboradores**: Cadastro completo, departamentos e funções, documentos trabalhistas, convites, controle de status.

**Documentos**: Ofícios e documentos oficiais, assinaturas digitais, arquivo organizado, templates personalizáveis, controle de versões.

**Tarefas**: Sistema Kanban, lembretes automáticos, prazos e responsáveis, dashboard de produtividade, categorização.

**Fornecedores**: Cadastro, pedidos e orçamentos, contratos, histórico de compras, avaliação.

**Finanças de Colaboradores**: Salários e benefícios, adiantamentos, contas bancárias, relatórios, férias e 13º.

### 📊 MÓDULO FINANCEIRO
**Transações**: Receitas e despesas, categorização, upload de comprovantes, contas a pagar/receber, conciliação bancária.

**Relatórios**: Fluxo de caixa, demonstrativo de resultados, relatórios por categoria, análise de tendências, comparativos.

**Cobrança**: Geração PIX, QR Codes, controle de inadimplência, notificações automáticas, integração bancária.

**Contas Bancárias**: Múltiplas contas, conciliação, controle de saldos, histórico de movimentações, transferências.

### 📦 MÓDULO DE ESTOQUE
**Produtos**: Materiais esportivos, controle de quantidades, alertas de estoque baixo, gestão por departamentos, imagens.

**Movimentação**: Entradas e saídas, histórico de transações, controle de responsáveis, relatórios, justificativas.

**Solicitações**: Sistema de requisições, aprovação, controle de entregas, assinatura digital, histórico.

**Relatórios**: Inventário completo, lista de compras, relatórios por departamento, análise de consumo, baixo estoque.

### 🍽️ MÓDULO DE ALIMENTAÇÃO
**Planejamento**: Tipos de refeição, locais de alimentação, sessões alimentares, cardápios personalizados, controle de horários.

**Participação**: Lista de presença, controle por categoria, relatórios de frequência, dietas especiais, assinatura digital.

**Relatórios Nutricionais**: Participação, custos alimentares, análise nutricional, cardápios semanais, estatísticas de consumo.

### 🏨 MÓDULO DE ALOJAMENTOS
**Acomodações**: Cadastro de alojamentos, controle de capacidade, quartos de hotel, distribuição de atletas/staff, controle de custos.

**Ocupação**: Disponibilidade de vagas, histórico de ocupação, relatórios de acomodação, gestão de convocações, check-in/check-out.

### 👥 MÓDULO DE USUÁRIOS E PERMISSÕES
**Usuários**: Cadastro no sistema, perfis e avatares, convites, gestão de senhas, controle de status.

**Permissões**: Controle granular, perfis personalizáveis, permissões por módulo, auditoria de acessos, hierarquia.

**Departamentos**: Organização por setores, hierarquia, permissões departamentais, gestão de equipes, relatórios.

### 💬 MÓDULO DE COMUNICAÇÃO
**Chat**: Salas por clube, mensagens em tempo real, histórico de conversas, status online, notificações.

**Notificações**: Push em tempo real, alertas por email, lembretes automáticos, central de notificações, configuração de preferências.

### 📋 MÓDULO DE RELATÓRIOS
**Geração**: PDFs personalizados, logo e identidade visual, múltiplos tipos, exportação e impressão, templates personalizáveis.

**Específicos**: Atletas, alojamentos, médicos, financeiros, estoque, alimentação, treinamento.

### 🌐 MÓDULO MASTER (SAAS)
**Clubes**: Cadastro de clientes, planos e assinaturas, gestão de pagamentos, monitoramento de uso, usuários presidente automáticos.

**Planos**: Diferentes assinaturas, cobrança automática, controle de inadimplência, relatórios de faturamento, gestão de trials.

**Suporte**: Tickets de suporte, logs de auditoria, monitoramento de performance, relatórios de uso, dashboard administrativo.

## FUNCIONALIDADES TRANSVERSAIS:
- **Segurança**: Supabase Auth, controle de sessões, reset de senhas, RLS, criptografia
- **Arquivos**: Upload de documentos/imagens, controle de tamanho/tipo, organização por pastas, backup automático, CDN
- **Auditoria**: Registro de todas as ações, histórico de alterações, controle de versões, rastreabilidade completa
- **Responsividade**: Interface adaptável mobile, design responsivo, otimização tablets, experiência consistente
- **PIX**: QR Codes, chaves personalizadas, cobrança automática, conciliação, relatórios de transações
- **Temporadas**: Controle por períodos esportivos, migração de dados, histórico temporal, análise comparativa
- **Assinatura Digital**: Assinatura eletrônica, validação de autenticidade, controle de integridade, arquivo seguro
- **Agenda**: Calendário unificado, eventos de diferentes módulos, lembretes automáticos, sincronização externa
- **Dashboard**: Widgets personalizáveis, métricas em tempo real, alertas importantes, gráficos e estatísticas
- **Backup**: Automático, versionamento, recuperação point-in-time, replicação, monitoramento de integridade

## FLUXOS INTEGRADOS:
1. **Convocação Completa**: Partida → Convocação → Alojamentos → Alimentação → Documentos
2. **Médico Integrado**: Agendamento → Atendimento → Prontuário → Prescrições → Acompanhamento → Relatórios
3. **Financeiro Completo**: Cobranças → Pagamentos → Conciliação → Relatórios → Inadimplência

**TOTAL**: Mais de 200 funcionalidades em 15 módulos principais com integração completa.
`;

        const contents = [
            {
                role: 'user',
                parts: [{ text: system + additionalInstructions + '\n\n' + systemDocumentation + '\n\nContexto adicional:\n' + contextChunks.join('\n\n') }],
            },
            ...history.map((m) => ({
                role: m.role === 'user' ? 'user' : 'model',
                parts: [{ text: m.text }],
            })),
        ];

        console.log('=== CHAMANDO GEMINI API ===');
        console.log('Contents length:', contents.length);
        console.log('API URL:', `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY.substring(0, 10)}...`);

        const response = await fetch(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`,
            {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents }),
            }
        );

        if (!response.ok) {
            console.error('Erro na resposta do Gemini:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('Detalhes do erro:', errorText);
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // Log para debug
        console.log('Gemini response:', data);

        if (data.error) {
            console.error('Erro retornado pelo Gemini:', data.error);
            throw new Error(`Gemini API error: ${data.error.message || 'Unknown error'}`);
        }

        const text =
            data.candidates?.[0]?.content?.parts?.[0]?.text ||
            `## 🤖 Sistema Temporariamente Indisponível

Desculpe, estou com dificuldades técnicas no momento. Mas posso te ajudar de outras formas:

### 📞 **Contato Direto**
- **WhatsApp**: Nosso time comercial está online para demonstrações personalizadas
- **Email**: Envie suas dúvidas e receba informações detalhadas

### 🎯 **O que posso te mostrar:**
- **Demonstração completa** dos 15 módulos integrados
- **Análise personalizada** para seu clube
- **Proposta comercial** adequada ao seu porte

### ⚽ **Game Day Nexus em números:**
- **200+ funcionalidades** integradas
- **15 módulos** especializados
- **Gestão completa** do clube

**Vamos agendar uma demonstração?** 🚀`;

        res.status(200).json({ text });
    } catch (err) {
        console.error('=== ERRO NA API ===');
        console.error('Error type:', typeof err);
        console.error('Error name:', err?.name);
        console.error('Error message:', err?.message);
        console.error('Error stack:', err?.stack);
        console.error('Full error object:', JSON.stringify(err, Object.getOwnPropertyNames(err), 2));

        const message = err instanceof Error ? err.message : 'Erro inesperado';
        res.status(500).json({
            error: message,
            details: {
                name: err?.name,
                stack: err?.stack,
                timestamp: new Date().toISOString()
            }
        });
    }
}