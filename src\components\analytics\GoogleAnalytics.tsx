import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

interface GoogleAnalyticsProps {
  measurementId: string;
}

export function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  const location = useLocation();

  useEffect(() => {
    // Carregar Google Analytics apenas se não estiver carregado
    if (!window.gtag) {
      // Criar dataLayer
      window.dataLayer = window.dataLayer || [];
      
      // Função gtag
      window.gtag = function() {
        window.dataLayer.push(arguments);
      };

      // Configuração inicial
      window.gtag('js', new Date());
      window.gtag('config', measurementId, {
        send_page_view: false, // Vamos controlar manualmente
        custom_map: {
          'dimension1': 'user_type',
          'dimension2': 'club_size',
          'dimension3': 'content_category'
        }
      });

      // Carregar script do GA4
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
      document.head.appendChild(script);
    }
  }, [measurementId]);

  // Rastrear mudanças de página
  useEffect(() => {
    if (window.gtag) {
      window.gtag('config', measurementId, {
        page_path: location.pathname + location.search,
      });
    }
  }, [location, measurementId]);

  return null;
}

// Funções utilitárias para eventos personalizados
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

export const trackBlogEngagement = (action: string, postSlug: string, value?: number) => {
  trackEvent('blog_engagement', {
    event_category: 'blog',
    event_label: postSlug,
    action: action,
    value: value
  });
};

export const trackLeadMagnetDownload = (leadMagnetName: string, postSlug?: string) => {
  trackEvent('lead_magnet_download', {
    event_category: 'lead_generation',
    event_label: leadMagnetName,
    post_slug: postSlug,
    value: 1
  });
};

export const trackCTAClick = (ctaType: string, location: string, destination: string) => {
  trackEvent('cta_click', {
    event_category: 'conversion',
    event_label: `${ctaType}_${location}`,
    destination: destination,
    value: 1
  });
};

export const trackVideoProgress = (videoId: string, progress: number) => {
  trackEvent('video_progress', {
    event_category: 'video',
    event_label: videoId,
    value: progress
  });
};

export const trackReadingTime = (postSlug: string, timeSpent: number) => {
  trackEvent('reading_time', {
    event_category: 'engagement',
    event_label: postSlug,
    value: timeSpent
  });
};

export const trackScrollDepth = (postSlug: string, depth: number) => {
  trackEvent('scroll_depth', {
    event_category: 'engagement',
    event_label: postSlug,
    value: depth
  });
};