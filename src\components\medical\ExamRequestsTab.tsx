import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Download } from "lucide-react";
import { useMedicalExamsStore } from "@/store/useMedicalExamsStore";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { useUser } from "@/context/UserContext";
import { generateExamRequestReport } from "@/utils/examRequestReportGenerator";
import { getMedicalProfessionalById } from "@/api/api";
import { usePermission } from "@/hooks/usePermission";
import { format } from "date-fns";
import { ExamRequestDialog } from "./ExamRequestDialog";
import { MEDICAL_PERMISSIONS } from "@/constants/permissions";
import { PermissionControl } from "@/components/PermissionControl";

export function ExamRequestsTab() {
  const clubId = useCurrentClubId();
  const { exams, fetchClubExams } = useMedicalExamsStore();
  const { players, fetchPlayers } = usePlayersStore();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const { user } = useUser();
  const { role } = usePermission();
  const [startDate, setStartDate] = useState(() => format(new Date(), "yyyy-MM-dd"));
  const [endDate, setEndDate] = useState(() => format(new Date(), "yyyy-MM-dd"));
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    if (clubId) {
      fetchClubExams(clubId, startDate, endDate);
      if (players.length === 0) fetchPlayers(clubId);
      if (!clubInfo) fetchClubInfo(clubId);
    }
  }, [clubId, startDate, endDate]);

  const handleDownload = async (exam: any) => {
    if (!clubInfo || !user) return;
    const player = players.find(p => p.id === exam.player_id);
    let doctorName = user.name || "";
    let doctorRole = "Médico";
    try {
      const prof = await getMedicalProfessionalById(clubId, exam.requested_by);
      doctorName = prof.name;
      doctorRole = prof.role;
    } catch (err) {
      console.error("Erro ao buscar médico responsável:", err);
    }
    const blob = await generateExamRequestReport(
      { ...exam, player_name: player?.name },
      clubInfo,
      doctorName,
      doctorRole,
      user.name || "",
      role || "medical",
      `solicitacao-exame-${exam.id}.pdf`
    );
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `solicitacao-exame-${exam.id}.pdf`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <><Card>
      <CardHeader className="pb-0 flex flex-col sm:flex-row justify-between">
        <div>
          <CardTitle>Solicitações de Exame</CardTitle>
          <CardDescription>Registros de exames solicitados</CardDescription>
        </div>
        <div className="flex gap-2 mb-4 items-center">
          <Input type="date" value={startDate} onChange={e => setStartDate(e.target.value)} className="w-36" />
          <Input type="date" value={endDate} onChange={e => setEndDate(e.target.value)} className="w-36" />
          <PermissionControl permission={MEDICAL_PERMISSIONS.EXAM_REQUESTS.CREATE}>
            <Button onClick={() => setDialogOpen(true)}>Solicitar Exame</Button>
          </PermissionControl>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Jogador</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Data</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {exams.map(exam => {
                const player = players.find(p => p.id === exam.player_id);
                return (
                  <TableRow key={exam.id}>
                    <TableCell>{player ? player.name : exam.player_id}</TableCell>
                    <TableCell>{exam.exam_type}</TableCell>
                    <TableCell>{exam.request_date}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" onClick={() => handleDownload(exam)}>
                        <Download className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
    <ExamRequestDialog open={dialogOpen} onOpenChange={setDialogOpen} onSuccess={() => fetchClubExams(clubId, startDate, endDate)} /></>
  );
}