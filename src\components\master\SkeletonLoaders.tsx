import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';

// Dashboard Statistics Card Skeleton
export const DashboardStatsCardSkeleton: React.FC = () => (
  <Card>
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-4 w-20" />
        </div>
        <Skeleton className="h-12 w-12 rounded-full" />
      </div>
    </CardContent>
  </Card>
);

// Dashboard Secondary Card Skeleton
export const DashboardSecondaryCardSkeleton: React.FC<{ title: string }> = ({ title }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <Skeleton className="w-5 h-5 mr-2" />
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex justify-between items-center">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-8" />
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

// Activity Item Skeleton
export const ActivityItemSkeleton: React.FC = () => (
  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
    <Skeleton className="w-4 h-4" />
    <div className="flex-1 space-y-2">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
    </div>
  </div>
);

// Activities Section Skeleton
export const ActivitiesSectionSkeleton: React.FC = () => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center justify-between">
        Atividades Recentes
        <Skeleton className="w-4 h-4" />
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <ActivityItemSkeleton key={i} />
        ))}
      </div>
    </CardContent>
  </Card>
);

// Clubs Stats Card Skeleton
export const ClubsStatsCardSkeleton: React.FC = () => (
  <Card>
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-8 w-16" />
        </div>
        <Skeleton className="h-8 w-8" />
      </div>
    </CardContent>
  </Card>
);

// Table Row Skeleton for Clubs
export const ClubsTableRowSkeleton: React.FC = () => (
  <TableRow>
    <TableCell>
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-3 w-48" />
      </div>
    </TableCell>
    <TableCell>
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-3 w-20" />
      </div>
    </TableCell>
    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
    <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
  </TableRow>
);

// Full Clubs Table Skeleton
export const ClubsTableSkeleton: React.FC = () => (
  <Card>
    <CardHeader>
      <CardTitle>Clubes Cadastrados</CardTitle>
    </CardHeader>
    <CardContent>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Clube</TableHead>
            <TableHead>Plano</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Pagamento</TableHead>
            <TableHead>Próximo Vencimento</TableHead>
            <TableHead>Criado em</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {[...Array(5)].map((_, i) => (
            <ClubsTableRowSkeleton key={i} />
          ))}
        </TableBody>
      </Table>
    </CardContent>
  </Card>
);

// Filter Section Skeleton
export const FilterSectionSkeleton: React.FC = () => (
  <Card>
    <CardContent className="p-6">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <Skeleton className="h-10 w-full" />
        </div>
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-24" />
      </div>
    </CardContent>
  </Card>
);

// Page Header Skeleton
export const PageHeaderSkeleton: React.FC = () => (
  <div className="flex justify-between items-center">
    <div className="space-y-2">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-64" />
    </div>
    <div className="flex space-x-3">
      <Skeleton className="h-10 w-24" />
      <Skeleton className="h-10 w-32" />
    </div>
  </div>
);

// Full Dashboard Skeleton
export const DashboardSkeleton: React.FC = () => (
  <div className="space-y-6">
    <PageHeaderSkeleton />
    
    {/* Stats Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <DashboardStatsCardSkeleton key={i} />
      ))}
    </div>

    {/* Secondary Cards */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <DashboardSecondaryCardSkeleton title="Status dos Clubes" />
      <DashboardSecondaryCardSkeleton title="Novos Clubes" />
      <DashboardSecondaryCardSkeleton title="Crescimento" />
    </div>

    {/* Activities */}
    <ActivitiesSectionSkeleton />
  </div>
);

// Full Clubs Page Skeleton
export const ClubsPageSkeleton: React.FC = () => (
  <div className="space-y-6">
    <PageHeaderSkeleton />
    
    {/* Stats Cards */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <ClubsStatsCardSkeleton key={i} />
      ))}
    </div>

    {/* Filters */}
    <FilterSectionSkeleton />

    {/* Table */}
    <ClubsTableSkeleton />
  </div>
);