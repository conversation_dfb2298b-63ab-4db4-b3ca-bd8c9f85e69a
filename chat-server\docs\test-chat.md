# Teste do Chat - Passo a Passo

## 1. <PERSON><PERSON>, pegue a SERVICE_ROLE KEY

Vá em: https://supabase.com/dashboard/project/qoujacltecwxvymynbsh/settings/api

Copie a **service_role** key (não a anon!)

## 2. Atualize o .env do chat-server

```bash
# chat-server/.env
SUPABASE_URL=https://qoujacltecwxvymynbsh.supabase.co
SUPABASE_SERVICE_KEY=SUA_SERVICE_ROLE_KEY_AQUI
PORT=3001
```

## 3. Teste local

```bash
# Terminal 1 - Servidor Socket.IO
cd chat-server
npm run dev

# Terminal 2 - Frontend
npm run dev
```

## 4. Abra o chat no frontend

1. Faça login no sistema
2. Clique no ícone de chat
3. Veja se aparece "Online" no status
4. Teste enviar uma mensagem

## 5. Se funcionar local, deploy na Vercel

```bash
cd chat-server
npx vercel env add SUPABASE_SERVICE_KEY
# Cole a SERVICE_ROLE KEY
npx vercel --prod
```

## 6. Atualize o frontend para usar a URL da Vercel

No `.env.local`:
```
VITE_SOCKET_URL=https://chat-server-ude8tyml5-brunobosso98s-projects.vercel.app
```

---

**RESULTADO**: Chat funcionando 100% com todas as funcionalidades! 🎉