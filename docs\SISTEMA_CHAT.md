# Sistema de Chat em Tempo Real

## Visão Geral

O sistema de chat permite comunicação em tempo real entre usuários do mesmo clube, com salas organizadas e controle de presença online.

## Funcionalidades

### 🏠 Salas de Chat
- **Sala Geral**: Criada automaticamente para cada clube
- **Salas Personalizadas**: Usuários podem criar salas temáticas
- **Controle de Participantes**: Sistema de entrada/saída de salas
- **Mensagens não lidas**: Contador de mensagens não visualizadas

### 💬 Mensagens
- **Envio em tempo real**: Mensagens aparecem instantaneamente
- **Respostas**: Sistema de reply para contexto
- **Edição**: Usuários podem editar suas próprias mensagens
- **Exclusão**: Usuários podem deletar suas mensagens
- **Histórico**: Mensagens são persistidas no banco

### 👥 Presença Online
- **Status em tempo real**: Online, Ausente, Ocupado, Offline
- **Lista de usuários**: Visualização de quem está online
- **Atualização automática**: Status atualizado baseado na atividade

### 🔒 Segurança
- **Isolamento por clube**: Usuários só veem dados do seu clube
- **RLS (Row Level Security)**: Políticas de segurança no banco
- **Autenticação**: Apenas usuários logados podem usar o chat

## Arquitetura Técnica

### Backend (Supabase)
```sql
-- Tabelas principais
- chat_rooms: Salas de chat por clube
- chat_messages: Mensagens das salas
- chat_room_participants: Participantes das salas
- user_presence: Status de presença dos usuários
```

### Frontend (React + TypeScript)
```
src/
├── api/chat.ts              # API calls para o chat
├── store/useChatStore.ts    # Estado global do chat
├── hooks/useChat.ts         # Hook personalizado
├── types/chat.ts            # Tipos TypeScript
└── components/chat/         # Componentes do chat
    ├── ChatContainer.tsx    # Container principal
    ├── ChatRoom.tsx         # Sala de chat
    ├── ChatRoomList.tsx     # Lista de salas
    ├── ChatMessage.tsx      # Componente de mensagem
    ├── OnlineUsers.tsx      # Lista de usuários online
    └── CreateRoomDialog.tsx # Diálogo para criar salas
```

### Realtime (Supabase Realtime)
- **Subscrições**: Mensagens e presença em tempo real
- **Channels**: Um canal por sala e um por clube (presença)
- **Eventos**: INSERT, UPDATE, DELETE nas tabelas relevantes

## Como Usar

### Para Usuários

1. **Abrir Chat**: Clique no ícone de mensagem no header
2. **Navegar Salas**: Use as abas "Salas" e "Online"
3. **Entrar em Sala**: Clique em uma sala para entrar
4. **Enviar Mensagem**: Digite e pressione Enter
5. **Responder**: Clique no ícone de reply na mensagem
6. **Criar Sala**: Use o botão "Nova Sala"

### Para Desenvolvedores

#### Integração Básica
```tsx
import { useChat } from '@/hooks/useChat';

function MyComponent() {
  const { 
    isChatOpen, 
    toggleChat, 
    hasUnreadMessages,
    totalUnreadCount 
  } = useChat();

  return (
    <button onClick={toggleChat}>
      Chat {hasUnreadMessages && `(${totalUnreadCount})`}
    </button>
  );
}
```

#### Uso do Store
```tsx
import { useChatStore } from '@/store/useChatStore';

function ChatComponent() {
  const { 
    rooms, 
    currentRoom, 
    sendMessage,
    loadRooms 
  } = useChatStore();

  // Carregar salas
  useEffect(() => {
    loadRooms(clubId);
  }, [clubId]);

  // Enviar mensagem
  const handleSend = async (content: string) => {
    await sendMessage({
      room_id: currentRoom.id,
      content
    });
  };
}
```

## Configuração

### 1. Executar SQL
Execute o arquivo `sql/chat_system.sql` no seu banco Supabase.

### 2. Configurar RLS
As políticas RLS já estão incluídas no SQL, garantindo isolamento por clube.

### 3. Habilitar Realtime
No painel Supabase, habilite Realtime para as tabelas:
- `chat_messages`
- `user_presence`
Habilite Realtime:

No painel Supabase, vá em Database > Replication
Habilite para as tabelas: chat_messages, user_presence

### 4. Integrar no Layout
O chat já está integrado no `Layout.tsx` principal.

## Personalização

### Temas
O chat usa as cores do tema do clube automaticamente.

### Notificações
Integrado com o sistema de notificações existente.

### Permissões
Respeita as permissões do usuário no clube.

## Troubleshooting

### Mensagens não aparecem
1. Verificar se Realtime está habilitado
2. Verificar políticas RLS
3. Verificar se usuário está na sala

### Usuários não aparecem online
1. Verificar subscrição de presença
2. Verificar se `user_presence` tem dados
3. Verificar se usuário está no mesmo clube

### Performance
- Mensagens são paginadas (50 por vez)
- Presença é atualizada a cada 30 segundos
- Limpeza automática ao fechar chat

## Próximas Funcionalidades

- [ ] Anexos de arquivos
- [ ] Emojis e reações
- [ ] Menções (@usuário)
- [ ] Notificações push
- [ ] Salas privadas (DM)
- [ ] Moderação de salas
- [ ] Histórico de mensagens
- [ ] Busca em mensagens