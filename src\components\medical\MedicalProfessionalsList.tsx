import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { MedicalProfessional, getMedicalProfessionals, getMedicalProfessionalByUserId, deleteMedicalProfessional } from "@/api/api";
import { Search, UserPlus, Edit, Trash2, FileText, UserCog, Shield, DollarSign, Key } from "lucide-react";
import { Di<PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { MedicalProfessionalAccountForm } from "./MedicalProfessionalAccountForm";
import { MedicalPermissionsDialog } from "./MedicalPermissionsDialog";
import { MedicalProfessionalFinanceiroDialog } from "./MedicalProfessionalFinanceiroDialog";
import { usePermission } from "@/hooks/usePermission";
import { PermissionControl } from "@/components/PermissionControl";
import { USER_PERMISSIONS } from "@/constants/permissions";
import { ChangePasswordDialog } from "@/components/users/ChangePasswordDialog";

interface MedicalProfessionalsListProps {
  onEdit?: (professional: MedicalProfessional) => void;
}

export function MedicalProfessionalsList({ onEdit }: MedicalProfessionalsListProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const navigate = useNavigate();
  const { role, can } = usePermission();

  const [professionals, setProfessionals] = useState<MedicalProfessional[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [professionalToDelete, setProfessionalToDelete] = useState<MedicalProfessional | null>(null);
  const [viewCertificate, setViewCertificate] = useState<string | null>(null);
  const [createAccountOpen, setCreateAccountOpen] = useState(false);
  const [professionalForAccount, setProfessionalForAccount] = useState<MedicalProfessional | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [professionalToEdit, setProfessionalToEdit] = useState<MedicalProfessional | null>(null);
  const [permissionsDialogOpen, setPermissionsDialogOpen] = useState(false);
  const [professionalForPermissions, setProfessionalForPermissions] = useState<MedicalProfessional | null>(null);
  const [financeDialogOpen, setFinanceDialogOpen] = useState(false);
  const [professionalForFinance, setProfessionalForFinance] = useState<MedicalProfessional | null>(null);
  const [passwordProfessional, setPasswordProfessional] = useState<MedicalProfessional | null>(null);

  // Carregar profissionais médicos
  useEffect(() => {
    const fetchProfessionals = async () => {
      try {
        setLoading(true);

        // Se o usuário for médico, mostrar apenas o próprio perfil
        if (role === "medical" && user?.id) {
          const ownProfile = await getMedicalProfessionalByUserId(clubId, user.id);
          if (ownProfile) {
            setProfessionals([ownProfile]);
          } else {
            setProfessionals([]);
          }
        } else {
          // Para administradores e outros papéis, mostrar todos os profissionais
          const data = await getMedicalProfessionals(clubId);
          setProfessionals(data);
        }
      } catch (err: any) {
        console.error("Erro ao carregar profissionais médicos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os profissionais médicos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProfessionals();
  }, [clubId, role, user?.id]);

  // Filtrar profissionais médicos
  const filteredProfessionals = professionals.filter(professional => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      professional.name.toLowerCase().includes(query) ||
      professional.role.toLowerCase().includes(query) ||
      (professional.credential && professional.credential.toLowerCase().includes(query))
    );
  });

  // Função para confirmar exclusão
  const handleConfirmDelete = (professional: MedicalProfessional) => {
    setProfessionalToDelete(professional);
    setDeleteConfirmOpen(true);
  };

  // Função para excluir profissional médico
  const handleDelete = async () => {
    if (!professionalToDelete) return;

    try {
      await deleteMedicalProfessional(clubId, user?.id || "", professionalToDelete.id);

      // Atualizar a lista
      setProfessionals(professionals.filter(p => p.id !== professionalToDelete.id));

      toast({
        title: "Sucesso",
        description: "Profissional médico excluído com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao excluir profissional médico:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir profissional médico",
        variant: "destructive",
      });
    } finally {
      setDeleteConfirmOpen(false);
      setProfessionalToDelete(null);
    }
  };

  // Função para obter as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(part => part[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  // Função para abrir o diálogo de criação de conta
  const handleCreateAccount = (professional: MedicalProfessional) => {
    setProfessionalForAccount(professional);
    setCreateAccountOpen(true);
  };

  // Função para abrir o diálogo de gerenciamento de permissões
  const handleManagePermissions = (professional: MedicalProfessional) => {
    if (!professional.user_id) {
      toast({
        title: "Aviso",
        description: "Este profissional não possui uma conta de usuário. Crie uma conta primeiro.",
        variant: "default",
      });
      return;
    }

    setProfessionalForPermissions(professional);
    setPermissionsDialogOpen(true);
  };

  // Função para obter o rótulo da função
  const getRoleLabel = (role: string) => {
    switch (role) {
      case "médico":
        return "Médico";
      case "fisioterapeuta":
        return "Fisioterapeuta";
      case "enfermeiro":
        return "Enfermeiro";
      case "massagista":
        return "Massagista";
      case "outros":
        return "Outros";
      default:
        return role;
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Profissionais Médicos</CardTitle>
          {role !== "medical" && (
            <Button onClick={() => navigate("/medicos/cadastro")}>
              <UserPlus className="h-4 w-4 mr-2" />
              Novo Profissional
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar profissionais..."
                className="pl-8"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <p>Carregando profissionais médicos...</p>
            </div>
          ) : filteredProfessionals.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <p className="text-muted-foreground mb-2">Nenhum profissional médico encontrado</p>
              {searchQuery ? (
                <Button variant="outline" onClick={() => setSearchQuery("")}>
                  Limpar busca
                </Button>
              ) : (
                <Button onClick={() => navigate("/medicos/cadastro")}>
                  Cadastrar Profissional
                </Button>
              )}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Função</TableHead>
                    <TableHead className="hidden md:table-cell">Credencial</TableHead>
                    <TableHead className="hidden md:table-cell">Certificado</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProfessionals.map(professional => (
                    <TableRow key={professional.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar>
                            <AvatarFallback 
                              style={{
                                backgroundColor: 'var(--color-primary)',
                                opacity: 0.1,
                                color: 'var(--color-primary)'
                              }}
                            >
                              {getInitials(professional.name)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{professional.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{getRoleLabel(professional.role)}</Badge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {professional.credential || "-"}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {professional.certificate_url ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewCertificate(professional.certificate_url || null)}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            Ver
                          </Button>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          {/* Botão de criar conta - apenas para admins e quando não há conta */}
                          {role !== "medical" && !professional.user_id && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCreateAccount(professional)}
                              title="Criar conta"
                            >
                              <UserCog className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botão de gerenciar permissões - apenas para admins e quando há conta */}
                          {role !== "medical" && professional.user_id && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleManagePermissions(professional)}
                              title="Gerenciar permissões"
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botão de alterar senha - apenas para admins e quando há conta */}
                          {professional.user_id && (
                            <PermissionControl permission={USER_PERMISSIONS.RESET_PASSWORD}>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setPasswordProfessional(professional)}
                                title="Alterar senha"
                                className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                              >
                                <Key className="h-4 w-4" />
                              </Button>
                            </PermissionControl>
                          )}

                          {/* Botão de finanças */}
                          {can("medical_professionals.edit") && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setProfessionalForFinance(professional);
                                setFinanceDialogOpen(true);
                              }}
                              title="Finanças"
                            >
                              <DollarSign className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botão de editar - para admins, presidents, usuários com permissão ou para médicos editarem seu próprio perfil */}
                          {(onEdit ||
                            (role === "medical" && professional.user_id === user?.id) ||
                            role === "president" ||
                            can("medical_professionals.edit")) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => role === "medical" ? navigate("/medicos/cadastro") : onEdit && onEdit(professional)}
                              title="Editar"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botão de excluir - apenas para admins */}
                          {role !== "medical" && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleConfirmDelete(professional)}
                              title="Excluir"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o profissional médico{" "}
              <strong>{professionalToDelete?.name}</strong>?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para visualizar certificado */}
      <Dialog open={!!viewCertificate} onOpenChange={() => setViewCertificate(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Certificado</DialogTitle>
          </DialogHeader>
          <div className="mt-4 h-[70vh] overflow-auto">
            {viewCertificate && (
              <iframe
                src={viewCertificate.endsWith(".pdf")
                  ? `https://docs.google.com/viewer?url=${encodeURIComponent(viewCertificate)}&embedded=true`
                  : viewCertificate}
                className="w-full h-full border-0"
                title="Certificado"
                allowFullScreen
              />
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setViewCertificate(null)}>Fechar</Button>
            <Button variant="outline" onClick={() => window.open(viewCertificate || "", "_blank")}>
              Abrir em nova aba
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para criar conta */}
      {professionalForAccount && (
        <MedicalProfessionalAccountForm
          open={createAccountOpen}
          onOpenChange={setCreateAccountOpen}
          professionalId={professionalForAccount.id}
          professionalName={professionalForAccount.name}
        />
      )}

      {/* Diálogo para gerenciar permissões */}
      {professionalForPermissions && (
        <MedicalPermissionsDialog
          open={permissionsDialogOpen}
          onOpenChange={setPermissionsDialogOpen}
          professionalId={professionalForPermissions.id}
          professionalName={professionalForPermissions.name}
          userId={professionalForPermissions.user_id}
          clubId={clubId}
        />
      )}

      {/* Diálogo de finanças */}
      {professionalForFinance && (
        <MedicalProfessionalFinanceiroDialog
          open={financeDialogOpen}
          onOpenChange={setFinanceDialogOpen}
          clubId={clubId}
          professional={professionalForFinance}
          onSuccess={() => {}}
        />
      )}

      {/* Diálogo para alterar senha */}
      {passwordProfessional && passwordProfessional.user_id && (
        <ChangePasswordDialog
          open={!!passwordProfessional}
          onOpenChange={(open) => !open && setPasswordProfessional(null)}
          userId={passwordProfessional.user_id}
          userName={passwordProfessional.name}
        />
      )}
    </>
  );
}