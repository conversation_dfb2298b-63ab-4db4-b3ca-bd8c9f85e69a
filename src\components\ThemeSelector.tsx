import { useState, useEffect } from "react";
import { useTheme } from "@/context/ThemeContext";
import { teamThemes } from "@/data/themes";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check } from "lucide-react";
import { generateSecondaryColor, hexToHsl } from "@/utils/themeUtils";
import { useCurrentClubId } from "@/context/ClubContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { toast } from "@/components/ui/use-toast";

export function ThemeSelector({ disabled = false }: { disabled?: boolean }) {
  const { theme, setThemeId } = useTheme();
  const clubId = useCurrentClubId();
  const { updateClubInfo } = useClubInfoStore();
  const [selectedTheme, setSelectedTheme] = useState(theme.id);
  const [customPrimary, setCustomPrimary] = useState(theme.colors.primary);
  const [customSecondary, setCustomSecondary] = useState(generateSecondaryColor(theme.colors.primary));
  const [showCustom, setShowCustom] = useState(false);
  const [saving, setSaving] = useState(false);

  // Atualizar o tema selecionado quando o tema global mudar
  useEffect(() => {
    setSelectedTheme(theme.id);
  }, [theme.id]);

  // Atualiza cor secundária quando a primária muda
  useEffect(() => {
    setCustomSecondary(generateSecondaryColor(customPrimary));
  }, [customPrimary]);

  // Função para aplicar o tema selecionado
  const handleApplyTheme = async () => {
    try {
      setSaving(true);
      await updateClubInfo(clubId, {
        primary_color: selectedTheme,
        secondary_color: teamThemes[selectedTheme]?.colors.secondary
      });
      setThemeId(selectedTheme);

      toast({
        title: "Tema atualizado",
        description: "As cores do clube foram atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o tema.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Função para aplicar cores personalizadas
  const handleApplyCustomColors = async () => {
    if (!customPrimary) {
      toast({
        title: "Erro",
        description: "Por favor, selecione uma cor primária.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);
      await updateClubInfo(clubId, {
        primary_color: customPrimary,
        secondary_color: customSecondary
      });

      // Aplicar cores customizadas diretamente
      const root = document.documentElement;
      root.style.setProperty("--color-primary", customPrimary);
      root.style.setProperty("--color-secondary", customSecondary);
      root.style.setProperty("--primary", hexToHsl(customPrimary));
      root.style.setProperty("--secondary", hexToHsl(customSecondary));
      root.style.setProperty("--ring", hexToHsl(customPrimary));
      root.style.setProperty("--accent", hexToHsl(customSecondary));
      root.style.setProperty("--sidebar-primary", hexToHsl(customPrimary));
      root.style.setProperty("--sidebar-accent", hexToHsl(customSecondary));

      toast({
        title: "Cores personalizadas aplicadas",
        description: "As cores customizadas foram salvas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as cores personalizadas.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      <RadioGroup
        value={selectedTheme}
        onValueChange={setSelectedTheme}
        className="grid grid-cols-2 gap-2"
      >
        {Object.values(teamThemes).map((themeOption) => (
          <div key={themeOption.id} className="flex items-center space-x-2">
            <RadioGroupItem value={themeOption.id} id={`theme-${themeOption.id}`} />
            <Label
              htmlFor={`theme-${themeOption.id}`}
              className="flex items-center gap-2 cursor-pointer"
            >
              <div
                className="w-5 h-5 rounded-full border"
                style={{ backgroundColor: themeOption.colors.primary }}
              ></div>
              <span>{themeOption.name}</span>
              {theme.id === themeOption.id && (
                <Check className="h-4 w-4 text-green-500" />
              )}
            </Label>
          </div>
        ))}
      </RadioGroup>

      <Button
        onClick={handleApplyTheme}
        variant="outline"
        size="sm"
        className="w-full"
        disabled={saving || disabled}
      >
        {saving ? "Salvando..." : "Aplicar Tema"}
      </Button>

      <div className="pt-4 border-t">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium">Cores Personalizadas</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCustom(!showCustom)}
            disabled={disabled}
          >
            {showCustom ? "Ocultar" : "Mostrar"}
          </Button>
        </div>

        {showCustom && (
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="primary-color">Cor Primária</Label>
              <div className="flex items-center gap-2">
                <input
                  id="primary-color"
                  type="color"
                  value={customPrimary}
                  onChange={(e) => setCustomPrimary(e.target.value)}
                  disabled={disabled}
                  className="h-10 w-10 rounded border"
                />
                <Input
                  type="text"
                  value={customPrimary}
                  onChange={(e) => setCustomPrimary(e.target.value)}
                  disabled={disabled}
                  className="w-24"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Cor Secundária (automática)</Label>
              <div className="flex items-center gap-2">
                <div
                  className="w-10 h-10 rounded border"
                  style={{ backgroundColor: customSecondary }}
                ></div>
                <span className="text-sm">{customSecondary}</span>
              </div>
            </div>

            <Button
              onClick={handleApplyCustomColors}
              variant="outline"
              size="sm"
              className="w-full"
              disabled={saving || disabled}
            >
              {saving ? "Salvando..." : "Aplicar Cores Personalizadas"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
