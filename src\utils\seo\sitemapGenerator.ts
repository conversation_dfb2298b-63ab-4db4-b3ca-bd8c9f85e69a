// Gerador automático de sitemap.xml

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
  images?: Array<{
    loc: string;
    title?: string;
    caption?: string;
  }>;
}

export interface BlogPost {
  slug: string;
  title: string;
  publishedAt: string;
  updatedAt?: string;
  featuredImage?: string;
  category?: string;
}

export interface VideoContent {
  slug: string;
  title: string;
  publishedAt: string;
  thumbnail: string;
  category?: string;
}

// Gerar sitemap principal (index)
export function generateSitemapIndex(): string {
  const lastmod = new Date().toISOString();
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>https://gamedaynexus.com/sitemap-pages.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>https://gamedaynexus.com/sitemap-blog.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>https://gamedaynexus.com/sitemap-videos.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>https://gamedaynexus.com/sitemap-recursos.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
</sitemapindex>`;
}

// Gerar sitemap de páginas estáticas
export function generatePagesSitemap(): string {
  const pages: SitemapUrl[] = [
    {
      loc: 'https://gamedaynexus.com/',
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 1.0
    },
    {
      loc: 'https://gamedaynexus.com/blog',
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 0.9
    },
    {
      loc: 'https://gamedaynexus.com/videos',
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: 'https://gamedaynexus.com/recursos',
      lastmod: new Date().toISOString(),
      changefreq: 'monthly',
      priority: 0.7
    },
    {
      loc: 'https://gamedaynexus.com/sobre',
      lastmod: new Date().toISOString(),
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      loc: 'https://gamedaynexus.com/contato',
      lastmod: new Date().toISOString(),
      changefreq: 'monthly',
      priority: 0.6
    }
  ];

  return generateSitemapXML(pages);
}

// Gerar sitemap do blog
export function generateBlogSitemap(posts: BlogPost[]): string {
  const urls: SitemapUrl[] = posts.map(post => ({
    loc: `https://gamedaynexus.com/blog/${post.slug}`,
    lastmod: post.updatedAt || post.publishedAt,
    changefreq: 'monthly',
    priority: 0.8,
    ...(post.featuredImage && {
      images: [{
        loc: post.featuredImage,
        title: post.title,
        caption: `Imagem do post: ${post.title}`
      }]
    })
  }));

  return generateSitemapXML(urls, true);
}

// Gerar sitemap de vídeos
export function generateVideosSitemap(videos: VideoContent[]): string {
  const urls: SitemapUrl[] = videos.map(video => ({
    loc: `https://gamedaynexus.com/videos/${video.slug}`,
    lastmod: video.publishedAt,
    changefreq: 'monthly',
    priority: 0.7,
    images: [{
      loc: video.thumbnail,
      title: video.title,
      caption: `Thumbnail do vídeo: ${video.title}`
    }]
  }));

  return generateSitemapXML(urls, true);
}

// Gerar sitemap de recursos (lead magnets)
export function generateRecursosSitemap(): string {
  const recursos = [
    'planilha-fluxo-caixa',
    'template-prontuario-medico',
    'checklist-convocacao',
    'calculadora-minutagem',
    'kit-mensalidades-pix',
    'controle-estoque-materiais',
    'templates-relatorios'
  ];

  const urls: SitemapUrl[] = recursos.map(recurso => ({
    loc: `https://gamedaynexus.com/recursos/${recurso}`,
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.6
  }));

  return generateSitemapXML(urls);
}

// Função auxiliar para gerar XML do sitemap
function generateSitemapXML(urls: SitemapUrl[], includeImages = false): string {
  const imageNamespace = includeImages ? 
    ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"' : '';
  
  const urlsXML = urls.map(url => {
    let urlXML = `  <url>
    <loc>${url.loc}</loc>`;
    
    if (url.lastmod) {
      urlXML += `
    <lastmod>${url.lastmod}</lastmod>`;
    }
    
    if (url.changefreq) {
      urlXML += `
    <changefreq>${url.changefreq}</changefreq>`;
    }
    
    if (url.priority) {
      urlXML += `
    <priority>${url.priority}</priority>`;
    }
    
    if (url.images && url.images.length > 0) {
      url.images.forEach(image => {
        urlXML += `
    <image:image>
      <image:loc>${image.loc}</image:loc>`;
        
        if (image.title) {
          urlXML += `
      <image:title>${escapeXML(image.title)}</image:title>`;
        }
        
        if (image.caption) {
          urlXML += `
      <image:caption>${escapeXML(image.caption)}</image:caption>`;
        }
        
        urlXML += `
    </image:image>`;
      });
    }
    
    urlXML += `
  </url>`;
    
    return urlXML;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"${imageNamespace}>
${urlsXML}
</urlset>`;
}

// Escapar caracteres especiais para XML
function escapeXML(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

// Função para salvar sitemaps (usar em build ou API route)
export async function generateAllSitemaps(
  blogPosts: BlogPost[],
  videos: VideoContent[]
) {
  const sitemaps = {
    'sitemap.xml': generateSitemapIndex(),
    'sitemap-pages.xml': generatePagesSitemap(),
    'sitemap-blog.xml': generateBlogSitemap(blogPosts),
    'sitemap-videos.xml': generateVideosSitemap(videos),
    'sitemap-recursos.xml': generateRecursosSitemap()
  };

  return sitemaps;
}