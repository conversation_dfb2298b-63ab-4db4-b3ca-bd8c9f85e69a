import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      buffer: "buffer",
    },
  },
  // Polyfills para Node.js APIs no browser
  define: {
    global: 'globalThis',
    'import.meta.env.VITE_BREVO_API_KEY': JSON.stringify(process.env.VITE_BREVO_API_KEY || ''),
    'import.meta.env.VITE_BREVO_SENDER_NAME': JSON.stringify(process.env.VITE_BREVO_SENDER_NAME || 'GameDayNexus'),
    'import.meta.env.VITE_BREVO_SENDER_EMAIL': JSON.stringify(process.env.VITE_BREVO_SENDER_EMAIL || '<EMAIL>'),
  },
  optimizeDeps: {
    include: ['buffer']
  }
}));
