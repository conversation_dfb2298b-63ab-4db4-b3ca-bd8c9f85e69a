import { useEffect } from 'react';
import { useClubSlugStore } from '@/store/useClubSlugStore';

export function useClubFavicon() {
  const currentClub = useClubSlugStore((state) => state.currentClub);

  useEffect(() => {
    const iconUrl = currentClub?.emblem_url || '/favicon.svg';

    // Atualiza todos os links de favicon existentes
    const links = document.querySelectorAll<HTMLLinkElement>("link[rel~='icon']");

    if (links.length > 0) {
      links.forEach((link) => {
        link.href = iconUrl;
      });
    } else {
      // Se nenhum link existir, cria um novo
      const link = document.createElement('link');
      link.rel = 'icon';
      link.href = iconUrl;
      document.head.appendChild(link);
    }

    // Atualiza também o apple-touch-icon, se existir
    const appleLink = document.querySelector<HTMLLinkElement>('link[rel="apple-touch-icon"]');
    if (appleLink) {
      appleLink.href = iconUrl;
    }
  }, [currentClub?.emblem_url]);
}

export default useClubFavicon;