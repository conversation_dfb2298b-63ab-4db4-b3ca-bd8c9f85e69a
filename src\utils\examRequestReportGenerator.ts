import { jsPD<PERSON> } from "jspdf";
import autoTable from "jspdf-autotable";
import { MedicalExam, ClubInfo } from "@/api/api";
import { generateDigitalSignatureText } from "@/utils/digitalSignature";

export async function generateExamRequestReport(
  exam: MedicalExam & { player_name?: string },
  clubInfo: ClubInfo,
  doctorName: string,
  doctorRole: string,
  signatureName: string,
  signatureRole: string,
  filename: string = "solicitacao-exame.pdf"
): Promise<Blob> {
  const doc = new jsPDF() as jsPDF & { autoTable: typeof autoTable };

  const title = `Solicitação de Exame`;
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || "Seu Clube"}`, 14, 30);

  if (clubInfo.logo_url) {
    try {
      const img = new Image();
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, "PNG", 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (err) {
            reject(err);
          }
        };
        img.onerror = reject;
      });
      img.src = clubInfo.logo_url;
      await loadImage;
    } catch (err) {
      console.error("Erro ao adicionar logo ao PDF:", err);
    }
  }

  let y = 40;
  doc.setFontSize(12);
  doc.text(`Paciente: ${exam.player_name || exam.player_id}`, 14, y);
  y += 6;
  doc.text(`Tipo de Exame: ${exam.exam_type}`, 14, y);
  y += 6;
  doc.text(`Data da Solicitação: ${exam.request_date}`, 14, y);
  y += 10;

  if (exam.notes) {
    doc.setFontSize(11);
    doc.text("Solicitação:", 14, y);
    y += 6;
    const splitNotes = doc.splitTextToSize(exam.notes, 180);
    doc.text(splitNotes, 14, y);
    y += splitNotes.length * 5 + 10;
  }

  doc.setFontSize(11);
  doc.text(`Médico Responsável: ${doctorName} - ${doctorRole}`, 14, y);
  y += 6;
  const signatureLines = generateDigitalSignatureText(signatureName, signatureRole).split("\n");
  signatureLines.forEach((line) => {
    doc.text(line, 14, y);
    y += 5;
  });

  const now = new Date();
  doc.setFontSize(8);
  doc.text(
    `Documento gerado em ${now.toLocaleDateString("pt-BR")} às ${now.toLocaleTimeString("pt-BR")}`,
    14,
    285
  );

  return doc.output("blob");
}