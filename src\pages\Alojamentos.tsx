import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Plus, Home, Building, Edit, Trash2, User, Calendar, ChevronLeft } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { usePermission } from "@/hooks/usePermission";
import { PermissionControl } from "@/components/PermissionControl";
import { ACCOMMODATION_PERMISSIONS } from "@/constants/permissions";
import {
  Accommodation,
  PlayerAccommodation,
  CollaboratorAccommodation,
  Player,
  Collaborator,
  HotelRoom,
  getAccommodations,
  createAccommodation,
  updateAccommodation,
  deleteAccommodation,
  getPlayerAccommodations,
  getCollaboratorAccommodations,
  assignPlayerToAccommodation,
  assignCollaboratorToAccommodation,
  removePlayerFromAccommodation,
  removeCollaboratorFromAccommodation,
  getPlayers,
  getCollaborators,
  getHotelRooms,
  createHotelRoom,
  updateHotelRoom,
  deleteHotelRoom,
  getRoomAvailability,
  getAvailableRooms,
  createMultipleRooms
} from "@/api/api";
import { ModuleGuard } from "@/components/guards/ModuleGuard";

export default function Alojamentos() {
  return (
    <ModuleGuard module="accommodations">
      <AlojamentosContent />
    </ModuleGuard>
  );
}

function AlojamentosContent() {
  const navigate = useNavigate();
  const clubId = useCurrentClubId();
  const { can } = usePermission();

  // Estados
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [playerAccommodations, setPlayerAccommodations] = useState<PlayerAccommodation[]>([]);
  const [collaboratorAccommodations, setCollaboratorAccommodations] = useState<CollaboratorAccommodation[]>([]);
  const [players, setPlayers] = useState<Player[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [hotelRooms, setHotelRooms] = useState<HotelRoom[]>([]);
  const [availableRooms, setAvailableRooms] = useState<{
    id: number;
    room_number: string;
    capacity: number;
    occupied: number;
    available: number;
  }[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingRooms, setLoadingRooms] = useState(false);
  const [selectedAccommodation, setSelectedAccommodation] = useState<Accommodation | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [isRoomDialogOpen, setIsRoomDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Formulário para novo alojamento
  const [formData, setFormData] = useState({
    name: "",
    type: "hotel",
    address: "",
    capacity: "",
    cost: "",
    notes: "",
    rooms_count: ""
  });

  // Formulário para associar jogador ou colaborador
  const [assignForm, setAssignForm] = useState({
    personType: "player", // "player" ou "collaborator"
    playerId: "",
    collaboratorId: "",
    roomNumber: "",
    hotelRoomId: 0,
    checkInDate: new Date().toISOString().split("T")[0],
    checkOutDate: "",
    notes: ""
  });

  // Formulário para gerenciar quartos
  const [roomsForm, setRoomsForm] = useState<{
    id?: number;
    room_number: string;
    capacity: number;
  }[]>([]);

  // Função para carregar os quartos de um hotel
  const fetchHotelRooms = useCallback(async (accommodationId: number) => {
    setLoadingRooms(true);
    try {
      const rooms = await getHotelRooms(clubId, accommodationId);
      setHotelRooms(rooms);

      // Carregar disponibilidade dos quartos
      const availableRoomsData = await getAvailableRooms(clubId, accommodationId);
      setAvailableRooms(availableRoomsData);

      setLoadingRooms(false);
    } catch (error) {
      console.error("Erro ao carregar quartos do hotel:", error);
      setHotelRooms([]);
      setAvailableRooms([]);
      setLoadingRooms(false);
    }
  }, [clubId]);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [accommodationsData, playersData, collaboratorsData] = await Promise.all([
        getAccommodations(clubId),
        getPlayers(clubId, undefined, { includeInactive: false, includeLoaned: false }),
        getCollaborators(clubId)
      ]);

      setAccommodations(accommodationsData);
      setPlayers(playersData);
      setCollaborators(collaboratorsData.filter(c => c.status !== 'inactive'));

      if (accommodationsData.length > 0) {
        const firstAccommodation = accommodationsData[0];
        setSelectedAccommodation(firstAccommodation);

        const [playerAccommodationsData, collaboratorAccommodationsData] = await Promise.all([
          getPlayerAccommodations(clubId),
          getCollaboratorAccommodations(clubId)
        ]);

        setPlayerAccommodations(playerAccommodationsData);
        setCollaboratorAccommodations(collaboratorAccommodationsData);

        // Se o primeiro alojamento for um hotel, carregar os quartos
        if (firstAccommodation.type === "hotel") {
          await fetchHotelRooms(firstAccommodation.id);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados dos alojamentos.",
        variant: "destructive"
      });
      setLoading(false);
    }
  }, [clubId, fetchHotelRooms]);

  // Carregar dados
  useEffect(() => {
    if (clubId) {
      fetchData();
    }
  }, [clubId, fetchData]);

  // Carregar quartos quando o alojamento selecionado mudar
  useEffect(() => {
    if (selectedAccommodation && selectedAccommodation.type === "hotel") {
      fetchHotelRooms(selectedAccommodation.id);
    } else {
      // Limpar os quartos se não for um hotel
      setHotelRooms([]);
      setAvailableRooms([]);
    }
  }, [selectedAccommodation, fetchHotelRooms]);


  // Filtrar alojamentos com base no termo de pesquisa
  const filteredAccommodations = accommodations.filter(accommodation =>
    accommodation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    accommodation.address?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    accommodation.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filtrar jogadores e colaboradores associados ao alojamento selecionado
  const accommodationPlayers = playerAccommodations.filter(
    pa => pa.accommodation_id === selectedAccommodation?.id
  );

  const accommodationCollaborators = collaboratorAccommodations.filter(
    ca => ca.accommodation_id === selectedAccommodation?.id
  );

  const totalGuests = accommodationPlayers.length + accommodationCollaborators.length;

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Voltar
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">Alojamentos</h1>
        </div>
        <PermissionControl permission={ACCOMMODATION_PERMISSIONS.CREATE}>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-1" />
            Novo Alojamento
          </Button>
        </PermissionControl>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Coluna da esquerda - Lista de alojamentos */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Lista de Alojamentos</CardTitle>
            <div className="mt-2">
              <Input
                placeholder="Pesquisar alojamentos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-2"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p>Carregando alojamentos...</p>
            ) : filteredAccommodations.length === 0 ? (
              <p>Nenhum alojamento encontrado.</p>
            ) : (
              <div className="space-y-2">
                {filteredAccommodations.map((accommodation) => (
                  <div
                    key={accommodation.id}
                    className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 flex justify-between items-center ${
                      selectedAccommodation?.id === accommodation.id ? "bg-gray-100" : ""
                    }`}
                    onClick={() => setSelectedAccommodation(accommodation)}
                  >
                    <div>
                      <div className="font-medium">{accommodation.name}</div>
                      <div className="text-sm text-gray-500">
                        {accommodation.type === "hotel" ? "Hotel" : "Apartamento"}
                      </div>
                    </div>
                    <Badge variant="outline">
                      {accommodation.capacity || "?"} vagas
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Coluna da direita - Detalhes do alojamento */}
        <Card className="md:col-span-2">
          {!selectedAccommodation ? (
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">
                Selecione um alojamento para ver os detalhes
              </p>
            </CardContent>
          ) : (
            <>
              <CardHeader className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div>
                  <CardTitle>{selectedAccommodation.name}</CardTitle>
                  <div className="text-sm text-gray-500 mt-1">
                    {selectedAccommodation.type === "hotel" ? (
                      <div className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Hotel
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        Apartamento
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <PermissionControl permission={ACCOMMODATION_PERMISSIONS.EDIT}>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Atualizar o formulário com os dados do alojamento
                        setFormData({
                          name: selectedAccommodation.name,
                        type: selectedAccommodation.type,
                        address: selectedAccommodation.address || "",
                        capacity: selectedAccommodation.capacity?.toString() || "",
                        cost: selectedAccommodation.cost?.toString() || "",
                        notes: selectedAccommodation.notes || "",
                        rooms_count: selectedAccommodation.rooms_count?.toString() || ""
                      });

                      // Se for um hotel, carregar os quartos para edição
                      if (selectedAccommodation.type === "hotel") {
                        // Buscar os quartos do hotel
                        getHotelRooms(clubId, selectedAccommodation.id)
                          .then(rooms => {
                            if (rooms.length > 0) {
                              setRoomsForm(rooms.map(room => ({
                                id: room.id,
                                room_number: room.room_number,
                                capacity: room.capacity
                              })));
                            } else {
                              // Se não tiver quartos, inicializar com um quarto vazio
                              setRoomsForm([{ room_number: "", capacity: 1 }]);
                            }
                          })
                          .catch(error => {
                            console.error("Erro ao carregar quartos para edição:", error);
                            setRoomsForm([{ room_number: "", capacity: 1 }]);
                          });
                      } else {
                        // Limpar o formulário de quartos se não for hotel
                        setRoomsForm([]);
                      }
                      setIsEditDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Editar
                    </Button>
                  </PermissionControl>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                    onClick={() => {
                      setAssignForm({
                        personType: "player",
                        playerId: "",
                        collaboratorId: "",
                        roomNumber: "",
                        hotelRoomId: 0,
                        checkInDate: new Date().toISOString().split("T")[0],
                        checkOutDate: "",
                        notes: ""
                      });
                      setIsAssignDialogOpen(true);
                    }}
                  >
                    <User className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    <span className="hidden sm:inline">Adicionar Hóspede</span>
                    <span className="sm:hidden">Adicionar</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="info">
                  <TabsList className="mb-4">
                    <TabsTrigger value="info">Informações</TabsTrigger>
                    <TabsTrigger value="guests">Hóspedes ({totalGuests})</TabsTrigger>
                  </TabsList>

                  <TabsContent value="info">
                    <div className="space-y-4">
                      {selectedAccommodation.address && (
                        <div>
                          <h3 className="font-medium">Endereço</h3>
                          <p className="text-gray-600">{selectedAccommodation.address}</p>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h3 className="font-medium">Capacidade</h3>
                          <p className="text-gray-600">{selectedAccommodation.capacity || "Não informada"}</p>
                        </div>
                        <div>
                          <h3 className="font-medium">Custo</h3>
                          <p className="text-gray-600">
                            {selectedAccommodation.cost
                              ? `R$ ${parseFloat(selectedAccommodation.cost.toString()).toFixed(2)}`
                              : "Não informado"}
                          </p>
                        </div>
                      </div>

                      {selectedAccommodation.type === "hotel" && (
                        <div>
                          <h3 className="font-medium">Quartos</h3>
                          {loadingRooms ? (
                            <p className="text-gray-600">Carregando informações dos quartos...</p>
                          ) : hotelRooms.length === 0 ? (
                            <p className="text-gray-600">Nenhum quarto cadastrado</p>
                          ) : (
                            <div className="mt-2 border rounded-md overflow-hidden">
                              <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Quarto
                                    </th>
                                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Capacidade
                                    </th>
                                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Ocupação
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {availableRooms.map((room) => (
                                    <tr key={room.id}>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {room.room_number}
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        {room.capacity} {room.capacity > 1 ? "pessoas" : "pessoa"}
                                      </td>
                                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                          room.available === 0
                                            ? "bg-red-100 text-red-800"
                                            : room.available < room.capacity / 2
                                              ? "bg-yellow-100 text-yellow-800"
                                              : "bg-green-100 text-green-800"
                                        }`}>
                                          {room.occupied}/{room.capacity} ({room.available} {room.available === 1 ? "vaga" : "vagas"})
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          )}
                        </div>
                      )}

                      {selectedAccommodation.type === "apartment" && (
                        <div>
                          <h3 className="font-medium">Apartamento</h3>
                          <div className="mt-2 border rounded-md overflow-hidden">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quarto
                                  </th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Capacidade
                                  </th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ocupação
                                  </th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                <tr>
                                  <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                    01
                                  </td>
                                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                    {selectedAccommodation.capacity || 0} {(selectedAccommodation.capacity || 0) > 1 ? "pessoas" : "pessoa"}
                                  </td>
                                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                      totalGuests >= (selectedAccommodation.capacity || 0)
                                        ? "bg-red-100 text-red-800"
                                        : totalGuests >= (selectedAccommodation.capacity || 0) / 2
                                          ? "bg-yellow-100 text-yellow-800"
                                          : "bg-green-100 text-green-800"
                                    }`}>
                                      {totalGuests}/{selectedAccommodation.capacity || 0} ({(selectedAccommodation.capacity || 0) - totalGuests} {((selectedAccommodation.capacity || 0) - totalGuests) === 1 ? "vaga" : "vagas"})
                                    </span>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {selectedAccommodation.notes && (
                        <div>
                          <h3 className="font-medium">Observações</h3>
                          <p className="text-gray-600">{selectedAccommodation.notes}</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="guests">
                    {totalGuests === 0 ? (
                      <p className="text-center text-gray-500 py-4">
                        Nenhum hóspede associado a este alojamento
                      </p>
                    ) : (
                      <div className="space-y-6">
                        {/* Seção de Jogadores */}
                        {accommodationPlayers.length > 0 && (
                          <div>
                            <h3 className="text-lg font-medium mb-3">Jogadores ({accommodationPlayers.length})</h3>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Nome</TableHead>
                                  <TableHead>Apelido</TableHead>
                                  <TableHead>Quarto</TableHead>
                                  <TableHead>Entrada no Clube</TableHead>
                                  <TableHead>Check-in</TableHead>
                                  <TableHead>Check-out</TableHead>
                                  <TableHead>Ações</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {accommodationPlayers.map((pa) => (
                                  <TableRow key={`player-${pa.id}`}>
                                    <TableCell className="font-medium">{pa.player_name}</TableCell>
                                    <TableCell>{pa.player_nickname || "-"}</TableCell>
                                    <TableCell>{pa.room_number || "-"}</TableCell>
                                    <TableCell>
                                      {pa.player_entry_date
                                        ? new Date(pa.player_entry_date).toLocaleDateString()
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      {pa.check_in_date
                                        ? new Date(pa.check_in_date).toLocaleDateString()
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      {pa.check_out_date
                                        ? new Date(pa.check_out_date).toLocaleDateString()
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={async () => {
                                          if (confirm("Tem certeza que deseja remover este jogador do alojamento?")) {
                                            try {
                                              await removePlayerFromAccommodation(clubId, pa.id);
                                              toast({
                                                title: "Sucesso",
                                                description: "Jogador removido do alojamento com sucesso.",
                                              });
                                              fetchData();
                                            } catch (error) {
                                              console.error("Erro ao remover jogador:", error);
                                              toast({
                                                title: "Erro",
                                                description: "Não foi possível remover o jogador do alojamento.",
                                                variant: "destructive"
                                              });
                                            }
                                          }
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        )}

                        {/* Seção de Colaboradores */}
                        {accommodationCollaborators.length > 0 && (
                          <div>
                            <h3 className="text-lg font-medium mb-3">Colaboradores ({accommodationCollaborators.length})</h3>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Nome</TableHead>
                                  <TableHead>Função</TableHead>
                                  <TableHead>Quarto</TableHead>
                                  <TableHead>Check-in</TableHead>
                                  <TableHead>Check-out</TableHead>
                                  <TableHead>Ações</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {accommodationCollaborators.map((ca) => (
                                  <TableRow key={`collaborator-${ca.id}`}>
                                    <TableCell className="font-medium">{ca.collaborator_name}</TableCell>
                                    <TableCell>{ca.collaborator_role || "-"}</TableCell>
                                    <TableCell>{ca.room_number || "-"}</TableCell>
                                    <TableCell>
                                      {ca.check_in_date
                                        ? new Date(ca.check_in_date).toLocaleDateString()
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      {ca.check_out_date
                                        ? new Date(ca.check_out_date).toLocaleDateString()
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={async () => {
                                          if (confirm("Tem certeza que deseja remover este colaborador do alojamento?")) {
                                            try {
                                              await removeCollaboratorFromAccommodation(clubId, ca.collaborator_id!, ca.accommodation_id!);
                                              toast({
                                                title: "Sucesso",
                                                description: "Colaborador removido do alojamento com sucesso.",
                                              });
                                              fetchData();
                                            } catch (error) {
                                              console.error("Erro ao remover colaborador:", error);
                                              toast({
                                                title: "Erro",
                                                description: "Não foi possível remover o colaborador do alojamento.",
                                                variant: "destructive"
                                              });
                                            }
                                          }
                                        }}
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </>
          )}
        </Card>
      </div>

      {/* Diálogo para adicionar novo alojamento */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Novo Alojamento</DialogTitle>
            <DialogDescription>
              Preencha os dados do novo alojamento abaixo.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nome *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome do alojamento"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  setFormData({
                    ...formData,
                    type: value,
                    // Limpar campos específicos de hotel se mudar para apartamento
                    rooms_count: value === "apartment" ? "" : formData.rooms_count
                  });

                  // Inicializar o formulário de quartos se for hotel
                  if (value === "hotel") {
                    setRoomsForm([{ room_number: "", capacity: 1 }]);
                  } else {
                    setRoomsForm([]);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hotel">Hotel</SelectItem>
                  <SelectItem value="apartment">Apartamento</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                placeholder="Endereço completo"
              />
            </div>

            {formData.type === "hotel" ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="rooms_count">Número de Quartos</Label>
                  <Input
                    id="rooms_count"
                    type="number"
                    min="1"
                    value={formData.rooms_count}
                    onChange={(e) => {
                      const count = parseInt(e.target.value) || 0;
                      setFormData({ ...formData, rooms_count: e.target.value });

                      // Atualizar o formulário de quartos com base no número informado
                      if (count > 0) {
                        const newRoomsForm = Array.from({ length: count }, (_, index) => {
                          // Manter os quartos já definidos
                          if (index < roomsForm.length) {
                            return roomsForm[index];
                          }
                          // Adicionar novos quartos
                          return {
                            room_number: `${index + 1}`.padStart(2, '0'),
                            capacity: 1
                          };
                        });
                        setRoomsForm(newRoomsForm);
                      } else {
                        setRoomsForm([]);
                      }
                    }}
                    placeholder="Quantidade de quartos"
                  />
                </div>

                {roomsForm.length > 0 && (
                  <div className="border rounded-md p-3 space-y-3">
                    <h4 className="font-medium text-sm">Detalhes dos Quartos</h4>

                    <div className="max-h-40 overflow-y-auto space-y-3">
                      {roomsForm.map((room, index) => (
                        <div key={index} className="grid grid-cols-2 gap-2">
                          <div>
                            <Label htmlFor={`room-number-${index}`} className="text-xs">
                              Quarto #{index + 1}
                            </Label>
                            <Input
                              id={`room-number-${index}`}
                              value={room.room_number}
                              onChange={(e) => {
                                const newRoomsForm = [...roomsForm];
                                newRoomsForm[index].room_number = e.target.value;
                                setRoomsForm(newRoomsForm);
                              }}
                              placeholder="Número"
                              className="h-8"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`room-capacity-${index}`} className="text-xs">
                              Capacidade
                            </Label>
                            <Input
                              id={`room-capacity-${index}`}
                              type="number"
                              min="1"
                              value={room.capacity}
                              onChange={(e) => {
                                const newRoomsForm = [...roomsForm];
                                newRoomsForm[index].capacity = parseInt(e.target.value) || 1;
                                setRoomsForm(newRoomsForm);
                              }}
                              placeholder="Vagas"
                              className="h-8"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="capacity">Capacidade</Label>
                  <Input
                    id="capacity"
                    type="number"
                    min="1"
                    value={formData.capacity}
                    onChange={(e) => setFormData({ ...formData, capacity: e.target.value })}
                    placeholder="Número de vagas"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cost">Custo (R$)</Label>
                  <Input
                    id="cost"
                    type="number"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) => setFormData({ ...formData, cost: e.target.value })}
                    placeholder="Valor mensal"
                  />
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="notes">Observações</Label>
              <Input
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Informações adicionais"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={async () => {
                if (!can(ACCOMMODATION_PERMISSIONS.CREATE)) {
                  toast({
                    title: "Permissão negada",
                    description: "Você não pode criar alojamentos",
                    variant: "destructive"
                  });
                  return;
                }
                if (!formData.name) {
                  toast({
                    title: "Erro",
                    description: "O nome do alojamento é obrigatório.",
                    variant: "destructive"
                  });
                  return;
                }

                try {
                  // Preparar os dados do alojamento
                  const accommodationData = {
                    name: formData.name,
                    type: formData.type,
                    address: formData.address,
                    // Se for hotel, a capacidade é a soma das capacidades dos quartos
                    capacity: formData.type === "hotel"
                      ? roomsForm.reduce((sum, room) => sum + room.capacity, 0)
                      : formData.capacity ? parseInt(formData.capacity) : null,
                    cost: formData.cost ? parseFloat(formData.cost) : null,
                    notes: formData.notes,
                    rooms_count: formData.type === "hotel" && formData.rooms_count
                      ? parseInt(formData.rooms_count)
                      : null
                  };

                  // Criar o alojamento com os quartos, se for um hotel
                  if (formData.type === "hotel" && roomsForm.length > 0) {
                    await createAccommodation(clubId, accommodationData, roomsForm);
                  } else {
                    await createAccommodation(clubId, accommodationData);
                  }

                  toast({
                    title: "Sucesso",
                    description: "Alojamento criado com sucesso.",
                  });

                  setIsAddDialogOpen(false);
                  fetchData();

                  // Limpar formulário
                  setFormData({
                    name: "",
                    type: "hotel",
                    address: "",
                    capacity: "",
                    cost: "",
                    notes: "",
                    rooms_count: ""
                  });

                  // Limpar formulário de quartos
                  setRoomsForm([]);
                } catch (error) {
                  console.error("Erro ao criar alojamento:", error);
                  toast({
                    title: "Erro",
                    description: "Não foi possível criar o alojamento.",
                    variant: "destructive"
                  });
                }
              }}
            >
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar alojamento */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Alojamento</DialogTitle>
            <DialogDescription>
              Atualize os dados do alojamento abaixo.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nome *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome do alojamento"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-type">Tipo *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  setFormData({
                    ...formData,
                    type: value,
                    // Limpar campos específicos de hotel se mudar para apartamento
                    rooms_count: value === "apartment" ? "" : formData.rooms_count
                  });

                  // Inicializar o formulário de quartos se for hotel
                  if (value === "hotel" && roomsForm.length === 0) {
                    setRoomsForm([{ room_number: "", capacity: 1 }]);
                  } else if (value === "apartment") {
                    setRoomsForm([]);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hotel">Hotel</SelectItem>
                  <SelectItem value="apartment">Apartamento</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-address">Endereço</Label>
              <Input
                id="edit-address"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                placeholder="Endereço completo"
              />
            </div>

            {formData.type === "hotel" ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-rooms-count">Número de Quartos</Label>
                  <Input
                    id="edit-rooms-count"
                    type="number"
                    min="1"
                    value={formData.rooms_count}
                    onChange={(e) => {
                      const count = parseInt(e.target.value) || 0;
                      setFormData({ ...formData, rooms_count: e.target.value });

                      // Atualizar o formulário de quartos com base no número informado
                      if (count > 0) {
                        const newRoomsForm = Array.from({ length: count }, (_, index) => {
                          // Manter os quartos já definidos
                          if (index < roomsForm.length) {
                            return roomsForm[index];
                          }
                          // Adicionar novos quartos
                          return {
                            room_number: `${index + 1}`.padStart(2, '0'),
                            capacity: 1
                          };
                        });
                        setRoomsForm(newRoomsForm);
                      } else {
                        setRoomsForm([]);
                      }
                    }}
                    placeholder="Quantidade de quartos"
                  />
                </div>

                {roomsForm.length > 0 && (
                  <div className="border rounded-md p-3 space-y-3">
                    <h4 className="font-medium text-sm">Detalhes dos Quartos</h4>

                    <div className="max-h-40 overflow-y-auto space-y-3">
                      {roomsForm.map((room, index) => (
                        <div key={index} className="grid grid-cols-2 gap-2">
                          <div>
                            <Label htmlFor={`edit-room-number-${index}`} className="text-xs">
                              Quarto #{index + 1}
                            </Label>
                            <Input
                              id={`edit-room-number-${index}`}
                              value={room.room_number}
                              onChange={(e) => {
                                const newRoomsForm = [...roomsForm];
                                newRoomsForm[index].room_number = e.target.value;
                                setRoomsForm(newRoomsForm);
                              }}
                              placeholder="Número"
                              className="h-8"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`edit-room-capacity-${index}`} className="text-xs">
                              Capacidade
                            </Label>
                            <Input
                              id={`edit-room-capacity-${index}`}
                              type="number"
                              min="1"
                              value={room.capacity}
                              onChange={(e) => {
                                const newRoomsForm = [...roomsForm];
                                newRoomsForm[index].capacity = parseInt(e.target.value) || 1;
                                setRoomsForm(newRoomsForm);
                              }}
                              placeholder="Vagas"
                              className="h-8"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-capacity">Capacidade</Label>
                  <Input
                    id="edit-capacity"
                    type="number"
                    min="1"
                    value={formData.capacity}
                    onChange={(e) => setFormData({ ...formData, capacity: e.target.value })}
                    placeholder="Número de vagas"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-cost">Custo (R$)</Label>
                  <Input
                    id="edit-cost"
                    type="number"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) => setFormData({ ...formData, cost: e.target.value })}
                    placeholder="Valor mensal"
                  />
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="edit-notes">Observações</Label>
              <Input
                id="edit-notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Informações adicionais"
              />
            </div>

            <div className="pt-2">
              <PermissionControl permission={ACCOMMODATION_PERMISSIONS.DELETE}>
                <Button
                  variant="destructive"
                  size="sm"
                  className="w-full"
                  onClick={async () => {
                    if (!can(ACCOMMODATION_PERMISSIONS.DELETE)) {
                      toast({
                        title: "Permissão negada",
                        description: "Você não pode excluir alojamentos",
                        variant: "destructive"
                      });
                      return;
                    }
                    if (confirm("Tem certeza que deseja excluir este alojamento? Esta ação não pode ser desfeita. Se houver jogadores alojados, eles serão desassociados automaticamente.")) {
                      try {
                        const result = await deleteAccommodation(clubId, selectedAccommodation!.id);

                      let message = "Alojamento excluído com sucesso.";
                      if (result.playersRemoved > 0) {
                        message += ` ${result.playersRemoved} jogador(es) foram desassociados.`;
                      }

                      toast({
                        title: "Sucesso",
                        description: message,
                      });

                      setIsEditDialogOpen(false);
                      fetchData();
                      setSelectedAccommodation(null);
                    } catch (error) {
                      console.error("Erro ao excluir alojamento:", error);
                      toast({
                        title: "Erro",
                        description: "Não foi possível excluir o alojamento.",
                        variant: "destructive"
                      });
                    }
                  }
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Excluir Alojamento
                </Button>
              </PermissionControl>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={async () => {
                if (!can(ACCOMMODATION_PERMISSIONS.EDIT)) {
                  toast({
                    title: "Permissão negada",
                    description: "Você não pode editar alojamentos",
                    variant: "destructive"
                  });
                  return;
                }
                if (!formData.name) {
                  toast({
                    title: "Erro",
                    description: "O nome do alojamento é obrigatório.",
                    variant: "destructive"
                  });
                  return;
                }

                try {
                  // Preparar os dados do alojamento
                  const accommodationData = {
                    name: formData.name,
                    type: formData.type,
                    address: formData.address,
                    // Se for hotel, a capacidade é a soma das capacidades dos quartos
                    capacity: formData.type === "hotel"
                      ? roomsForm.reduce((sum, room) => sum + room.capacity, 0)
                      : formData.capacity ? parseInt(formData.capacity) : null,
                    cost: formData.cost ? parseFloat(formData.cost) : null,
                    notes: formData.notes,
                    rooms_count: formData.type === "hotel" && formData.rooms_count
                      ? parseInt(formData.rooms_count)
                      : null
                  };

                  // Atualizar o alojamento com os quartos, se for um hotel
                  if (formData.type === "hotel" && roomsForm.length > 0) {
                    await updateAccommodation(clubId, selectedAccommodation!.id, accommodationData, roomsForm);
                  } else {
                    await updateAccommodation(clubId, selectedAccommodation!.id, accommodationData);
                  }

                  toast({
                    title: "Sucesso",
                    description: "Alojamento atualizado com sucesso.",
                  });

                  setIsEditDialogOpen(false);
                  fetchData();
                } catch (error) {
                  console.error("Erro ao atualizar alojamento:", error);
                  toast({
                    title: "Erro",
                    description: "Não foi possível atualizar o alojamento.",
                    variant: "destructive"
                  });
                }
              }}
            >
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para associar hóspede ao alojamento */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Hóspede ao Alojamento</DialogTitle>
            <DialogDescription>
              Selecione um hóspede e preencha os detalhes da hospedagem.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="personType">Tipo de Hóspede *</Label>
              <Select
                value={assignForm.personType}
                onValueChange={(value) => setAssignForm({
                  ...assignForm,
                  personType: value,
                  playerId: "",
                  collaboratorId: ""
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="player">Jogador</SelectItem>
                  <SelectItem value="collaborator">Colaborador</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="person">Hóspede *</Label>
              {assignForm.personType === "player" ? (
                <Select
                  value={assignForm.playerId}
                  onValueChange={(value) => setAssignForm({ ...assignForm, playerId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um jogador" />
                  </SelectTrigger>
                  <SelectContent>
                    {players
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((player) => (
                        <SelectItem key={player.id} value={player.id}>
                          {player.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              ) : (
                <Select
                  value={assignForm.collaboratorId}
                  onValueChange={(value) => setAssignForm({ ...assignForm, collaboratorId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um colaborador" />
                  </SelectTrigger>
                  <SelectContent>
                    {collaborators
                      .sort((a, b) => a.full_name.localeCompare(b.full_name))
                      .map((collaborator) => (
                        <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                          {collaborator.full_name} - {collaborator.role}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {selectedAccommodation?.type === "hotel" && availableRooms.length > 0 ? (
              <div className="space-y-2">
                <Label htmlFor="hotel-room">Quarto</Label>
                <Select
                  value={assignForm.hotelRoomId ? assignForm.hotelRoomId.toString() : ""}
                  onValueChange={(value) => {
                    const roomId = parseInt(value);
                    const selectedRoom = availableRooms.find(r => r.id === roomId);

                    setAssignForm({
                      ...assignForm,
                      hotelRoomId: roomId,
                      // Também atualizar o número do quarto para manter compatibilidade
                      roomNumber: selectedRoom ? selectedRoom.room_number : ""
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um quarto" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRooms
                      .filter(room => room.available > 0) // Mostrar apenas quartos com vagas
                      .map((room) => (
                        <SelectItem key={room.id} value={room.id.toString()}>
                          Quarto {room.room_number} - {room.available} {room.available === 1 ? "vaga" : "vagas"} disponível
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="room">Número do Quarto</Label>
                <Input
                  id="room"
                  value={assignForm.roomNumber}
                  onChange={(e) => setAssignForm({ ...assignForm, roomNumber: e.target.value })}
                  placeholder="Ex: 101"
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="checkin">Data de Check-in *</Label>
                <Input
                  id="checkin"
                  type="date"
                  value={assignForm.checkInDate}
                  onChange={(e) => setAssignForm({ ...assignForm, checkInDate: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="checkout">Data de Check-out</Label>
                <Input
                  id="checkout"
                  type="date"
                  value={assignForm.checkOutDate}
                  onChange={(e) => setAssignForm({ ...assignForm, checkOutDate: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="assign-notes">Observações</Label>
              <Input
                id="assign-notes"
                value={assignForm.notes}
                onChange={(e) => setAssignForm({ ...assignForm, notes: e.target.value })}
                placeholder="Informações adicionais"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={async () => {
                // Validar se um hóspede foi selecionado
                const hasSelectedPerson = assignForm.personType === "player"
                  ? assignForm.playerId
                  : assignForm.collaboratorId;

                if (!hasSelectedPerson) {
                  toast({
                    title: "Erro",
                    description: `Selecione um ${assignForm.personType === "player" ? "jogador" : "colaborador"}.`,
                    variant: "destructive"
                  });
                  return;
                }

                if (!assignForm.checkInDate) {
                  toast({
                    title: "Erro",
                    description: "A data de check-in é obrigatória.",
                    variant: "destructive"
                  });
                  return;
                }

                try {
                  if (assignForm.personType === "player") {
                    await assignPlayerToAccommodation(
                      clubId,
                      assignForm.playerId,
                      selectedAccommodation!.id,
                      {
                        room_number: assignForm.roomNumber,
                        hotel_room_id: assignForm.hotelRoomId || undefined,
                        check_in_date: assignForm.checkInDate,
                        check_out_date: assignForm.checkOutDate || undefined,
                        status: "active",
                        notes: assignForm.notes
                      }
                    );
                  } else {
                    await assignCollaboratorToAccommodation(
                      clubId,
                      parseInt(assignForm.collaboratorId),
                      selectedAccommodation!.id,
                      {
                        room_number: assignForm.roomNumber,
                        hotel_room_id: assignForm.hotelRoomId || undefined,
                        check_in_date: assignForm.checkInDate,
                        check_out_date: assignForm.checkOutDate || undefined,
                        status: "active",
                        notes: assignForm.notes
                      }
                    );
                  }

                  toast({
                    title: "Sucesso",
                    description: `${assignForm.personType === "player" ? "Jogador" : "Colaborador"} adicionado ao alojamento com sucesso.`,
                  });

                  setIsAssignDialogOpen(false);
                  fetchData();

                  // Limpar formulário
                  setAssignForm({
                    personType: "player",
                    playerId: "",
                    collaboratorId: "",
                    roomNumber: "",
                    hotelRoomId: 0,
                    checkInDate: new Date().toISOString().split("T")[0],
                    checkOutDate: "",
                    notes: ""
                  });

                  // Recarregar a disponibilidade dos quartos
                  if (selectedAccommodation?.type === "hotel") {
                    fetchHotelRooms(selectedAccommodation.id);
                  }
                } catch (error) {
                  console.error("Erro ao associar hóspede:", error);
                  toast({
                    title: "Erro",
                    description: `Não foi possível adicionar o ${assignForm.personType === "player" ? "jogador" : "colaborador"} ao alojamento.`,
                    variant: "destructive"
                  });
                }
              }}
            >
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
