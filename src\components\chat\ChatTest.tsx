import { useState } from 'react';
import { useSocketChatTest } from '@/hooks/useSocketChatTest';

export function ChatTest() {
  const { isConnected, messages, sendTestMessage } = useSocketChatTest();
  const [inputMessage, setInputMessage] = useState('');

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      sendTestMessage(inputMessage);
      setInputMessage('');
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Teste de Conexão Socket.IO</h3>
      
      <div className="mb-4">
        <div className={`inline-flex items-center px-2 py-1 rounded text-sm ${
          isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            isConnected ? 'bg-green-500' : 'bg-red-500'
          }`} />
          {isConnected ? 'Conectado' : 'Desconectado'}
        </div>
      </div>

      <div className="mb-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Digite uma mensagem de teste..."
            className="flex-1 px-3 py-2 border rounded-md"
            disabled={!isConnected}
          />
          <button
            onClick={handleSendMessage}
            disabled={!isConnected || !inputMessage.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md disabled:bg-gray-300"
          >
            Enviar
          </button>
        </div>
      </div>

      <div className="border rounded-md p-3 h-64 overflow-y-auto bg-gray-50">
        <h4 className="font-medium mb-2">Log de Mensagens:</h4>
        {messages.length === 0 ? (
          <p className="text-gray-500 text-sm">Nenhuma mensagem ainda...</p>
        ) : (
          <div className="space-y-1">
            {messages.map((message, index) => (
              <div key={index} className="text-sm font-mono">
                <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> {message}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}