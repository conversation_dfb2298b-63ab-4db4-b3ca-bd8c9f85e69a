import { create } from 'zustand';
import { chatApi } from '@/api/chat-simple';
import type { ChatState, ChatRoom, ChatMessage, UserPresence } from '@/types/chat';

interface ChatActions {
  // Ações básicas
  setCurrentRoom: (room: ChatRoom | null) => void;
  setError: (error: string | null) => void;
  setIsConnected: (connected: boolean) => void;
  
  // Carregar dados
  loadRooms: (clubId: string) => Promise<void>;
  loadMessages: (roomId: string, clubId: string) => Promise<void>;
  loadOnlineUsers: (clubId: string) => Promise<void>;
  
  // Mensagens (Socket.IO)
  addMessage: (roomId: string, message: ChatMessage) => void;
  editMessage: (messageId: string, content: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  
  // Salas (Socket.IO)
  addRoom: (room: ChatRoom) => void;
  leaveRoom: (roomId: string) => Promise<void>;
  
  // Presença (Socket.IO)
  updateUserPresence: (presence: UserPresence) => void;
  setOnlineUsers: (users: UserPresence[]) => void;

  // Limpeza
  cleanup: () => void;
}

export const useChatStore = create<ChatState & ChatActions>((set, get) => ({
  // Estado inicial
  rooms: [],
  currentRoom: null,
  messages: {},
  participants: {},
  onlineUsers: [],
  isLoading: false,
  error: null,

  // Ações básicas
  setCurrentRoom: (room) => set({ currentRoom: room }),
  setError: (error) => set({ error }),
  setIsConnected: (connected) => set({ isConnected: connected }),

  // Carregar salas
  loadRooms: async (clubId: string) => {
    set({ isLoading: true, error: null });
    try {
      const rooms = await chatApi.getRooms(clubId);
      set({ rooms, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  // Carregar mensagens
  loadMessages: async (roomId: string, clubId: string) => {
    set({ isLoading: true, error: null });
    try {
      const messages = await chatApi.getMessages(roomId, clubId);
      set(state => ({
        messages: {
          ...state.messages,
          [roomId]: messages
        },
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  // Carregar usuários online
  loadOnlineUsers: async (clubId: string) => {
    try {
      const users = await chatApi.getOnlineUsers(clubId);
      set({ onlineUsers: users });
    } catch (error) {
      console.error('Erro ao carregar usuários online:', error);
    }
  },

  // Adicionar mensagem (via Socket.IO)
  addMessage: (roomId: string, message: ChatMessage) => {
    set(state => {
      const roomMessages = state.messages[roomId] || [];
      // Evitar duplicatas
      const exists = roomMessages.some(msg => msg.id === message.id);
      if (exists) return state;
      
      return {
        messages: {
          ...state.messages,
          [roomId]: [...roomMessages, message]
        }
      };
    });
  },

  // Editar mensagem
  editMessage: async (messageId: string, content: string) => {
    try {
      await chatApi.editMessage(messageId, content);
      
      // Atualizar localmente
      set(state => {
        const newMessages = { ...state.messages };
        Object.keys(newMessages).forEach(roomId => {
          newMessages[roomId] = newMessages[roomId].map(msg =>
            msg.id === messageId 
              ? { ...msg, content, edited_at: new Date().toISOString() }
              : msg
          );
        });
        return { messages: newMessages };
      });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  // Deletar mensagem
  deleteMessage: async (messageId: string) => {
    try {
      await chatApi.deleteMessage(messageId);
      
      // Remover localmente
      set(state => {
        const newMessages = { ...state.messages };
        Object.keys(newMessages).forEach(roomId => {
          newMessages[roomId] = newMessages[roomId].filter(msg => msg.id !== messageId);
        });
        return { messages: newMessages };
      });
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  // Adicionar sala (via Socket.IO)
  addRoom: (room: ChatRoom) => {
    set(state => {
      // Evitar duplicatas
      const exists = state.rooms.some(r => r.id === room.id);
      if (exists) return state;
      
      return {
        rooms: [room, ...state.rooms]
      };
    });
  },

  // Sair da sala
  leaveRoom: async (roomId: string) => {
    try {
      await chatApi.leaveRoom(roomId);
      
      // Remover da lista local
      set(state => ({
        rooms: state.rooms.filter(room => room.id !== roomId),
        currentRoom: state.currentRoom?.id === roomId ? null : state.currentRoom
      }));
    } catch (error) {
      set({ error: (error as Error).message });
    }
  },

  // Atualizar presença de usuário específico (via Socket.IO)
  updateUserPresence: (presence: UserPresence) => {
    console.log('[ChatStore] updateUserPresence', presence);
    set(state => {
      const exists = state.onlineUsers.some(u => u.user_id === presence.user_id);
      if (exists) {
        return {
          onlineUsers: state.onlineUsers.map(u =>
            u.user_id === presence.user_id ? { ...u, ...presence } : u
          )
        };
      }
      return {
        onlineUsers: [...state.onlineUsers, presence]
      };
    });
  },

  // Definir lista completa de usuários online (via Socket.IO)
  setOnlineUsers: (users: UserPresence[]) => {
    console.log('[ChatStore] setOnlineUsers', users);
    set({ onlineUsers: users });
  },

  // Limpeza
  cleanup: () => {
    set({
      rooms: [],
      currentRoom: null,
      messages: {},
      participants: {},
      onlineUsers: [],
      isLoading: false,
      error: null
    });
  }
}));