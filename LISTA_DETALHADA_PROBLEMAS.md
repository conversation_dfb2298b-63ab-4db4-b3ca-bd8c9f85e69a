# Lista Detalhada de Problemas Encontrados - Sistema de Treinamentos

## Problemas Críticos (Impedem uso básico)

### 1. Sistema de Desenho de Trajetórias - TrajectorySystem
**Arquivo**: `src/components/training/TrajectorySystem.tsx`
**Problema**: <PERSON><PERSON><PERSON> "desenhar" na tab Animação não funciona
**Detalhes**:
- <PERSON> bot<PERSON> "desenhar" não ativa o modo de desenho
- Não há captura de pontos quando clicado no campo
- `onDrawingModeChange` não está sendo chamado corretamente
- Estado `drawingMode` não sincroniza com TrainingField
- Preview de trajetória não aparece durante desenho

**Evidência no Código**:
```typescript
// TrajectorySystem.tsx - linha ~82
export function TrajectorySystem({
  // ...props
  onDrawingModeChange,
  drawingMode = 'select',
  // ...
}) {
  // Implementação incompleta do modo de desenho
}
```

### 2. Integração TrainingField ↔ TrajectorySystem
**Arquivo**: `src/components/training/TrainingField.tsx`
**Problema**: Campo não responde ao modo de desenho de trajetórias
**Detalhes**:
- `drawingMode` não afeta comportamento do campo para trajetórias
- Eventos de mouse não capturam pontos para trajetórias
- Não há diferenciação entre desenho normal e desenho de trajetórias
- `onDrawingAdd` não é chamado para trajetórias

**Evidência no Código**:
```typescript
// TrainingField.tsx - linha ~31
export const TrainingField = forwardRef<HTMLDivElement, TrainingFieldProps>(
  ({
    // ...props
    drawingMode, // Recebe mas não usa adequadamente para trajetórias
    // ...
  }, ref) => {
    // Lógica de desenho não diferencia trajetórias
  }
);
```

### 3. useAnimationEngine Hook Não Implementado
**Arquivo**: `src/hooks/useAnimationEngine.ts`
**Problema**: Hook retorna interface mas não tem implementação funcional
**Detalhes**:
- `AnimationEngine` class não existe ou não está implementada
- Callbacks não funcionam
- Controles de animação dependem deste hook
- Estado de animação não é gerenciado

**Evidência no Código**:
```typescript
// useAnimationEngine.ts - linha ~1
import { AnimationEngine, AnimationSettings, PlaybackState, AnimationFrame, TimelineEvent } from '@/lib/AnimationEngine';
// AnimationEngine não existe em @/lib/AnimationEngine
```

## Problemas de Alto Impacto

### 4. AnimationControls Não Funcionais
**Arquivo**: `src/components/training/AnimationControls.tsx`
**Problema**: Controles de animação não executam ações
**Detalhes**:
- Botões play/pause não iniciam animação
- Timeline não é interativa
- Slider de frame não funciona
- `handleFrameSliderChange` implementação incompleta
- Dependência do useAnimationEngine quebrado

**Evidência no Código**:
```typescript
// AnimationControls.tsx - linha ~63
const animationEngine = useAnimationEngine({
  // Configuração, mas engine não funciona
});

const handleFrameSliderChange = (value: number[]) => {
  // Implementação incompleta - linha cortada
```

### 5. DrawingTools Não Criam Elementos Visuais
**Arquivo**: `src/components/training/DrawingTools.tsx`
**Problema**: Ferramentas de desenho não resultam em elementos no campo
**Detalhes**:
- Seleção de ferramenta não ativa modo de desenho
- Cores e estilos não são aplicados
- Operações em elementos selecionados falham
- `onUpdateElements` não funciona corretamente

### 6. Modais com Funcionalidades Quebradas

#### SavedDrillsDialog
**Arquivo**: `src/components/training/SavedDrillsDialog.tsx`
**Problema**: Carregamento e preview de drills falha
**Detalhes**:
- `getDrill()` pode retornar null
- Preview de drill não implementado
- Filtros podem não funcionar adequadamente

#### ExportSystem
**Arquivo**: `src/components/training/ExportSystem.tsx`
**Problema**: Exportação não funciona
**Detalhes**:
- Hooks de exportação não implementados (`usePDFExport`, `useImageExport`, etc.)
- `handleExport` implementação incompleta
- Dependências de exportação faltando

**Evidência no Código**:
```typescript
// ExportSystem.tsx - linha ~45
const pdfExport = usePDFExport(); // Hook não existe
const imageExport = useImageExport(); // Hook não existe
const videoExport = useVideoExport(); // Hook não existe
```

## Problemas de Médio Impacto

### 7. TemplateLibrary Carregamento Problemático
**Arquivo**: `src/components/training/TemplateLibrary.tsx`
**Problema**: Templates podem não carregar corretamente
**Detalhes**:
- Templates predefinidos hardcoded
- Carregamento de templates do banco pode falhar
- Preview de templates não funciona

### 8. Persistência de Dados Inconsistente
**Arquivo**: `src/lib/training-api.ts`
**Problema**: Salvamento pode falhar silenciosamente
**Detalhes**:
- Funções de API podem não tratar todos os erros
- Sincronização com Supabase pode falhar
- Configurações de usuário podem não persistir

### 9. ElementToolbar Integração Problemática
**Problema**: Elementos da toolbar podem não ser adicionados ao campo
**Detalhes**:
- Drag & drop pode falhar
- Elementos podem não aparecer no campo
- Propriedades de elementos podem não ser salvas

### 10. PlayerSelector Não Funcional
**Problema**: Seleção de jogadores pode não funcionar
**Detalhes**:
- Lista de jogadores pode não carregar
- Adição de jogadores ao campo pode falhar
- Propriedades de jogadores podem não ser salvas

## Problemas de Baixo Impacto (UX/Performance)

### 11. Feedback Visual Inadequado
**Problemas**:
- Falta de loading states
- Falta de confirmações de ações
- Falta de mensagens de erro claras
- Falta de tooltips explicativos

### 12. Performance de Renderização
**Problemas**:
- Re-renders desnecessários
- Falta de memoização em componentes pesados
- SVG pode ser lento com muitos elementos
- Falta de virtualização

### 13. Responsividade
**Problemas**:
- Layout pode quebrar em telas menores
- Controles podem ficar inacessíveis
- Campo pode não se adaptar adequadamente

### 14. Acessibilidade
**Problemas**:
- Falta de labels ARIA
- Falta de navegação por teclado
- Falta de suporte a screen readers
- Contraste de cores pode ser inadequado

## Problemas de Configuração/Dependências

### 15. Dependências Faltando
**Problemas**:
- `@/lib/AnimationEngine` não existe
- Hooks de exportação não implementados
- Utilitários de animação faltando

### 16. Tipos TypeScript Incompletos
**Problemas**:
- Alguns tipos podem estar mal definidos
- Interfaces podem estar incompletas
- Props opcionais podem causar erros

## Resumo por Severidade

### 🔴 Crítico (3 problemas):
1. Sistema de desenho de trajetórias não funciona
2. Integração TrainingField ↔ TrajectorySystem quebrada
3. useAnimationEngine não implementado

### 🟡 Alto Impacto (6 problemas):
4. AnimationControls não funcionais
5. DrawingTools não criam elementos
6. SavedDrillsDialog com problemas
7. ExportSystem não funciona
8. TemplateLibrary problemática
9. Persistência inconsistente

### 🟢 Médio/Baixo Impacto (7 problemas):
10. ElementToolbar integração
11. PlayerSelector não funcional
12. Feedback visual inadequado
13. Performance de renderização
14. Responsividade
15. Acessibilidade
16. Dependências/tipos faltando

**Total**: 16 problemas identificados, sendo 3 críticos que impedem o uso básico do sistema.