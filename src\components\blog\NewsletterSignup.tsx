import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, CheckCircle, AlertCircle, Gift } from 'lucide-react';
import { useBlogAnalytics } from '@/utils/analytics/blogTracking';

interface NewsletterSignupProps {
  variant?: 'default' | 'compact' | 'sidebar' | 'popup';
  title?: string;
  description?: string;
  incentive?: string;
  source?: string;
  className?: string;
}

export function NewsletterSignup({
  variant = 'default',
  title,
  description,
  incentive,
  source = 'blog',
  className = ''
}: NewsletterSignupProps) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const { trackEmailSignup } = useBlogAnalytics();

  const variants = {
    default: {
      title: title || 'Receba Dicas Exclusivas de Gestão Esportiva',
      description: description || 'Estratégias práticas, templates gratuitos e cases de sucesso direto no seu email. Sem spam, apenas conteúdo de valor.',
      incentive: incentive || '🎁 Bônus: Kit com 5 planilhas gratuitas ao se inscrever',
      cardClass: 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200',
      size: 'lg'
    },
    compact: {
      title: title || 'Newsletter Semanal',
      description: description || 'Dicas práticas de gestão esportiva toda semana.',
      incentive: incentive || '📊 + Planilhas gratuitas',
      cardClass: 'bg-gray-50 border-gray-200',
      size: 'sm'
    },
    sidebar: {
      title: title || 'Não Perca Nenhuma Dica',
      description: description || 'Receba nossos melhores conteúdos por email.',
      incentive: incentive || '✨ Conteúdo exclusivo',
      cardClass: 'bg-white border-gray-200 shadow-sm',
      size: 'sm'
    },
    popup: {
      title: title || 'Antes de Sair...',
      description: description || 'Que tal receber mais conteúdos como este?',
      incentive: incentive || '🚀 Templates gratuitos inclusos',
      cardClass: 'bg-white border-gray-200 shadow-lg',
      size: 'md'
    }
  };

  const config = variants[variant];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || status === 'loading') return;

    setStatus('loading');
    setErrorMessage('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          source,
          variant,
          timestamp: new Date().toISOString()
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Erro ao processar inscrição');
      }

      setStatus('success');
      trackEmailSignup(source, 'newsletter');
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setEmail('');
        setStatus('idle');
      }, 3000);

    } catch (error) {
      console.error('Erro na inscrição:', error);
      setStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Erro interno. Tente novamente.');
    }
  };

  if (status === 'success') {
    return (
      <Card className={`${config.cardClass} ${className}`}>
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Inscrição Confirmada! 🎉
            </h3>
            <p className="text-green-700 mb-4">
              Enviamos um email de confirmação. Verifique sua caixa de entrada.
            </p>
            <p className="text-sm text-green-600">
              Você receberá nosso primeiro email com o kit de planilhas em breve!
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${config.cardClass} ${className}`}>
      <CardHeader className={config.size === 'sm' ? 'pb-4' : 'pb-6'}>
        <div className="flex items-center gap-3">
          <div className="bg-blue-600 p-2 rounded-full">
            <Mail className="h-5 w-5 text-white" />
          </div>
          <div>
            <CardTitle className={`${config.size === 'sm' ? 'text-lg' : 'text-xl'} text-blue-900`}>
              {config.title}
            </CardTitle>
            <CardDescription className="text-blue-700">
              {config.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {config.incentive && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2">
              <Gift className="h-4 w-4 text-yellow-600" />
              <p className="text-sm text-yellow-800 font-medium">
                {config.incentive}
              </p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-3">
          <div>
            <Input
              type="email"
              placeholder="Seu melhor email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={status === 'loading'}
              className="border-blue-300 focus:border-blue-500"
            />
          </div>
          
          {status === 'error' && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{errorMessage}</span>
            </div>
          )}
          
          <Button 
            type="submit" 
            className="w-full bg-blue-600 hover:bg-blue-700"
            disabled={status === 'loading'}
            size={config.size === 'sm' ? 'default' : 'lg'}
          >
            {status === 'loading' ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processando...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Quero Receber as Dicas
              </>
            )}
          </Button>
        </form>

        <p className="text-xs text-blue-600 mt-3 text-center">
          📧 Sem spam. Cancele quando quiser. Enviamos apenas 1 email por semana.
        </p>

        {/* Social proof */}
        <div className="mt-4 pt-4 border-t border-blue-200">
          <p className="text-xs text-blue-700 text-center">
            ⭐ Mais de 2.500 gestores esportivos já recebem nossas dicas
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Componente inline para usar no meio de posts
export function InlineNewsletterSignup({ source = 'blog_inline' }: { source?: string }) {
  return (
    <div className="my-8 p-6 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg text-white">
      <div className="max-w-2xl mx-auto text-center">
        <h3 className="text-xl font-bold mb-2">
          Gostando do conteúdo? 📚
        </h3>
        <p className="text-blue-100 mb-4">
          Receba mais dicas como esta direto no seu email. Sem spam, apenas valor.
        </p>
        <NewsletterSignup
          variant="compact"
          source={source}
          className="bg-white/10 border-white/20"
        />
      </div>
    </div>
  );
}

// Hook para popup de exit-intent
export function useExitIntentPopup() {
  const [showPopup, setShowPopup] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  React.useEffect(() => {
    if (hasShown) return;

    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0) {
        setShowPopup(true);
        setHasShown(true);
      }
    };

    // Show after 30 seconds if not shown yet
    const timer = setTimeout(() => {
      if (!hasShown) {
        setShowPopup(true);
        setHasShown(true);
      }
    }, 30000);

    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      clearTimeout(timer);
    };
  }, [hasShown]);

  return {
    showPopup,
    hidePopup: () => setShowPopup(false)
  };
}

// Componente de popup
export function NewsletterPopup({ 
  show, 
  onClose 
}: { 
  show: boolean; 
  onClose: () => void; 
}) {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="relative max-w-md w-full">
        <button
          onClick={onClose}
          className="absolute -top-2 -right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 z-10"
        >
          ✕
        </button>
        <NewsletterSignup
          variant="popup"
          source="exit_intent_popup"
        />
      </div>
    </div>
  );
}