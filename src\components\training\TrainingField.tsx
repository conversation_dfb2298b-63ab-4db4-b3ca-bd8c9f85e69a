import React, { forwardRef, useCallback, useRef, useState, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { TrainingElement, DrawingElement } from './InteractiveTrainingBuilder';
import { Trajectory } from './TrajectorySystem';
import { DraggableElement } from './DraggableElement';

interface TrainingFieldProps {
  elements: TrainingElement[];
  drawings: DrawingElement[];
  trajectories: Trajectory[];
  selectedElements: string[];
  selectedDrawings: string[];
  zoom: number;
  showGrid: boolean;
  showPlayerNames: boolean;
  onElementUpdate: (id: string, updates: Partial<TrainingElement>) => void;
  onElementSelect: (id: string, multiSelect?: boolean) => void;
  onElementDelete: (id: string) => void;
  onElementAdd: (element: Omit<TrainingElement, 'id'>) => void;
  onDrawingSelect: (id: string, multi?: boolean) => void;
  onDrawingUpdate: (id: string, updates: Partial<DrawingElement>) => void;
  onDrawingDelete: (id: string) => void;
  onDrawingAdd: (drawing: Omit<DrawingElement, 'id'>) => void;
  drawingMode: 'select' | 'draw' | 'erase';
  activeTool: string | null;
  drawingTool: string;
  strokeColor: string;
  strokeWidth: number;
  // Trajectory-specific props
  onTrajectoryPointAdd?: (x: number, y: number) => void;
  isTrajectoryDrawing?: boolean;
}

export const TrainingField = forwardRef<HTMLDivElement, TrainingFieldProps>(
  ({
    elements,
    drawings,
    trajectories,
    selectedElements,
    selectedDrawings,
    zoom,
    showGrid,
    showPlayerNames,
    onElementUpdate,
    onElementSelect,
    onElementDelete,
    onElementAdd,
    onDrawingSelect,
    onDrawingUpdate,
    onDrawingDelete,
    onDrawingAdd,
    drawingMode,
    activeTool,
    drawingTool,
    strokeColor,
    strokeWidth,
    onTrajectoryPointAdd,
    isTrajectoryDrawing
  }, ref) => {
    const fieldRef = useRef<SVGSVGElement>(null);
    const [fieldDimensions, setFieldDimensions] = useState({ width: 800, height: 600 });
    const [isDrawing, setIsDrawing] = useState(false);
    const [drawingPath, setDrawingPath] = useState<string>('');
    const [currentDrawing, setCurrentDrawing] = useState<{ x: number; y: number }[]>([]);
    const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);

    // Dimensões reais de um campo de futebol (em metros)
    const FIELD_LENGTH = 105; // metros
    const FIELD_WIDTH = 68; // metros
    const GOAL_WIDTH = 7.32; // metros
    const GOAL_DEPTH = 2.44; // metros
    const PENALTY_AREA_LENGTH = 16.5; // metros
    const PENALTY_AREA_WIDTH = 40.32; // metros
    const GOAL_AREA_LENGTH = 5.5; // metros
    const GOAL_AREA_WIDTH = 18.32; // metros
    const CENTER_CIRCLE_RADIUS = 9.15; // metros
    const PENALTY_SPOT_DISTANCE = 11; // metros

    // Conversão de metros para pixels
    const metersToPixels = useCallback((meters: number, isWidth = false) => {
      const baseSize = isWidth ? fieldDimensions.width : fieldDimensions.height;
      const fieldSize = isWidth ? FIELD_WIDTH : FIELD_LENGTH;
      return (meters / fieldSize) * baseSize;
    }, [fieldDimensions]);

    // Drop zone para elementos arrastados da toolbar
    const [{ isOver }, drop] = useDrop({
      accept: ['cone', 'ball', 'goal', 'player', 'marker', 'annotation'],
      drop: (item: { type: string; properties?: any }, monitor) => {
        const offset = monitor.getClientOffset();
        const fieldRect = fieldRef.current?.getBoundingClientRect();

        if (offset && fieldRect) {
          const x = ((offset.x - fieldRect.left) / fieldRect.width) * 100;
          const y = ((offset.y - fieldRect.top) / fieldRect.height) * 100;

          // Criar novo elemento na posição do drop
          const newElement: Omit<TrainingElement, 'id'> = {
            type: item.type as any,
            position: { x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) },
            properties: item.properties || {}
          };

          // Chamar função para adicionar elemento
          onElementAdd(newElement);
          console.log('Novo elemento adicionado:', newElement);
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    });

    // Handler para clique no campo (adicionar anotações e trajetórias)
    const handleFieldClick = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
      const rect = fieldRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      // Se estiver em modo de desenho de trajetória, capturar ponto
      if (isTrajectoryDrawing && onTrajectoryPointAdd) {
        e.stopPropagation();
        onTrajectoryPointAdd(
          Math.max(0, Math.min(100, x)), 
          Math.max(0, Math.min(100, y))
        );
        return;
      }

      // Se uma ferramenta de anotação estiver ativa, adicionar anotação
      if (activeTool === 'annotation') {
        const text = prompt('Digite o texto da anotação:');
        if (text) {
          onElementAdd({
            type: 'annotation',
            position: { x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) },
            properties: { text, color: '#3b82f6' }
          });
        }
        return;
      }

      // Limpar seleção se não estiver em modo de desenho
      if (drawingMode === 'select') {
        onElementSelect('', false); // Limpar seleção
        onDrawingSelect('', false);
      }
    }, [activeTool, drawingMode, onElementAdd, onElementSelect, onDrawingSelect, isTrajectoryDrawing, onTrajectoryPointAdd]);

    // Handlers para desenho
    const handleMouseDown = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
      // Don't handle drawing if in trajectory drawing mode
      if (isTrajectoryDrawing) return;
      if (drawingMode !== 'draw') return;

      const rect = fieldRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      const point = { x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) };

      setIsDrawing(true);
      setStartPoint(point);

      if (drawingTool === 'text') {
        setCurrentDrawing([point]);
        return;
      }

      if (drawingTool === 'freehand' || drawingTool === 'polygon') {
        setCurrentDrawing([point]);
        if (drawingTool === 'freehand') {
          setDrawingPath(
            `M ${(point.x / 100) * fieldDimensions.width} ${(point.y / 100) * fieldDimensions.height}`
          );
        }
      } else {
        setCurrentDrawing([point]);
      }
    }, [drawingMode, drawingTool, fieldDimensions, isTrajectoryDrawing]);

    const handleMouseMove = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
      if (!isDrawing || drawingMode !== 'draw') return;

      const rect = fieldRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      const point = { x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) };

      if (drawingTool === 'freehand' || drawingTool === 'polygon') {
        setCurrentDrawing(prev => [...prev, point]);
        if (drawingTool === 'freehand') {
          const pixelX = (point.x / 100) * fieldDimensions.width;
          const pixelY = (point.y / 100) * fieldDimensions.height;
          setDrawingPath(prev => `${prev} L ${pixelX} ${pixelY}`);
        }
      } else if (drawingTool !== 'text') {
        // Para outras ferramentas, apenas atualizar o ponto final
        setCurrentDrawing([startPoint!, point]);
      }
   }, [isDrawing, drawingMode, drawingTool, fieldDimensions, startPoint]);

    const handleMouseUp = useCallback(() => {
      if (isDrawing && currentDrawing.length > 0) {
        setIsDrawing(false);

        // Criar o desenho baseado na ferramenta selecionada
        const newDrawing: Omit<DrawingElement, 'id'> = {
          type: drawingTool as any,
          points: currentDrawing,
          properties: {
            strokeColor,
            strokeWidth,
            fillColor:
              drawingTool === 'circle' || drawingTool === 'rectangle' || drawingTool === 'polygon'
                ? strokeColor + '20'
                : undefined,
            text: drawingTool === 'text' ? 'Texto' : undefined,
            fontSize: drawingTool === 'text' ? 12 : undefined,
          },
        };

        onDrawingAdd(newDrawing);
        console.log('Desenho finalizado:', newDrawing);

        // Reset
        setDrawingPath('');
        setCurrentDrawing([]);
        setStartPoint(null);
      }
    }, [isDrawing, currentDrawing, drawingTool, strokeColor, strokeWidth, onDrawingAdd]);

    // Atualizar dimensões do campo baseado no container
    useEffect(() => {
      const updateDimensions = () => {
        if (ref && 'current' in ref && ref.current) {
          const rect = ref.current.getBoundingClientRect();
          const aspectRatio = FIELD_LENGTH / FIELD_WIDTH;
          let width = rect.width - 32; // padding
          let height = width / aspectRatio;
          
          if (height > rect.height - 32) {
            height = rect.height - 32;
            width = height * aspectRatio;
          }
          
          setFieldDimensions({ width, height });
        }
      };

      updateDimensions();
      window.addEventListener('resize', updateDimensions);
      return () => window.removeEventListener('resize', updateDimensions);
    }, [ref]);

    return (
      <div 
        ref={ref}
        className={`w-full h-full flex items-center justify-center p-4 ${
          isOver ? 'bg-primary/5' : 'bg-muted/20'
        } ${isTrajectoryDrawing ? 'bg-blue-50 border-2 border-blue-300 border-dashed' : ''}`}
        style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
      >
        {isTrajectoryDrawing && (
          <div className="absolute top-4 left-4 z-10 bg-blue-100 border border-blue-300 rounded-lg p-2">
            <p className="text-xs text-blue-700 font-medium">
              🎯 Modo de Desenho de Trajetória Ativo
            </p>
            <p className="text-xs text-blue-600">
              Clique no campo para adicionar pontos à trajetória
            </p>
          </div>
        )}
        <svg
          ref={(node) => {
            fieldRef.current = node;
            drop(node);
          }}
          width={fieldDimensions.width}
          height={fieldDimensions.height}
          viewBox={`0 0 ${fieldDimensions.width} ${fieldDimensions.height}`}
          className="border border-green-600 bg-green-100 cursor-crosshair"
          onClick={handleFieldClick}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Definições para padrões */}
          <defs>
            {showGrid && (
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#10b981" strokeWidth="0.5" opacity="0.3"/>
              </pattern>
            )}
          </defs>

          {/* Grade de fundo */}
          {showGrid && (
            <rect width="100%" height="100%" fill="url(#grid)" />
          )}

          {/* Campo de futebol */}
          <g stroke="#10b981" strokeWidth="2" fill="none">
            {/* Perímetro do campo */}
            <rect 
              x="0" 
              y="0" 
              width={fieldDimensions.width} 
              height={fieldDimensions.height}
              className="stroke-green-700"
            />
            
            {/* Linha central */}
            <line 
              x1={fieldDimensions.width / 2} 
              y1="0" 
              x2={fieldDimensions.width / 2} 
              y2={fieldDimensions.height}
            />
            
            {/* Círculo central */}
            <circle 
              cx={fieldDimensions.width / 2} 
              cy={fieldDimensions.height / 2} 
              r={metersToPixels(CENTER_CIRCLE_RADIUS)}
            />
            
            {/* Ponto central */}
            <circle 
              cx={fieldDimensions.width / 2} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />

            {/* Área penal esquerda */}
            <rect 
              x="0" 
              y={(fieldDimensions.height - metersToPixels(PENALTY_AREA_WIDTH, true)) / 2}
              width={metersToPixels(PENALTY_AREA_LENGTH)} 
              height={metersToPixels(PENALTY_AREA_WIDTH, true)}
            />
            
            {/* Área penal direita */}
            <rect 
              x={fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} 
              y={(fieldDimensions.height - metersToPixels(PENALTY_AREA_WIDTH, true)) / 2}
              width={metersToPixels(PENALTY_AREA_LENGTH)} 
              height={metersToPixels(PENALTY_AREA_WIDTH, true)}
            />

            {/* Área do goleiro esquerda */}
            <rect 
              x="0" 
              y={(fieldDimensions.height - metersToPixels(GOAL_AREA_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_AREA_LENGTH)} 
              height={metersToPixels(GOAL_AREA_WIDTH, true)}
            />
            
            {/* Área do goleiro direita */}
            <rect 
              x={fieldDimensions.width - metersToPixels(GOAL_AREA_LENGTH)} 
              y={(fieldDimensions.height - metersToPixels(GOAL_AREA_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_AREA_LENGTH)} 
              height={metersToPixels(GOAL_AREA_WIDTH, true)}
            />

            {/* Pontos de pênalti */}
            <circle 
              cx={metersToPixels(PENALTY_SPOT_DISTANCE)} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />
            <circle 
              cx={fieldDimensions.width - metersToPixels(PENALTY_SPOT_DISTANCE)} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />

            {/* Arcos da área penal */}
            <path 
              d={`M ${metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 - metersToPixels(CENTER_CIRCLE_RADIUS)} 
                  A ${metersToPixels(CENTER_CIRCLE_RADIUS)} ${metersToPixels(CENTER_CIRCLE_RADIUS)} 0 0 1 
                  ${metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 + metersToPixels(CENTER_CIRCLE_RADIUS)}`}
            />
            <path 
              d={`M ${fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 - metersToPixels(CENTER_CIRCLE_RADIUS)} 
                  A ${metersToPixels(CENTER_CIRCLE_RADIUS)} ${metersToPixels(CENTER_CIRCLE_RADIUS)} 0 0 0 
                  ${fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 + metersToPixels(CENTER_CIRCLE_RADIUS)}`}
            />
          </g>

          {/* Gols */}
          <g stroke="#8b5cf6" strokeWidth="3" fill="none">
            {/* Gol esquerdo */}
            <rect 
              x={-metersToPixels(GOAL_DEPTH)} 
              y={(fieldDimensions.height - metersToPixels(GOAL_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_DEPTH)} 
              height={metersToPixels(GOAL_WIDTH, true)}
            />
            
            {/* Gol direito */}
            <rect 
              x={fieldDimensions.width} 
              y={(fieldDimensions.height - metersToPixels(GOAL_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_DEPTH)} 
              height={metersToPixels(GOAL_WIDTH, true)}
            />
          </g>


          {/* Desenhos salvos */}
          {drawings.map((drawing) => {
            const pixelPoints = drawing.points.map(p => ({
              x: (p.x / 100) * fieldDimensions.width,
              y: (p.y / 100) * fieldDimensions.height
            }));
            const isSelected = selectedDrawings.includes(drawing.id);

            const handleMouseDown = (e: React.MouseEvent) => {
              if (drawingMode === 'erase') {
                e.stopPropagation();
                onDrawingDelete(drawing.id);
                return;
              }
              if (drawingMode !== 'select') return;
              e.stopPropagation();

              const multi = e.ctrlKey || e.metaKey;
              const alreadySelected = selectedDrawings.includes(drawing.id);
              if (!alreadySelected || multi) {
                onDrawingSelect(drawing.id, multi);
              }

              const moveIds = alreadySelected && !multi ? selectedDrawings : [drawing.id];
              const startX = e.clientX;
              const startY = e.clientY;
              const startMap: Record<string, { x: number; y: number }[]> = {};
              moveIds.forEach(id => {
                const d = drawings.find(dr => dr.id === id);
                if (d) startMap[id] = d.points.map(p => ({ ...p }));
              });

              const move = (me: MouseEvent) => {
                const dx = me.clientX - startX;
                const dy = me.clientY - startY;
                moveIds.forEach(id => {
                  const startPts = startMap[id];
                  if (!startPts) return;
                  const updated = startPts.map(p => ({
                    x: Math.max(0, Math.min(100, p.x + (dx / fieldDimensions.width) * 100)),
                    y: Math.max(0, Math.min(100, p.y + (dy / fieldDimensions.height) * 100))
                  }));
                  onDrawingUpdate(id, { points: updated });
                });
              };
              const up = () => {
                document.removeEventListener('mousemove', move);
                document.removeEventListener('mouseup', up);
              };
              document.addEventListener('mousemove', move);
              document.addEventListener('mouseup', up);
            };

            let shape: React.ReactNode = null;
            switch (drawing.type) {
              case 'freehand': {
                const pathData = pixelPoints.reduce((path, point, index) => {
                  return index === 0 ? `M ${point.x} ${point.y}` : `${path} L ${point.x} ${point.y}`;
                }, '');
                shape = (
                  <path
                    d={pathData}
                    stroke={drawing.properties.strokeColor}
                    strokeWidth={drawing.properties.strokeWidth}
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                );
                break;
              }
              case 'line':
                if (pixelPoints.length >= 2) {
                  shape = (
                    <line
                      x1={pixelPoints[0].x}
                      y1={pixelPoints[0].y}
                      x2={pixelPoints[1].x}
                      y2={pixelPoints[1].y}
                      stroke={drawing.properties.strokeColor}
                      strokeWidth={drawing.properties.strokeWidth}
                      strokeLinecap="round"
                    />
                  );
                }
                break;
              case 'arrow':
                if (pixelPoints.length >= 2) {
                  const start = pixelPoints[0];
                  const end = pixelPoints[1];
                  const angle = Math.atan2(end.y - start.y, end.x - start.x);
                  const arrowLength = 10;
                  const arrowAngle = Math.PI / 6;
                  shape = (
                    <g>
                      <line
                        x1={start.x}
                        y1={start.y}
                        x2={end.x}
                        y2={end.y}
                        stroke={drawing.properties.strokeColor}
                        strokeWidth={drawing.properties.strokeWidth}
                        strokeLinecap="round"
                      />
                      <path
                        d={`M ${end.x} ${end.y} L ${end.x - arrowLength * Math.cos(angle - arrowAngle)} ${end.y - arrowLength * Math.sin(angle - arrowAngle)} M${end.x} ${end.y} L ${end.x - arrowLength * Math.cos(angle + arrowAngle)} ${end.y - arrowLength * Math.sin(angle + arrowAngle)}`}
                        stroke={drawing.properties.strokeColor}
                        strokeWidth={drawing.properties.strokeWidth}
                        strokeLinecap="round"
                      />
                    </g>
                  );
                }
                break;
              case 'circle':
                if (pixelPoints.length >= 2) {
                  const center = pixelPoints[0];
                  const edge = pixelPoints[1];
                  const radius = Math.sqrt(Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2));
                  shape = (
                    <circle
                      cx={center.x}
                      cy={center.y}
                      r={radius}
                      stroke={drawing.properties.strokeColor}
                      strokeWidth={drawing.properties.strokeWidth}
                      fill={drawing.properties.fillColor || 'none'}
                    />
                  );
                }
                break;
              case 'rectangle':
                if (pixelPoints.length >= 2) {
                  const start = pixelPoints[0];
                  const end = pixelPoints[1];
                  const width = Math.abs(end.x - start.x);
                  const height = Math.abs(end.y - start.y);
                  const x = Math.min(start.x, end.x);
                  const y = Math.min(start.y, end.y);
                  shape = (
                    <rect
                      x={x}
                      y={y}
                      width={width}
                      height={height}
                      stroke={drawing.properties.strokeColor}
                      strokeWidth={drawing.properties.strokeWidth}
                      fill={drawing.properties.fillColor || 'none'}
                    />
                  );
                }
                break;
              case 'polygon':
                if (pixelPoints.length >= 3) {
                  const pts = [...pixelPoints, pixelPoints[0]]
                    .map(p => `${p.x},${p.y}`)
                    .join(' ');
                  shape = (
                    <polygon
                      points={pts}
                      stroke={drawing.properties.strokeColor}
                      strokeWidth={drawing.properties.strokeWidth}
                      fill={drawing.properties.fillColor || 'none'}
                    />
                  );
                }
                break;
              case 'text':
                if (pixelPoints.length >= 1) {
                  const p = pixelPoints[0];
                  shape = (
                    <text
                      x={p.x}
                      y={p.y}
                      fontSize={drawing.properties.fontSize || 12}
                      fill={drawing.properties.fillColor || drawing.properties.strokeColor}
                      textAnchor="middle"
                      dominantBaseline="middle"
                    >
                      {drawing.properties.text || 'Texto'}
                    </text>
                  );
                }
                break;
              default:
                break;
            }

            if (!shape) return null;
            return (
              <g
                key={drawing.id}
                onMouseDown={handleMouseDown}
                onClick={(e) => e.stopPropagation()}
                className={`cursor-pointer ${isSelected ? 'ring-2 ring-primary ring-offset-1' : ''}`}
              >
                {shape}
              </g>
          );
        })}

          {/* Trajetórias */}
          {trajectories.map((traj) => {
            if (traj.points.length === 0) return null;
            const pixelPoints = traj.points.map(p => {
              // Validate coordinates to prevent NaN
              const x = isNaN(p.x) ? 0 : (p.x / 100) * fieldDimensions.width;
              const y = isNaN(p.y) ? 0 : (p.y / 100) * fieldDimensions.height;
              return { x, y };
            }).filter(p => !isNaN(p.x) && !isNaN(p.y)); // Filter out any remaining NaN values
            
            if (pixelPoints.length === 0) return null;
            
            const pathData = pixelPoints.reduce(
              (acc, p, idx) => (idx === 0 ? `M ${p.x} ${p.y}` : `${acc} L ${p.x} ${p.y}`),
              ''
            );
            const markerId = `arrow-${traj.id}`;
            const isPreview = traj.id === 'preview';
            return (
              <g key={traj.id} fill="none" stroke={traj.style.color} opacity={isPreview ? 0.7 : 1}>
                {traj.style.showArrows && (
                  <defs>
                    <marker id={markerId} markerWidth="6" markerHeight="6" refX="5" refY="3" orient="auto" markerUnits="strokeWidth">
                      <path d="M0,0 L6,3 L0,6 Z" fill={traj.style.color} />
                    </marker>
                  </defs>
                )}
                <path
                  d={pathData}
                  strokeWidth={traj.style.width}
                  strokeDasharray={isPreview ? "5,5" : traj.style.dashArray}
                  markerMid={traj.style.showArrows ? `url(#${markerId})` : undefined}
                />
                {pixelPoints.map((pt, i) => (
                  <circle 
                    key={i} 
                    cx={pt.x} 
                    cy={pt.y} 
                    r={isPreview ? 3 : 2} 
                    fill={traj.style.color}
                    stroke={isPreview ? "#ffffff" : "none"}
                    strokeWidth={isPreview ? 1 : 0}
                  />
                ))}
                {traj.style.showSpeed &&
                  pixelPoints.map((pt, i) =>
                    traj.points[i]?.speed ? (
                      <text key={`s-${i}`} x={pt.x + 4} y={pt.y - 4} fontSize={8} fill={traj.style.color}>
                        {traj.points[i].speed?.toFixed(1)}
                      </text>
                    ) : null
                  )}
              </g>
            );
          })}
          {/* Desenho em progresso */}
          {isDrawing && currentDrawing.length > 0 && (
            (() => {
              const pixelPoints = currentDrawing.map(p => ({
                x: (p.x / 100) * fieldDimensions.width,
                y: (p.y / 100) * fieldDimensions.height
              }));

              switch (drawingTool) {
                case 'freehand':
                  return (
                    <path
                      d={drawingPath}
                      stroke={strokeColor}
                      strokeWidth={strokeWidth}
                      fill="none"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      opacity="0.7"
                    />
                  );

                case 'line':
                  if (pixelPoints.length >= 2) {
                    return (
                      <line
                        x1={pixelPoints[0].x}
                        y1={pixelPoints[0].y}
                        x2={pixelPoints[1].x}
                        y2={pixelPoints[1].y}
                        stroke={strokeColor}
                        strokeWidth={strokeWidth}
                        strokeLinecap="round"
                        opacity="0.7"
                      />
                    );
                  }
                  break;

                case 'arrow':
                  if (pixelPoints.length >= 2) {
                    const start = pixelPoints[0];
                    const end = pixelPoints[1];
                    const angle = Math.atan2(end.y - start.y, end.x - start.x);
                    const arrowLength = 10;
                    const arrowAngle = Math.PI / 6;

                    return (
                      <g opacity="0.7">
                        <line
                          x1={start.x}
                          y1={start.y}
                          x2={end.x}
                          y2={end.y}
                          stroke={strokeColor}
                          strokeWidth={strokeWidth}
                          strokeLinecap="round"
                        />
                        <path
                          d={`M ${end.x} ${end.y} L ${end.x - arrowLength * Math.cos(angle - arrowAngle)} ${end.y - arrowLength * Math.sin(angle - arrowAngle)} M ${end.x} ${end.y} L ${end.x - arrowLength * Math.cos(angle + arrowAngle)} ${end.y - arrowLength * Math.sin(angle + arrowAngle)}`}
                          stroke={strokeColor}
                          strokeWidth={strokeWidth}
                          strokeLinecap="round"
                        />
                      </g>
                    );
                  }
                  break;

                case 'circle':
                  if (pixelPoints.length >= 2) {
                    const center = pixelPoints[0];
                    const edge = pixelPoints[1];
                    const radius = Math.sqrt(Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2));

                    return (
                      <circle
                        cx={center.x}
                        cy={center.y}
                        r={radius}
                        stroke={strokeColor}
                        strokeWidth={strokeWidth}
                        fill={strokeColor + '20'}
                        opacity="0.7"
                      />
                    );
                  }
                  break;

                case 'rectangle':
                  if (pixelPoints.length >= 2) {
                    const start = pixelPoints[0];
                    const end = pixelPoints[1];
                    const width = Math.abs(end.x - start.x);
                    const height = Math.abs(end.y - start.y);
                    const x = Math.min(start.x, end.x);
                    const y = Math.min(start.y, end.y);

                    return (
                      <rect
                        x={x}
                        y={y}
                        width={width}
                        height={height}
                        stroke={strokeColor}
                        strokeWidth={strokeWidth}
                        fill={strokeColor + '20'}
                        opacity="0.7"
                      />
                    );
                  }
                  break;
                case 'polygon':
                    if (pixelPoints.length >= 2) {
                      const pts = [...pixelPoints, pixelPoints[0]]
                        .map(p => `${p.x},${p.y}`)
                        .join(' ');
                      return (
                        <polygon
                          points={pts}
                          stroke={strokeColor}
                          strokeWidth={strokeWidth}
                          fill={strokeColor + '20'}
                          opacity="0.7"
                        />
                      );
                    }
                    break;
                  case 'text':
                    if (pixelPoints.length >= 1) {
                      const p = pixelPoints[0];
                      return (
                        <text
                          x={p.x}
                          y={p.y}
                          fontSize={12}
                          fill={strokeColor}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          opacity="0.7"
                        >
                          Texto
                        </text>
                      );
                    }
                    break;

                default:
                  return null;
              }
            })()
          )}

          {/* Elementos do treino */}
          {elements.map((element) => (
            <DraggableElement
              key={element.id}
              element={element}
              isSelected={selectedElements.includes(element.id)}
              fieldDimensions={fieldDimensions}
              showPlayerNames={showPlayerNames}
              onUpdate={(updates) => onElementUpdate(element.id, updates)}
              onSelect={(multiSelect) => onElementSelect(element.id, multiSelect)}
              onDelete={() => onElementDelete(element.id)}
              drawingMode={drawingMode}
            />
          ))}
        </svg>
      </div>
    );
  }
);

TrainingField.displayName = 'TrainingField';