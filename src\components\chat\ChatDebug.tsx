import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useChat } from '@/hooks/useChat';
import { useUser } from '@/context/UserContext';
import { useChatStore } from '@/store/useChatStore';

export function ChatDebug() {
  const { user } = useUser();
  const { isConnected, onlineUsers, rooms } = useChat();
  const { error } = useChatStore();

  if (!user) return null;

  return (
    <Card className="fixed top-4 right-4 w-80 z-50">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm">Chat Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 text-xs">
        <div className="flex justify-between">
          <span>Status:</span>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Conectado" : "Desconectado"}
          </Badge>
        </div>
        
        <div className="flex justify-between">
          <span>Usuário:</span>
          <span className="truncate max-w-32">{user.email}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Clube ID:</span>
          <span>{user.club_id || 'N/A'}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Salas:</span>
          <span>{rooms.length}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Online:</span>
          <span>{onlineUsers.length}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Socket URL:</span>
          <span className="truncate max-w-32">
            {import.meta.env.VITE_SOCKET_URL || 'localhost:3001'}
          </span>
        </div>
        
        {error && (
          <div className="text-red-500 text-xs p-2 bg-red-50 rounded">
            Erro: {error}
          </div>
        )}
        
        <div className="pt-2">
          <Button 
            size="sm" 
            className="w-full text-xs"
            onClick={() => window.location.reload()}
          >
            Recarregar Página
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}