import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { name, email, password, role, permissions, is_active, organization_id } = await req.json()

    // Validate required fields
    if (!name || !email || !password || !role) {
      throw new Error('Campos obrigatórios: name, email, password, role')
    }

    // Validate role
    const validRoles = ['super_admin', 'admin', 'support', 'viewer']
    if (!validRoles.includes(role)) {
      throw new Error('Role inválido')
    }

    // 1. Create user in Supabase Auth
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        name: name,
        role: role,
        is_master_user: true
      }
    })

    if (authError) {
      throw new Error(`Erro ao criar usuário no auth: ${authError.message}`)
    }

    if (!authUser.user) {
      throw new Error('Usuário não foi criado no auth')
    }

    // 2. Create record in master_users table
    const { data: masterUser, error: masterUserError } = await supabaseAdmin
      .from('master_users')
      .insert({
        id: authUser.user.id,
        organization_id: organization_id || null,
        name: name,
        email: email,
        role: role,
        permissions: permissions || {},
        is_active: is_active !== false, // default true
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (masterUserError) {
      // If master_users insert fails, delete the auth user
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
      throw new Error(`Erro ao criar usuário na tabela master: ${masterUserError.message}`)
    }

    // 3. Log the action in audit logs
    await supabaseAdmin
      .from('master_audit_logs')
      .insert({
        user_id: authUser.user.id, // The creator (should be passed from frontend)
        action: 'create_master_user',
        entity_type: 'master_user',
        entity_id: authUser.user.id,
        new_values: {
          name: name,
          email: email,
          role: role,
          is_active: is_active !== false
        },
        details: {
          message: 'Usuário master criado',
          created_by: 'system' // In production, pass the creator's ID
        }
      })

    return new Response(
      JSON.stringify({
        success: true,
        user: masterUser,
        message: 'Usuário master criado com sucesso'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Erro ao criar usuário master:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})