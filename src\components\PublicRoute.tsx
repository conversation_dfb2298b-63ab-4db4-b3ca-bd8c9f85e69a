import { Navigate } from "react-router-dom";
import { isAuthenticated } from "@/utils/auth";

export function PublicRoute({ children }: { children: JSX.Element }) {
  if (isAuthenticated()) {
    // Se autenticado, redirecionar para a página de redirecionamento
    // que vai determinar o clube correto e redirecionar adequadamente
    return <Navigate to="/redirect-to-club" replace />;
  }
  return children;
}
