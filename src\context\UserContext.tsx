import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/api/supabaseClient";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";
import { ChangePasswordDialog } from "@/components/modals/ChangePasswordDialog";

export interface UserData {
  id: string;
  name: string;
  email: string;
  first_login?: boolean;
  profile_image?: string;
  club_id?: number;
  club_info?: {
    id: number;
    name: string;
    subscription_status: 'active' | 'suspended' | 'cancelled' | 'trial';
    payment_status: 'current' | 'overdue' | 'cancelled';
    master_plan_id: number;
    master_plans?: {
      id: number;
      name: string;
      modules: Record<string, boolean>;
      features: Record<string, any>;
      max_users: number | null;
      max_players: number | null;
      price: number;
    };
    is_trial: boolean;
    trial_end_date?: string;
    next_payment_date?: string;
    custom_modules?: Record<string, boolean>;
    usage_limits?: Record<string, any>;
  };
}

interface UserContextType {
  user: UserData | null;
  loading: boolean;
  refreshUser: (forceRefresh?: boolean) => Promise<void>;
  showChangePassword: boolean;
  setShowChangePassword: (show: boolean) => void;
}

const UserContext = createContext<UserContextType>({
  user: null,
  loading: true,
  refreshUser: async () => { },
  showChangePassword: false,
  setShowChangePassword: () => { },
});

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [isFirstLogin, setIsFirstLogin] = useState(false);

  async function fetchUser(forceRefresh = false) {
    setLoading(true);
    const userId = localStorage.getItem("userId");

    if (!userId) {
      setUser(null);
      setLoading(false);
      return;
    }

    // Buscar dados do usuário - usar cache: false para garantir dados atualizados
    const timestamp = forceRefresh ? `?ts=${new Date().getTime()}` : '';

    await ensureAuthenticated();
    // Primeiro buscar dados do usuário
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, name, email, first_login, profile_image")
      .eq("id", userId)
      .single();

    if (userError || !userData) {
      setUser(null);
      setLoading(false);
      return;
    }

    // Buscar o clube do usuário através da tabela club_members
    const { data: memberData, error: memberError } = await supabase
      .from("club_members")
      .select("club_id, role")
      .eq("user_id", userId)
      .single();

    let clubData = null;
    if (!memberError && memberData && memberData.club_id) {
      const { data: club, error: clubError } = await supabase
        .from("club_info")
        .select(`
          id,
          name,
          subscription_status,
          payment_status,
          master_plan_id,
          is_trial,
          trial_end_date,
          next_payment_date,
          custom_modules,
          usage_limits
        `)
        .eq("id", memberData.club_id)
        .single();



      // Se encontrou o clube, buscar dados do plano separadamente
      let planData = null;
      if (!clubError && club && club.master_plan_id) {
        const { data: plan, error: planError } = await supabase
          .from("master_plans")
          .select(`
            id,
            name,
            modules,
            features,
            max_users,
            max_players,
            price
          `)
          .eq("id", club.master_plan_id)
          .single();



        if (!planError && plan) {
          planData = plan;
        }
      }

      // Combinar dados do clube com plano
      if (!clubError && club) {
        clubData = {
          ...club,
          master_plans: planData
        };
      }


    }

    // Combinar dados
    const data = {
      ...userData,
      club_id: memberData?.club_id,
      club_info: clubData
    };



    if (!data) {
      console.log('❌ UserContext - Dados finais não encontrados');
      setUser(null);
    } else {
      // Verificar se o profile_image está vazio e se há um valor no localStorage
      const cachedProfileImage = localStorage.getItem("userProfileImage");
      if (!data.profile_image && cachedProfileImage) {
        data.profile_image = cachedProfileImage;
      } else if (data.profile_image) {
        // Armazenar a imagem no localStorage para uso futuro
        localStorage.setItem("userProfileImage", data.profile_image);
      }

      setUser(data);

      // Verificar se é o primeiro login
      if (data.first_login) {
        setIsFirstLogin(true);
        setShowChangePassword(true);

        // Atualizar o flag de primeiro login para false
        await supabase
          .from("users")
          .update({ first_login: false })
          .eq("id", userId);
      }
    }

    setLoading(false);
  }

  // Função para lidar com o sucesso da alteração de senha
  const handlePasswordChangeSuccess = async () => {
    setIsFirstLogin(false);
    await fetchUser();
  };

  // Efeito para buscar o usuário quando o componente é montado
  // ou quando o userId muda no localStorage
  useEffect(() => {
    const checkUserAuth = () => {
      const userId = localStorage.getItem("userId");
      
      if (userId && userId !== user?.id) {
        fetchUser();
      } else if (!userId) {
        setUser(null);
        setLoading(false);
      }
    };

    // Verificar autenticação inicialmente
    checkUserAuth();

    // Adicionar listener para mudanças no localStorage
    window.addEventListener('storage', (event) => {
      if (event.key === 'userId') {
        checkUserAuth();
      }
    });

    // Verificar periodicamente se o userId mudou (para detectar login)
    const interval = setInterval(() => {
      const userId = localStorage.getItem("userId");
      if (userId && userId !== user?.id) {
        fetchUser();
      }
    }, 1000); // Verificar a cada 1 segundo

    return () => {
      window.removeEventListener('storage', checkUserAuth);
      clearInterval(interval);
    };
  }, [user?.id]);

  return (
    <UserContext.Provider
      value={{
        user,
        loading,
        refreshUser: fetchUser,
        showChangePassword,
        setShowChangePassword
      }}
    >
      {children}

      {/* Diálogo de alteração de senha */}
      <ChangePasswordDialog
        open={showChangePassword}
        onOpenChange={setShowChangePassword}
        isFirstLogin={isFirstLogin}
        onSuccess={handlePasswordChangeSuccess}
      />
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
}