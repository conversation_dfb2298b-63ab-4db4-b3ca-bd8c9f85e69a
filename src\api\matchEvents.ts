import { supabase } from "@/integrations/supabase/client";

async function ensureAuth() {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Sessão não autenticada");
  }
}

// Tipos
export type MatchEvent = {
  id: string;
  match_id: string;
  club_id: number;
  event_type: "goal" | "card" | "substitution" | "note";
  minute: string | null;
  player_id: string | null;
  player_in_id: string | null;
  event_data: any;
  created_at: string;
};

export type GoalEvent = {
  player_id: string;
  minute?: string;
  assist_player_id?: string;
  type?: "normal" | "penalty" | "free_kick" | "own_goal";
  team: "home" | "away";
};

export type CardEvent = {
  player_id: string;
  minute?: string;
  card_type: "yellow" | "red" | "second_yellow";
  reason?: string;
};

export type SubstitutionEvent = {
  player_out_id: string;
  player_in_id: string;
  minute?: string;
  reason?: string;
};

export type NoteEvent = {
  minute?: string;
  note: string;
};

// Funções para Match Events
export async function getMatchEvents(clubId: number, matchId: string): Promise<MatchEvent[]> {
  await ensureAuth();
  const { data, error } = await supabase
    .from("match_events")
    .select("*")
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .order("created_at");

  if (error) {
    console.error("Erro ao buscar eventos da partida:", error);
    throw new Error(`Erro ao buscar eventos da partida: ${error.message}`);
  }

  return data as MatchEvent[];
}

export async function createGoalEvent(
  clubId: number,
  matchId: string,
  goalEvent: GoalEvent
): Promise<MatchEvent> {
  await ensureAuth();
  const { data, error } = await supabase
    .from("match_events")
    .insert({
      match_id: matchId,
      club_id: clubId,
      event_type: "goal",
      minute: goalEvent.minute || null,
      player_id: goalEvent.player_id,
      event_data: {
        assist_player_id: goalEvent.assist_player_id || null,
        type: goalEvent.type || "normal",
        team: goalEvent.team
      }
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar evento de gol:", error);
    throw new Error(`Erro ao criar evento de gol: ${error.message}`);
  }

  // Atualizar o placar da partida
  await updateMatchScore(clubId, matchId);

  return data as MatchEvent;
}

export async function createCardEvent(
  clubId: number,
  matchId: string,
  cardEvent: CardEvent
): Promise<MatchEvent> {
  await ensureAuth();
  const { data, error } = await supabase
    .from("match_events")
    .insert({
      match_id: matchId,
      club_id: clubId,
      event_type: "card",
      minute: cardEvent.minute || null,
      player_id: cardEvent.player_id,
      event_data: {
        card_type: cardEvent.card_type,
        reason: cardEvent.reason || null
      }
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar evento de cartão:", error);
    throw new Error(`Erro ao criar evento de cartão: ${error.message}`);
  }

  // Verificar e aplicar suspensão automática se necessário
  console.log('🟡 [CREATE CARD] Criando cartão e processando suspensão:', {
    clubId,
    matchId,
    playerId: cardEvent.player_id,
    cardType: cardEvent.card_type
  });

  try {
    // Buscar informações da partida para obter competição/categoria
    const { data: matchData } = await supabase
      .from('matches')
      .select('competition_id, category_id')
      .eq('id', matchId)
      .eq('club_id', clubId)
      .single();

    console.log('🟡 [CREATE CARD] Dados da partida:', matchData);

    const { processCardSuspension } = await import('./cardSuspensions');
    await processCardSuspension(
      clubId, 
      cardEvent.player_id, 
      cardEvent.card_type, 
      matchId,
      matchData?.competition_id,
      matchData?.category_id
    );
  } catch (suspensionError) {
    console.error("❌ [CREATE CARD] Erro ao processar suspensão automática:", suspensionError);
    // Não falha a criação do cartão se houver erro na suspensão
  }

  return data as MatchEvent;
}

export async function createSubstitutionEvent(
  clubId: number,
  matchId: string,
  substitutionEvent: SubstitutionEvent
): Promise<MatchEvent> {
  await ensureAuth();
  const { data, error } = await supabase
    .from("match_events")
    .insert({
      match_id: matchId,
      club_id: clubId,
      event_type: "substitution",
      minute: substitutionEvent.minute || null,
      player_id: substitutionEvent.player_out_id,
      player_in_id: substitutionEvent.player_in_id,
      event_data: {
        reason: substitutionEvent.reason || null
      }
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar evento de substituição:", error);
    throw new Error(`Erro ao criar evento de substituição: ${error.message}`);
  }

  return data as MatchEvent;
}

export async function createNoteEvent(
  clubId: number,
  matchId: string,
  noteEvent: NoteEvent
): Promise<MatchEvent> {
  await ensureAuth();
  const { data, error } = await supabase
    .from("match_events")
    .insert({
      match_id: matchId,
      club_id: clubId,
      event_type: "note",
      minute: noteEvent.minute || null,
      event_data: {
        note: noteEvent.note
      }
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar evento de nota:", error);
    throw new Error(`Erro ao criar evento de nota: ${error.message}`);
  }

  return data as MatchEvent;
}

export async function deleteMatchEvent(clubId: number, eventId: string): Promise<boolean> {
  await ensureAuth();
  // Primeiro, obter o evento para saber se é um gol
  const { data: eventData, error: eventError } = await supabase
    .from("match_events")
    .select("*")
    .eq("id", eventId)
    .eq("club_id", clubId)
    .single();

  if (eventError) {
    console.error("Erro ao buscar evento para exclusão:", eventError);
    throw new Error(`Erro ao buscar evento para exclusão: ${eventError.message}`);
  }

  const event = eventData as MatchEvent;
  const isGoal = event.event_type === "goal";
  const matchId = event.match_id;

  // Excluir o evento
  const { error } = await supabase
    .from("match_events")
    .delete()
    .eq("id", eventId)
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao deletar evento da partida:", error);
    throw new Error(`Erro ao deletar evento da partida: ${error.message}`);
  }

  // Se for um gol, atualizar o placar
  if (isGoal) {
    await updateMatchScore(clubId, matchId);
  }

  return true;
}

// Função auxiliar para atualizar o placar da partida
async function updateMatchScore(clubId: number, matchId: string): Promise<void> {
  await ensureAuth();
  // Buscar todos os gols da partida
  const { data: goalsData, error: goalsError } = await supabase
    .from("match_events")
    .select("*")
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .eq("event_type", "goal");

  if (goalsError) {
    console.error("Erro ao buscar gols para atualizar placar:", goalsError);
    throw new Error(`Erro ao buscar gols para atualizar placar: ${goalsError.message}`);
  }

  const goals = goalsData as MatchEvent[];
  
  // Contar gols de casa e fora
  let homeGoals = 0;
  let awayGoals = 0;
  
  goals.forEach(goal => {
    const team = goal.event_data?.team;
    if (team === "home") {
      homeGoals++;
    } else if (team === "away") {
      awayGoals++;
    }
  });

  // Atualizar o placar na tabela matches
  const { error: updateError } = await supabase
    .from("matches")
    .update({
      score_home: homeGoals,
      score_away: awayGoals
    })
    .eq("id", matchId)
    .eq("club_id", clubId);

  if (updateError) {
    console.error("Erro ao atualizar placar da partida:", updateError);
    throw new Error(`Erro ao atualizar placar da partida: ${updateError.message}`);
  }
}
