import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { generateFAQSchema, type FAQItem } from '@/utils/seo/schemaGenerators';
import { Helmet } from 'react-helmet-async';

interface FAQSectionProps {
  faqs: FAQItem[];
  title?: string;
  description?: string;
}

export function FAQSection({ 
  faqs, 
  title = "Perguntas Frequentes",
  description 
}: FAQSectionProps) {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  const faqSchema = generateFAQSchema(faqs);

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(faqSchema)}
        </script>
      </Helmet>

      <section className="py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {title}
            </h2>
            {description && (
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index} className="overflow-hidden">
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full text-left p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {faq.question}
                    </h3>
                    {openItems.has(index) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    )}
                  </div>
                </button>
                
                {openItems.has(index) && (
                  <CardContent className="pt-0 pb-6">
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}

// FAQs pré-definidas por categoria
export const gestaoFAQs: FAQItem[] = [
  {
    question: "Como reduzir inadimplência de mensalidades em clubes?",
    answer: "Para reduzir inadimplência: 1) Automatize lembretes por email 3 dias antes do vencimento, 2) Ofereça pagamento via PIX com QR Code, 3) Implemente portal do atleta para acompanhamento em tempo real, 4) Configure aprovação rápida de comprovantes, 5) Ofereça desconto para pagamento antecipado."
  },
  {
    question: "Qual o melhor sistema para controle de minutagem de atletas?",
    answer: "O controle ideal de minutagem deve: 1) Calcular automaticamente baseado em escalação e substituições, 2) Gerar relatórios por atleta e temporada, 3) Alertar sobre sobrecarga de trabalho, 4) Integrar com sistema de escalação, 5) Permitir análise comparativa entre jogadores."
  },
  {
    question: "Como organizar departamentos em um clube de futebol?",
    answer: "Organize por: 1) Departamento Técnico (comissão, atletas), 2) Médico (fisioterapeutas, médicos), 3) Administrativo (dirigentes, secretaria), 4) Financeiro (tesouraria, contabilidade). Cada departamento deve ter permissões específicas no sistema."
  },
  {
    question: "Quais relatórios são essenciais para dirigentes de clube?",
    answer: "Relatórios essenciais: 1) Financeiro mensal (receitas/despesas), 2) Inadimplência por categoria, 3) Frequência de atletas, 4) Minutagem por jogador, 5) Agenda unificada, 6) Relatórios médicos consolidados."
  },
  {
    question: "Como implementar controle de acesso por perfis?",
    answer: "Defina perfis: 1) Administrador (acesso total), 2) Dirigente (módulos específicos), 3) Técnico (atletas e treinos), 4) Médico (prontuários), 5) Financeiro (mensalidades). Configure permissões granulares por funcionalidade."
  }
];

export const financeiroFAQs: FAQItem[] = [
  {
    question: "Como configurar mensalidades com PIX automático?",
    answer: "Configure: 1) Integração com API do banco, 2) Geração automática de QR Code por atleta, 3) Webhook para confirmação de pagamento, 4) Atualização automática do status, 5) Envio de comprovante por email."
  },
  {
    question: "Qual a melhor forma de fazer conciliação bancária?",
    answer: "Use: 1) Importação automática de extratos (OFX/CSV), 2) Matching automático por valor e data, 3) Regras de conciliação personalizadas, 4) Aprovação manual para divergências, 5) Relatórios de pendências."
  },
  {
    question: "Como controlar despesas por categoria no clube?",
    answer: "Organize por: 1) Categorias pré-definidas (alimentação, transporte, material), 2) Centro de custo por equipe, 3) Aprovação por alçada, 4) Anexo de comprovantes obrigatório, 5) Relatórios comparativos mensais."
  }
];

export const medicoFAQs: FAQItem[] = [
  {
    question: "Como organizar prontuários eletrônicos de atletas?",
    answer: "Estruture: 1) Dados pessoais e contatos, 2) Histórico médico familiar, 3) Exames periódicos com alertas, 4) Lesões e tratamentos, 5) Medicamentos em uso, 6) Liberações médicas para jogos."
  },
  {
    question: "Quais exames são obrigatórios para atletas?",
    answer: "Exames básicos: 1) Eletrocardiograma, 2) Ecocardiograma, 3) Teste ergométrico, 4) Hemograma completo, 5) Exame oftalmológico, 6) Avaliação ortopédica. Periodicidade conforme regulamento da federação."
  },
  {
    question: "Como controlar estoque de materiais médicos?",
    answer: "Implemente: 1) Cadastro por categoria (medicamentos, materiais), 2) Controle de validade com alertas, 3) Movimentação por atendimento, 4) Estoque mínimo por item, 5) Relatórios de consumo mensal."
  }
];