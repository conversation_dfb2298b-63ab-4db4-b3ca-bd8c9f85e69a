// Funções relacionadas a Matches serão migradas para cá a partir do api.ts

import { supabase } from "@/integrations/supabase/client";
import { createCallup } from "./callups";
import { getCategoryPlayers } from "./categories";
import { syncPlayerAggregatedStats } from "./playerStatsSync";
import { addMatchCategories } from "./matchCategories";

async function ensureAuth() {
  const {
    data: { session }
  } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("Sessão não autenticada");
  }
}

// Tipos
export type UpcomingMatch = {
  id: string;
  club_id: number;
  opponent: string;
  opponent_id?: string;
  opponent_logo_url?: string | null;
  competition: string;
  competition_id?: string;
  date: string;
  time: string;
  location: string;
  type: "casa" | "fora";
  escalacao: string[] | Record<string, string | null>;
  formation: string;
  season_id?: number;
  ida_volta?: boolean;
  shots?: number | null;
  shots_on_target?: number | null;
  corners?: number | null;
  fouls?: number | null;
  offsides?: number | null;
  category_id?: number;
  category_ids?: number[];
  create_callup?: boolean;
  callup_id?: number; // Novo campo para vincular à convocação
};

export type MatchHistory = {
  id: string;
  club_id: number;
  date: string;
  opponent: string;
  result: "win" | "loss" | "draw";
  score_home: number;
  score_away: number;
  competition: string;
  location: string;
  summary?: string;
  status?: string;
  type?: string;
  // Apenas para exibição agrupada no frontend, não salvo no banco
  estatisticas?: {
    posse: number;
    chutes: number;
    chutesNoGol: number;
    escanteios: number;
    faltas: number;
    impedimentos: number;
  };
  // CAMPOS DO BANCO (fonte de verdade)
  shots?: number | null;
  shots_on_target?: number | null;
  corners?: number | null;
  fouls?: number | null;
  offsides?: number | null;
  gols?: Array<Gol>;
  cartoes?: Array<Cartao>;
  escalacao: string[] | object;
  formation: string;
  season_id?: number;
};
export type Gol = {
  jogador: string;
  minuto?: number;
  assist?: string;
};

export type Cartao = {
  jogador: string;
  tipo: "amarelo" | "vermelho";
};

export type MatchStats = {
    posse: number;
    chutes: number;
    chutesNoGol: number;
    escanteios: number;
    faltas: number;
    impedimentos: number;
  };

// Tipos adicionais para compatibilidade
interface MatchDB {
    season_id: number;
    id: string;
    club_id: number;
    opponent: string;
    competition?: string;
    date: string;
    time?: string;
    location: string;
    type?: string;
    notes?: string;
    result?: string;
    escalacao?: Record<string, string | null> | string[];
    formation?: string;
    shots?: number | null;
    shots_on_target?: number | null;
    corners?: number | null;
    fouls?: number | null;
    offsides?: number | null;
    category_id?: number | null;
    callup_id?: number | null; // Novo campo para vincular à convocação
  }


// Funções para Upcoming Matches
export async function getUpcomingMatches(clubId: number, seasonId?: number, categoryId?: number): Promise<UpcomingMatch[]> {
    // const today = new Date();
    // const oneDayAgo = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    // const oneDayAgoStr = oneDayAgo.toISOString().split('T')[0];

    let query = supabase
      .from("matches")
      .select("*, opponents(*), competitions(*), categories(*)")
      .eq("club_id", clubId)
      .is("result", null)
      .order("date", { ascending: true });
      // .gte("date", oneDayAgoStr); // Apenas partidas que não passaram de 1 dia

    if (seasonId) {
      query = query.eq("season_id", seasonId);
    }

    if (categoryId) {
      const { data: mcData, error: mcError } = await supabase
        .from("match_categories")
        .select("match_id")
        .eq("club_id", clubId)
        .eq("category_id", categoryId);
      if (mcError) {
        console.error("Erro ao buscar categorias da partida:", mcError);
        throw new Error(`Erro ao buscar categorias da partida: ${mcError.message}`);
      }
      const matchIds = mcData?.map((mc: any) => mc.match_id) || [];
      if (matchIds.length === 0) {
        return [];
      }
      query = query.in("id", matchIds);
    }

    const { data, error } = await (query as any);

    if (error) {
      console.error("Erro ao buscar jogos futuros:", error);
      throw new Error(`Erro ao buscar jogos futuros: ${error.message}`);
    }

    const matchesData = (data || []) as Array<any>;

    return matchesData.map((match) => {
      if (!('escalacao' in match)) {
        console.error('ATENÇÃO: Campo escalacao não retornado no match:', match);
      }

      // Usar os novos campos se disponíveis, caso contrário usar o formato antigo
      const competition = match.competitions?.name || match.notes?.split('|')[0] || 'Amistoso';
      const time = match.time || match.notes?.split('|')[1] || '15:00';
      const type = match.type || match.notes?.split('|')[2] as "casa" | "fora" || 'casa';

      return {
        id: match.id,
        club_id: match.club_id,
        opponent: match.opponent,
        opponent_id: match.opponent_id,
        opponent_logo_url: match.opponents?.logo_url || null,
        competition: competition,
        competition_id: match.competition_id,
        date: match.date,
        time: time,
        location: match.location,
        type: type,
        escalacao: parseEscalacao(match.escalacao),
        formation: match.formation || '4-4-2',
        season_id: match.season_id,
        ida_volta: match.ida_volta,
        shots: match.shots ?? null,
        shots_on_target: match.shots_on_target ?? null,
        corners: match.corners ?? null,
        fouls: match.fouls ?? null,
        offsides: match.offsides ?? null,
        category_id: match.category_id ?? null,
      };
    });
  }

  export async function createUpcomingMatch(clubId: number, match: Omit<UpcomingMatch, "id">, userId?: string): Promise<UpcomingMatch> {
    // Ainda mantemos o notes para compatibilidade com código existente
    const notes = `${match.competition}|${match.time}|${match.type}`;

    console.log("Criando partida com categoria_id:", match.category_id);

    const { data, error } = await supabase
      .from("matches")
      .insert({
        club_id: clubId,
        opponent: match.opponent,
        opponent_id: match.opponent_id,
        competition_id: match.competition_id,
        date: match.date,
        location: match.location,
        notes: notes,
        type: match.type,
        time: match.time,
        result: null,
        escalacao: match.escalacao || [],
        formation: match.formation || '4-4-2',
        season_id: match.season_id,
        ida_volta: match.ida_volta ?? false,
        score_home: 0,
        score_away: 0,
        shots: match.shots ?? null,
        shots_on_target: match.shots_on_target ?? null,
        corners: match.corners ?? null,
        fouls: match.fouls ?? null,
        offsides: match.offsides ?? null,
        category_id: match.category_ids?.[0] ?? match.category_id ?? null,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar jogo:", error);
      throw new Error(`Erro ao criar jogo: ${error.message}`);
    }

    const matchDB = data as unknown as MatchDB;

    // Salvar categorias extras da partida
    try {
      if (match.category_ids && match.category_ids.length > 0) {
        await addMatchCategories(clubId, matchDB.id, match.category_ids);
      } else if (match.category_id) {
        await addMatchCategories(clubId, matchDB.id, [match.category_id]);
      }
    } catch (e) {
      console.error("Erro ao associar categorias à partida:", e);
    }

    // Atualizar eventos de gols e cartões se fornecidos
    if (match.gols !== undefined || match.cartoes !== undefined) {
      try {
        // Buscar jogadores para mapear nome -> ID
        const { data: playersData } = await supabase
          .from('players')
          .select('id, name')
          .eq('club_id', clubId);

        const playerMap = new Map<string, string>();
        (playersData || []).forEach(p => {
          playerMap.set(p.name, p.id);
        });

        if (match.gols !== undefined) {
          // Remover gols existentes
          await ensureAuth();
          await supabase
            .from('match_events')
            .delete()
            .eq('club_id', clubId)
            .eq('match_id', id)
            .eq('event_type', 'goal');

          const team = (matchDB.type || 'casa') === 'casa' ? 'home' : 'away';

          for (const g of match.gols) {
            await supabase.from('match_events').insert({
              match_id: id,
              club_id: clubId,
              event_type: 'goal',
              minute: g.minuto ? String(g.minuto) : null,
              player_id: playerMap.get(g.jogador) || null,
              event_data: {
                assist_player_id: g.assist ? playerMap.get(g.assist) || null : null,
                team,
                type: 'normal'
              }
            });
          }
        }

        if (match.cartoes !== undefined) {
          // Remover cartões existentes
          await ensureAuth();
          await supabase
            .from('match_events')
            .delete()
            .eq('club_id', clubId)
            .eq('match_id', id)
            .eq('event_type', 'card');

          for (const c of match.cartoes) {
            await supabase.from('match_events').insert({
              match_id: id,
              club_id: clubId,
              event_type: 'card',
              minute: null,
              player_id: playerMap.get(c.jogador) || null,
              event_data: {
                card_type: c.tipo === 'vermelho' ? 'red' : 'yellow',
                reason: null
              }
            });
          }
        }
      } catch (e) {
        console.error('Erro ao atualizar eventos da partida:', e);
      }
    }

    // Se a opção de criar convocação estiver habilitada e uma categoria estiver selecionada
    if (match.create_callup && match.category_id) {
      try {
        // Criar a convocação
        const callupId = await createCallupFromMatch(clubId, {
          ...match,
          id: matchDB.id,
          club_id: clubId
        }, match.category_id, userId);

        // Se a convocação foi criada com sucesso, NÃO adicionar automaticamente todos os jogadores
        // Os jogadores serão adicionados apenas quando a escalação for definida
        if (callupId) {
          console.log(`Convocação ${callupId} criada. Jogadores serão adicionados via escalação.`);
          // Atualizar a partida com o ID da convocação criada
          await supabase
            .from("matches")
            .update({ callup_id: callupId })
            .eq("id", matchDB.id);
        }
      } catch (callupError) {
        console.error("Erro ao criar convocação:", callupError);
        // Não interromper o fluxo se houver erro na criação da convocação
      }
    }

    return {
      id: matchDB.id,
      club_id: matchDB.club_id,
      opponent: match.opponent,
      competition: match.competition,
      date: matchDB.date,
      time: match.time,
      location: matchDB.location,
      type: match.type,
      escalacao: Array.isArray(matchDB.escalacao) ? matchDB.escalacao : matchDB.escalacao || [],
      formation: matchDB.formation || match.formation || '4-4-2',
      season_id: matchDB.season_id,
      ida_volta: match.ida_volta ?? false,
      shots: match.shots ?? null,
      shots_on_target: match.shots_on_target ?? null,
      corners: match.corners ?? null,
      fouls: match.fouls ?? null,
      offsides: match.offsides ?? null,
      category_id: match.category_id ?? null,
    };
  }

  export async function deleteUpcomingMatch(clubId: number, id: string): Promise<boolean> {
    const { error } = await supabase
      .from("matches")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      console.error("Erro ao deletar jogo:", error);
      throw new Error(`Erro ao deletar jogo: ${error.message}`);
    }

    return true;
  }

// Function to get upcoming matches for a specific player based on their categories
export async function getPlayerUpcomingMatches(clubId: number, playerId: string): Promise<UpcomingMatch[]> {
  // const today = new Date();
  // const oneDayAgo = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  // const oneDayAgoStr = oneDayAgo.toISOString().split('T')[0];

  // First, get the player's categories
  const { data: playerCategories, error: categoriesError } = await supabase
    .from("player_categories")
    .select("category_id")
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (categoriesError) {
    console.error("Erro ao buscar categorias do jogador:", categoriesError);
    throw new Error(`Erro ao buscar categorias do jogador: ${categoriesError.message}`);
  }

  if (!playerCategories || playerCategories.length === 0) {
    return [];
  }

  const categoryIds = playerCategories.map(pc => pc.category_id);

  const { data: mcData, error: mcError } = await supabase
    .from("match_categories")
    .select("match_id")
    .eq("club_id", clubId)
    .in("category_id", categoryIds);

  if (mcError) {
    console.error("Erro ao buscar categorias das partidas:", mcError);
    throw new Error(`Erro ao buscar categorias das partidas: ${mcError.message}`);
  }

  const matchIds = mcData?.map((mc: any) => mc.match_id) || [];

  if (matchIds.length === 0) {
    return [];
  }

  let query = supabase
    .from("matches")
    .select("*, opponents(*), competitions(*), categories(*)")
    .eq("club_id", clubId)
    .is("result", null)
    .in("id", matchIds)
    .order("date", { ascending: true });

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar partidas do jogador:", error);
    throw new Error(`Erro ao buscar partidas do jogador: ${error.message}`);
  }

  const matchesData = (data || []) as Array<any>;

  return matchesData.map((match) => {
    if (!('escalacao' in match)) {
      console.error('ATENÇÃO: Campo escalacao não retornado no match:', match);
    }

    return {
      id: match.id,
      club_id: match.club_id,
      opponent: match.opponent,
      opponent_id: match.opponent_id,
      opponent_logo_url: match.opponents?.logo_url || null,
      competition: match.competitions?.name || match.competition || "Competição não informada",
      competition_id: match.competition_id,
      date: match.date,
      time: match.time || "Horário não informado",
      location: match.location,
      type: match.type || "casa",
      escalacao: parseEscalacao(match.escalacao),
      formation: match.formation || '4-4-2',
      season_id: match.season_id,
      ida_volta: match.ida_volta || false,
      shots: match.shots,
      shots_on_target: match.shots_on_target,
      corners: match.corners,
      fouls: match.fouls,
      offsides: match.offsides,
      category_id: match.category_id
    };
  });
}

  // Funções para Match History
  export async function getMatchHistory(clubId: number, seasonId?: number, categoryId?: number): Promise<MatchHistory[]> {
    let query = supabase
      .from("matches")
      .select("*, categories(*)")
      .eq("club_id", clubId)
      .not("result", "is", null);

    if (seasonId) {
      query = query.eq("season_id", seasonId);
    }

    if (categoryId) {
      query = query.eq("category_id", categoryId);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Erro ao buscar histórico de partidas:", error);
      throw new Error(`Erro ao buscar histórico de partidas: ${error.message}`);
    }

    // Criar um array para armazenar as partidas com eventos
    const matchesWithEvents: MatchHistory[] = [];

    // Para cada partida, buscar seus eventos
    for (const match of data || []) {
      if (!('escalacao' in match)) {
        console.error('ATENÇÃO: Campo escalacao não retornado no match (histórico):', match);
      }

      const notesparts = match.notes?.split('|') || [];

      // Criar o objeto base da partida
      const matchHistory: MatchHistory = {
        id: match.id,
        club_id: match.club_id,
        date: match.date,
        opponent: match.opponent,
        result: match.result as "win" | "loss" | "draw",
        score_home: match.score_home || parseInt(notesparts[3] || '0'),
        score_away: match.score_away || parseInt(notesparts[4] || '0'),
        competition: notesparts[0],
        location: match.location,
        summary: notesparts[5],
        status: 'finalizado',
        type: notesparts[2] || 'casa',
        estatisticas: {
          posse: 50,
          chutes: match.shots ?? 0,
          chutesNoGol: match.shots_on_target ?? 0,
          escanteios: match.corners ?? 0,
          faltas: match.fouls ?? 0,
          impedimentos: match.offsides ?? 0
        },
        shots: match.shots ?? 0,
        shots_on_target: match.shots_on_target ?? 0,
        corners: match.corners ?? 0,
        fouls: match.fouls ?? 0,
        offsides: match.offsides ?? 0,
        gols: [],
        cartoes: [],
        escalacao: parseEscalacao(match.escalacao),
        formation: match.formation || '4-4-2',
        season_id: match.season_id
      };

      try {
        // Buscar eventos da partida
        await ensureAuth();
        const { data: eventsData, error: eventsError } = await supabase
          .from("match_events")
          .select("*")
          .eq("club_id", clubId)
          .eq("match_id", match.id);

        if (eventsError) {
          console.error("Erro ao buscar eventos da partida:", eventsError);
        } else if (eventsData) {
          // Processar gols
          const goals = eventsData.filter(event => event.event_type === "goal");
          const cards = eventsData.filter(event => event.event_type === "card");

          // Buscar informações dos jogadores
          try {
            // Buscar todos os jogadores do clube
            const { data: playersData, error: playersError } = await supabase
              .from("players")
              .select("id, name")
              .eq("club_id", clubId);

            if (playersError) {
              console.error("Erro ao buscar jogadores:", playersError);
            }

            // Criar um mapa de ID para nome de jogador
            const playerMap = new Map();
            if (playersData) {
              playersData.forEach(player => {
                playerMap.set(player.id, player.name);
              });
            }

            // Converter gols para o formato esperado
            matchHistory.gols = goals.map(goal => {
              // Buscar nome do jogador pelo ID
              const playerName = goal.player_id ? playerMap.get(goal.player_id) || "Jogador" : "Adversário";
              // Buscar nome do jogador que deu assistência pelo ID
              const assistName = goal.event_data?.assist_player_id ? playerMap.get(goal.event_data.assist_player_id) || "Jogador" : undefined;

              return {
                jogador: playerName,
                minuto: parseInt(goal.minute || "0"),
                assist: assistName
              };
            });

            // Converter cartões para o formato esperado
            matchHistory.cartoes = cards.map(card => {
              // Buscar nome do jogador pelo ID
              const playerName = card.player_id ? playerMap.get(card.player_id) || "Jogador" : "Jogador";

              return {
                jogador: playerName,
                tipo: card.event_data?.card_type === "red" ? "vermelho" : "amarelo"
              };
            });
          } catch (e) {
            console.error("Erro ao processar nomes dos jogadores:", e);

            // Fallback para IDs em caso de erro
            matchHistory.gols = goals.map(goal => {
              return {
                jogador: goal.player_id || "Adversário",
                minuto: parseInt(goal.minute || "0"),
                assist: goal.event_data?.assist_player_id
              };
            });

            matchHistory.cartoes = cards.map(card => {
              return {
                jogador: card.player_id || "Jogador",
                tipo: card.event_data?.card_type === "red" ? "vermelho" : "amarelo"
              };
            });
          }
        }
      } catch (e) {
        console.error("Erro ao processar eventos da partida:", e);
      }

      matchesWithEvents.push(matchHistory);
    }

    return matchesWithEvents;
  }

  export async function createMatchHistory(clubId: number, match: Omit<MatchHistory, "id"> & { placar_primeiro_jogo?: { home: number; away: number } }): Promise<MatchHistory> {
    // Soma placar do primeiro jogo se for ida/volta
    let score_home = match.score_home;
    let score_away = match.score_away;
    if (match.placar_primeiro_jogo) {
      score_home += match.placar_primeiro_jogo.home;
      score_away += match.placar_primeiro_jogo.away;
    }
    // Usa o type original do match, nunca calcula pelo location
    const notes = `${match.competition}|${match.date}|${match.type || 'casa'}|${score_home}|${score_away}|${match.summary || ''}`;

    const { data, error } = await supabase
      .from("matches")
      .insert({
        club_id: clubId,
        opponent: match.opponent,
        date: match.date,
        location: match.location,
        notes: notes,
        result: match.result,
        escalacao: match.escalacao || [],
        formation: match.formation || '4-4-2',
        season_id: match.season_id,
        score_home,
        score_away,
        shots: match.shots ?? null,
        shots_on_target: match.shots_on_target ?? null,
        corners: match.corners ?? null,
        fouls: match.fouls ?? null,
        offsides: match.offsides ?? null,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar histórico de partida:", error);
      throw new Error(`Erro ao criar histórico de partida: ${error.message}`);
    }

    const matchDB = data as unknown as MatchDB;

    return {
      id: matchDB.id,
      club_id: matchDB.club_id,
      date: matchDB.date,
      opponent: matchDB.opponent,
      result: matchDB.result as "win" | "loss" | "draw",
      score_home,
      score_away,
      competition: match.competition,
      location: matchDB.location,
      summary: match.summary,
      cartoes: [],
      escalacao: Array.isArray(matchDB.escalacao) ? matchDB.escalacao : matchDB.escalacao || [],
      formation: matchDB.formation || match.formation || '4-4-2',
      season_id: matchDB.season_id,
      shots: match.shots ?? null,
      shots_on_target: match.shots_on_target ?? null,
      corners: match.corners ?? null,
      fouls: match.fouls ?? null,
      offsides: match.offsides ?? null,
    };
  }

  export async function updateMatchHistory(clubId: number, id: string, match: Partial<MatchHistory>): Promise<MatchHistory> {
    // Primeiro precisamos obter o jogo atual
    const { data: existingMatchRaw, error: errorExisting } = await supabase
      .from("matches")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (errorExisting) {
      throw new Error(`Erro ao buscar jogo existente: ${errorExisting.message}`);
    }

    const existingMatch = existingMatchRaw as unknown as MatchDB;

    const notesparts = existingMatch.notes?.split('|') || ['Amistoso', '15:00', 'casa', '0', '0', ''];
    // Atualizamos apenas os campos fornecidos
    if (match.competition) notesparts[0] = match.competition;
    if (match.score_home !== undefined) notesparts[3] = String(match.score_home);
    if (match.score_away !== undefined) notesparts[4] = String(match.score_away);
    if (match.summary !== undefined) notesparts[5] = match.summary || '';

    const updatedNotes = notesparts.join('|');

    const { data, error } = await supabase
      .from("matches")
      .update({
        opponent: match.opponent || existingMatch.opponent,
        date: match.date || existingMatch.date,
        location: match.location || existingMatch.location,
        notes: updatedNotes,
        result: match.result || existingMatch.result,
        escalacao: (match as MatchDB).escalacao || existingMatch.escalacao,
        formation: (match as MatchDB).formation || existingMatch.formation || '4-4-2',
        season_id: match.season_id || existingMatch.season_id,
        score_home: match.score_home !== undefined ? match.score_home : existingMatch.score_home,
        score_away: match.score_away !== undefined ? match.score_away : existingMatch.score_away,
        shots: match.shots ?? existingMatch.shots ?? null,
        shots_on_target: match.shots_on_target ?? existingMatch.shots_on_target ?? null,
        corners: match.corners ?? existingMatch.corners ?? null,
        fouls: match.fouls ?? existingMatch.fouls ?? null,
        offsides: match.offsides ?? existingMatch.offsides ?? null,
      } as any)
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar histórico de partida:", error);
      throw new Error(`Erro ao atualizar histórico de partida: ${error.message}`);
    }

    const updatedNotesparts = data.notes?.split('|') || ['Amistoso', '15:00', 'casa', '0', '0', ''];

    const matchDB = data as unknown as MatchDB;

    // Atualizar eventos de gols e cartões se fornecidos
    if (match.gols !== undefined || match.cartoes !== undefined) {
      try {
        const { data: playersData } = await supabase
          .from('players')
          .select('id, name')
          .eq('club_id', clubId);

        const playerMap = new Map<string, string>();
        (playersData || []).forEach(p => {
          playerMap.set(p.name, p.id);
        });

        if (match.gols !== undefined) {
          await ensureAuth();
          await supabase
            .from('match_events')
            .delete()
            .eq('club_id', clubId)
            .eq('match_id', id)
            .eq('event_type', 'goal');

          const team = (matchDB.type || 'casa') === 'casa' ? 'home' : 'away';
          for (const g of match.gols) {
            await supabase.from('match_events').insert({
              match_id: id,
              club_id: clubId,
              event_type: 'goal',
              minute: g.minuto ? String(g.minuto) : null,
              player_id: playerMap.get(g.jogador) || null,
              event_data: {
                assist_player_id: g.assist ? playerMap.get(g.assist) || null : null,
                team,
                type: 'normal'
              }
            });
          }
        }

        if (match.cartoes !== undefined) {
          await ensureAuth();
          await supabase
            .from('match_events')
            .delete()
            .eq('club_id', clubId)
            .eq('match_id', id)
            .eq('event_type', 'card');

          for (const c of match.cartoes) {
            await supabase.from('match_events').insert({
              match_id: id,
              club_id: clubId,
              event_type: 'card',
              minute: null,
              player_id: playerMap.get(c.jogador) || null,
              event_data: {
                card_type: c.tipo === 'vermelho' ? 'red' : 'yellow',
                reason: null
              }
            });
          }
        }
      } catch (e) {
        console.error('Erro ao atualizar eventos da partida:', e);
      }
    }

    return {
      id: matchDB.id,
      club_id: matchDB.club_id,
      date: matchDB.date,
      opponent: matchDB.opponent,
      result: matchDB.result as "win" | "loss" | "draw",
      score_home: parseInt(updatedNotesparts[3]),
      score_away: parseInt(updatedNotesparts[4]),
      competition: updatedNotesparts[0],
      location: matchDB.location,
      summary: updatedNotesparts[5],
      status: "finalizado",
      type: updatedNotesparts[2] || "casa",
      estatisticas: {
        posse: 50,
        chutes: data.shots ?? 0,
        chutesNoGol: data.shots_on_target ?? 0,
        escanteios: data.corners ?? 0,
        faltas: data.fouls ?? 0,
        impedimentos: data.offsides ?? 0
      },
      shots: data.shots ?? 0,
      shots_on_target: data.shots_on_target ?? 0,
      corners: data.corners ?? 0,
      fouls: data.fouls ?? 0,
      offsides: data.offsides ?? 0,
      gols: match.gols ?? [],
      cartoes: match.cartoes ?? [],
      escalacao: Array.isArray(matchDB.escalacao) ? matchDB.escalacao : matchDB.escalacao || [],
      formation: matchDB.formation || (match as any).formation || '4-4-2',
      season_id: matchDB.season_id
    } satisfies MatchHistory;
  }

  export async function updateUpcomingMatch(clubId: number, id: string, match: Partial<UpcomingMatch>): Promise<UpcomingMatch> {
  const { data, error } = await supabase
    .from("matches")
    .update({
      opponent: match.opponent,
      competition: match.competition_id,
      date: match.date,
      time: match.time,
      location: match.location,
      type: match.type,
      season_id: match.season_id,
      category_id: match.category_id,
      ida_volta: match.ida_volta,
      shots: match.shots,
      shots_on_target: match.shots_on_target,
      corners: match.corners,
      fouls: match.fouls,
      offsides: match.offsides
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar partida futura:", error);
    throw new Error(`Erro ao atualizar partida futura: ${error.message}`);
  }

  return {
    id: data.id,
    club_id: data.club_id,
    opponent: data.opponent,
    competition: data.competition_name || data.competition || '', // Adicionado fallback para competition_name
    date: data.date,
    time: data.time || '18:00',
    location: data.location || '',
    type: data.type as "casa" | "fora",
    escalacao: data.escalacao || [],
    formation: data.formation || '4-4-2',
    season_id: data.season_id,
    category_id: data.category_id || null,
    ida_volta: data.ida_volta || false,
    shots: data.shots || null,
    shots_on_target: data.shots_on_target || null,
    corners: data.corners || null,
    fouls: data.fouls || null,
    offsides: data.offsides || null
  };
}

export async function deleteMatchHistory(clubId: number, id: string): Promise<boolean> {
    // Apagar dados relacionados que podem impedir a exclusão por FK
  const relatedTables = [
    "match_lineups",
    "match_squad",
    "match_substitutions",
    "match_player_minutes",
    "match_events",
  ];
  for (const table of relatedTables) {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq("club_id", clubId)
        .eq("match_id", id);
      if (error &&
          !error.message?.includes('does not exist') &&
          error.code !== 'PGRST106') {
        console.warn(`Erro ao limpar dados em ${table}:`, error.message);
      }
    } catch (err: any) {
      if (!err.message?.includes('does not exist')) {
        console.warn(`Erro inesperado ao limpar dados em ${table}:`, err);
      }
    }
  }
  
  // Excluir estatísticas dos jogadores relacionadas a esta partida
  try {
    const { error: statsError } = await supabase
      .from("player_match_statistics")
      .delete()
      .eq("club_id", clubId)
      .eq("match_id", id);
    if (statsError &&
        !statsError.message?.includes('does not exist') &&
        statsError.code !== 'PGRST106') {
      console.warn(`Erro ao limpar estatísticas dos jogadores:`, statsError.message);
    }
  } catch (err: any) {
    if (!err.message?.includes('does not exist')) {
      console.warn(`Erro inesperado ao limpar estatísticas dos jogadores:`, err);
    }
  }
  const { error } = await supabase
  .from("matches")
  .delete()
  .eq("club_id", clubId)
  .eq("id", id);

if (error) {
  console.error("Erro ao deletar histórico de partida:", error);
  throw new Error(`Erro ao deletar histórico de partida: ${error.message}`);
} 
if (!error) {
  // Sincronizar estatísticas agregadas dos jogadores após a exclusão
  try {
    await syncPlayerAggregatedStats(clubId);
  } catch (err) {
    console.warn('Erro ao sincronizar estatísticas agregadas após exclusão da partida:', err);
  }
}

return true;
}

export async function updateMatchEscalacao(
    clubId: number,
    matchId: string,
    escalacao: string[] | string | Record<string, string | null>,
    formation?: string
  ): Promise<boolean> {
    console.log('[updateMatchEscalacao] clubId:', clubId, 'matchId:', matchId, 'escalacao:', escalacao, 'formation:', formation);
    const { error } = await supabase
      .from("matches")
      .update({ escalacao, ...(formation ? { formation } : {}) } as any)
      .eq("club_id", clubId)
      .eq("id", matchId);
    console.log('[updateMatchEscalacao] error:', error);
    if (error) {
      console.error("Erro ao atualizar escalação da partida:", error);
      throw new Error(`Erro ao atualizar escalação da partida: ${error.message}`);
    }
    return true;
  }

// Função para criar uma convocação a partir de uma partida
export async function createCallupFromMatch(
  clubId: number,
  match: UpcomingMatch,
  categoryId: number,
  userId?: string
): Promise<number | null> {
  try {
    // Buscar logo do clube mandante
    const { data: clubInfo } = await supabase
      .from("club_info")
      .select("logo_url")
      .eq("id", clubId)
      .single();

    // Buscar logo do adversário se disponível
    let awayLogo: string | null = null;
    if (match.opponent_id) {
      const { data: opponent } = await supabase
        .from("opponents")
        .select("logo_url")
        .eq("id", match.opponent_id)
        .single();
      awayLogo = opponent?.logo_url || null;
    }

    // Criar a convocação
    const callupData = {
      category_id: categoryId,
      tournament_type: match.competition,
      match_date: new Date(match.date + 'T' + match.time).toISOString(),
      match_location: match.location,
      match_schedule: `Horário da partida: ${match.time}`,
      hotel_control: "",
      home_club_logo: match.type === "fora" ? awayLogo : (clubInfo?.logo_url || null),
      away_club_logo: match.type === "fora" ? (clubInfo?.logo_url || null) : awayLogo,
      match_id: match.id,
    };

    const callup = await createCallup(clubId, callupData, userId);

    return callup.id;
  } catch (error) {
    console.error("Erro ao criar convocação a partir da partida:", error);
    return null;
  }
}

// Função para adicionar jogadores de uma categoria a uma convocação
export async function addCategoryPlayersToCallup(
  clubId: number,
  callupId: number,
  categoryId: number,
  userId?: string
): Promise<boolean> {
  try {
    // Buscar jogadores da categoria
    const players = await getCategoryPlayers(clubId, categoryId);

    // Se não houver jogadores, retornar
    if (!players || players.length === 0) {
      return false;
    }

    // Adicionar jogadores à convocação
    for (const player of players) {
      // Usar a API do Supabase diretamente para evitar múltiplas chamadas de função
      await supabase
        .from("callup_players")
        .insert({
          club_id: clubId,
          callup_id: callupId,
          player_id: player.id,
          role: "Atleta"
        });
    }

    return true;
  } catch (error) {
    console.error("Erro ao adicionar jogadores à convocação:", error);
    return false;
  }
}

function parseEscalacao(escalacao: any): any {
  // Garantir compatibilidade: se for string JSON, parse, se array, retorna, se objeto, retorna array de valores
  if (!escalacao) return [];
  if (Array.isArray(escalacao)) return escalacao;
  if (typeof escalacao === 'string') {
    try {
      const parsed = JSON.parse(escalacao);
      if (Array.isArray(parsed)) return parsed;
      if (typeof parsed === 'object' && parsed !== null) return Object.values(parsed);
    } catch {
      // Se não for JSON válido, retorna string em array
      return [escalacao];
    }
  }
  if (typeof escalacao === 'object') return Object.values(escalacao);
  return [];
}