# Sistema de Permissões - Game Day Nexus Platform

## Visão Geral
O sistema de permissões é baseado em um modelo RBAC (Role-Based Access Control) com suporte a permissões granulares. As permissões são organizadas por módulos e podem ser atribuídas a papéis (roles) ou diretamente a usuários.

## Estrutura de Permissões

### Níveis de Acesso
1. **Visualização (VIEW)**: Permite apenas visualizar os dados
2. **Criação (CREATE)**: Permite adicionar novos registros
3. **Edição (EDIT)**: Permite modificar registros existentes
4. **Exclusão (DELETE)**: Permite remover registros
5. **Permissões Específicas**: Alguns módulos têm permissões adicionais específicas

## Módulos e Permissões

### 1. <PERSON><PERSON><PERSON> (Players)
```
players.view         // Visualizar lista de jogadores
players.view_own     // Visualizar apenas o próprio perfil (para jogadores)
players.edit_own     // Editar apenas o próprio perfil (para jogadores)
players.create       // Adicionar novos jogadores
players.edit         // Editar jogadores existentes
players.delete       // Remover jogadores
players.reset_password // Alterar senha do jogador
players.documents.view    // Visualizar documentos
players.documents.verify  // Verificar documentos
players.finances.view     // Visualizar finanças
players.finances.edit     // Gerenciar finanças
players.evaluation.view   // Visualizar avaliações
players.evaluation.edit   // Gerenciar avaliações
```

### 2. Partidas (Matches)
```
matches.view    // Visualizar partidas
matches.create  // Criar partidas
matches.edit    // Editar partidas
matches.delete  // Excluir partidas
matches.lineup  // Gerenciar escalação
matches.events  // Registrar eventos (gols, cartões, etc.)
```

### 3. Adversários (Opponents)
```
opponents.view    // Visualizar adversários
opponents.create  // Adicionar adversários
opponents.edit    // Editar adversários
opponents.delete  // Excluir adversários
```
### 4. Treinamentos (Trainings)
```
trainings.view    // Visualizar treinamentos
trainings.create  // Criar treinamentos
trainings.edit    // Editar treinamentos
trainings.delete  // Excluir treinamentos
trainings.locations // Gerenciar locais de treinamento
```

### 5. Departamento Médico (Medical)
```
medical.view    // Visualizar prontuários
medical.create  // Criar prontuários
medical.edit    // Editar prontuários
medical.delete  // Excluir prontuários
```

### 6. Finanças (Finance)
```
finances.view    // Visualizar finanças
finances.create  // Criar transações
finances.edit    // Editar transações
finances.delete  // Excluir transações
```

### 7. Usuários (Users)
```
users.view         // Visualizar usuários
users.create       // Criar usuários
users.edit         // Editar usuários
users.delete       // Excluir usuários
users.permissions  // Gerenciar permissões
```

### 8. Configurações (Settings)
```
settings.view  // Visualizar configurações
settings.edit  // Editar configurações
```

### 9. Agenda (Agenda)
```
agenda.view    // Visualizar agenda
agenda.create  // Criar eventos
agenda.edit    // Editar eventos
agenda.delete  // Excluir eventos
```

### 10. Pré Cadastro (Evaluation)
```
evaluation.tabs.players         // Acessar aba Atletas em Pré Cadastro
evaluation.tabs.invitations     // Acessar aba Convites
evaluation.tabs.new             // Acessar aba Novo Convite
evaluation.tabs.dashboard       // Acessar aba Dashboard
evaluation.invitations.create   // Criar convites de pré cadastro
evaluation.invitations.copy     // Copiar link de convite
evaluation.invitations.resend   // Reenviar convite
evaluation.invitations.delete   // Excluir convites de pré cadastro
evaluation.players.edit         // Editar atletas em pré cadastro
evaluation.players.delete       // Excluir atletas em pré cadastro
evaluation.players.schedule     // Agendar pré cadastro
evaluation.players.update_status // Atualizar status do pré cadastro
evaluation.players.verify_documents // Verificar documentos do pré cadastro
```

Para acessar a página de Pré Cadastro é necessário possuir pelo menos uma das
permissões de abas acima (`evaluation.tabs.*`).

## Papéis Predefinidos (Roles)

O sistema inclui papéis predefinidos como:
- `admin`: Acesso total ao sistema
- `manager`: Acesso a todas as funcionalidades, exceto configurações do sistema
- `coach`: Acesso a jogadores, treinamentos e partidas
- `doctor`: Acesso ao departamento médico
- `player`: Acesso limitado ao próprio perfil e informações relevantes

## Implementação Técnica

### Frontend
- **Hook**: `usePermission()` para verificar permissões
- **Componentes**: 
  - `PermissionGuard` para proteger rotas
  - `PermissionControl` para mostrar/ocultar elementos

### Backend
- **Tabela**: `club_members` armazena as permissões dos usuários
- **RLS**: Row Level Security no Supabase para proteção adicional

## Características Avançadas

1. **Permissões Específicas por Módulo**:
   - Ex: `players.documents.verify` para verificação de documentos
   - Ex: `matches.lineup` para gerenciar escalação de times
   - Ex: `users.permissions` para gerenciar permissões de outros usuários

2. **Permissões de Propriedade**:
   - `view_own` e `edit_own` permitem que usuários acessem apenas seus próprios dados

3. **Permissões por Departamento**:
   - É possível restringir permissões a departamentos específicos

4. **Permissões Específicas de Contexto**:
   - Algumas ações específicas podem ter suas próprias permissões (ex: aprovações, verificações)

5. **Hierarquia de Acesso**:
   - As permissões podem ser atribuídas de forma hierárquica, com alguns papéis tendo acesso a subconjuntos específicos de funcionalidades

## Como Adicionar Novas Permissões

1. Adicione a nova permissão em `src/constants/permissions.ts`
2. Atualize a documentação neste arquivo
3. Atualize os papéis predefinidos conforme necessário
4. Implemente as verificações de permissão no código

## Verificação de Permissões

Para verificar permissões em componentes React:

```typescript
import { usePermission } from '@/hooks/usePermission';

function MeuComponente() {
  const { hasPermission } = usePermission();
  
  if (hasPermission('players.view')) {
    return <div>Conteúdo restrito</div>;
  }
  
  return <div>Sem permissão</div>;
}
```

Para verificar permissões em funções assíncronas:

```typescript
import { usePermission } from '@/hooks/usePermission';

async function minhaFuncao() {
  const { checkPermission } = usePermission();
  
  if (await checkPermission('players.edit')) {
    // Executar ação restrita
  }
}
```

## Solução de Problemas

Se encontrar problemas com permissões:
1. Verifique se o usuário está autenticado
2. Confira se o usuário tem a role correta
3. Verifique se as permissões específicas estão atribuídas
4. Consulte os logs do servidor para erros de permissão

---

Documentação atualizada em: 27/05/2025