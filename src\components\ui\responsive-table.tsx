import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveTableProps {
  children: React.ReactNode;
  className?: string;
  minWidth?: string;
}

export function ResponsiveTable({
  children,
  className,
  minWidth = "600px"
}: ResponsiveTableProps) {
  return (
    <div className="overflow-x-auto -webkit-overflow-scrolling-touch">
      <div className={cn("inline-block min-w-full align-middle", className)}>
        <div
          className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
          style={{ minWidth }}
        >
          {children}
        </div>
      </div>
    </div>
  );
}

interface ResponsiveTableCellProps {
  children: React.ReactNode;
  className?: string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  showOnlyMobile?: boolean;
}

export function ResponsiveTableCell({
  children,
  className,
  hideOnMobile = false,
  hideOnTablet = false,
  showOnlyMobile = false
}: ResponsiveTableCellProps) {
  const cellClasses = cn(
    "py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm",
    {
      "hidden sm:table-cell": hideOnMobile,
      "hidden md:table-cell": hideOnTablet,
      "sm:hidden": showOnlyMobile,
    },
    className
  );

  return (
    <td className={cellClasses}>
      {children}
    </td>
  );
}

interface ResponsiveTableHeaderProps {
  children: React.ReactNode;
  className?: string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  showOnlyMobile?: boolean;
}

export function ResponsiveTableHeader({
  children,
  className,
  hideOnMobile = false,
  hideOnTablet = false,
  showOnlyMobile = false
}: ResponsiveTableHeaderProps) {
  const headerClasses = cn(
    "py-2 sm:py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-gray-500",
    {
      "hidden sm:table-cell": hideOnMobile,
      "hidden md:table-cell": hideOnTablet,
      "sm:hidden": showOnlyMobile,
    },
    className
  );

  return (
    <th className={headerClasses}>
      {children}
    </th>
  );
}

interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveCard({ children, className }: ResponsiveCardProps) {
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-sm border p-4 sm:p-6",
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveCardHeaderProps {
  title: string;
  description?: string;
  className?: string;
}

export function ResponsiveCardHeader({
  title,
  description,
  className
}: ResponsiveCardHeaderProps) {
  return (
    <div className={cn("mb-4 sm:mb-6", className)}>
      <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
        {title}
      </h3>
      {description && (
        <p className="text-sm sm:text-base text-gray-600 mt-1">
          {description}
        </p>
      )}
    </div>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: string;
  className?: string;
}

export function ResponsiveGrid({
  children,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = "gap-4",
  className
}: ResponsiveGridProps) {
  const gridClasses = cn(
    "grid",
    gap,
    {
      "grid-cols-1": cols.mobile === 1,
      "grid-cols-2": cols.mobile === 2,
      "grid-cols-3": cols.mobile === 3,
      "grid-cols-4": cols.mobile === 4,
    },
    {
      "sm:grid-cols-1": cols.tablet === 1,
      "sm:grid-cols-2": cols.tablet === 2,
      "sm:grid-cols-3": cols.tablet === 3,
      "sm:grid-cols-4": cols.tablet === 4,
    },
    {
      "lg:grid-cols-1": cols.desktop === 1,
      "lg:grid-cols-2": cols.desktop === 2,
      "lg:grid-cols-3": cols.desktop === 3,
      "lg:grid-cols-4": cols.desktop === 4,
      "lg:grid-cols-5": cols.desktop === 5,
    },
    className
  );

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
}

interface ResponsiveButtonProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export function ResponsiveButton({
  children,
  size = 'md',
  variant = 'primary',
  className,
  onClick,
  disabled = false
}: ResponsiveButtonProps) {
  const buttonClasses = cn(
    "inline-flex items-center justify-center rounded-md font-medium transition-colors",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    "disabled:pointer-events-none disabled:opacity-50",
    {
      // Size variants
      "px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm": size === 'sm',
      "px-3 sm:px-4 py-2 sm:py-2.5 text-sm sm:text-base": size === 'md',
      "px-4 sm:px-6 py-2.5 sm:py-3 text-base sm:text-lg": size === 'lg',

      // Variant styles
      "bg-primary text-primary-foreground hover:bg-primary/90": variant === 'primary',
      "bg-secondary text-secondary-foreground hover:bg-secondary/80": variant === 'secondary',
      "border border-input bg-background hover:bg-accent hover:text-accent-foreground": variant === 'outline',
    },
    className
  );

  return (
    <button
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

interface ResponsiveModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  className?: string;
}

export function ResponsiveModal({
  children,
  isOpen,
  onClose,
  title,
  className
}: ResponsiveModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Modal */}
      <div className={cn(
        "relative bg-white rounded-lg shadow-xl",
        "w-full max-w-md mx-4 sm:max-w-lg sm:mx-6 md:max-w-2xl lg:max-w-4xl",
        "max-h-[90vh] overflow-y-auto",
        className
      )}>
        {title && (
          <div className="px-4 sm:px-6 py-4 border-b">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              {title}
            </h2>
          </div>
        )}

        <div className="p-4 sm:p-6">
          {children}
        </div>
      </div>
    </div>
  );
}

// Hook para detectar tamanho da tela
export function useResponsive() {
  const [screenSize, setScreenSize] = React.useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  React.useEffect(() => {
    const checkScreenSize = () => {
      if (window.innerWidth < 640) {
        setScreenSize('mobile');
      } else if (window.innerWidth < 1024) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return {
    isMobile: screenSize === 'mobile',
    isTablet: screenSize === 'tablet',
    isDesktop: screenSize === 'desktop',
    screenSize,
  };
}