import { supabase } from "@/integrations/supabase/client";
import { syncPlayerAggregatedStats } from "@/api/playerStatsSync";

/**
 * Force synchronization of all player statistics
 * This function can be called to manually sync all player stats
 */
export async function forcePlayerStatsSync(clubId: number): Promise<void> {
  console.log("Iniciando sincronização forçada das estatísticas dos jogadores...");
  
  try {
    // 1. Buscar todas as partidas com gols e cartões
    const { data: matches, error: matchesError } = await supabase
      .from("match_history")
      .select("id, club_id, gols, cartoes, escalacao")
      .eq("club_id", clubId);
      
    if (matchesError) {
      throw new Error(`Erro ao buscar partidas: ${matchesError.message}`);
    }
    
    console.log(`Encontradas ${matches.length} partidas para processar`);
    
    // 2. Buscar todos os jogadores do clube
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name")
      .eq("club_id", clubId);
      
    if (playersError) {
      throw new Error(`Erro ao buscar jogadores: ${playersError.message}`);
    }
    
    const playerMap = new Map(players.map(p => [p.name, p.id]));
    
    // 3. Para cada partida, reprocessar as estatísticas
    for (const match of matches) {
      if (!match.gols && !match.cartoes) continue;
      
      console.log(`Processando partida ${match.id}...`);
      
      const goals = match.gols || [];
      const cards = match.cartoes || [];
      const lineup = Array.isArray(match.escalacao) ? match.escalacao : [];
      
      // Contar estatísticas por jogador
      const playerStats: Record<string, {
        goals: number;
        assists: number;
        yellowCards: number;
        redCards: number;
        minutesPlayed: number;
      }> = {};
      
      // Inicializar stats para todos os jogadores envolvidos
      const allPlayers = new Set([
        ...goals.map((g: any) => g.jogador),
        ...goals.filter((g: any) => g.assist).map((g: any) => g.assist),
        ...cards.map((c: any) => c.jogador),
        ...lineup
      ].filter(Boolean));
      
      allPlayers.forEach(playerName => {
        playerStats[playerName] = {
          goals: 0,
          assists: 0,
          yellowCards: 0,
          redCards: 0,
          minutesPlayed: lineup.includes(playerName) ? 90 : 0
        };
      });
      
      // Contar gols
      goals.forEach((goal: any) => {
        if (playerStats[goal.jogador]) {
          playerStats[goal.jogador].goals++;
        }
        if (goal.assist && playerStats[goal.assist]) {
          playerStats[goal.assist].assists++;
        }
      });
      
      // Contar cartões
      cards.forEach((card: any) => {
        if (playerStats[card.jogador]) {
          if (card.tipo === "amarelo") {
            playerStats[card.jogador].yellowCards++;
          } else {
            playerStats[card.jogador].redCards++;
          }
        }
      });
      
      // Salvar/atualizar estatísticas da partida
      for (const [playerName, stats] of Object.entries(playerStats)) {
        const playerId = playerMap.get(playerName);
        if (!playerId) continue;
        
        const matchStatsData = {
          club_id: clubId,
          match_id: match.id,
          player_id: playerId,
          minutes_played: stats.minutesPlayed,
          goals: stats.goals,
          assists: stats.assists,
          yellow_cards: stats.yellowCards,
          red_cards: stats.redCards,
          shots: 0,
          shots_on_target: 0,
          passes: 0,
          passes_completed: 0,
          key_passes: 0,
          tackles: 0,
          interceptions: 0,
          fouls_committed: 0,
          fouls_suffered: 0,
        };
        
        // Verificar se já existe
        const { data: existing } = await supabase
          .from("player_match_statistics")
          .select("id")
          .eq("club_id", clubId)
          .eq("match_id", match.id)
          .eq("player_id", playerId)
          .single();
          
        if (existing) {
          // Atualizar
          const { error } = await supabase
            .from("player_match_statistics")
            .update({
              ...matchStatsData,
              updated_at: new Date().toISOString(),
            })
            .eq("id", existing.id);
            
          if (error) {
            console.error(`Erro ao atualizar stats da partida para ${playerName}:`, error);
          }
        } else {
          // Inserir
          const { error } = await supabase
            .from("player_match_statistics")
            .insert(matchStatsData);
            
          if (error) {
            console.error(`Erro ao inserir stats da partida para ${playerName}:`, error);
          }
        }
      }
    }
    
    // 4. Sincronizar estatísticas agregadas para todos os jogadores
    console.log("Sincronizando estatísticas agregadas...");
    
    for (const player of players) {
      try {
        await syncPlayerAggregatedStats(clubId, player.id);
      } catch (error) {
        console.error(`Erro ao sincronizar stats para ${player.name}:`, error);
      }
    }
    
    console.log("Sincronização forçada concluída com sucesso!");
    
  } catch (error) {
    console.error("Erro durante a sincronização forçada:", error);
    throw error;
  }
}

/**
 * Sync stats for a specific player
 */
export async function forcePlayerStatsSyncForPlayer(clubId: number, playerId: string): Promise<void> {
  try {
    await syncPlayerAggregatedStats(clubId, playerId);
    console.log(`Estatísticas sincronizadas para o jogador ${playerId}`);
  } catch (error) {
    console.error(`Erro ao sincronizar estatísticas do jogador ${playerId}:`, error);
    throw error;
  }
}