import { TrainingDrill, TrainingElement, DrillStep } from '@/components/training/InteractiveTrainingBuilder';
import { 
  BaseExporter, 
  JSONExportOptions, 
  ExportResult, 
  ExportFormat 
} from '../ExportEngine';

// Data export schema versions
const EXPORT_SCHEMA_VERSION = '1.0.0';

// Exported drill data structure
interface ExportedDrillData {
  version: string;
  exportedAt: string;
  drill: TrainingDrill;
  metadata: {
    exportOptions: JSONExportOptions;
    platform: string;
    userAgent: string;
  };
  animations?: any[];
  trajectories?: any[];
  customData?: Record<string, any>;
}

// Import validation result
interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  version?: string;
  drill?: TrainingDrill;
}

// Data transformation utilities
class DataTransformer {
  // Serialize drill data with optional filtering
  static serializeDrill(
    drill: TrainingDrill, 
    options: JSONExportOptions
  ): ExportedDrillData {
    const exportData: ExportedDrillData = {
      version: EXPORT_SCHEMA_VERSION,
      exportedAt: new Date().toISOString(),
      drill: this.cleanDrillData(drill),
      metadata: {
        exportOptions: options,
        platform: 'Game Day Nexus Platform',
        userAgent: navigator.userAgent
      }
    };

    // Add animations if requested
    if (options.includeAnimations) {
      exportData.animations = this.extractAnimations(drill);
    }

    // Add trajectories if requested
    if (options.includeTrajectories) {
      exportData.trajectories = this.extractTrajectories(drill);
    }

    return exportData;
  }

  // Clean drill data by removing internal properties
  private static cleanDrillData(drill: TrainingDrill): TrainingDrill {
    const cleanedDrill = { ...drill };
    
    // Remove any internal or computed properties
    delete (cleanedDrill as any).__internal;
    delete (cleanedDrill as any).__computed;
    
    // Clean steps
    cleanedDrill.steps = drill.steps.map(step => this.cleanStepData(step));
    
    // Clean elements
    cleanedDrill.elements = drill.elements?.map(element => this.cleanElementData(element)) || [];
    
    return cleanedDrill;
  }

  private static cleanStepData(step: DrillStep): DrillStep {
    const cleanedStep = { ...step };
    
    // Remove internal properties
    delete (cleanedStep as any).__internal;
    
    // Clean elements in step
    cleanedStep.elements = step.elements.map(element => this.cleanElementData(element));
    
    return cleanedStep;
  }

  private static cleanElementData(element: TrainingElement): TrainingElement {
    const cleanedElement = { ...element };
    
    // Remove internal properties
    delete (cleanedElement as any).__internal;
    delete (cleanedElement as any).__selected;
    delete (cleanedElement as any).__dragging;
    
    return cleanedElement;
  }

  private static extractAnimations(drill: TrainingDrill): any[] {
    const animations: any[] = [];
    
    drill.steps.forEach((step, stepIndex) => {
      if (step.annotations) {
        step.annotations.forEach(annotation => {
          if (annotation.type === 'animation') {
            animations.push({
              stepIndex,
              stepId: step.id,
              ...annotation
            });
          }
        });
      }
    });
    
    return animations;
  }

  private static extractTrajectories(drill: TrainingDrill): any[] {
    const trajectories: any[] = [];
    
    drill.steps.forEach((step, stepIndex) => {
      if (step.annotations) {
        step.annotations.forEach(annotation => {
          if (annotation.type === 'trajectory') {
            trajectories.push({
              stepIndex,
              stepId: step.id,
              ...annotation
            });
          }
        });
      }
    });
    
    return trajectories;
  }

  // Deserialize and validate imported data
  static validateImportData(data: any): ImportValidationResult {
    const result: ImportValidationResult = {
      isValid: false,
      errors: [],
      warnings: []
    };

    try {
      // Check if data is valid JSON object
      if (!data || typeof data !== 'object') {
        result.errors.push('Invalid data format: Expected JSON object');
        return result;
      }

      // Check version
      if (!data.version) {
        result.warnings.push('No version information found');
      } else {
        result.version = data.version;
        if (!this.isVersionCompatible(data.version)) {
          result.warnings.push(`Version ${data.version} may not be fully compatible`);
        }
      }

      // Validate drill data
      if (!data.drill) {
        result.errors.push('No drill data found');
        return result;
      }

      const drillValidation = this.validateDrillData(data.drill);
      result.errors.push(...drillValidation.errors);
      result.warnings.push(...drillValidation.warnings);

      if (drillValidation.isValid) {
        result.drill = data.drill;
        result.isValid = result.errors.length === 0;
      }

      return result;
    } catch (error) {
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  private static isVersionCompatible(version: string): boolean {
    // Simple version compatibility check
    const [major] = version.split('.');
    const [currentMajor] = EXPORT_SCHEMA_VERSION.split('.');
    return major === currentMajor;
  }

  private static validateDrillData(drill: any): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!drill.id) errors.push('Drill ID is required');
    if (!drill.name) errors.push('Drill name is required');
    if (!drill.category) errors.push('Drill category is required');
    if (!drill.difficulty) errors.push('Drill difficulty is required');

    // Validate steps
    if (!Array.isArray(drill.steps)) {
      errors.push('Drill steps must be an array');
    } else {
      drill.steps.forEach((step: any, index: number) => {
        if (!step.id) errors.push(`Step ${index + 1}: ID is required`);
        if (!step.name) errors.push(`Step ${index + 1}: Name is required`);
        if (typeof step.duration !== 'number') errors.push(`Step ${index + 1}: Duration must be a number`);
        
        if (!Array.isArray(step.elements)) {
          warnings.push(`Step ${index + 1}: Elements should be an array`);
        }
      });
    }

    // Validate elements
    if (drill.elements && Array.isArray(drill.elements)) {
      drill.elements.forEach((element: any, index: number) => {
        if (!element.id) errors.push(`Element ${index + 1}: ID is required`);
        if (!element.type) errors.push(`Element ${index + 1}: Type is required`);
        if (!element.position || typeof element.position.x !== 'number' || typeof element.position.y !== 'number') {
          errors.push(`Element ${index + 1}: Valid position is required`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

export class DataExporter implements BaseExporter {
  private defaultOptions: JSONExportOptions = {
    format: 'json',
    quality: 'high',
    includeAnimations: true,
    includeTrajectories: true,
    minify: false,
    includeMetadata: true
  };

  async export(drill: TrainingDrill, options: JSONExportOptions): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      
      // Transform drill data
      const exportData = DataTransformer.serializeDrill(drill, mergedOptions);
      
      // Convert to JSON string
      const jsonString = mergedOptions.minify 
        ? JSON.stringify(exportData)
        : JSON.stringify(exportData, null, 2);
      
      // Create blob
      const jsonBlob = new Blob([jsonString], { type: 'application/json' });
      const filename = this.generateFilename(drill, 'json');
      
      return {
        success: true,
        data: jsonBlob,
        filename,
        size: jsonBlob.size,
        metadata: {
          format: 'json',
          fileSize: jsonBlob.size,
          exportedAt: new Date(),
          dimensions: { width: 0, height: 0 } // Not applicable for JSON
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: this.generateFilename(drill, 'json'),
        error: error instanceof Error ? error.message : 'JSON export failed'
      };
    }
  }

  // Export multiple drills as a collection
  async exportCollection(
    drills: TrainingDrill[], 
    options: JSONExportOptions,
    collectionName?: string
  ): Promise<ExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      
      const collectionData = {
        version: EXPORT_SCHEMA_VERSION,
        exportedAt: new Date().toISOString(),
        collectionName: collectionName || 'Drill Collection',
        drillCount: drills.length,
        drills: drills.map(drill => DataTransformer.serializeDrill(drill, mergedOptions)),
        metadata: {
          exportOptions: mergedOptions,
          platform: 'Game Day Nexus Platform',
          userAgent: navigator.userAgent
        }
      };
      
      const jsonString = mergedOptions.minify 
        ? JSON.stringify(collectionData)
        : JSON.stringify(collectionData, null, 2);
      
      const jsonBlob = new Blob([jsonString], { type: 'application/json' });
      const filename = `${collectionName || 'drill_collection'}_${new Date().toISOString().slice(0, 10)}.json`;
      
      return {
        success: true,
        data: jsonBlob,
        filename,
        size: jsonBlob.size,
        metadata: {
          format: 'json',
          fileSize: jsonBlob.size,
          exportedAt: new Date()
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: 'drill_collection_export_failed.json',
        error: error instanceof Error ? error.message : 'Collection export failed'
      };
    }
  }

  // Import and validate drill data
  async importDrill(jsonData: string | File): Promise<{
    success: boolean;
    drill?: TrainingDrill;
    errors: string[];
    warnings: string[];
  }> {
    try {
      let data: any;
      
      // Handle File input
      if (jsonData instanceof File) {
        const text = await this.readFileAsText(jsonData);
        data = JSON.parse(text);
      } else {
        data = JSON.parse(jsonData);
      }
      
      // Validate imported data
      const validation = DataTransformer.validateImportData(data);
      
      return {
        success: validation.isValid,
        drill: validation.drill,
        errors: validation.errors,
        warnings: validation.warnings
      };
    } catch (error) {
      return {
        success: false,
        errors: [`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  // Import drill collection
  async importCollection(jsonData: string | File): Promise<{
    success: boolean;
    drills: TrainingDrill[];
    errors: string[];
    warnings: string[];
    collectionName?: string;
  }> {
    try {
      let data: any;
      
      if (jsonData instanceof File) {
        const text = await this.readFileAsText(jsonData);
        data = JSON.parse(text);
      } else {
        data = JSON.parse(jsonData);
      }
      
      const errors: string[] = [];
      const warnings: string[] = [];
      const drills: TrainingDrill[] = [];
      
      // Check if it's a collection
      if (!data.drills || !Array.isArray(data.drills)) {
        // Try to import as single drill
        const singleImport = await this.importDrill(jsonData);
        return {
          success: singleImport.success,
          drills: singleImport.drill ? [singleImport.drill] : [],
          errors: singleImport.errors,
          warnings: singleImport.warnings
        };
      }
      
      // Validate each drill in collection
      data.drills.forEach((drillData: any, index: number) => {
        const validation = DataTransformer.validateImportData({ drill: drillData.drill || drillData });
        
        if (validation.isValid && validation.drill) {
          drills.push(validation.drill);
        } else {
          errors.push(`Drill ${index + 1}: ${validation.errors.join(', ')}`);
        }
        
        warnings.push(...validation.warnings.map(w => `Drill ${index + 1}: ${w}`));
      });
      
      return {
        success: drills.length > 0,
        drills,
        errors,
        warnings,
        collectionName: data.collectionName
      };
    } catch (error) {
      return {
        success: false,
        drills: [],
        errors: [`Collection import failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  // Export drill as template (simplified format)
  async exportTemplate(drill: TrainingDrill, templateName?: string): Promise<ExportResult> {
    try {
      const template = {
        version: EXPORT_SCHEMA_VERSION,
        type: 'template',
        name: templateName || drill.name,
        category: drill.category,
        difficulty: drill.difficulty,
        playersRequired: drill.playersRequired,
        description: drill.description,
        objectives: drill.objectives,
        equipmentNeeded: drill.equipmentNeeded,
        steps: drill.steps.map(step => ({
          name: step.name,
          description: step.description,
          duration: step.duration,
          elements: step.elements.map(element => ({
            type: element.type,
            position: element.position,
            properties: element.properties
          }))
        })),
        createdAt: new Date().toISOString()
      };
      
      const jsonString = JSON.stringify(template, null, 2);
      const jsonBlob = new Blob([jsonString], { type: 'application/json' });
      const filename = `template_${templateName || drill.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
      
      return {
        success: true,
        data: jsonBlob,
        filename,
        size: jsonBlob.size,
        metadata: {
          format: 'json',
          fileSize: jsonBlob.size,
          exportedAt: new Date()
        }
      };
    } catch (error) {
      return {
        success: false,
        filename: 'template_export_failed.json',
        error: error instanceof Error ? error.message : 'Template export failed'
      };
    }
  }

  // Utility methods
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private generateFilename(drill: TrainingDrill, format: string): string {
    const timestamp = new Date().toISOString().slice(0, 10);
    const safeName = drill.name.replace(/[^a-zA-Z0-9]/g, '_');
    return `${safeName}_${timestamp}.${format}`;
  }

  // Static utility methods for external use
  static async validateJSONFile(file: File): Promise<ImportValidationResult> {
    try {
      const text = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(new Error('Failed to read file'));
        reader.readAsText(file);
      });
      
      const data = JSON.parse(text);
      return DataTransformer.validateImportData(data);
    } catch (error) {
      return {
        isValid: false,
        errors: [`File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  static getSchemaVersion(): string {
    return EXPORT_SCHEMA_VERSION;
  }

  static getSupportedVersions(): string[] {
    return ['1.0.0']; // Add more versions as they become available
  }

  // Interface implementation
  validateOptions(options: JSONExportOptions): boolean {
    if (options.format !== 'json') return false;
    
    // All JSON options are optional with defaults
    return true;
  }

  getDefaultOptions(): JSONExportOptions {
    return { ...this.defaultOptions };
  }

  getSupportedFormats(): ExportFormat[] {
    return ['json'];
  }
}