import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Building2,
  Calendar,
  BarChart3,
  PieC<PERSON>,
  Download,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { supabase } from "@/integrations/supabase/client";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";
import { toast } from 'sonner';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface AnalyticsData {
  revenue: Array<{
    month: string;
    revenue: number;
    clubs: number;
    newClubs: number;
  }>;
  clubsGrowth: Array<{
    month: string;
    total: number;
    active: number;
    suspended: number;
    cancelled: number;
  }>;
  planDistribution: Array<{
    name: string;
    value: number;
    revenue: number;
  }>;
  churnRate: number;
  averageRevenue: number;
  ltv: number;
  monthlyGrowthRate: number;
  topClubs: Array<{
    name: string;
    revenue: number;
    plan: string;
    status: string;
  }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const Analytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    revenue: [],
    clubsGrowth: [],
    planDistribution: [],
    churnRate: 0,
    averageRevenue: 0,
    ltv: 0,
    monthlyGrowthRate: 0,
    topClubs: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('12'); // meses
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Calcular período
      const endDate = new Date();
      const startDate = subMonths(endDate, parseInt(timeRange));
      
      // Buscar dados de receita mensal
      const revenueData = await getRevenueData(startDate, endDate);
      
      // Buscar crescimento de clubes
      const clubsGrowthData = await getClubsGrowthData(startDate, endDate);
      
      // Buscar distribuição de planos
      const planDistributionData = await getPlanDistribution();
      
      // Buscar métricas gerais
      const metrics = await getGeneralMetrics();
      
      // Buscar top clubes
      const topClubsData = await getTopClubs();

      setAnalytics({
        revenue: revenueData,
        clubsGrowth: clubsGrowthData,
        planDistribution: planDistributionData,
        ...metrics,
        topClubs: topClubsData
      });
    } catch (error: any) {
      console.error('Erro ao carregar analytics:', error);
      toast.error('Erro ao carregar dados de analytics');
    } finally {
      setLoading(false);
    }
  };

  const getRevenueData = async (startDate: Date, endDate: Date) => {
    const { data: payments, error } = await supabase
      .from('master_payments')
      .select(`
        amount,
        paid_date,
        club_id,
        created_at
      `)
      .eq('status', 'paid')
      .gte('paid_date', startDate.toISOString())
      .lte('paid_date', endDate.toISOString());

    if (error) throw error;

    // Agrupar por mês
    const monthlyData: Record<string, { revenue: number; clubs: Set<number>; newClubs: Set<number> }> = {};
    
    payments?.forEach(payment => {
      const month = format(new Date(payment.paid_date!), 'yyyy-MM');
      const createdMonth = format(new Date(payment.created_at), 'yyyy-MM');
      
      if (!monthlyData[month]) {
        monthlyData[month] = { revenue: 0, clubs: new Set(), newClubs: new Set() };
      }
      
      monthlyData[month].revenue += payment.amount;
      monthlyData[month].clubs.add(payment.club_id);
      
      if (createdMonth === month) {
        monthlyData[month].newClubs.add(payment.club_id);
      }
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month: format(new Date(month + '-01'), 'MMM/yy', { locale: ptBR }),
      revenue: data.revenue,
      clubs: data.clubs.size,
      newClubs: data.newClubs.size
    })).sort((a, b) => a.month.localeCompare(b.month));
  };

  const getClubsGrowthData = async (startDate: Date, endDate: Date) => {
    await ensureAuthenticated();
    const { data: clubs, error } = await supabase
      .from('club_info')
      .select('created_at, subscription_status');

    if (error) throw error;

    // Simular dados mensais (em produção, isso seria calculado com base em snapshots históricos)
    const monthlyData: Record<string, { total: number; active: number; suspended: number; cancelled: number }> = {};
    
    for (let d = new Date(startDate); d <= endDate; d = new Date(d.getFullYear(), d.getMonth() + 1, 1)) {
      const month = format(d, 'yyyy-MM');
      const monthEnd = endOfMonth(d);
      
      const clubsUntilMonth = clubs?.filter(club => new Date(club.created_at) <= monthEnd) || [];
      
      monthlyData[month] = {
        total: clubsUntilMonth.length,
        active: clubsUntilMonth.filter(c => c.subscription_status === 'active').length,
        suspended: clubsUntilMonth.filter(c => c.subscription_status === 'suspended').length,
        cancelled: clubsUntilMonth.filter(c => c.subscription_status === 'cancelled').length
      };
    }

    return Object.entries(monthlyData).map(([month, data]) => ({
      month: format(new Date(month + '-01'), 'MMM/yy', { locale: ptBR }),
      ...data
    }));
  };

  const getPlanDistribution = async () => {
    await ensureAuthenticated();
    const { data, error } = await supabase
      .from('club_info')
      .select(`
        master_plan_id,
        master_plans:master_plan_id (name, price)
      `)
      .not('master_plan_id', 'is', null);

    if (error) throw error;

    const planCounts: Record<string, { count: number; revenue: number }> = {};
    
    data?.forEach(club => {
      const planName = club.master_plans?.name || 'Sem Plano';
      const planPrice = club.master_plans?.price || 0;
      
      if (!planCounts[planName]) {
        planCounts[planName] = { count: 0, revenue: 0 };
      }
      
      planCounts[planName].count++;
      planCounts[planName].revenue += planPrice;
    });

    return Object.entries(planCounts).map(([name, data]) => ({
      name,
      value: data.count,
      revenue: data.revenue
    }));
  };

  const getGeneralMetrics = async () => {
    // Buscar dados para calcular métricas
    await ensureAuthenticated();
    const { data: clubs } = await supabase
      .from('club_info')
      .select('created_at, subscription_status');

    const { data: payments } = await supabase
      .from('master_payments')
      .select('amount, club_id')
      .eq('status', 'paid');

    const totalClubs = clubs?.length || 0;
    const activeClubs = clubs?.filter(c => c.subscription_status === 'active').length || 0;
    const cancelledClubs = clubs?.filter(c => c.subscription_status === 'cancelled').length || 0;
    
    const churnRate = totalClubs > 0 ? (cancelledClubs / totalClubs) * 100 : 0;
    
    const totalRevenue = payments?.reduce((sum, p) => sum + p.amount, 0) || 0;
    const averageRevenue = activeClubs > 0 ? totalRevenue / activeClubs : 0;
    
    // LTV simplificado (receita média * 12 meses / churn rate)
    const ltv = churnRate > 0 ? (averageRevenue * 12) / (churnRate / 100) : averageRevenue * 12;
    
    // Taxa de crescimento mensal (simplificada)
    const currentMonth = clubs?.filter(c => {
      const created = new Date(c.created_at);
      const now = new Date();
      return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
    }).length || 0;
    
    const lastMonth = clubs?.filter(c => {
      const created = new Date(c.created_at);
      const lastMonthDate = subMonths(new Date(), 1);
      return created.getMonth() === lastMonthDate.getMonth() && created.getFullYear() === lastMonthDate.getFullYear();
    }).length || 0;
    
    const monthlyGrowthRate = lastMonth > 0 ? ((currentMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      churnRate: Math.round(churnRate * 100) / 100,
      averageRevenue: Math.round(averageRevenue * 100) / 100,
      ltv: Math.round(ltv * 100) / 100,
      monthlyGrowthRate: Math.round(monthlyGrowthRate * 100) / 100
    };
  };

  const getTopClubs = async () => {
    const { data, error } = await supabase
      .from('master_payments')
      .select(`
        club_id,
        amount,
        club_info:club_id (name, subscription_status),
        master_plans:plan_id (name)
      `)
      .eq('status', 'paid');

    if (error) throw error;

    // Agrupar por clube
    const clubRevenue: Record<number, { name: string; revenue: number; plan: string; status: string }> = {};
    
    data?.forEach(payment => {
      if (!clubRevenue[payment.club_id]) {
        clubRevenue[payment.club_id] = {
          name: payment.club_info?.name || 'Clube Desconhecido',
          revenue: 0,
          plan: payment.master_plans?.name || 'Sem Plano',
          status: payment.club_info?.subscription_status || 'unknown'
        };
      }
      clubRevenue[payment.club_id].revenue += payment.amount;
    });

    return Object.values(clubRevenue)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: { variant: 'default' as const, label: 'Ativo' },
      suspended: { variant: 'secondary' as const, label: 'Suspenso' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelado' }
    };

    const config = variants[status as keyof typeof variants] || variants.active;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Carregando analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-1">
            Análise detalhada de performance e crescimento
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3">Últimos 3 meses</SelectItem>
              <SelectItem value="6">Últimos 6 meses</SelectItem>
              <SelectItem value="12">Último ano</SelectItem>
              <SelectItem value="24">Últimos 2 anos</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={loadAnalytics} className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Atualizar
          </Button>
          <Button className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Exportar
          </Button>
        </div>
      </div>

      {/* KPIs Principais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Receita Média/Clube</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.averageRevenue)}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-500">+{analytics.monthlyGrowthRate}%</span>
                </div>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Taxa de Churn</p>
                <p className="text-2xl font-bold">{analytics.churnRate}%</p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                  <span className="text-sm text-gray-500">Meta: &lt;5%</span>
                </div>
              </div>
              <Users className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Lifetime Value</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.ltv)}</p>
                <p className="text-sm text-gray-500">Por clube</p>
              </div>
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Crescimento Mensal</p>
                <p className="text-2xl font-bold">{analytics.monthlyGrowthRate}%</p>
                <p className="text-sm text-gray-500">Novos clubes</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos Principais */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Receita Mensal */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Receita Mensal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analytics.revenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => formatCurrency(value)} />
                <Tooltip 
                  formatter={(value: number) => [formatCurrency(value), 'Receita']}
                  labelFormatter={(label) => `Mês: ${label}`}
                />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#0088FE" 
                  fill="#0088FE" 
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Crescimento de Clubes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Crescimento de Clubes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analytics.clubsGrowth}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="total" 
                  stroke="#0088FE" 
                  name="Total"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="active" 
                  stroke="#00C49F" 
                  name="Ativos"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="suspended" 
                  stroke="#FFBB28" 
                  name="Suspensos"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Distribuição de Planos e Top Clubes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Distribuição de Planos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Distribuição de Planos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={analytics.planDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {analytics.planDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number, name: string, props: any) => [
                    `${value} clubes`,
                    `Receita: ${formatCurrency(props.payload.revenue)}`
                  ]}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Clubes por Receita */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Top Clubes por Receita
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topClubs.slice(0, 8).map((club, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-sm">{club.name}</p>
                      <p className="text-xs text-gray-500">{club.plan}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm">{formatCurrency(club.revenue)}</p>
                    {getStatusBadge(club.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};