import { useState, useEffect } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Pencil, Trash2, Loader2, Trophy } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { ModuleGuard } from "@/components/guards/ModuleGuard";
import { useSeasonStore } from "@/store/useSeasonStore";
import { supabase } from "@/integrations/supabase/client";
import { COMPETITION_PERMISSIONS } from "@/constants/permissions";
import { usePermission } from "@/hooks/usePermission";
import { PermissionControl } from "@/components/PermissionControl";

// Tipo para as competições
type Competition = {
  id: string;
  club_id: number;
  name: string;
  season_id: number | null;
  type: string | null;
  logo_url: string | null;
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  updated_at: string;
};

// Tipo para as temporadas
type Season = {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
};

export default function Competicoes() {
  return (
    <ModuleGuard module="matches">
      <CompeticoesContent />
    </ModuleGuard>
  );
}

function CompeticoesContent() {
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentCompetition, setCurrentCompetition] = useState<Competition | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    season_id: "",
    type: "",
    logo_url: "",
    start_date: "",
    end_date: "",
  });
  const [submitting, setSubmitting] = useState(false);


  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { seasons, activeSeason } = useSeasonStore();
  const {can} = usePermission();

  // Carregar competições
  useEffect(() => {
    if (clubId) {
      fetchCompetitions();
    }
  }, [clubId]);

  const fetchCompetitions = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("competitions")
        .select("*")
        .eq("club_id", clubId)
        .order("name");

      if (error) throw error;
      setCompetitions(data || []);
    } catch (error) {
      console.error("Erro ao carregar competições:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as competições.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (competition: Competition | null = null) => {
    if (competition) {
      setCurrentCompetition(competition);
      setFormData({
        name: competition.name,
        season_id: competition.season_id?.toString() || "",
        type: competition.type || "",
        logo_url: competition.logo_url || "",
        start_date: competition.start_date || "",
        end_date: competition.end_date || "",
      });
    } else {
      setCurrentCompetition(null);
      setFormData({
        name: "",
        season_id: activeSeason?.id.toString() || "",
        type: "",
        logo_url: "",
        start_date: "",
        end_date: "",
      });
    }
    setDialogOpen(true);
  };

  const handleOpenDeleteDialog = (competition: Competition) => {
    setCurrentCompetition(competition);
    setDeleteDialogOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast({
        title: "Erro",
        description: "O nome da competição é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);

      const competitionData = {
        name: formData.name,
        season_id: formData.season_id ? parseInt(formData.season_id) : null,
        type: formData.type || null,
        logo_url: formData.logo_url || null,
        start_date: formData.start_date || null,
        end_date: formData.end_date || null,
        updated_at: new Date().toISOString(),
      };

      if (currentCompetition) {
        // Atualizar competição existente
        const { error } = await supabase
          .from("competitions")
          .update(competitionData)
          .eq("id", currentCompetition.id);

        if (error) throw error;

        toast({
          title: "Sucesso",
          description: "Competição atualizada com sucesso!",
        });
      } else {
        // Criar nova competição
        const { error } = await supabase.from("competitions").insert({
          ...competitionData,
          club_id: clubId,
        });

        if (error) throw error;

        toast({
          title: "Sucesso",
          description: "Competição adicionada com sucesso!",
        });
      }

      // Recarregar a lista de competições
      fetchCompetitions();
      setDialogOpen(false);
    } catch (error) {
      console.error("Erro ao salvar competição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a competição.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!currentCompetition) return;

    try {
      setSubmitting(true);

      // Verificar se a competição está sendo usada em alguma partida
      const { data: matchesData, error: matchesError } = await supabase
        .from("matches")
        .select("id")
        .eq("competition_id", currentCompetition.id)
        .limit(1);

      if (matchesError) throw matchesError;

      if (matchesData && matchesData.length > 0) {
        toast({
          title: "Erro",
          description: "Esta competição não pode ser excluída porque está sendo usada em partidas.",
          variant: "destructive",
        });
        setDeleteDialogOpen(false);
        return;
      }

      // Excluir competição
      const { error } = await supabase
        .from("competitions")
        .delete()
        .eq("id", currentCompetition.id);

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Competição excluída com sucesso!",
      });

      // Recarregar a lista de competições
      fetchCompetitions();
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir competição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a competição.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Função para obter o nome da temporada pelo ID
  const getSeasonName = (seasonId: number | null) => {
    if (!seasonId) return "-";
    const season = seasons.find(s => s.id === seasonId);
    return season ? season.name : "-";
  };

  // Função para obter a cor do badge pelo tipo de competição
  const getCompetitionTypeColor = (type: string | null) => {
    switch (type?.toLowerCase()) {
      case "liga":
        return "bg-primary/10 text-primary border-primary/20";
      case "copa":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "amistoso":
        return "bg-green-100 text-green-800 border-green-200";
      case "torneio":
        return "bg-secondary/10 text-secondary border-secondary/20";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">Competições</h1>
        <PermissionControl permission={COMPETITION_PERMISSIONS.CREATE}>
          <Button onClick={() => handleOpenDialog()}>
            <Plus className="h-4 w-4 mr-2" /> Adicionar Competição
          </Button>
        </PermissionControl>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Competições</CardTitle>
          <CardDescription>
            Gerencie as competições que seu clube participa.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : competitions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>Nenhuma competição cadastrada.</p>
              <PermissionControl permission={COMPETITION_PERMISSIONS.CREATE}>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => handleOpenDialog()}
                >
                  Adicionar sua primeira competição
                </Button>
              </PermissionControl>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Temporada</TableHead>
                  <TableHead>Período</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {competitions.map((competition) => (
                  <TableRow key={competition.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {competition.logo_url ? (
                          <img
                            src={competition.logo_url}
                            alt={competition.name}
                            className="h-6 w-6 object-contain"
                          />
                        ) : (
                          <Trophy className="h-5 w-5 text-amber-500" />
                        )}
                        {competition.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      {competition.type ? (
                        <Badge
                          variant="outline"
                          className={getCompetitionTypeColor(competition.type)}
                        >
                          {competition.type}
                        </Badge>
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell>{getSeasonName(competition.season_id)}</TableCell>
                    <TableCell>
                      {competition.start_date && competition.end_date ? (
                        `${new Date(competition.start_date).toLocaleDateString()} - ${new Date(competition.end_date).toLocaleDateString()}`
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                      <PermissionControl permission={COMPETITION_PERMISSIONS.EDIT}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleOpenDialog(competition)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </PermissionControl>
                        <PermissionControl permission={COMPETITION_PERMISSIONS.DELETE}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(competition)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </PermissionControl>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog para adicionar/editar competição */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>
              {currentCompetition ? "Editar Competição" : "Adicionar Competição"}
            </DialogTitle>
            <DialogDescription>
              Preencha os dados da competição abaixo.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nome *
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Tipo
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleSelectChange("type", value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Liga">Liga</SelectItem>
                    <SelectItem value="Copa">Copa</SelectItem>
                    <SelectItem value="Torneio">Torneio</SelectItem>
                    <SelectItem value="Amistoso">Amistoso</SelectItem>
                    <SelectItem value="Outro">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="season_id" className="text-right">
                  Temporada
                </Label>
                <Select
                  value={formData.season_id}
                  onValueChange={(value) => handleSelectChange("season_id", value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Selecione a temporada" />
                  </SelectTrigger>
                  <SelectContent>
                    {seasons.map((season) => (
                      <SelectItem key={season.id} value={season.id.toString()}>
                        {season.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="logo_url" className="text-right">
                  URL do Logo
                </Label>
                <Input
                  id="logo_url"
                  name="logo_url"
                  value={formData.logo_url}
                  onChange={handleInputChange}
                  className="col-span-3"
                  placeholder="https://exemplo.com/logo.png"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="start_date" className="text-right">
                  Data de Início
                </Label>
                <Input
                  id="start_date"
                  name="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="end_date" className="text-right">
                  Data de Término
                </Label>
                <Input
                  id="end_date"
                  name="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setDialogOpen(false)}
                disabled={submitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Dialog para confirmar exclusão */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir a competição "{currentCompetition?.name}"?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={submitting}
            >
              Cancelar
            </Button>
            <PermissionControl permission={COMPETITION_PERMISSIONS.DELETE}>
              <Button
                type="button"
                variant="destructive"
                onClick={handleDelete}
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Excluindo...
                  </>
                ) : (
                  "Excluir"
                )}
              </Button>
            </PermissionControl>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}