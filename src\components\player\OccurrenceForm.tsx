import { <PERSON><PERSON> } from "@/components/ui/button";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import * as z from "zod";
import { Download } from "lucide-react";
import { useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { generateOccurrencePDF } from '@/utils/occurrencePdfGenerator';

export const occurrenceSchema = z.object({
  type: z.enum(["occurrence", "divergence", "punishment", "training_release", "training_game_release"]),
  title: z.string().min(1, "Título é obrigatório"),
  description: z.string().min(1, "Descrição é obrigatória"),
  severity: z.enum(["low", "medium", "high"]),
  status: z.enum(["active", "resolved", "archived"]),
  resolution_notes: z.string().optional(),
});

export type OccurrenceFormData = z.infer<typeof occurrenceSchema>;

interface OccurrenceFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: OccurrenceFormData) => void;
  form: UseFormReturn<OccurrenceFormData>;
  playerName?: string;
}

export function OccurrenceForm({
  isOpen,
  onClose,
  onSubmit,
  form,
  playerName,
}: OccurrenceFormProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const { clubInfo } = useClubInfoStore();
  const { toast } = useToast();
  
  // Removendo a referência não utilizada para o relatório de impressão
  // Apenas mantendo para referência futura se necessário

  const handleSubmit = (data: OccurrenceFormData) => {
    onSubmit(data);
  };

  const handleDownloadPDF = async () => {
    try {
      const pdfBlob = await generateOccurrencePDF(
        { ...form.getValues(), player_name: playerName },
        clubInfo!
      );
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ocorrencia-${form.getValues('title')?.toLowerCase().replace(/\s+/g, '-') || 'sem-titulo'}.pdf`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o PDF da ocorrência.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Ocorrência</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="occurrence">Ocorrência</SelectItem>
                      <SelectItem value="divergence">Divergência</SelectItem>
                      <SelectItem value="punishment">Punição</SelectItem>
                      <SelectItem value="training_release">Liberação P/ Treinar</SelectItem>
                      <SelectItem value="training_game_release">Liberação P/ Treino e Jogo</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Digite a descrição"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Severidade</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a severidade" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="low">Baixa</SelectItem>
                      <SelectItem value="medium">Média</SelectItem>
                      <SelectItem value="high">Alta</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Ativo</SelectItem>
                      <SelectItem value="resolved">Resolvido</SelectItem>
                      <SelectItem value="archived">Arquivado</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("status") === "resolved" && (
              <FormField
                control={form.control}
                name="resolution_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notas de Resolução</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Digite as notas de resolução"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Área oculta para impressão */}
            <div ref={printRef} style={{ display: 'none' }}>
              <div style={{ padding: '20px', fontFamily: 'Arial' }}>
                <h1 style={{ fontSize: '24px', textAlign: 'center', marginBottom: '30px' }}>
                  REGISTRO DE OCORRÊNCIA
                </h1>
                <div style={{ marginBottom: '20px' }}>
                  <p><strong>Título:</strong> {form.watch('title')}</p>
                  <p><strong>Tipo:</strong> {
                    form.watch('type') === 'occurrence'
                      ? 'Ocorrência'
                      : form.watch('type') === 'divergence'
                        ? 'Divergência'
                        : form.watch('type') === 'punishment'
                          ? 'Punição'
                          : form.watch('type') === 'training_game_release'
                            ? 'Liberação P/ Treino e Jogo'
                            : 'Liberação P/ Treinar'
                  }</p>
                  <p><strong>Severidade:</strong> {form.watch('severity') === 'low' ? 'Baixa' : 
                                                      form.watch('severity') === 'medium' ? 'Média' : 'Alta'}</p>
                  <p><strong>Status:</strong> {form.watch('status') === 'active' ? 'Ativa' : 
                                               form.watch('status') === 'resolved' ? 'Resolvida' : 'Arquivada'}</p>
                  <p><strong>Data:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
                </div>
                <div style={{ marginBottom: '20px' }}>
                  <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Descrição:</h2>
                  <p style={{ whiteSpace: 'pre-wrap' }}>{form.watch('description')}</p>
                </div>
                {form.watch('status') === 'resolved' && form.watch('resolution_notes') && (
                  <div style={{ marginBottom: '20px' }}>
                    <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Notas de Resolução:</h2>
                    <p style={{ whiteSpace: 'pre-wrap' }}>{form.watch('resolution_notes')}</p>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="gap-2">
              <div className="flex gap-2">
                {form.watch('title') && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleDownloadPDF}
                    className="gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Baixar PDF
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button type="submit">
                  Salvar
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 