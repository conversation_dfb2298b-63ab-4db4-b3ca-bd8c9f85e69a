import { supabase } from "@/integrations/supabase/client";
import { ensureAuthenticated } from "@/integrations/supabase/ensureAuth";

const ensureSession = async () => {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    throw new Error('No active session');
  }
};

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables: string[];
}

export interface NotificationLog {
  id: number;
  type: string;
  recipient_email: string;
  subject: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at?: string;
  error_message?: string;
  club_id?: number;
  payment_id?: number;
  organization_id?: number;
}

// Templates de email predefinidos
export const EMAIL_TEMPLATES: Record<string, NotificationTemplate> = {
  PAYMENT_REMINDER: {
    id: 'payment_reminder',
    name: 'Lembrete de Pagamento',
    subject: 'Lembrete: Pagamento em aberto - {{clubName}}',
    body: `
      <PERSON><PERSON><PERSON> {{clubName}},
      
      <PERSON><PERSON><PERSON>s que esteja tudo bem! Este é um lembrete amigável sobre o pagamento do seu plano {{planName}}.
      
      Detalhes do pagamento:
      - Valor: {{amount}}
      - Vencimento: {{dueDate}}
      - Status: {{status}}
      
      Para manter seu acesso ativo, realize o pagamento até a data de vencimento.
      
      Se já realizou o pagamento, pode ignorar este email.
      
      Atenciosamente,
      Equipe Game Day Nexus
    `,
    variables: ['clubName', 'planName', 'amount', 'dueDate', 'status']
  },
  
  PAYMENT_OVERDUE: {
    id: 'payment_overdue',
    name: 'Pagamento em Atraso',
    subject: 'URGENTE: Pagamento em atraso - {{clubName}}',
    body: `
      Olá {{clubName}},
      
      Identificamos que o pagamento do seu plano está em atraso há {{daysOverdue}} dias.
      
      Detalhes:
      - Valor: {{amount}}
      - Vencimento: {{dueDate}}
      - Dias em atraso: {{daysOverdue}}
      
      Para evitar a suspensão do seu acesso, regularize o pagamento o quanto antes.
      
      Em caso de dúvidas, entre em contato conosco.
      
      Atenciosamente,
      Equipe Game Day Nexus
    `,
    variables: ['clubName', 'amount', 'dueDate', 'daysOverdue']
  },
  
  ACCESS_SUSPENDED: {
    id: 'access_suspended',
    name: 'Acesso Suspenso',
    subject: 'Acesso suspenso - {{clubName}}',
    body: `
      Olá {{clubName}},
      
      Infelizmente, devido ao atraso no pagamento, seu acesso ao Game Day Nexus foi temporariamente suspenso.
      
      Para reativar seu acesso:
      1. Realize o pagamento pendente de {{amount}}
      2. Entre em contato conosco para confirmar o pagamento
      
      Estamos aqui para ajudar. Entre em contato conosco para resolver esta situação.
      
      Atenciosamente,
      Equipe Game Day Nexus
    `,
    variables: ['clubName', 'amount']
  },
  
  ACCESS_REACTIVATED: {
    id: 'access_reactivated',
    name: 'Acesso Reativado',
    subject: 'Bem-vindo de volta! - {{clubName}}',
    body: `
      Olá {{clubName}},
      
      Ótimas notícias! Seu acesso ao Game Day Nexus foi reativado com sucesso.
      
      Você já pode acessar todas as funcionalidades do seu plano {{planName}}.
      
      Obrigado por manter sua conta em dia!
      
      Atenciosamente,
      Equipe Game Day Nexus
    `,
    variables: ['clubName', 'planName']
  },
  
  WELCOME_NEW_CLUB: {
    id: 'welcome_new_club',
    name: 'Boas-vindas Novo Clube',
    subject: 'Bem-vindo ao Game Day Nexus! - {{clubName}}',
    body: `
      Olá {{clubName}},
      
      Seja muito bem-vindo ao Game Day Nexus!
      
      Seu clube foi cadastrado com sucesso no plano {{planName}}.
      
      Próximos passos:
      1. Acesse sua conta em {{loginUrl}}
      2. Configure seu perfil
      3. Adicione seus usuários
      4. Comece a usar o sistema
      
      Se precisar de ajuda, nossa equipe está à disposição.
      
      Atenciosamente,
      Equipe Game Day Nexus
    `,
    variables: ['clubName', 'planName', 'loginUrl']
  }
};

// Enviar email usando Brevo
export const sendEmail = async (
  to: string,
  subject: string,
  htmlContent: string,
  textContent?: string
): Promise<boolean> => {
  try {
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': import.meta.env.VITE_BREVO_API_KEY || ''
      },
      body: JSON.stringify({
        sender: {
          name: import.meta.env.VITE_BREVO_SENDER_NAME || 'Game Day Nexus',
          email: import.meta.env.VITE_BREVO_SENDER_EMAIL || '<EMAIL>'
        },
        to: [{ email: to }],
        subject: subject,
        htmlContent: htmlContent,
        textContent: textContent || htmlContent.replace(/<[^>]*>/g, '')
      })
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Erro ao enviar email:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao enviar email:', error);
    return false;
  }
};

// Processar template com variáveis
export const processTemplate = (template: NotificationTemplate, variables: Record<string, string>): { subject: string; body: string } => {
  let processedSubject = template.subject;
  let processedBody = template.body;

  // Substituir variáveis no subject e body
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    processedSubject = processedSubject.replace(new RegExp(placeholder, 'g'), value);
    processedBody = processedBody.replace(new RegExp(placeholder, 'g'), value);
  });

  return {
    subject: processedSubject,
    body: processedBody
  };
};

// Registrar log de notificação
export const logNotification = async (
  type: string,
  recipientEmail: string,
  subject: string,
  status: 'sent' | 'failed' | 'pending',
  errorMessage?: string,
  clubId?: number,
  paymentId?: number,
  organizationId?: number
): Promise<void> => {
  try {
    await supabase
      .from('master_notification_logs')
      .insert({
        type,
        recipient_email: recipientEmail,
        subject,
        status,
        error_message: errorMessage,
        club_id: clubId,
        payment_id: paymentId,
        organization_id: organizationId,
        sent_at: status === 'sent' ? new Date().toISOString() : null
      });
  } catch (error) {
    console.error('Erro ao registrar log de notificação:', error);
  }
};

// Enviar lembrete de pagamento
export const sendPaymentReminder = async (clubId: number, paymentId: number): Promise<boolean> => {
  try {
    await ensureSession();
    // Buscar dados do clube e pagamento
    const { data: payment, error: paymentError } = await supabase
      .from('master_payments')
      .select(`
        *,
        club_info:club_id (name, email),
        master_plans:plan_id (name)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      throw new Error('Pagamento não encontrado');
    }

    const template = EMAIL_TEMPLATES.PAYMENT_REMINDER;
    const variables = {
      clubName: payment.club_info?.name || 'Clube',
      planName: payment.master_plans?.name || 'Plano',
      amount: new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(payment.amount),
      dueDate: new Date(payment.due_date).toLocaleDateString('pt-BR'),
      status: payment.status === 'overdue' ? 'Em atraso' : 'Pendente'
    };

    const { subject, body } = processTemplate(template, variables);
    const success = await sendEmail(payment.club_info?.email || '', subject, body);

    // Registrar log
    await logNotification(
      'payment_reminder',
      payment.club_info?.email || '',
      subject,
      success ? 'sent' : 'failed',
      success ? undefined : 'Falha no envio',
      clubId,
      paymentId
    );

    return success;
  } catch (error: any) {
    console.error('Erro ao enviar lembrete de pagamento:', error);
    return false;
  }
};

// Enviar notificação de pagamento em atraso
export const sendOverdueNotification = async (clubId: number, paymentId: number, daysOverdue: number): Promise<boolean> => {
  try {
    await ensureSession();
    const { data: payment, error: paymentError } = await supabase
      .from('master_payments')
      .select(`
        *,
        club_info:club_id (name, email),
        master_plans:plan_id (name)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      throw new Error('Pagamento não encontrado');
    }

    const template = EMAIL_TEMPLATES.PAYMENT_OVERDUE;
    const variables = {
      clubName: payment.club_info?.name || 'Clube',
      amount: new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(payment.amount),
      dueDate: new Date(payment.due_date).toLocaleDateString('pt-BR'),
      daysOverdue: daysOverdue.toString()
    };

    const { subject, body } = processTemplate(template, variables);
    const success = await sendEmail(payment.club_info?.email || '', subject, body);

    await logNotification(
      'payment_overdue',
      payment.club_info?.email || '',
      subject,
      success ? 'sent' : 'failed',
      success ? undefined : 'Falha no envio',
      clubId,
      paymentId
    );

    return success;
  } catch (error: any) {
    console.error('Erro ao enviar notificação de atraso:', error);
    return false;
  }
};

// Enviar notificação de suspensão de acesso
export const sendAccessSuspendedNotification = async (clubId: number): Promise<boolean> => {
  try {
    await ensureSession();
    await ensureAuthenticated();
    const { data: club, error: clubError } = await supabase
      .from('club_info')
      .select(`
        *,
        master_plans:master_plan_id (name)
      `)
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error('Clube não encontrado');
    }

    // Buscar pagamento em atraso
    const { data: overduePayment } = await supabase
      .from('master_payments')
      .select('amount')
      .eq('club_id', clubId)
      .eq('status', 'overdue')
      .order('due_date', { ascending: false })
      .limit(1)
      .single();

    const template = EMAIL_TEMPLATES.ACCESS_SUSPENDED;
    const variables = {
      clubName: club.name,
      amount: overduePayment ? 
        new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(overduePayment.amount) :
        'Valor pendente'
    };

    const { subject, body } = processTemplate(template, variables);
    const success = await sendEmail(club.email, subject, body);

    await logNotification(
      'access_suspended',
      club.email,
      subject,
      success ? 'sent' : 'failed',
      success ? undefined : 'Falha no envio',
      clubId
    );

    return success;
  } catch (error: any) {
    console.error('Erro ao enviar notificação de suspensão:', error);
    return false;
  }
};

// Enviar notificação de reativação de acesso
export const sendAccessReactivatedNotification = async (clubId: number): Promise<boolean> => {
  try {
    await ensureAuthenticated();
    const { data: club, error: clubError } = await supabase
      .from('club_info')
      .select(`
        *,
        master_plans:master_plan_id (name)
      `)
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error('Clube não encontrado');
    }

    const template = EMAIL_TEMPLATES.ACCESS_REACTIVATED;
    const variables = {
      clubName: club.name,
      planName: club.master_plans?.name || 'Seu plano'
    };

    const { subject, body } = processTemplate(template, variables);
    const success = await sendEmail(club.email, subject, body);

    await logNotification(
      'access_reactivated',
      club.email,
      subject,
      success ? 'sent' : 'failed',
      success ? undefined : 'Falha no envio',
      clubId
    );

    return success;
  } catch (error: any) {
    console.error('Erro ao enviar notificação de reativação:', error);
    return false;
  }
};

// Enviar boas-vindas para novo clube
export const sendWelcomeNewClubNotification = async (clubId: number): Promise<boolean> => {
  try {
    await ensureAuthenticated();
    const { data: club, error: clubError } = await supabase
      .from('club_info')
      .select(`
        *,
        master_plans:master_plan_id (name)
      `)
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error('Clube não encontrado');
    }

    const template = EMAIL_TEMPLATES.WELCOME_NEW_CLUB;
    const variables = {
      clubName: club.name,
      planName: club.master_plans?.name || 'Seu plano',
      loginUrl: import.meta.env.VITE_SITE_URL || 'https://app.gamedaynexus.com'
    };

    const { subject, body } = processTemplate(template, variables);
    const success = await sendEmail(club.email, subject, body);

    await logNotification(
      'welcome_new_club',
      club.email,
      subject,
      success ? 'sent' : 'failed',
      success ? undefined : 'Falha no envio',
      clubId
    );

    return success;
  } catch (error: any) {
    console.error('Erro ao enviar boas-vindas:', error);
    return false;
  }
};

// Enviar notificações em lote para pagamentos em atraso
export const sendBulkOverdueNotifications = async (): Promise<{ sent: number; failed: number }> => {
  try {
    await ensureSession();
    // Buscar pagamentos em atraso
    const { data: overduePayments, error } = await supabase
      .from('master_payments')
      .select(`
        id,
        club_id,
        due_date,
        club_info:club_id (name, email)
      `)
      .eq('status', 'overdue');

    if (error) throw error;

    let sent = 0;
    let failed = 0;

    for (const payment of overduePayments || []) {
      const daysOverdue = Math.floor(
        (new Date().getTime() - new Date(payment.due_date).getTime()) / (1000 * 60 * 60 * 24)
      );

      const success = await sendOverdueNotification(payment.club_id, payment.id, daysOverdue);
      
      if (success) {
        sent++;
      } else {
        failed++;
      }

      // Pequena pausa entre envios para não sobrecarregar o serviço
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return { sent, failed };
  } catch (error: any) {
    console.error('Erro ao enviar notificações em lote:', error);
    return { sent: 0, failed: 0 };
  }
};

// Buscar logs de notificação
export const getNotificationLogs = async (
  filters?: {
    type?: string;
    status?: string;
    club_id?: number;
    organization_id?: number;
    start_date?: string;
    end_date?: string;
  }
): Promise<NotificationLog[]> => {
  try {
    let query = supabase
      .from('master_notification_logs')
      .select('*');

    if (filters?.type) {
      query = query.eq('type', filters.type);
    }

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.club_id) {
      query = query.eq('club_id', filters.club_id);
    }

    if (filters?.organization_id) {
      query = query.eq('organization_id', filters.organization_id);
    }

    if (filters?.start_date) {
      query = query.gte('sent_at', filters.start_date);
    }

    if (filters?.end_date) {
      query = query.lte('sent_at', filters.end_date);
    }

    const { data, error } = await query.order('sent_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Erro ao buscar logs de notificação:', error);
    return [];
  }
};