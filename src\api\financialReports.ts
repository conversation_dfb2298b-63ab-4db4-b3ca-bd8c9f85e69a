import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
import { getFinancialTransactions, getFinancialAccounts } from "./api";
import { getPlayersByIds } from "./players";
import { getCollaboratorsByIds } from "./collaborators";
import { getMedicalProfessionalsByIds } from "./medicalProfessionals";
import { getSalaryAdvances, getSalaryAdvancesByPersonIds } from "./salaryAdvances";

type Tables = Database['public']['Tables'];
type SupplierOrderRow = Tables['supplier_orders']['Row'] & {
  supplier: { company_name: string; bank_pix?: string };
  items?: Array<{
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
};

// Interface para pedidos de fornecedores
interface SupplierOrder {
  id: number;
  club_id: number;
  supplier_id: number;
  financial_account_id: number | null;
  order_number: string;
  order_date: string;
  purchase_date: string; // Data de compra do pedido
  due_date?: string; // Data de vencimento (opcional)
  delivery_date: string | null;
  status: string;
  payment_terms: string | null;
  payment_date: string | null;
  payment_method: string | null;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  total_amount: number; // Mantido para compatibilidade
  notes: string | null;
  created_at: string;
  updated_at: string;
  supplier: {
    id: number;
    company_name: string;
    contact_name: string | null;
    email: string | null;
    phone: string | null;
    address: string | null;
    city: string | null;
    state: string | null;
    postal_code: string | null;
    country: string | null;
    tax_id: string | null;
    bank_name: string | null;
    bank_branch: string | null;
    bank_account: string | null;
    bank_pix: string | null;
    notes: string | null;
    created_at: string;
    updated_at: string;
  };
  items: Array<{
    id: number;
    order_id: number;
    product_id: number | null;
    product_name: string;
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    created_at: string;
    updated_at: string;
  }>;
}

interface FinancialAccount {
  id: number;
  club_id: number;
  type: string;
  status: string;
  due_date: string;
  supplier_client: string;
  description: string;
  category: string;
  amount: number;
  created_at: string;
  updated_at: string;
}

interface Supplier {
  id: number;
  club_id: number;
  company_name: string;
  bank_pix?: string;
  observation?: string;
  [key: string]: any;
}

// Types for consolidated financial data
export interface ConsolidatedPayable {
  id: string;
  type: 'account' | 'salary' | 'advance';
  name: string;
  description: string;
  transaction_date: string;
  due_date?: string;
  entry_date?: string | null;
  pix_key?: string;
  role?: string;
  category: string;
  amount: number;
  status: 'pendente' | 'pago';
  person_type?: 'player' | 'collaborator' | 'medical';
  department?: string;
}

export interface ConsolidatedReceivable {
  id: string;
  name: string;
  description: string;
  transaction_date: string;
  phone?: string;
  category: string;
  amount: number;
  status: 'pendente' | 'recebido';
  department?: string;
}
export interface CashFlowEntry {
  id: string;
  date: string;
  type: 'receita' | 'despesa';
  description: string;
  category: string;
  amount: number;
  running_balance: number;
  payment_status: string;
  supplier?: string;
  details?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  source?: 'transaction' | 'supplier_account' | 'supplier_order';
}

/**
 * Get all pending payables (accounts + salaries) for a club
 * @param clubId Club ID
 * @returns Consolidated list of all pending payables
 */
export async function getAllPendingPayables(clubId: number): Promise<ConsolidatedPayable[]> {
  try {
    const payables: ConsolidatedPayable[] = [];

    // 1. Get pending financial accounts (suppliers, general expenses)
    // Buscar contas financeiras, fornecedores e pedidos de fornecedores em paralelo
    const [accounts, suppliers, supplierOrders] = await Promise.all([
      getFinancialAccounts(clubId),
      // Get all suppliers to map PIX keys
      (async (): Promise<Array<{company_name: string, bank_pix?: string}>> => {
        try {
          const { data, error } = await supabase
            .from("suppliers")
            .select("company_name, bank_pix")
            .eq("club_id", String(clubId));
          
          if (error) throw error;
          return (data as Array<{company_name: string, bank_pix?: string}>) || [];
        } catch (error) {
          console.error("Error fetching suppliers:", error);
          return [];
        }
      })(),
      // Get all supplier orders to map purchase dates
      (async (): Promise<Array<{financial_account_id: number, purchase_date: string}>> => {
        try {
          const { data, error } = await supabase
            .from("supplier_orders")
            .select("financial_account_id, purchase_date")
            .not("financial_account_id", "is", null)
            .eq("club_id", String(clubId));
          
          if (error) throw error;
          return (data as Array<{financial_account_id: number, purchase_date: string}>) || [];
        } catch (error) {
          console.error("Error fetching supplier orders:", error);
          return [];
        }
      })()
    ]);

    const pendingAccounts = accounts.filter(account =>
      account.type === 'a_pagar' && account.status === 'pendente'
    );

    for (const account of pendingAccounts) {
      // Find supplier by name to get PIX key
      const supplier = suppliers.find(s => 
        s.company_name.toLowerCase() === account.supplier_client?.toLowerCase()
      );
      const pixKey = supplier?.bank_pix || 'Não informado';
      
      // Encontrar a data de compra correspondente a esta conta financeira
      const order = supplierOrders.find(o => o.financial_account_id === account.id);
      
      payables.push({
        id: `account-${account.id}`,
        type: 'account',
        name: account.supplier_client,
        description: account.description,
        // Usar a data de compra do pedido, se disponível, senão a data de criação da conta
        transaction_date: order?.purchase_date || account.creation_date,
        // Usar a data de compra como data de vencimento se disponível, senão usa a data de vencimento da conta
        due_date: order?.purchase_date || account.due_date,
        pix_key: pixKey,
        role: undefined,
        category: account.category,
        amount: account.amount,
        status: 'pendente',
        department: 'Fornecedores'
      });
    }

    // 2. Get pending financial transactions
    const transactions = await getFinancialTransactions(clubId);

    // 2a. Filter salary transactions
    const pendingSalaryTransactions = transactions.filter(transaction =>
      transaction.category === 'salários' &&
      transaction.type === 'despesa' &&
      ['pending', 'pendente'].includes((transaction.payment_status || '').toLowerCase())
    );

     // 2b. Filter bonus transactions
     const pendingBonusTransactions = transactions.filter(transaction =>
      transaction.category === 'bônus' &&
      transaction.type === 'despesa' &&
      ['pending', 'pendente'].includes((transaction.payment_status || '').toLowerCase())
    );

    // 2c. Filter other expense transactions (manual entries)
    const pendingExpenseTransactions = transactions.filter(transaction =>
      transaction.type === 'despesa' &&
      ['pending', 'pendente'].includes((transaction.payment_status || '').toLowerCase()) &&
      transaction.category !== 'salários' &&
      transaction.category !== 'bônus'
    );

    const playerTransactions = pendingSalaryTransactions.filter(t => t.player_id);
    const collaboratorTransactions = pendingSalaryTransactions.filter(t => t.collaborator_id);
    const medicalTransactions = pendingSalaryTransactions.filter(t => t.medical_professional_id);

    const bonusPlayerTransactions = pendingBonusTransactions.filter(t => t.player_id);
    const bonusCollaboratorTransactions = pendingBonusTransactions.filter(t => t.collaborator_id);
    const bonusMedicalTransactions = pendingBonusTransactions.filter(t => t.medical_professional_id);

    const playerIds = Array.from(new Set([
      ...playerTransactions.map(t => t.player_id!),
      ...bonusPlayerTransactions.map(t => t.player_id!)
    ]));
    const collaboratorIds = Array.from(new Set([
      ...collaboratorTransactions.map(t => t.collaborator_id!),
      ...bonusCollaboratorTransactions.map(t => t.collaborator_id!)
    ]));
    const medicalIds = Array.from(new Set([
      ...medicalTransactions.map(t => t.medical_professional_id!),
      ...bonusMedicalTransactions.map(t => t.medical_professional_id!)
    ]));
    const [players, collaborators, medics, playerCategoriesData, playerAdvances, collaboratorAdvances, medicalAdvances] = await Promise.all([
      getPlayersByIds(clubId, playerIds),
      getCollaboratorsByIds(clubId, collaboratorIds),
      getMedicalProfessionalsByIds(clubId, medicalIds),
      playerIds.length
        ? supabase
            .from('player_categories')
            .select('player_id, categories!player_categories_category_id_fkey(name)')
            .eq('club_id', clubId)
            .in('player_id', playerIds)
        : Promise.resolve({ data: [] } as any),
      getSalaryAdvancesByPersonIds(clubId, 'player', playerIds),
      getSalaryAdvancesByPersonIds(clubId, 'collaborator', collaboratorIds),
      getSalaryAdvancesByPersonIds(clubId, 'medical', medicalIds)
    ]);

    const playersMap = new Map(players.map(p => [p.id.toString(), p]));
    const collaboratorsMap = new Map(collaborators.map(c => [c.id.toString(), c]));
    const medicsMap = new Map(medics.map(m => [m.id.toString(), m]));
    const categoriesMap = new Map<string, string>();
    if (playerCategoriesData?.data) {
      (playerCategoriesData.data as any[]).forEach(pc => {
        if (pc.player_id && pc.categories?.name) {
          categoriesMap.set(pc.player_id.toString(), pc.categories.name);
        }
      });
    }

    const playerAdvancesMap = new Map<string, SalaryAdvance[]>();
    playerAdvances.forEach(a => {
      const id = a.person_id.toString();
      if (!playerAdvancesMap.has(id)) playerAdvancesMap.set(id, []);
      playerAdvancesMap.get(id)!.push(a);
    });

    const collaboratorAdvancesMap = new Map<string, SalaryAdvance[]>();
    collaboratorAdvances.forEach(a => {
      const id = a.person_id.toString();
      if (!collaboratorAdvancesMap.has(id)) collaboratorAdvancesMap.set(id, []);
      collaboratorAdvancesMap.get(id)!.push(a);
    });

    const medicalAdvancesMap = new Map<string, SalaryAdvance[]>();
    medicalAdvances.forEach(a => {
      const id = a.person_id.toString();
      if (!medicalAdvancesMap.has(id)) medicalAdvancesMap.set(id, []);
      medicalAdvancesMap.get(id)!.push(a);
    });

    for (const transaction of pendingExpenseTransactions) {
      payables.push({
        id: `transaction-${transaction.id}`,
        type: 'account',
        name: transaction.description,
        description: transaction.description,
        transaction_date: transaction.date,
        due_date: transaction.date,
        category: transaction.category,
        amount: typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount,
        status: 'pendente',
        department: transaction.category || 'Outros'
      });
    }


    // Process player salaries with salary advances calculation
    for (const transaction of playerTransactions) {
      try {
        const playerData = playersMap.get(transaction.player_id!.toString());
        if (!playerData || playerData.status === 'inativo') continue;

        const pixKey = playerData.bank_pix_key || playerData.bank_info?.pix || 'Não informado';

        let category = categoriesMap.get(playerData.id.toString()) ||
          ((transaction as any).categories?.map((c: any) => c.name).join(', ') || 'Sem categoria');

        const salaryAmount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        const transactionDate = new Date(transaction.date);
        const transactionMonth = transactionDate.getMonth() + 1;
        const transactionYear = transactionDate.getFullYear();

        const relevantAdvances = (playerAdvancesMap.get(playerData.id.toString()) || []).filter(advance => {
          const advanceDate = new Date(advance.advance_date);
          return advanceDate.getMonth() + 1 === transactionMonth &&
                 advanceDate.getFullYear() === transactionYear &&
                 advance.status === 'active';
        });

        const totalAdvances = relevantAdvances.reduce((sum, advance) => sum + advance.amount, 0);
        const netSalary = salaryAmount - totalAdvances;

        if (netSalary > 0) {
          let description = transaction.description || 'Salário';
          if (totalAdvances > 0) {
            description += ` (Bruto: R$ ${salaryAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} - Vales: R$ ${totalAdvances.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`
          }

          payables.push({
            id: `salary-player-${transaction.id}`,
            type: 'salary',
            name: playerData.name,
            description: description,
            transaction_date: transaction.date,
            entry_date: playerData.entry_date || null,
            pix_key: pixKey,
            role: 'Jogador',
            category: category,
            amount: netSalary,
            status: 'pendente',
            person_type: 'player',
            department: `Jogadores - ${category}`
          });
        }
      } catch (error) {
        console.error(`Error processing player transaction ${transaction.id}:`, error);
      }
    }

    // Process collaborator salaries with salary advances calculation
    for (const transaction of collaboratorTransactions) {
      try {
        const collaboratorData = collaboratorsMap.get(transaction.collaborator_id!.toString());
        if (!collaboratorData || collaboratorData.status === 'inactive') continue;

        let pixKey = 'Não informado';
        if (collaboratorData.bank_info?.pix) {
          pixKey = collaboratorData.bank_info.pix;
        }

        const salaryAmount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        const transactionDate = new Date(transaction.date);
        const transactionMonth = transactionDate.getMonth() + 1;
        const transactionYear = transactionDate.getFullYear();

        const relevantAdvances = (collaboratorAdvancesMap.get(collaboratorData.id.toString()) || []).filter(advance => {
          const advanceDate = new Date(advance.advance_date);
          return advanceDate.getMonth() + 1 === transactionMonth &&
                 advanceDate.getFullYear() === transactionYear &&
                 advance.status === 'active';
        });

        const totalAdvances = relevantAdvances.reduce((sum, advance) => sum + advance.amount, 0);
        const netSalary = salaryAmount - totalAdvances;

        if (netSalary > 0) {
          let description = transaction.description || 'Salário';
          if (totalAdvances > 0) {
            description += ` (Bruto: R$ ${salaryAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} - Vales: R$ ${totalAdvances.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          }

          payables.push({
            id: `salary-collaborator-${transaction.id}`,
            type: 'salary',
            name: collaboratorData.full_name,
            description: description,
            transaction_date: transaction.date,
            entry_date: collaboratorData.entry_date || null,
            pix_key: pixKey,
            role: collaboratorData.role,
            category: 'Comissão Técnica',
            amount: netSalary,
            status: 'pendente',
            person_type: 'collaborator',
            department: 'Comissão Técnica'
          });
        }
      } catch (error) {
        console.error(`Error processing collaborator transaction ${transaction.id}:`, error);
      }
    }

    // Process medical professional salaries with salary advances calculation
    for (const transaction of medicalTransactions) {
      try {
        const medicData = medicsMap.get(transaction.medical_professional_id!.toString());
        if (!medicData) continue;

        let pixKey = 'Não informado';
        if (medicData.bank_info?.pix) {
          pixKey = medicData.bank_info.pix;
        }

        const salaryAmount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        const transactionDate = new Date(transaction.date);
        const transactionMonth = transactionDate.getMonth() + 1;
        const transactionYear = transactionDate.getFullYear();

        const relevantAdvances = (medicalAdvancesMap.get(medicData.id.toString()) || []).filter(advance => {
          const advanceDate = new Date(advance.advance_date);
          return advanceDate.getMonth() + 1 === transactionMonth &&
                 advanceDate.getFullYear() === transactionYear &&
                 advance.status === 'active';
        });

        const totalAdvances = relevantAdvances.reduce((sum, advance) => sum + advance.amount, 0);
        const netSalary = salaryAmount - totalAdvances;

        if (netSalary > 0) {
          let description = transaction.description || 'Salário';
          if (totalAdvances > 0) {
            description += ` (Bruto: R$ ${salaryAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} - Vales: R$ ${totalAdvances.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`
          }

          payables.push({
            id: `salary-medical-${transaction.id}`,
            type: 'salary',
            name: medicData.name,
            description: description,
            transaction_date: transaction.date,
            pix_key: pixKey,
            role: medicData.role,
            category: 'Departamento Médico',
            amount: netSalary,
            status: 'pendente',
            person_type: 'medical',
            department: 'Departamento Médico'
          });
        }
      } catch (error) {
        console.error(`Error processing medical transaction ${transaction.id}:`, error);
      }
    }

    // Process bonus transactions
    for (const transaction of bonusPlayerTransactions) {
      try {
        const playerData = playersMap.get(transaction.player_id!.toString());
        if (!playerData || playerData.status === 'inativo') continue;

        const pixKey = playerData.bank_pix_key || playerData.bank_info?.pix || 'Não informado';
        const amount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        payables.push({
          id: `bonus-player-${transaction.id}`,
          type: 'salary',
          name: playerData.name,
          description: transaction.description,
          transaction_date: transaction.date,
          entry_date: playerData.entry_date || null,
          pix_key: pixKey,
          role: 'Jogador',
          category: 'Bônus',
          amount: amount,
          status: 'pendente',
          person_type: 'player',
          department: 'Bônus'
        });
      } catch (error) {
        console.error(`Error processing player bonus ${transaction.id}:`, error);
      }
    }

    for (const transaction of bonusCollaboratorTransactions) {
      try {
        const collaboratorData = collaboratorsMap.get(transaction.collaborator_id!.toString());
        if (!collaboratorData) continue;

        const pixKey = collaboratorData.bank_info?.pix || 'Não informado';
        const amount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        payables.push({
          id: `bonus-collaborator-${transaction.id}`,
          type: 'salary',
          name: collaboratorData.full_name,
          description: transaction.description,
          transaction_date: transaction.date,
          entry_date: collaboratorData.entry_date || null,
          pix_key: pixKey,
          role: collaboratorData.role,
          category: 'Bônus',
          amount: amount,
          status: 'pendente',
          person_type: 'collaborator',
          department: 'Bônus'
        });
      } catch (error) {
        console.error(`Error processing collaborator bonus ${transaction.id}:`, error);
      }
    }

    for (const transaction of bonusMedicalTransactions) {
      try {
        const medicData = medicsMap.get(transaction.medical_professional_id!.toString());
        if (!medicData) continue;

        const pixKey = medicData.bank_info?.pix || 'Não informado';
        const amount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

        payables.push({
          id: `bonus-medical-${transaction.id}`,
          type: 'salary',
          name: medicData.name,
          description: transaction.description,
          transaction_date: transaction.date,
          pix_key: pixKey,
          role: medicData.role,
          category: 'Bônus',
          amount: amount,
          status: 'pendente',
          person_type: 'medical',
          department: 'Bônus'
        });
      } catch (error) {
        console.error(`Error processing medical bonus ${transaction.id}:`, error);
      }
    }

    // 3. Add salary advances (vales) as informational items (amount = 0 since already deducted from salaries)
    // This provides transparency about existing advances without double-counting
    // Only show advances from the current and next month to keep the report focused
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    // Calculate next month and handle year rollover
    let nextMonth = currentMonth + 1;
    let nextMonthYear = currentYear;
    if (nextMonth > 12) {
      nextMonth = 1;
      nextMonthYear++;
    }
    
    // Get all active advances for the club
    const allAdvances = await getSalaryAdvances(clubId);
    
    // Filter advances to include only those from current or next month
    const activeAdvances = allAdvances.filter(advance => {
      // Only active advances
      if (advance.status !== 'active') return false;
      
      // Parse advance date
      const advanceDate = new Date(advance.advance_date);
      const advanceMonth = advanceDate.getMonth() + 1;
      const advanceYear = advanceDate.getFullYear();
      
      // Include if in current month/year or next month/year
      const isCurrentMonth = advanceMonth === currentMonth && advanceYear === currentYear;
      const isNextMonth = advanceMonth === nextMonth && advanceYear === nextMonthYear;
      
      // Check if has valid person_id
      const hasValidPersonId = advance.person_id && 
                              advance.person_id.toString().trim() !== '';
      
      return (isCurrentMonth || isNextMonth) && hasValidPersonId;
    });

    const advPlayerIds = Array.from(new Set(activeAdvances.filter(a => a.person_type === 'player').map(a => a.person_id.toString())));
    const advCollaboratorIds = Array.from(new Set(activeAdvances.filter(a => a.person_type === 'collaborator').map(a => a.person_id.toString())));
    const advMedicalIds = Array.from(new Set(activeAdvances.filter(a => a.person_type === 'medical').map(a => a.person_id.toString())));

    const missingPlayerIds = advPlayerIds.filter(id => !playersMap.has(id));
    const missingCollaboratorIds = advCollaboratorIds.filter(id => !collaboratorsMap.has(id));
    const missingMedicalIds = advMedicalIds.filter(id => !medicsMap.has(id));

    if (missingPlayerIds.length) {
      const extraPlayers = await getPlayersByIds(clubId, missingPlayerIds);
      extraPlayers.forEach(p => playersMap.set(p.id.toString(), p));
      const { data: extraCats } = await supabase
        .from('player_categories')
        .select('player_id, categories!player_categories_category_id_fkey(name)')
        .eq('club_id', clubId)
        .in('player_id', missingPlayerIds);
      (extraCats as any[] || []).forEach(pc => {
        if (pc.player_id && pc.categories?.name) {
          categoriesMap.set(pc.player_id.toString(), pc.categories.name);
        }
      });
    }

    if (missingCollaboratorIds.length) {
      const extraCollaborators = await getCollaboratorsByIds(clubId, missingCollaboratorIds);
      extraCollaborators.forEach(c => collaboratorsMap.set(c.id.toString(), c));
    }

    if (missingMedicalIds.length) {
      const extraMedics = await getMedicalProfessionalsByIds(clubId, missingMedicalIds);
      extraMedics.forEach(m => medicsMap.set(m.id.toString(), m));
    }

    for (const advance of activeAdvances) {
      try {
        let name = 'Desconhecido';
        let pixKey = 'Não informado';
        let category = 'Vales';
        let department = 'Vales (Informativos)';

        if (advance.person_type === 'player') {
          const playerData = playersMap.get(advance.person_id.toString());
          if (!playerData || playerData.status === 'inativo') continue;

          name = playerData.name;
          pixKey = playerData.bank_pix_key || playerData.bank_info?.pix || 'Não informado';
          category = categoriesMap.get(playerData.id.toString()) || category;
          department = `Vales - Jogadores ${category} (Informativos)`;
        } else if (advance.person_type === 'collaborator') {
          const collaboratorData = collaboratorsMap.get(advance.person_id.toString());
          if (!collaboratorData) continue;

          name = collaboratorData.full_name;
          if (collaboratorData.bank_info?.pix) {
            pixKey = collaboratorData.bank_info.pix;
          }
          department = 'Vales - Comissão Técnica (Informativos)';
        } else if (advance.person_type === 'medical') {
          const medicData = medicsMap.get(advance.person_id.toString());
          if (!medicData) continue;

          name = medicData.name;
          if (medicData.bank_info?.pix) {
            pixKey = medicData.bank_info.pix;
          }
          department = 'Vales - Departamento Médico (Informativos)';
        }

        payables.push({
          id: `advance-info-${advance.id}`,
          type: 'advance',
          name: name,
          description: `Vale Concedido - ${advance.description || 'Adiantamento'} (Já descontado do salário)`,
          transaction_date: advance.advance_date,
          pix_key: pixKey,
          role: advance.person_type === 'player'
            ? 'Jogador'
            : advance.person_type === 'collaborator'
              ? 'Colaborador'
              : 'Médico',
          category: category,
          amount: 0,
          status: 'pendente',
          person_type: advance.person_type,
          department: department
        });
      } catch (error) {
        console.error(`Error processing advance ${advance.id}:`, error);
      }
    }

    // Sort by due date, then by amount (descending)
    payables.sort((a, b) => {
      const dateA = new Date(a.due_date || a.transaction_date);
      const dateB = new Date(b.due_date || b.transaction_date);

      if (dateA.getTime() !== dateB.getTime()) {
        return dateA.getTime() - dateB.getTime();
      }

      return b.amount - a.amount;
    });

    return payables;
  } catch (error) {
    console.error("Error getting all pending payables:", error);
    throw new Error("Erro ao buscar contas a pagar");
  }
}

export async function getAllPendingReceivables(clubId: number): Promise<ConsolidatedReceivable[]> {
  try {
    const receivables: ConsolidatedReceivable[] = [];

    const [accounts, transactions] = await Promise.all([
      getFinancialAccounts(clubId),
      getFinancialTransactions(clubId)
    ]);

    const pendingAccounts = accounts.filter(a => a.type === 'a_receber' && a.status === 'pendente');
    for (const acc of pendingAccounts) {
      receivables.push({
        id: `account-${acc.id}`,
        name: acc.supplier_client,
        description: acc.description,
        transaction_date: acc.creation_date,
        phone: undefined,
        category: acc.category,
        amount: acc.amount,
        status: 'pendente',
        department: acc.category
      });
    }

    const pendingTransactions = transactions.filter(t =>
      t.type === 'receita' && ['pending', 'pendente'].includes((t.payment_status || '').toLowerCase())
    );

    const playerIds = Array.from(new Set(pendingTransactions.filter(t => t.player_id).map(t => t.player_id!)));
    const players = await getPlayersByIds(clubId, playerIds);
    const playersMap = new Map(players.map(p => [p.id.toString(), p]));

    for (const tx of pendingTransactions) {
      const phone = tx.player_id ? playersMap.get(tx.player_id.toString())?.phone : undefined;
      receivables.push({
        id: `transaction-${tx.id}`,
        name: tx.player_name || tx.description,
        description: tx.description,
        transaction_date: tx.date,
        phone,
        category: tx.category,
        amount: typeof tx.amount === 'string' ? parseFloat(tx.amount) : tx.amount,
        status: 'pendente',
        department: 'Receitas'
      });
    }

    receivables.sort((a, b) => new Date(a.transaction_date).getTime() - new Date(b.transaction_date).getTime());

    return receivables;
  } catch (error) {
    console.error('Error getting pending receivables:', error);
    throw new Error('Erro ao buscar contas a receber');
  }
}

/**
 * Get cash flow entries for a specific date range
 * @param clubId Club ID
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Cash flow entries with running balance
 */
// Função para buscar pedidos de fornecedores pagos
async function getPaidSupplierOrders(clubId: number, startDate: string, endDate: string): Promise<SupplierOrder[]> {
  try {
    // Buscar pedidos de fornecedores com data de compra dentro do período
    const { data, error } = await supabase
      .from('supplier_orders')
      .select(`
        *,
        supplier:supplier_id (*),
        items:supplier_order_items(*)
      `)
      .eq('club_id', clubId as unknown as string)
      .eq('status', 'pago')
      .gte('purchase_date', startDate)
      .lte('purchase_date', endDate)
      .order('purchase_date', { ascending: true });

    if (error) {
      console.error('Erro ao buscar pedidos de fornecedores pagos:', error);
      throw error;
    }
    
    // Converter os dados para o tipo SupplierOrder
    return (data || []) as unknown as SupplierOrder[];
  } catch (error) {
    console.error('Erro ao buscar pedidos de fornecedores pagos:', error);
    return [];
  }
}

export async function getCashFlowByDateRange(
  clubId: number,
  startDate: string,
  endDate: string
): Promise<CashFlowEntry[]> {
  try {
    // Buscar transações financeiras em paralelo
    const [transactions, { data: paidAccounts = [] }, paidOrders] = await Promise.all([
      getFinancialTransactions(clubId),
      // Buscar contas de fornecedores pagas
      supabase
        .from("financial_accounts")
        .select('*')
        .eq('club_id', clubId as unknown as string)
        .eq('type', 'a_pagar')
        .eq('status', 'pago')
        .gte('due_date', startDate)
        .lte('due_date', endDate),
      // Buscar pedidos de fornecedores pagos (filtrados por purchase_date)
      getPaidSupplierOrders(clubId, startDate, endDate)
    ]);

    // Mapear pedidos para o formato de entradas de fluxo de caixa
    const processedOrders = paidOrders.map((order) => ({
      id: `order-${order.id}`,
      // Usar a data de compra (purchase_date) como data para o fluxo de caixa
      date: order.purchase_date || order.due_date || order.payment_date || order.updated_at,
      type: 'despesa' as const,
      description: `Pedido #${order.order_number} - ${order.supplier.company_name}`,
      category: 'Fornecedores',
      amount: order.total_amount,
      payment_status: 'pago',
      source: 'supplier_order' as const,
      supplier: order.supplier.company_name,
      details: order.items?.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total_price
      })) || []
    }));

    // 1. Processar transações financeiras
    const processedTransactions = transactions
      .filter(transaction => {
        const transactionDate = transaction.date;
        const isInDateRange = transactionDate >= startDate && transactionDate <= endDate;
        
        if (!isInDateRange) return false;
        
        const paymentStatus = (transaction.payment_status || '').toLowerCase();
        
        // Para receitas, incluir apenas as concluídas/pagas
        if (transaction.type === 'receita') {
          return ['paid', 'pago', 'concluído', 'realizado'].includes(paymentStatus);
        }
        
        // Para despesas, incluir apenas as pagas
        if (transaction.type === 'despesa') {
          return ['paid', 'pago', 'concluído'].includes(paymentStatus);
        }
        
        return false;
      })
      .map(transaction => ({
        id: `transaction-${transaction.id}`,
        date: transaction.date,
        type: transaction.type as 'receita' | 'despesa',
        description: transaction.description,
        category: transaction.category,
        amount: typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount,
        payment_status: transaction.payment_status || 'pending',
        source: 'transaction' as const,
        supplier: 'Outros',
        details: []
      }));

    // 2. Processar contas de fornecedores pagas
    const processedAccounts = (paidAccounts || []).map(account => ({
      id: `account-${account.id}`,
      date: account.due_date, // Manter a data de vencimento para contas de fornecedores
      type: 'despesa' as const, // Contas a pagar são sempre despesas
      description: `Fornecedor: ${account.supplier_client} - ${account.description}`,
      category: account.category || 'Fornecedores',
      amount: account.amount,
      payment_status: 'pago',
      source: 'supplier_account' as const,
      supplier: account.supplier_client || 'Fornecedor não identificado',
      details: []
    }));

    // 3. Combinar e ordenar todas as entradas por data
    const allEntries = [
      ...processedTransactions.map(t => ({ 
        ...t, 
        supplier: 'Outros', 
        details: [] as Array<{description: string; quantity: number; unitPrice: number; total: number;}> 
      })),
      ...processedAccounts,
      ...processedOrders
    ].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // 4. Calcular o saldo acumulado
    let runningBalance = 0;
    const cashFlowEntries: CashFlowEntry[] = [];

    for (const entry of allEntries) {
      if (entry.type === 'receita') {
        runningBalance += entry.amount;
      } else {
        runningBalance -= entry.amount;
      }

      cashFlowEntries.push({
        id: entry.id,
        date: entry.date,
        type: entry.type,
        description: entry.description,
        category: entry.category,
        amount: entry.type === 'receita' ? entry.amount : -entry.amount,
        running_balance: runningBalance,
        payment_status: entry.payment_status,
        supplier: (entry as any).supplier || 'Outros',
        details: (entry as any).details || []
      });
    }

    return cashFlowEntries;
  } catch (error) {
    console.error("Error getting cash flow by date range:", error);
    throw new Error("Erro ao buscar fluxo de caixa");
  }
}

/**
 * Get pending payables for a specific period
 * @param clubId Club ID
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Filtered list of pending payables
 */
export async function getPendingPayablesByPeriod(
  clubId: number,
  startDate: string,
  endDate: string
): Promise<ConsolidatedPayable[]> {
  try {
    const allPayables = await getAllPendingPayables(clubId);

    // Filter by date range
    const filteredPayables = allPayables.filter(payable => {
      // Para contas de fornecedores, sempre usar a transaction_date (que contém a data de compra)
      // Para outros tipos (salários, adiantamentos), usar due_date ou transaction_date
      const dateToCheck = payable.transaction_date; // Já contém a data de compra para contas de fornecedores
      
      // Verificar se a data está dentro do período
      return dateToCheck >= startDate && dateToCheck <= endDate;
    });

    return filteredPayables;
  } catch (error) {
    console.error("Error getting pending payables by period:", error);
    throw new Error("Erro ao buscar contas a pagar por período");
  }
}