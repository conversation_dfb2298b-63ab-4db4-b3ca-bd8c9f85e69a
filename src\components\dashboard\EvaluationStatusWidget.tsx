import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useCurrentClubId } from "@/context/ClubContext";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { getPlayersInEvaluation } from "@/api/playerEvaluationInvitations";

// Define player type
interface Player {
  id: string;
  name: string;
  position?: string;
  status: string;
  player_evaluation_invitations?: Array<{
    id: number;
    evaluation_date?: string;
    evaluation_location?: string;
    evaluation_requirements?: string;
    evaluation_notes?: string;
    documents_status?: "pending" | "approved" | "rejected";
    documents_verified_at?: string;
    documents_verified_by?: string;
    documents_rejection_reason?: string;
    evaluation_status?:
      | "em avaliacao"
      | "aguardando agendamento"
      | "aprovado"
      | "disponivel";
  }>;
}

export function EvaluationStatusWidget() {
  const clubId = useCurrentClubId();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [pendingCount, setPendingCount] = useState(0);
  const [scheduledCount, setScheduledCount] = useState(0);
  const [players, setPlayers] = useState<Player[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get players in evaluation using the API
        const playersData = await getPlayersInEvaluation(clubId);

        setPlayers(playersData || []);

        // Count pending and scheduled evaluations
        let pending = 0;
        let scheduled = 0;

        playersData?.forEach((player: Player) => {
          const invitation = player.player_evaluation_invitations?.[0];

          if (!invitation) {
            pending++;
            return;
          }

          if (player.status === "jogador agendado") {
            scheduled++;
            return;
          }

          // Check evaluation status first
          if (
            invitation.evaluation_status === "disponivel" ||
            invitation.evaluation_status === "aprovado"
          ) {
            // Don't count approved or available players
            return;
          } else if (invitation.evaluation_status === "aguardando agendamento") {
            pending++;
            return;
          }

          // Check document status
          if (invitation.documents_status === "rejected") {
            pending++;
            return;
          }

          // Check evaluation date
          if (invitation.evaluation_date) {
            const evaluationDate = new Date(invitation.evaluation_date);
            const now = new Date();

            if (evaluationDate > now) {
              scheduled++;
            } else {
              pending++;
            }
          } else {
            pending++;
          }
        });

        setPendingCount(pending);
        setScheduledCount(scheduled);
      } catch (error) {
        console.error("Error fetching evaluation data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clubId]);

  const handleViewAll = () => {
    import('@/utils/clubNavigation').then(({ navigateToClubPath }) => {
      navigateToClubPath("/avaliacao");
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Avaliações</CardTitle>
        <Button
          variant="ghost"
          size="sm"
          className="text-xs flex items-center"
          onClick={handleViewAll}
        >
          Ver todos <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-[100px] flex items-center justify-center">
            <p className="text-muted-foreground">Carregando...</p>
          </div>
        ) : players.length === 0 ? (
          <div className="h-[100px] flex items-center justify-center">
            <p className="text-muted-foreground">Nenhum jogador em pré cadastro</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col items-center justify-center p-4 bg-red-50 rounded-md">
                <span className="text-2xl font-bold text-red-600">{pendingCount}</span>
                <span className="text-xs text-red-600">Aguardando</span>
              </div>
              <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-md">
                <span className="text-2xl font-bold text-blue-600">{scheduledCount}</span>
                <span className="text-xs text-blue-600">Agendados</span>
              </div>
            </div>

            <div className="space-y-2">
              {players.slice(0, 3).map((player: Player) => {
                const invitation = player.player_evaluation_invitations?.[0];

                // Determine status and colors
                let statusText = "Aguardando";
                let statusClass = "bg-yellow-100 text-yellow-800";

                if (!invitation) {
                  statusText = "Sem Convite";
                  statusClass = "bg-gray-100 text-gray-800";
                } else if (player.status === "jogador agendado") {
                  statusText = "Jogador Agendado";
                  statusClass = "bg-blue-100 text-blue-800";
                } else if (invitation.evaluation_status === "disponivel") {
                  statusText = "Disponível";
                  statusClass = "bg-green-100 text-green-800";
                } else if (invitation.evaluation_status === "aprovado") {
                  statusText = "Aprovado";
                  statusClass = "bg-emerald-100 text-emerald-800";
                } else if (invitation.evaluation_status === "aguardando agendamento") {
                  statusText = "Aguardando Agenda";
                  statusClass = "bg-red-100 text-red-800";
                } else if (invitation.documents_status === "rejected") {
                  statusText = "Docs. Rejeitados";
                  statusClass = "bg-orange-100 text-orange-800";
                } else if (invitation.evaluation_date) {
                  const evaluationDate = new Date(invitation.evaluation_date);
                  const now = new Date();

                  if (evaluationDate > now) {
                    statusText = "Agendado";
                    statusClass = "bg-blue-100 text-blue-800";
                  } else {
                    statusText = "Avaliado";
                    statusClass = "bg-purple-100 text-purple-800";
                  }
                } else if (invitation.documents_status === "approved") {
                  statusText = "Aguardando Agenda";
                  statusClass = "bg-red-100 text-red-800";
                } else {
                  statusText = "Docs. Pendentes";
                  statusClass = "bg-yellow-100 text-yellow-800";
                }

                return (
                  <div
                    key={player.id}
                    className="flex items-center justify-between p-2 text-sm border rounded-md"
                    onClick={() => {
                      import('@/utils/clubNavigation').then(({ navigateToClubPath }) => {
                        navigateToClubPath(`/jogador/${player.id}`);
                      });
                    }}
                    style={{ cursor: 'pointer' }}
                  >
                    <div>
                      <p className="font-medium">{player.name}</p>
                      <p className="text-xs text-muted-foreground">{player.position}</p>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs ${statusClass}`}>
                      {statusText}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}