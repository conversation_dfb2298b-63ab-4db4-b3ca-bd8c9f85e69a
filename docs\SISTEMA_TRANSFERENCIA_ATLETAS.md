# Sistema de Transferência de Atletas

## Análise do Sistema Atual

### Estrutura Atual
- **Tabela `players`**: Contém dados dos jogadores com `club_id`, `user_id`, `cpf_number`, etc.
- **Tabela `auth.users`**: Gerenciada pelo Supabase para autenticação
- **Tabela `club_members`**: Relaciona usuários com clubes e suas permissões
- **Tabela `player_accounts`**: Controla contas de jogadores e suas expirações
- **Tabela `player_documents`**: Armazena documentos dos jogadores
- **Storage**: Documentos armazenados em buckets separados por clube/jogador

### Problema Identificado
Quando um jogador sai de um clube e entra em outro:
1. O clube anterior marca o jogador como "inativo" para manter histórico
2. O novo clube precisa cadastrar o jogador novamente
3. Isso gera duplicação de dados e documentos
4. O jogador mantém a mesma conta (`auth.users`) mas precisa trocar de clube

## Solução Proposta

### 1. Nova Tabela: `player_transfers`
```sql
CREATE TABLE player_transfers (
  id SERIAL PRIMARY KEY,
  player_id UUID NOT NULL,
  from_club_id INTEGER REFERENCES club_info(id),
  to_club_id INTEGER REFERENCES club_info(id),
  transfer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transfer_type TEXT DEFAULT 'transfer', -- 'transfer', 'loan', 'return'
  status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'completed'
  requested_by UUID REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);
```

### 2. Nova Tabela: `global_players`
```sql
CREATE TABLE global_players (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cpf_number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  birthdate DATE,
  birthplace TEXT,
  nationality TEXT,
  rg_number TEXT,
  father_name TEXT,
  mother_name TEXT,
  phone TEXT,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Nova Tabela: `global_player_documents`
```sql
CREATE TABLE global_player_documents (
  id SERIAL PRIMARY KEY,
  global_player_id UUID REFERENCES global_players(id),
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  original_club_id INTEGER REFERENCES club_info(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);
```

### 4. Modificação na Tabela `players`
```sql
ALTER TABLE players 
ADD COLUMN global_player_id UUID REFERENCES global_players(id),
ADD COLUMN is_transfer BOOLEAN DEFAULT FALSE,
ADD COLUMN transfer_id INTEGER REFERENCES player_transfers(id);
```

## Fluxo de Transferência

### 1. Busca por CPF
```typescript
export async function searchPlayerByCPF(cpf: string): Promise<{
  found: boolean;
  globalPlayer?: GlobalPlayer;
  lastClub?: string;
  documents?: GlobalPlayerDocument[];
}> {
  // Buscar na tabela global_players
  const { data: globalPlayer, error } = await supabase
    .from('global_players')
    .select('*')
    .eq('cpf_number', cpf)
    .single();

  if (error || !globalPlayer) {
    return { found: false };
  }

  // Buscar último clube
  const { data: lastPlayerRecord } = await supabase
    .from('players')
    .select('club_id, club_info(name)')
    .eq('global_player_id', globalPlayer.id)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  // Buscar documentos
  const { data: documents } = await supabase
    .from('global_player_documents')
    .select('*')
    .eq('global_player_id', globalPlayer.id)
    .eq('is_active', true);

  return {
    found: true,
    globalPlayer,
    lastClub: lastPlayerRecord?.club_info?.name,
    documents: documents || []
  };
}
```

### 2. Processo de Transferência
```typescript
export async function initiatePlayerTransfer(
  cpf: string,
  toClubId: number,
  playerData: Partial<Player>,
  userId: string
): Promise<{ success: boolean; playerId?: string; transferId?: number }> {
  
  // 1. Buscar jogador global
  const searchResult = await searchPlayerByCPF(cpf);
  
  let globalPlayerId: string;
  
  if (!searchResult.found) {
    // Criar novo jogador global
    const { data: newGlobalPlayer, error } = await supabase
      .from('global_players')
      .insert({
        cpf_number: cpf,
        name: playerData.name,
        birthdate: playerData.birthdate,
        // ... outros campos globais
      })
      .select()
      .single();
      
    if (error) throw error;
    globalPlayerId = newGlobalPlayer.id;
  } else {
    globalPlayerId = searchResult.globalPlayer!.id;
    
    // Verificar se jogador já está ativo em outro clube
    const { data: activePlayer } = await supabase
      .from('players')
      .select('club_id, status')
      .eq('global_player_id', globalPlayerId)
      .eq('status', 'ativo')
      .single();
      
    if (activePlayer && activePlayer.club_id !== toClubId) {
      throw new Error('Jogador já está ativo em outro clube');
    }
  }
  
  // 2. Criar registro de transferência
  const { data: transfer, error: transferError } = await supabase
    .from('player_transfers')
    .insert({
      player_id: globalPlayerId,
      to_club_id: toClubId,
      transfer_type: 'transfer',
      requested_by: userId,
      status: 'pending'
    })
    .select()
    .single();
    
  if (transferError) throw transferError;
  
  // 3. Criar jogador no novo clube
  const { data: newPlayer, error: playerError } = await supabase
    .from('players')
    .insert({
      ...playerData,
      club_id: toClubId,
      global_player_id: globalPlayerId,
      is_transfer: true,
      transfer_id: transfer.id,
      status: 'ativo'
    })
    .select()
    .single();
    
  if (playerError) throw playerError;
  
  // 4. Copiar documentos se existirem
  if (searchResult.found && searchResult.documents) {
    await copyPlayerDocuments(
      globalPlayerId,
      newPlayer.id,
      toClubId,
      searchResult.documents
    );
  }
  
  return {
    success: true,
    playerId: newPlayer.id,
    transferId: transfer.id
  };
}
```

### 3. Cópia de Documentos
```typescript
async function copyPlayerDocuments(
  globalPlayerId: string,
  newPlayerId: string,
  newClubId: number,
  documents: GlobalPlayerDocument[]
): Promise<void> {
  
  for (const doc of documents) {
    // Copiar arquivo no storage para o novo clube
    const newFileUrl = await copyDocumentToNewClub(
      doc.file_url,
      newClubId,
      newPlayerId,
      doc.document_type
    );
    
    // Registrar documento para o novo jogador
    await supabase
      .from('player_documents')
      .insert({
        club_id: newClubId,
        player_id: newPlayerId,
        document_type: doc.document_type,
        file_url: newFileUrl,
        status: 'verified' // Manter como verificado se já estava
      });
  }
}

async function copyDocumentToNewClub(
  originalUrl: string,
  newClubId: number,
  newPlayerId: string,
  documentType: string
): Promise<string> {
  
  // Baixar arquivo original
  const response = await fetch(originalUrl);
  const blob = await response.blob();
  
  // Gerar novo caminho
  const fileExt = originalUrl.split('.').pop();
  const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
  const newFilePath = `${newClubId}/${newPlayerId}/${fileName}`;
  
  // Upload para novo local
  const { error } = await supabase.storage
    .from('playerdocuments')
    .upload(newFilePath, blob, {
      cacheControl: '3600',
      upsert: true
    });
    
  if (error) throw error;
  
  // Retornar nova URL
  const { data: { publicUrl } } = supabase.storage
    .from('playerdocuments')
    .getPublicUrl(newFilePath);
    
  return publicUrl;
}
```

## Gestão de Contas de Usuário

### 1. Transferência de Conta
```typescript
export async function transferPlayerAccount(
  playerId: string,
  fromClubId: number,
  toClubId: number
): Promise<void> {
  
  // 1. Buscar user_id do jogador
  const { data: player } = await supabase
    .from('players')
    .select('user_id')
    .eq('id', playerId)
    .single();
    
  if (!player?.user_id) return;
  
  // 2. Atualizar club_members
  await supabase
    .from('club_members')
    .update({ club_id: toClubId })
    .eq('user_id', player.user_id)
    .eq('club_id', fromClubId);
    
  // 3. Atualizar player_accounts
  await supabase
    .from('player_accounts')
    .update({ club_id: toClubId })
    .eq('user_id', player.user_id)
    .eq('club_id', fromClubId);
}
```

### 2. Isolamento de Dados
O sistema já possui RLS (Row Level Security) que garante que:
- Jogadores só veem dados do clube atual
- Histórico permanece isolado por clube
- Documentos são acessíveis apenas pelo clube atual

## Interface de Usuário

### 1. Tela de Cadastro com Busca por CPF
```typescript
// Componente de busca por CPF
const CPFSearchComponent = () => {
  const [cpf, setCpf] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const handleSearch = async () => {
    setLoading(true);
    try {
      const result = await searchPlayerByCPF(cpf);
      setSearchResult(result);
    } catch (error) {
      console.error('Erro na busca:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div>
      <Input
        value={cpf}
        onChange={(e) => setCpf(e.target.value)}
        placeholder="Digite o CPF do jogador"
        mask="999.999.999-99"
      />
      <Button onClick={handleSearch} disabled={loading}>
        {loading ? 'Buscando...' : 'Buscar Jogador'}
      </Button>
      
      {searchResult?.found && (
        <PlayerFoundCard 
          player={searchResult.globalPlayer}
          lastClub={searchResult.lastClub}
          documents={searchResult.documents}
          onTransfer={(playerData) => initiatePlayerTransfer(cpf, clubId, playerData, userId)}
        />
      )}
    </div>
  );
};
```

## Vantagens da Solução

1. **Evita Duplicação**: Dados pessoais e documentos ficam centralizados
2. **Mantém Histórico**: Cada clube mantém seu histórico específico
3. **Reutiliza Documentos**: Documentos são copiados automaticamente
4. **Conta Única**: Jogador mantém a mesma conta, apenas muda de clube
5. **Auditoria**: Sistema de transferências rastreável
6. **Flexibilidade**: Suporta transferências, empréstimos e retornos

## Implementação Gradual

1. **Fase 1**: Criar tabelas e funções básicas
2. **Fase 2**: Implementar busca por CPF
3. **Fase 3**: Sistema de transferências
4. **Fase 4**: Interface de usuário
5. **Fase 5**: Migração de dados existentes

Esta solução resolve o problema de duplicação mantendo a integridade dos dados e a segurança do sistema multi-tenant.