import React, { useState } from 'react';
import { X, Building2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { createMasterClub, CreateClubData, MasterPlan } from '@/api/masterClubs';

interface CreateClubModalProps {
  plans: MasterPlan[];
  onClose: () => void;
  onSuccess: () => void;
}

export const CreateClubModal: React.FC<CreateClubModalProps> = ({
  plans,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<CreateClubData>({
    name: '',
    email: '',
    president_password: '',
    phone: '',
    document: '',
    address: {},
    master_plan_id: 0,
    is_trial: false,
    trial_days: 30,
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const selectedPlan = plans.find(p => p.id === formData.master_plan_id);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.master_plan_id) {
      setError('Nome, email e plano são obrigatórios');
      return;
    }

    // Validar formato do email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Por favor, insira um email válido');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await createMasterClub(formData);
      onSuccess();
    } catch (err: any) {
      setError(err.message || 'Erro ao criar clube');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateClubData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-2">
            <Building2 className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Novo Clube</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Informações básicas */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informações Básicas</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Nome do Clube *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Ex: Flamengo FC"
                  required
                />
              </div>

              <div>
                <Label htmlFor="email">Email do Presidente *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Uma conta de presidente será criada automaticamente e as credenciais serão enviadas para este email
                </p>
              </div>
              
              <div>
                <Label htmlFor="president_password">Senha do Presidente</Label>
                <Input
                  id="president_password"
                  type="text"
                  value={formData.president_password || ''}
                  onChange={(e) => handleInputChange('president_password', e.target.value)}
                  placeholder="Deixe em branco para gerar automaticamente"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="(11) 99999-9999"
                />
              </div>

              <div>
                <Label htmlFor="document">CNPJ</Label>
                <Input
                  id="document"
                  value={formData.document}
                  onChange={(e) => handleInputChange('document', e.target.value)}
                  placeholder="00.000.000/0001-00"
                />
              </div>
            </div>
          </div>

          {/* Seleção de plano */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Plano</h3>

            <div>
              <Label>Selecionar Plano *</Label>
              <Select onValueChange={(value) => handleInputChange('master_plan_id', parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Escolha um plano" />
                </SelectTrigger>
                <SelectContent>
                  {plans.map(plan => (
                    <SelectItem key={plan.id} value={plan.id.toString()}>
                      <div className="flex items-center justify-between w-full">
                        <span>{plan.name}</span>
                        <span className="ml-2 text-sm text-gray-500">
                          {formatCurrency(plan.price)}/{plan.billing_cycle === 'yearly' ? 'ano' : 'mês'}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Detalhes do plano selecionado */}
            {selectedPlan && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{selectedPlan.name}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant={selectedPlan.is_trial ? 'outline' : 'secondary'}>
                        {selectedPlan.is_trial ? 'Trial' : 'Pago'}
                      </Badge>
                      <span className="font-bold text-lg">
                        {formatCurrency(selectedPlan.price)}
                      </span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{selectedPlan.description}</p>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Usuários:</span> {' '}
                      {selectedPlan.max_users === -1 ? 'Ilimitado' : selectedPlan.max_users}
                    </div>
                    <div>
                      <span className="font-medium">Jogadores:</span> {' '}
                      {selectedPlan.max_players === -1 ? 'Ilimitado' : selectedPlan.max_players}
                    </div>
                    <div>
                      <span className="font-medium">Armazenamento:</span> {' '}
                      {selectedPlan.max_storage_gb}GB
                    </div>
                    <div>
                      <span className="font-medium">Cobrança:</span> {' '}
                      {selectedPlan.billing_cycle === 'yearly' ? 'Anual' : 'Mensal'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Configurações de trial */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Configurações</h3>

            <div className="flex items-center justify-between">
              <div>
                <Label>Iniciar como Trial</Label>
                <p className="text-sm text-gray-500">
                  O clube começará com período de teste gratuito
                </p>
              </div>
              <Switch
                checked={formData.is_trial}
                onCheckedChange={(checked) => handleInputChange('is_trial', checked)}
              />
            </div>

            {formData.is_trial && (
              <div>
                <Label htmlFor="trial_days">Dias de Trial</Label>
                <Input
                  id="trial_days"
                  type="number"
                  value={formData.trial_days}
                  onChange={(e) => handleInputChange('trial_days', parseInt(e.target.value))}
                  min="1"
                  max="90"
                />
              </div>
            )}
          </div>

          {/* Observações */}
          <div>
            <Label htmlFor="notes">Observações</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Observações internas sobre o clube..."
              rows={3}
            />
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Criando...
                </div>
              ) : (
                'Criar Clube'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};