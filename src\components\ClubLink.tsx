import { ReactNode } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { useClubNavigation } from '@/hooks/useClubNavigation';

interface ClubLinkProps extends Omit<LinkProps, 'to'> {
  to: string;
  children: ReactNode;
}

/**
 * Componente Link que automaticamente inclui o slug do clube na URL
 */
export function ClubLink({ to, children, ...props }: ClubLinkProps) {
  const { getClubUrl } = useClubNavigation();
  
  return (
    <Link to={getClubUrl(to)} {...props}>
      {children}
    </Link>
  );
}