import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { InventoryProduct, INVENTORY_DEPARTMENTS, createInventoryProduct, updateInventoryProduct, filterDepartmentsByPermissions, getPlayers, getCollaborators } from "@/api/api";
import { usePermission } from "@/hooks/usePermission";
import { sendEmailWithBrevo } from "@/services/brevoEmailService";

// Esquema de validação
const productSchema = z.object({
  name: z.string().min(1, "Nome do produto é obrigatório"),
  quantity: z.coerce.number().min(0, "Quantidade não pode ser negativa"),
  minimum_quantity: z.coerce.number().min(0, "Quantidade mínima não pode ser negativa"),
  unit_of_measure: z.string().min(1, "Unidade de medida é obrigatória"),
  registration_date: z.string().min(1, "Data de cadastro é obrigatória"),
  expiration_date: z.string().optional().nullable(),
  location: z.string().optional(),
  department: z.string().min(1, "Departamento é obrigatório"),
  recipient_type: z.string().optional().nullable(),
  recipient_id: z.string().optional().nullable(),
  description: z.string().optional(),
  sale_price: z.coerce.number().min(0, "Preço de venda não pode ser negativo").optional().nullable(),
}).refine((data) => {
  // Se o departamento for "A Venda", o preço de venda é obrigatório
  if (data.department === "A Venda") {
    return data.sale_price !== null && data.sale_price !== undefined && data.sale_price > 0;
  }
  return true;
}, {
  message: "Preço de venda é obrigatório para produtos do departamento 'A Venda'",
  path: ["sale_price"],
}).refine((data) => {
  if (data.department === "Correspondencia") {
    return !!data.recipient_type && !!data.recipient_id;
  }
  return true;
}, {
  message: "Destinatário é obrigatório para correspondências",
  path: ["recipient_id"],
});

type ProductFormValues = z.infer<typeof productSchema>;

interface CadastrarProdutoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product: InventoryProduct | null;
  onSuccess: () => void;
}

export function CadastrarProdutoDialog({
  open,
  onOpenChange,
  product,
  onSuccess,
}: CadastrarProdutoDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const { role, permissions } = usePermission();
  const [players, setPlayers] = useState<any[]>([]);
  const [collaborators, setCollaborators] = useState<any[]>([]);
  
  // Filtrar departamentos baseado nas permissões do usuário
  const filteredDepartments = filterDepartmentsByPermissions(INVENTORY_DEPARTMENTS, role, permissions);

  // Inicializar formulário
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      quantity: 0,
      minimum_quantity: 0,
      unit_of_measure: "unidade",
      registration_date: new Date().toISOString().split("T")[0],
      expiration_date: null,
      location: "",
      department: "",
      recipient_type: '',
      recipient_id: null,
      description: "",
    },
  });

  // Atualizar formulário quando o produto for selecionado para edição
  useEffect(() => {
    if (product) {
      form.reset({
        name: product.name,
        quantity: product.quantity,
        minimum_quantity: product.minimum_quantity || 0,
        unit_of_measure: product.unit_of_measure || "unidade",
        registration_date: new Date(product.registration_date).toISOString().split("T")[0],
        expiration_date: product.expiration_date ? new Date(product.expiration_date).toISOString().split("T")[0] : null,
        location: product.location || "",
        department: product.department,
        recipient_type: product.recipient_type || '',
        recipient_id: product.recipient_id || null,
        description: product.description || "",
        sale_price: product.sale_price || null,
      });
    } else {
      form.reset({
        name: "",
        quantity: 0,
        minimum_quantity: 0,
        unit_of_measure: "unidade",
        registration_date: new Date().toISOString().split("T")[0],
        expiration_date: null,
        location: "",
        department: "",
        recipient_type: '',
        recipient_id: null,
        description: "",
      });
    }
  }, [product, form]);

  useEffect(() => {
    if (open) {
      loadPeople();
    }
  }, [open]);

  const loadPeople = async () => {
    try {
      const playersData = await getPlayers(clubId, undefined, { includeInactive: false });
      setPlayers(
        playersData.filter(
          (player) =>
            player.status !== "em avaliacao" &&
            player.status !== "aguardando documentacao" &&
            player.status !== "aguardando agendamento" &&
            player.status !== "jogador agendado" &&
            player.status !== "inativo"
        )
      );

      const collaboratorsData = (await getCollaborators(clubId)).filter(
        (collaborator: any) => collaborator.status !== "inativo"
      );
      setCollaborators(collaboratorsData);
    } catch (error) {
      console.error("Erro ao carregar dados de destinatários:", error);
    }
  };

  // Função para salvar o produto
  const onSubmit = async (data: ProductFormValues) => {
    try {
      setIsLoading(true);

      if (product) {
        // Atualizar produto existente
        await updateInventoryProduct(clubId, product.id, data, user?.id);
        toast({
          title: "Produto atualizado",
          description: "O produto foi atualizado com sucesso.",
        });
      } else {
        // Criar novo produto
        await createInventoryProduct(clubId, data, user?.id);
        toast({
          title: "Produto cadastrado",
          description: "O produto foi cadastrado com sucesso.",
        });

        if (data.department === "Correspondencia" && data.recipient_type && data.recipient_id) {
          try {
            let email = "";
            let name = "";
            if (data.recipient_type === "player") {
              const player = players.find((p) => p.id === data.recipient_id);
              email = player?.email || "";
              name = player?.name || "";
            } else {
              const collaborator = collaborators.find((c) => c.id.toString() === data.recipient_id);
              email = collaborator?.email || "";
              name = collaborator?.full_name || collaborator?.email || "";
            }
            if (email) {
              const clubName = user?.club_info?.name ?? "seu clube";
              const locationInfo = data.location ? `<p><strong>Localização:</strong> ${data.location}</p>` : "";
              const descriptionInfo = data.description ? `<p><strong>Descrição:</strong> ${data.description}</p>` : "";

              await sendEmailWithBrevo({
                to: email,
                subject: `Correspondência disponível no ${clubName}`,
                body: `<p>Prezado(a) ${name},</p>
                  <p>Informamos que há uma correspondência em seu nome disponível para retirada na secretaria do ${clubName}.</p>
                  ${locationInfo}
                  ${descriptionInfo}
                  <p>Por favor, dirija-se à secretaria para realizar a retirada.</p>
                  <p>Atenciosamente,<br/>${clubName}</p>`,
              });
            }
          } catch (err) {
            console.error("Erro ao enviar email de correspondência:", err);
          }
        }
      }

      // Fechar o diálogo e atualizar a lista
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Erro ao salvar produto:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar o produto. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{product ? "Editar Produto" : "Cadastrar Novo Produto"}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Departamento</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um departamento" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {filteredDepartments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("department") === "Correspondencia" && (
              <>
                <FormField
                  control={form.control}
                  name="recipient_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Destinatário</FormLabel>
                      <Select
                        value={field.value || ""}
                        onValueChange={(value) => {
                          field.onChange(value);
                          form.setValue("recipient_id", null);
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="collaborator">Colaborador</SelectItem>
                          <SelectItem value="player">Jogador</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="recipient_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Destinatário</FormLabel>
                      <Select
                        value={field.value || ""}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a pessoa" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {form.watch("recipient_type") === "player"
                            ? players
                                .sort((a, b) => a.name.localeCompare(b.name))
                                .map((player) => (
                                  <SelectItem key={player.id} value={player.id}>
                                    {player.name}
                                  </SelectItem>
                                ))
                            : collaborators
                                .sort((a, b) =>
                                  (a.full_name || a.email).localeCompare(
                                    b.full_name || b.email
                                  )
                                )
                                .map((collaborator) => (
                                  <SelectItem
                                    key={collaborator.id}
                                    value={collaborator.id.toString()}
                                  >
                                    {collaborator.full_name || collaborator.email}
                                  </SelectItem>
                                ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Produto</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome do produto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade</FormLabel>
                    <FormControl>
                      <Input type="number" min="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="registration_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Cadastro</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="expiration_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Validade (opcional)</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      value={field.value || ''}
                      onChange={(e) => field.onChange(e.target.value || null)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="minimum_quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade Mínima</FormLabel>
                    <FormControl>
                      <Input type="number" min="0" {...field} />
                    </FormControl>
                    <FormDescription>
                      Quantidade mínima para alertas de estoque baixo
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_of_measure"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unidade de Medida</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a unidade" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="unidade">Unidade</SelectItem>
                        <SelectItem value="kg">Quilograma (kg)</SelectItem>
                        <SelectItem value="g">Grama (g)</SelectItem>
                        <SelectItem value="l">Litro (l)</SelectItem>
                        <SelectItem value="ml">Mililitro (ml)</SelectItem>
                        <SelectItem value="caixa">Caixa</SelectItem>
                        <SelectItem value="pacote">Pacote</SelectItem>
                        <SelectItem value="saco">Saco</SelectItem>
                        <SelectItem value="par">Par</SelectItem>
                        <SelectItem value="conjunto">Conjunto</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Campo de preço de venda - apenas para departamento "A Venda" */}
            {form.watch("department") === "A Venda" && (
              <FormField
                control={form.control}
                name="sale_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço de Venda (R$)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || null)}
                      />
                    </FormControl>
                    <FormDescription>
                      Preço unitário para venda deste produto
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Localização</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Almoxarifado A, Sala 1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descrição do produto"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button type="submit" isLoading={isLoading}>
                {product ? "Atualizar" : "Cadastrar"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}