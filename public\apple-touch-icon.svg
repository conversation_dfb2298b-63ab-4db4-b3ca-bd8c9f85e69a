<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 180 180" width="180" height="180">
  <defs>
    <linearGradient id="ballGradientApple" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rounded rectangle background -->
  <rect x="0" y="0" width="180" height="180" rx="40" ry="40" fill="url(#ballGradientApple)"/>
  
  <!-- Soccer ball -->
  <circle cx="90" cy="90" r="70" fill="#ffffff" opacity="0.1"/>
  <circle cx="90" cy="90" r="65" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>
  
  <g fill="none" stroke="#ffffff" stroke-width="3" opacity="0.9">
    <!-- Pentagon in center -->
    <polygon points="90,45 110,65 102,90 78,90 70,65"/>
    
    <!-- Hexagon patterns -->
    <polygon points="90,45 70,65 50,55 58,35 78,35"/>
    <polygon points="90,45 110,65 130,55 122,35 102,35"/>
    <polygon points="70,65 78,90 58,110 38,98 50,78"/>
    <polygon points="110,65 102,90 122,110 142,98 130,78"/>
    <polygon points="78,90 102,90 110,115 90,135 70,115"/>
  </g>
  
  <!-- Game Day Nexus text -->
  <text x="90" y="155" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="12" font-weight="600" opacity="0.8">GAME DAY</text>
  <text x="90" y="170" text-anchor="middle" fill="#ffffff" font-family="Inter, sans-serif" font-size="10" font-weight="400" opacity="0.6">NEXUS</text>
</svg>