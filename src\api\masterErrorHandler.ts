/**
 * Master API Error Handler Utility
 * Provides consistent error handling and logging for master APIs
 */

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

/**
 * Standard error codes for master APIs
 */
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  MIGRATION_ERROR: 'MIGRATION_ERROR',
  DUPLICATE_ERROR: 'DUPLICATE_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

/**
 * Log error with context for debugging
 */
export const logError = (context: string, error: any, additionalData?: any): void => {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    context,
    error: {
      message: error.message,
      code: error.code,
      stack: error.stack
    },
    additionalData
  };
  
  console.error(`[${timestamp}] Master API Error in ${context}:`, logData);
  
  // In production, you might want to send this to a logging service
  // Example: sendToLoggingService(logData);
};

/**
 * Handle database errors with specific error codes
 */
export const handleDatabaseError = (error: any, context: string): ApiError => {
  logError(context, error);
  
  // Handle specific PostgreSQL/Supabase error codes
  switch (error.code) {
    case 'PGRST116':
      return {
        code: ERROR_CODES.MIGRATION_ERROR,
        message: 'Campos não encontrados na tabela. Verifique se a migração foi aplicada.',
        details: { originalError: error.message },
        timestamp: new Date().toISOString()
      };
      
    case 'PGRST202':
      return {
        code: ERROR_CODES.MIGRATION_ERROR,
        message: 'Função não encontrada no banco de dados. Verifique se a migração foi aplicada.',
        details: { originalError: error.message },
        timestamp: new Date().toISOString()
      };
      
    case '23505':
      return {
        code: ERROR_CODES.DUPLICATE_ERROR,
        message: 'Registro duplicado. Este item já existe.',
        details: { originalError: error.message },
        timestamp: new Date().toISOString()
      };
      
    case '23503':
      return {
        code: ERROR_CODES.VALIDATION_ERROR,
        message: 'Referência inválida. Verifique se os dados relacionados existem.',
        details: { originalError: error.message },
        timestamp: new Date().toISOString()
      };
      
    case 'PGRST106':
      return {
        code: ERROR_CODES.NOT_FOUND,
        message: 'Registro não encontrado.',
        details: { originalError: error.message },
        timestamp: new Date().toISOString()
      };
      
    default:
      return {
        code: ERROR_CODES.DATABASE_ERROR,
        message: `Erro no banco de dados: ${error.message}`,
        details: { originalError: error.message, code: error.code },
        timestamp: new Date().toISOString()
      };
  }
};

/**
 * Handle validation errors
 */
export const handleValidationError = (message: string, details?: any): ApiError => {
  return {
    code: ERROR_CODES.VALIDATION_ERROR,
    message,
    details,
    timestamp: new Date().toISOString()
  };
};

/**
 * Create a successful API response
 */
export const createSuccessResponse = <T>(data: T): ApiResponse<T> => {
  return {
    success: true,
    data
  };
};

/**
 * Create an error API response
 */
export const createErrorResponse = (error: ApiError): ApiResponse<never> => {
  return {
    success: false,
    error
  };
};

/**
 * Wrapper for API functions to provide consistent error handling
 */
export const withErrorHandling = async <T>(
  fn: () => Promise<T>,
  context: string
): Promise<ApiResponse<T>> => {
  try {
    const result = await fn();
    return createSuccessResponse(result);
  } catch (error: any) {
    let apiError: ApiError;
    
    if (error.code) {
      // Database error
      apiError = handleDatabaseError(error, context);
    } else if (error.message.includes('obrigatório') || 
               error.message.includes('deve ser') || 
               error.message.includes('inválido')) {
      // Validation error
      apiError = handleValidationError(error.message);
    } else {
      // Unknown error
      logError(context, error);
      apiError = {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: error.message || 'Erro desconhecido',
        timestamp: new Date().toISOString()
      };
    }
    
    return createErrorResponse(apiError);
  }
};

/**
 * Validate required fields
 */
export const validateRequired = (data: Record<string, any>, requiredFields: string[]): void => {
  for (const field of requiredFields) {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim().length === 0)) {
      throw new Error(`Campo ${field} é obrigatório`);
    }
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): void => {
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    throw new Error('Email deve ter um formato válido');
  }
};

/**
 * Validate positive number
 */
export const validatePositiveNumber = (value: number, fieldName: string): void => {
  if (!value || value <= 0) {
    throw new Error(`${fieldName} deve ser um número positivo`);
  }
};

/**
 * Validate date range
 */
export const validateDateRange = (startDate: string, endDate: string): void => {
  if (startDate && endDate && startDate > endDate) {
    throw new Error('Data inicial deve ser anterior à data final');
  }
};