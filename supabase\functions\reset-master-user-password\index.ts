import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { userId, newPassword } = await req.json()

    if (!userId || !newPassword) {
      throw new Error('userId e newPassword são obrigatórios')
    }

    // Validate password strength
    if (newPassword.length < 6) {
      throw new Error('A senha deve ter pelo menos 6 caracteres')
    }

    // 1. Get user data for audit log
    const { data: userData, error: getUserError } = await supabaseAdmin
      .from('master_users')
      .select('name, email')
      .eq('id', userId)
      .single()

    if (getUserError) {
      throw new Error(`Usuário não encontrado: ${getUserError.message}`)
    }

    // 2. Update password in auth.users
    const { error: updatePasswordError } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { password: newPassword }
    )

    if (updatePasswordError) {
      throw new Error(`Erro ao atualizar senha: ${updatePasswordError.message}`)
    }

    // 3. Log the action in audit logs
    await supabaseAdmin
      .from('master_audit_logs')
      .insert({
        user_id: userId, // The user whose password was reset
        action: 'reset_master_user_password',
        entity_type: 'master_user',
        entity_id: userId,
        details: {
          message: 'Senha do usuário master redefinida',
          user_email: userData.email,
          reset_by: 'system' // In production, pass the current user's ID
        }
      })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Senha redefinida com sucesso'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Erro ao redefinir senha:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})