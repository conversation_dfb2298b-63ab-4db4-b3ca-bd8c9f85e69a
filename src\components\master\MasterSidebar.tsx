import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Building2, 
  CreditCard, 
  Users, 
  BarChart3, 
  Settings,
  DollarSign,
  FileText,
  Shield,
  HelpCircle
} from 'lucide-react';
import { useMasterPermissions } from '@/hooks/useMasterAuth';
import { Badge } from "@/components/ui/badge";
import { MasterDashboardStats } from '@/api/masterDashboard';

interface MenuItem {
  icon: React.ElementType;
  label: string;
  href: string;
  permission?: string;
  roles?: string[];
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

interface MasterSidebarProps {
  stats: MasterDashboardStats;
}

const masterMenuItems: MenuItem[] = [
  {
    icon: LayoutDashboard,
    label: 'Dashboard',
    href: '/master/dashboard'
  },
  {
    icon: Building2,
    label: 'Clubes',
    href: '/master/clubs',
    permission: 'clubs.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: CreditCard,
    label: 'Planos',
    href: '/master/plans',
    permission: 'plans.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: DollarSign,
    label: 'Cobrança',
    href: '/master/billing',
    permission: 'payments.view',
    roles: ['super_admin', 'admin'],
    badge: '5',
    badgeVariant: 'destructive'
  },
  {
    icon: BarChart3,
    label: 'Analytics',
    href: '/master/analytics',
    permission: 'analytics.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: FileText,
    label: 'Relatórios',
    href: '/master/reports',
    permission: 'reports.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: Users,
    label: 'Usuários Master',
    href: '/master/users',
    permission: 'users.view',
    roles: ['super_admin']
  },
  {
    icon: Shield,
    label: 'Auditoria',
    href: '/master/audit',
    permission: 'audit.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: Settings,
    label: 'Configurações',
    href: '/master/settings',
    permission: 'settings.view',
    roles: ['super_admin', 'admin']
  },
  {
    icon: HelpCircle,
    label: 'Suporte',
    href: '/master/support'
  }
];

export const MasterSidebar: React.FC<MasterSidebarProps> = ({ stats }) => {
  const location = useLocation();
  const { checkPermission, checkRole, masterUser } = useMasterPermissions();

  const hasAccess = (item: MenuItem): boolean => {
    // Se não há restrições, permitir acesso
    if (!item.permission && !item.roles) {
      return true;
    }

    // Verificar permissão específica
    if (item.permission && !checkPermission(item.permission)) {
      return false;
    }

    // Verificar roles
    if (item.roles && !checkRole(item.roles)) {
      return false;
    }

    return true;
  };

  const isActive = (href: string): boolean => {
    if (href === '/master/dashboard') {
      return location.pathname === '/master' || location.pathname === '/master/dashboard';
    }
    return location.pathname.startsWith(href);
  };

  const formatRevenue = (value: number) => {
    if (value >= 1000) {
      return `R$ ${(value / 1000).toFixed(1)}K`;
    }
    return `R$ ${value}`;
  };

  return (
    <aside className="fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white border-r border-gray-200 overflow-y-auto">
      <div className="p-4">
        {/* Informações do usuário */}
        <div className="mb-6 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {masterUser?.name?.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {masterUser?.name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {masterUser?.role === 'super_admin' ? 'Super Admin' : 
                 masterUser?.role === 'admin' ? 'Administrador' :
                 masterUser?.role === 'support' ? 'Suporte' : 'Visualizador'}
              </p>
            </div>
          </div>
        </div>

        {/* Menu de navegação */}
        <nav className="space-y-1">
          {masterMenuItems.map((item) => {
            if (!hasAccess(item)) {
              return null;
            }

            const Icon = item.icon;
            const active = isActive(item.href);

            return (
              <NavLink
                key={item.href}
                to={item.href}
                className={`
                  flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors
                  ${active 
                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700' 
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }
                `}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`w-5 h-5 ${active ? 'text-blue-700' : 'text-gray-500'}`} />
                  <span>{item.label}</span>
                </div>
                
                {item.badge && (
                  <Badge 
                    variant={item.badgeVariant || 'secondary'} 
                    className="text-xs"
                  >
                    {item.badge}
                  </Badge>
                )}
              </NavLink>
            );
          })}
        </nav>

        {/* Seção de estatísticas rápidas */}
        <div className="mt-8 p-3 bg-gray-50 rounded-lg">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
            Estatísticas Rápidas
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Clubes Ativos</span>
              <Badge variant="secondary" className="text-xs">{stats.activeClubs}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Receita Mensal</span>
              <Badge variant="secondary" className="text-xs">{formatRevenue(stats.monthlyRevenue)}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Em Atraso</span>
              <Badge variant="destructive" className="text-xs">{stats.overduePayments}</Badge>
            </div>
          </div>
        </div>

        {/* Links úteis */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="space-y-1">
            <a
              href="/docs"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <FileText className="w-4 h-4 mr-3" />
              Documentação
            </a>
            <a
              href="/api-docs"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Settings className="w-4 h-4 mr-3" />
              API Docs
            </a>
          </div>
        </div>
      </div>
    </aside>
  );
};