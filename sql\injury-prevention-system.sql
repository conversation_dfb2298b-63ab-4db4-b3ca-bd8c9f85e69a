-- Sistema de IA para Prevenção de Lesões
-- Estrutura de banco de dados para coleta de dados

-- Tabela para dados de carga de trabalho
CREATE TABLE IF NOT EXISTS player_workload_data (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  player_id INTEGER REFERENCES players(id) NOT NULL,
  date DATE NOT NULL,
  
  -- Dados de treino
  training_duration INTEGER, -- minutos
  training_intensity DECIMAL(3,1), -- 1-10 escala
  training_type TEXT, -- 'técnico', 'físico', 'tático', 'jogo'
  
  -- Dados físicos
  distance_covered DECIMAL(6,2), -- km
  sprint_count INTEGER,
  max_speed DECIMAL(4,1), -- km/h
  heart_rate_avg INTEGER, -- bpm
  heart_rate_max INTEGER, -- bpm
  
  -- Dados subjetivos (RPE - Rate of Perceived Exertion)
  perceived_exertion INTEGER, -- 1-10
  sleep_quality INTEGER, -- 1-10
  stress_level INTEGER, -- 1-10
  muscle_soreness INTEGER, -- 1-10
  
  -- Dad<PERSON> calculados
  training_load DECIMAL(6,2), -- intensidade * duração
  acute_chronic_ratio DECIMAL(4,2), -- ratio de carga aguda/crônica
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para histórico de lesões detalhado
CREATE TABLE IF NOT EXISTS injury_history (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  player_id INTEGER REFERENCES players(id) NOT NULL,
  
  injury_date DATE NOT NULL,
  injury_type TEXT NOT NULL, -- 'muscular', 'articular', 'ligamentar', etc.
  body_part TEXT NOT NULL, -- 'joelho', 'tornozelo', 'coxa', etc.
  severity TEXT NOT NULL, -- 'leve', 'moderada', 'grave'
  mechanism TEXT, -- 'contato', 'sem contato', 'sobrecarga'
  
  days_out INTEGER, -- dias afastado
  return_date DATE,
  
  -- Contexto da lesão
  occurred_during TEXT, -- 'treino', 'jogo', 'aquecimento'
  field_position TEXT, -- posição no campo quando ocorreu
  weather_conditions TEXT,
  
  -- Fatores contribuintes
  previous_injury BOOLEAN DEFAULT FALSE,
  fatigue_level INTEGER, -- 1-10
  workload_week DECIMAL(6,2), -- carga da semana
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para dados de wellness diário
CREATE TABLE IF NOT EXISTS player_wellness_data (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  player_id INTEGER REFERENCES players(id) NOT NULL,
  date DATE NOT NULL,
  
  -- Questionário de wellness
  sleep_hours DECIMAL(3,1),
  sleep_quality INTEGER, -- 1-10
  fatigue_level INTEGER, -- 1-10
  muscle_soreness INTEGER, -- 1-10
  stress_level INTEGER, -- 1-10
  mood INTEGER, -- 1-10
  
  -- Dados físicos básicos
  weight DECIMAL(5,2),
  resting_heart_rate INTEGER,
  
  -- Observações
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(club_id, player_id, date)
);

-- Tabela para fatores de risco calculados
CREATE TABLE IF NOT EXISTS injury_risk_factors (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  player_id INTEGER REFERENCES players(id) NOT NULL,
  calculated_date DATE NOT NULL,
  
  -- Scores de risco (0-100)
  overall_risk_score DECIMAL(5,2),
  muscular_risk_score DECIMAL(5,2),
  joint_risk_score DECIMAL(5,2),
  overload_risk_score DECIMAL(5,2),
  
  -- Fatores específicos
  acute_chronic_ratio DECIMAL(4,2),
  training_monotony DECIMAL(4,2),
  wellness_score DECIMAL(4,2),
  previous_injury_factor DECIMAL(4,2),
  
  -- Recomendações da IA
  risk_level TEXT, -- 'baixo', 'moderado', 'alto', 'crítico'
  recommendations JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(club_id, player_id, calculated_date)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_workload_player_date ON player_workload_data(player_id, date);
CREATE INDEX IF NOT EXISTS idx_injury_history_player ON injury_history(player_id);
CREATE INDEX IF NOT EXISTS idx_wellness_player_date ON player_wellness_data(player_id, date);
CREATE INDEX IF NOT EXISTS idx_risk_factors_player_date ON injury_risk_factors(player_id, calculated_date);

-- RLS (Row Level Security)
ALTER TABLE player_workload_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE injury_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_wellness_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE injury_risk_factors ENABLE ROW LEVEL SECURITY;

-- Políticas RLS
CREATE POLICY "Users can only access their club's workload data" ON player_workload_data
  FOR ALL USING (club_id = (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY "Users can only access their club's injury history" ON injury_history
  FOR ALL USING (club_id = (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY "Users can only access their club's wellness data" ON player_wellness_data
  FOR ALL USING (club_id = (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY "Users can only access their club's risk factors" ON injury_risk_factors
  FOR ALL USING (club_id = (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

-- Tabela para alertas de lesão
CREATE TABLE IF NOT EXISTS injury_alerts (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  player_id INTEGER REFERENCES players(id) NOT NULL,
  player_name TEXT NOT NULL,

  alert_type TEXT NOT NULL CHECK (alert_type IN ('critical', 'high', 'moderate', 'improvement')),
  risk_score DECIMAL(5,2) NOT NULL,
  previous_risk_score DECIMAL(5,2),

  alert_message TEXT NOT NULL,
  recommendations JSONB DEFAULT '[]',

  acknowledged BOOLEAN DEFAULT FALSE,
  acknowledged_by UUID REFERENCES users(id),
  acknowledged_at TIMESTAMP WITH TIME ZONE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para alertas
CREATE INDEX IF NOT EXISTS idx_injury_alerts_club_date ON injury_alerts(club_id, created_at);
CREATE INDEX IF NOT EXISTS idx_injury_alerts_player ON injury_alerts(player_id);
CREATE INDEX IF NOT EXISTS idx_injury_alerts_unacknowledged ON injury_alerts(club_id, acknowledged) WHERE acknowledged = FALSE;

-- RLS para alertas
ALTER TABLE injury_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their club's alerts" ON injury_alerts
  FOR ALL USING (club_id = (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

-- Função para calcular score de wellness
CREATE OR REPLACE FUNCTION calculate_wellness_score(
    p_sleep_quality INTEGER,
    p_fatigue_level INTEGER,
    p_muscle_soreness INTEGER,
    p_stress_level INTEGER,
    p_mood INTEGER
) RETURNS DECIMAL(4,2) AS $$
BEGIN
    -- Calcular score médio (invertendo valores negativos)
    RETURN (
        COALESCE(p_sleep_quality, 5) +
        (10 - COALESCE(p_fatigue_level, 5)) +
        (10 - COALESCE(p_muscle_soreness, 5)) +
        (10 - COALESCE(p_stress_level, 5)) +
        COALESCE(p_mood, 5)
    ) / 5.0;
END;
$$ LANGUAGE plpgsql;

-- Função para calcular carga aguda (últimos 7 dias)
CREATE OR REPLACE FUNCTION calculate_acute_load(
    p_club_id INTEGER,
    p_player_id INTEGER,
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS DECIMAL(6,2) AS $$
DECLARE
    v_total_load DECIMAL(6,2) := 0;
    v_days_count INTEGER := 0;
BEGIN
    SELECT
        COALESCE(SUM(training_load), 0),
        COUNT(*)
    INTO v_total_load, v_days_count
    FROM player_workload_data
    WHERE club_id = p_club_id
      AND player_id = p_player_id
      AND date BETWEEN (p_date - INTERVAL '6 days') AND p_date;

    IF v_days_count > 0 THEN
        RETURN v_total_load / v_days_count;
    ELSE
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Função para calcular carga crônica (últimos 28 dias)
CREATE OR REPLACE FUNCTION calculate_chronic_load(
    p_club_id INTEGER,
    p_player_id INTEGER,
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS DECIMAL(6,2) AS $$
DECLARE
    v_total_load DECIMAL(6,2) := 0;
    v_days_count INTEGER := 0;
BEGIN
    SELECT
        COALESCE(SUM(training_load), 0),
        COUNT(*)
    INTO v_total_load, v_days_count
    FROM player_workload_data
    WHERE club_id = p_club_id
      AND player_id = p_player_id
      AND date BETWEEN (p_date - INTERVAL '27 days') AND p_date;

    IF v_days_count > 0 THEN
        RETURN v_total_load / v_days_count;
    ELSE
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Função para calcular ratio agudo:crônico
CREATE OR REPLACE FUNCTION calculate_acute_chronic_ratio(
    p_club_id INTEGER,
    p_player_id INTEGER,
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS DECIMAL(4,2) AS $$
DECLARE
    v_acute_load DECIMAL(6,2);
    v_chronic_load DECIMAL(6,2);
BEGIN
    v_acute_load := calculate_acute_load(p_club_id, p_player_id, p_date);
    v_chronic_load := calculate_chronic_load(p_club_id, p_player_id, p_date);

    IF v_chronic_load > 0 THEN
        RETURN v_acute_load / v_chronic_load;
    ELSE
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar acute_chronic_ratio automaticamente
CREATE OR REPLACE FUNCTION update_workload_acr()
RETURNS TRIGGER AS $$
BEGIN
    NEW.acute_chronic_ratio := calculate_acute_chronic_ratio(NEW.club_id, NEW.player_id, NEW.date);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_workload_acr
    BEFORE INSERT OR UPDATE ON player_workload_data
    FOR EACH ROW
    EXECUTE FUNCTION update_workload_acr();
