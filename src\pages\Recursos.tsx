import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Download, FileText, Calculator, CheckSquare, Users, TrendingUp, Shield, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BlogSEO } from '@/components/blog/BlogSEO';
import { trackLeadMagnetDownload } from '@/components/analytics/GoogleAnalytics';

interface LeadMagnet {
  id: string;
  title: string;
  description: string;
  type: 'excel' | 'pdf' | 'zip';
  category: string;
  downloadUrl: string;
  icon: any;
  color: string;
  features: string[];
  estimatedValue: string;
}

const leadMagnets: LeadMagnet[] = [
  {
    id: 'kit-gestao-completa',
    title: 'Kit Completo de Gestão de Clubes',
    description: 'Planil<PERSON>, checklists e templates para implementar gestão profissional no seu clube',
    type: 'zip',
    category: 'Gestão Geral',
    downloadUrl: '/downloads/kit-gestao-clubes.txt',
    icon: Users,
    color: 'from-blue-500 to-blue-600',
    features: [
      'Diagnóstico completo de gestão',
      'Checklist de implementação 90 dias',
      'Template de relatório mensal',
      'Matriz de permissões',
      'Guia passo a passo (20 páginas)'
    ],
    estimatedValue: 'R$ 497'
  },
  {
    id: 'fluxo-caixa-clubes',
    title: 'Planilha de Fluxo de Caixa Completa',
    description: 'Template com fórmulas automáticas e tutorial passo a passo para controle financeiro',
    type: 'excel',
    category: 'Financeiro',
    downloadUrl: '/downloads/fluxo-caixa-clubes.csv',
    icon: TrendingUp,
    color: 'from-green-500 to-green-600',
    features: [
      'Dashboard com indicadores automáticos',
      'Projeção de 12 meses',
      'Categorização inteligente',
      'Gráficos visuais',
      'Alertas de saldo baixo'
    ],
    estimatedValue: 'R$ 197'
  },
  {
    id: 'planilha-permissoes',
    title: 'Matriz de Permissões e Controle de Acesso',
    description: 'Matriz completa de funções e permissões para organizar acessos no seu clube',
    type: 'excel',
    category: 'Segurança',
    downloadUrl: '/downloads/planilha-permissoes.csv',
    icon: Shield,
    color: 'from-purple-500 to-purple-600',
    features: [
      'Matriz de funções vs permissões',
      'Templates por tipo de clube',
      'Guia de implementação',
      'Checklist de segurança',
      'Exemplos práticos'
    ],
    estimatedValue: 'R$ 147'
  },
  {
    id: 'calculadora-minutagem',
    title: 'Calculadora de Minutagem de Atletas',
    description: 'Planilha para controlar tempo de jogo e carga de trabalho dos atletas',
    type: 'excel',
    category: 'Esportivo',
    downloadUrl: '/downloads/calculadora-minutagem.csv',
    icon: Target,
    color: 'from-orange-500 to-orange-600',
    features: [
      'Cálculo automático de minutos',
      'Controle por temporada',
      'Alertas de sobrecarga',
      'Relatórios por posição',
      'Gráficos de utilização'
    ],
    estimatedValue: 'R$ 97'
  }
];

export default function Recursos() {
  const [selectedResource, setSelectedResource] = useState<LeadMagnet | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    club: '',
    role: '',
    clubSize: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleDownload = (resource: LeadMagnet) => {
    setSelectedResource(resource);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedResource) return;

    setIsSubmitting(true);

    try {
      // Simular envio para API de email marketing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Track no Google Analytics
      trackLeadMagnetDownload(selectedResource.title);
      
      // Iniciar download
      const link = document.createElement('a');
      link.href = selectedResource.downloadUrl;
      link.download = selectedResource.downloadUrl.split('/').pop() || '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setSubmitted(true);
      setSelectedResource(null);
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        club: '',
        role: '',
        clubSize: ''
      });
      
    } catch (error) {
      console.error('Erro ao processar download:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'excel': return '📊';
      case 'pdf': return '📄';
      case 'zip': return '📦';
      default: return '📁';
    }
  };

  return (
    <>
      <BlogSEO
        title="Recursos Gratuitos - Game Day Nexus | Templates e Planilhas"
        description="Downloads gratuitos para gestão de clubes: planilhas, templates, checklists e guias. Tudo que você precisa para profissionalizar seu clube."
        canonical="/recursos"
        type="website"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Simple Header */}
        <header className="bg-white shadow-sm border-b sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/" className="flex items-center space-x-2">
                <img src="/logo-branca.png" alt="Game Day Nexus Logo" className="h-10 w-auto" />
                <div className="flex flex-col">
                  <span className="font-bold text-xl text-gray-900">Game Day Nexus</span>
                  <span className="text-xs text-blue-600 font-medium">Gestão Esportiva</span>
                </div>
              </Link>
              
              <div className="flex items-center space-x-4">
                <Link to="/blog" className="text-gray-600 hover:text-blue-600 font-medium">
                  Blog
                </Link>
                <Link to="/" className="text-gray-600 hover:text-blue-600 font-medium">
                  Home
                </Link>
                <Link to="/login">
                  <Button variant="outline" size="sm">Entrar</Button>
                </Link>
                <Link to="/trial">
                  <Button size="sm">Teste Grátis</Button>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Recursos Gratuitos para Seu Clube
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              Templates, planilhas e guias profissionais para transformar a gestão do seu clube. 
              Tudo gratuito, tudo pronto para usar.
            </p>
            <div className="flex items-center justify-center space-x-8 text-sm opacity-80">
              <div className="flex items-center">
                <Download className="h-5 w-5 mr-2" />
                <span>Download Imediato</span>
              </div>
              <div className="flex items-center">
                <CheckSquare className="h-5 w-5 mr-2" />
                <span>100% Gratuito</span>
              </div>
              <div className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                <span>Pronto para Usar</span>
              </div>
            </div>
          </div>
        </div>

        {/* Resources Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {leadMagnets.map((resource) => {
              const Icon = resource.icon;
              return (
                <Card key={resource.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className={`h-2 bg-gradient-to-r ${resource.color}`} />
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-lg bg-gradient-to-r ${resource.color} text-white`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-2xl">{getTypeIcon(resource.type)}</span>
                            <span className="text-sm text-gray-500 uppercase font-medium">
                              {resource.category}
                            </span>
                          </div>
                          <CardTitle className="text-xl">{resource.title}</CardTitle>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500 line-through">
                          {resource.estimatedValue}
                        </div>
                        <div className="text-lg font-bold text-green-600">
                          GRÁTIS
                        </div>
                      </div>
                    </div>
                    <CardDescription className="text-base">
                      {resource.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 mb-6">
                      <h4 className="font-semibold text-gray-900">O que está incluído:</h4>
                      <ul className="space-y-2">
                        {resource.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <CheckSquare className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <Button 
                      onClick={() => handleDownload(resource)}
                      className="w-full"
                      size="lg"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Baixar Grátis
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-blue-50 py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Quer Automatizar Tudo Isso?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Estes recursos são ótimos para começar, mas imagine ter tudo automatizado, 
              integrado e funcionando 24/7 para seu clube.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/trial">
                <Button size="lg" className="px-8">
                  Teste Grátis por 14 Dias
                </Button>
              </Link>
              <Link to="/demo">
                <Button variant="outline" size="lg" className="px-8">
                  Agendar Demonstração
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Download Modal */}
        {selectedResource && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-xl font-bold mb-4">
                Baixar: {selectedResource.title}
              </h3>
              <p className="text-gray-600 mb-6">
                Preencha os dados abaixo para receber o download e dicas exclusivas por email.
              </p>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Nome Completo *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="email">Email Profissional *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="club">Nome do Clube *</Label>
                  <Input
                    id="club"
                    value={formData.club}
                    onChange={(e) => setFormData({...formData, club: e.target.value})}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="role">Sua Função</Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData({...formData, role: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione sua função" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="presidente">Presidente</SelectItem>
                      <SelectItem value="diretor">Diretor</SelectItem>
                      <SelectItem value="tecnico">Técnico</SelectItem>
                      <SelectItem value="coordenador">Coordenador</SelectItem>
                      <SelectItem value="secretario">Secretário</SelectItem>
                      <SelectItem value="outro">Outro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="clubSize">Tamanho do Clube</Label>
                  <Select value={formData.clubSize} onValueChange={(value) => setFormData({...formData, clubSize: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Número de atletas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1-30">1-30 atletas</SelectItem>
                      <SelectItem value="31-100">31-100 atletas</SelectItem>
                      <SelectItem value="101-300">101-300 atletas</SelectItem>
                      <SelectItem value="300+">Mais de 300 atletas</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setSelectedResource(null)}
                    className="flex-1"
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? 'Processando...' : 'Baixar Agora'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Success Modal */}
        {submitted && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6 text-center">
              <div className="text-green-500 text-6xl mb-4">✅</div>
              <h3 className="text-xl font-bold mb-4">Download Iniciado!</h3>
              <p className="text-gray-600 mb-6">
                Seu download começou automaticamente. Você também receberá dicas exclusivas 
                por email nos próximos dias.
              </p>
              <Button onClick={() => setSubmitted(false)}>
                Fechar
              </Button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}