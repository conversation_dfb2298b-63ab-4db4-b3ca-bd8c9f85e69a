import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Play, Download, MessageCircle, Zap } from 'lucide-react';

interface BlogCTAProps {
  title: string;
  description: string;
  buttonText?: string;
  variant?: 'trial' | 'demo' | 'contact' | 'download' | 'video';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function BlogCTA({
  title,
  description,
  buttonText,
  variant = 'trial',
  size = 'md',
  className = ''
}: BlogCTAProps) {
  const variants = {
    trial: {
      gradient: 'from-blue-600 to-indigo-600',
      icon: Zap,
      defaultText: 'Teste Grátis por 14 Dias',
      href: '/trial',
      trackingLabel: 'trial_cta'
    },
    demo: {
      gradient: 'from-green-600 to-emerald-600',
      icon: Play,
      defaultText: 'Agendar Demonstração',
      href: '/demo',
      trackingLabel: 'demo_cta'
    },
    contact: {
      gradient: 'from-purple-600 to-pink-600',
      icon: MessageCircle,
      defaultText: 'Falar com Especialista',
      href: '/contato',
      trackingLabel: 'contact_cta'
    },
    download: {
      gradient: 'from-orange-600 to-red-600',
      icon: Download,
      defaultText: 'Baixar Recurso Grátis',
      href: '#lead-magnet',
      trackingLabel: 'download_cta'
    },
    video: {
      gradient: 'from-teal-600 to-cyan-600',
      icon: Play,
      defaultText: 'Assistir Vídeo',
      href: '/videos',
      trackingLabel: 'video_cta'
    }
  };

  const config = variants[variant];
  const Icon = config.icon;
  const text = buttonText || config.defaultText;

  const sizes = {
    sm: {
      padding: 'p-6',
      titleSize: 'text-xl',
      descSize: 'text-sm',
      buttonSize: 'default' as const
    },
    md: {
      padding: 'p-8',
      titleSize: 'text-2xl',
      descSize: 'text-base',
      buttonSize: 'lg' as const
    },
    lg: {
      padding: 'p-12',
      titleSize: 'text-3xl',
      descSize: 'text-lg',
      buttonSize: 'lg' as const
    }
  };

  const sizeConfig = sizes[size];

  const handleClick = () => {
    // Track CTA click
    if (typeof gtag !== 'undefined') {
      gtag('event', 'cta_click', {
        event_category: 'conversion',
        event_label: config.trackingLabel,
        value: 1
      });
    }

    // Scroll to lead magnet if download variant
    if (variant === 'download') {
      const leadMagnet = document.querySelector('#lead-magnet');
      if (leadMagnet) {
        leadMagnet.scrollIntoView({ behavior: 'smooth' });
        return;
      }
    }

    // Navigate to href
    window.location.href = config.href;
  };

  return (
    <Card className={`my-8 overflow-hidden ${className}`}>
      <div className={`bg-gradient-to-r ${config.gradient} text-white ${sizeConfig.padding}`}>
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-white/20 p-3 rounded-full">
              <Icon className="h-8 w-8" />
            </div>
          </div>
          
          <h3 className={`${sizeConfig.titleSize} font-bold mb-4 leading-tight`}>
            {title}
          </h3>
          
          <p className={`${sizeConfig.descSize} text-white/90 mb-6 max-w-2xl mx-auto leading-relaxed`}>
            {description}
          </p>
          
          <Button
            size={sizeConfig.buttonSize}
            onClick={handleClick}
            className="bg-white text-gray-900 hover:bg-gray-100 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          >
            {text}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}

// CTAs pré-definidos para diferentes contextos
export const blogCTAs = {
  // Para posts sobre gestão financeira
  financeiro: {
    title: "Automatize o Financeiro do Seu Clube",
    description: "Controle mensalidades, fluxo de caixa e relatórios em uma plataforma completa. Reduza inadimplência e ganhe tempo para focar no que importa.",
    variant: 'trial' as const
  },

  // Para posts sobre departamento médico
  medico: {
    title: "Digitalize o Departamento Médico",
    description: "Prontuários eletrônicos, controle de exames e liberações médicas em um sistema seguro e profissional.",
    variant: 'demo' as const
  },

  // Para posts sobre escalação e treinos
  tecnico: {
    title: "Revolucione a Gestão Técnica",
    description: "Editor de escalação, controle de minutagem e planejamento de treinos. Tudo integrado para máxima eficiência.",
    variant: 'trial' as const
  },

  // Para posts sobre logística
  logistica: {
    title: "Simplifique a Logística dos Jogos",
    description: "Convocações automáticas, gestão de alojamentos e controle de materiais. Organize tudo em poucos cliques.",
    variant: 'demo' as const
  },

  // CTA genérico
  geral: {
    title: "Transforme a Gestão do Seu Clube",
    description: "Junte-se a centenas de clubes que já modernizaram sua gestão. Teste todas as funcionalidades gratuitamente.",
    variant: 'trial' as const
  },

  // Para meio do post
  meio: {
    title: "Quer Ver Isso na Prática?",
    description: "Agende uma demonstração personalizada e veja como nossa plataforma pode resolver os desafios do seu clube.",
    variant: 'demo' as const
  },

  // Para final do post
  final: {
    title: "Pronto para Começar?",
    description: "Implemente essas estratégias hoje mesmo com nossa plataforma. Teste grátis por 14 dias, sem compromisso.",
    variant: 'trial' as const
  }
};

// Hook para usar CTAs contextuais
export function useBlogCTAs(category?: string) {
  const getCTAByCategory = (position: 'top' | 'middle' | 'bottom') => {
    const categoryMap: Record<string, keyof typeof blogCTAs> = {
      'financeiro': 'financeiro',
      'medico': 'medico',
      'tecnico': 'tecnico',
      'logistica': 'logistica'
    };

    const positionMap = {
      top: 'geral',
      middle: 'meio',
      bottom: 'final'
    };

    const ctaKey = category && categoryMap[category.toLowerCase()] 
      ? categoryMap[category.toLowerCase()]
      : positionMap[position] as keyof typeof blogCTAs;

    return blogCTAs[ctaKey];
  };

  return { getCTAByCategory };
}