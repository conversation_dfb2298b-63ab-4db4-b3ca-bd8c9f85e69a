import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell, User, LogOut, Settings, ChevronDown } from 'lucide-react';
import { useMasterAuth } from '@/hooks/useMasterAuth';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MasterActivity } from '@/api/masterDashboard';

interface MasterHeaderProps {
  notifications: MasterActivity[];
}

export const MasterHeader: React.FC<MasterHeaderProps> = ({ notifications }) => {
  const { masterUser, organization, signOut } = useMasterAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/master/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-100 text-red-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'support':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'admin':
        return 'Administrador';
      case 'support':
        return 'Suporte';
      case 'viewer':
        return 'Visualizador';
      default:
        return role;
    }
  };

  const getNotificationType = (action: string) => {
    if (action.includes('suspend') || action.includes('overdue')) {
      return 'warning';
    }
    return 'info';
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo e título */}
          <div className="flex items-center">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">GDN</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Game Day Nexus</h1>
                <p className="text-sm text-gray-500">Painel Master</p>
              </div>
            </div>
          </div>

          {/* Ações do header */}
          <div className="flex items-center space-x-4">
            {/* Notificações */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="relative">
                  <Bell className="w-5 h-5" />
                  {notifications.length > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center text-xs"
                    >
                      {notifications.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Notificações</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <DropdownMenuItem key={notification.id} className="flex-col items-start p-3">
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm">{notification.description}</span>
                        <Badge 
                          variant={getNotificationType(notification.action) === 'warning' ? 'destructive' : 'secondary'}
                          className="text-xs"
                        >
                          {getNotificationType(notification.action) === 'warning' ? 'Atenção' : 'Info'}
                        </Badge>
                      </div>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem disabled>
                    Nenhuma notificação
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Informações da organização */}
            {organization && (
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium text-gray-900">{organization.name}</p>
                <p className="text-xs text-gray-500">{organization.email}</p>
              </div>
            )}

            {/* Menu do usuário */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2 px-3 py-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="hidden md:block text-left">
                    <p className="text-sm font-medium text-gray-900">{masterUser?.name}</p>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${getRoleBadgeColor(masterUser?.role || '')}`}>
                        {getRoleLabel(masterUser?.role || '')}
                      </Badge>
                    </div>
                  </div>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div>
                    <p className="font-medium">{masterUser?.name}</p>
                    <p className="text-sm text-gray-500">{masterUser?.email}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => navigate('/master/profile')}>
                  <User className="w-4 h-4 mr-2" />
                  Meu Perfil
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => navigate('/master/settings')}>
                  <Settings className="w-4 h-4 mr-2" />
                  Configurações
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem 
                  onClick={handleSignOut}
                  className="text-red-600 focus:text-red-600"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sair
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};