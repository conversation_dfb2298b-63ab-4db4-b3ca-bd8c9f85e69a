import { useMemo } from 'react';
import { TrainingDrill, TrainingElement } from '@/components/training/InteractiveTrainingBuilder';

export interface AnalysisMetrics {
  intensity: number;
  complexity: number;
  spatialCoverage: number;
  playerInteraction: number;
  tacticalFocus: {
    attacking: number;
    defending: number;
    transition: number;
    setpieces: number;
  };
  physicalDemand: {
    cardio: number;
    strength: number;
    agility: number;
    coordination: number;
  };
  skillDevelopment: {
    technical: number;
    tactical: number;
    physical: number;
    mental: number;
  };
}

// Utility functions for calculations
export const calculateSpatialCoverage = (positions: { x: number; y: number }[]): number => {
  if (positions.length < 2) return 0;
  
  const minX = Math.min(...positions.map(p => p.x));
  const maxX = Math.max(...positions.map(p => p.x));
  const minY = Math.min(...positions.map(p => p.y));
  const maxY = Math.max(...positions.map(p => p.y));
  
  const coverage = ((maxX - minX) * (maxY - minY)) / 10000; // Normalizado para 0-100
  return Math.min(100, coverage * 100);
};

export const calculatePlayerInteraction = (elements: TrainingElement[]): number => {
  const playerElements = elements.filter(e => e.type === 'player');
  if (playerElements.length < 2) return 0;
  
  let totalDistance = 0;
  let pairs = 0;
  
  for (let i = 0; i < playerElements.length; i++) {
    for (let j = i + 1; j < playerElements.length; j++) {
      const distance = Math.sqrt(
        Math.pow(playerElements[i].position.x - playerElements[j].position.x, 2) +
        Math.pow(playerElements[i].position.y - playerElements[j].position.y, 2)
      );
      totalDistance += distance;
      pairs++;
    }
  }
  
  const avgDistance = totalDistance / pairs;
  return Math.max(0, 100 - avgDistance); // Menor distância = maior interação
};

export const analyzeTacticalFocus = (drill: TrainingDrill) => {
  const category = drill.category;
  const objectives = drill.objectives.join(' ').toLowerCase();
  
  let attacking = 0, defending = 0, transition = 0, setpieces = 0;
  
  if (category === 'finishing' || objectives.includes('ataque') || objectives.includes('gol')) {
    attacking = 80;
  }
  if (category === 'tactical' || objectives.includes('defesa') || objectives.includes('marcação')) {
    defending = 70;
  }
  if (objectives.includes('transição') || objectives.includes('contra-ataque')) {
    transition = 90;
  }
  if (objectives.includes('escanteio') || objectives.includes('falta')) {
    setpieces = 85;
  }
  
  return { attacking, defending, transition, setpieces };
};

export const analyzePhysicalDemand = (drill: TrainingDrill) => {
  const duration = drill.totalDuration;
  const category = drill.category;
  
  let cardio = 0, strength = 0, agility = 0, coordination = 0;
  
  if (category === 'physical') {
    cardio = Math.min(100, duration / 10);
    strength = 70;
    agility = 60;
  } else if (category === 'technical') {
    coordination = 80;
    agility = 60;
    cardio = 40;
  } else if (category === 'tactical') {
    cardio = 50;
    coordination = 70;
    agility = 50;
  }
  
  return { cardio, strength, agility, coordination };
};

export const analyzeSkillDevelopment = (drill: TrainingDrill) => {
  const category = drill.category;
  
  const skillMap: Record<string, { technical: number; tactical: number; physical: number; mental: number }> = {
    technical: { technical: 90, tactical: 30, physical: 40, mental: 50 },
    tactical: { technical: 40, tactical: 90, physical: 50, mental: 80 },
    physical: { technical: 20, tactical: 30, physical: 90, mental: 40 },
    finishing: { technical: 80, tactical: 60, physical: 60, mental: 70 },
    transition: { technical: 60, tactical: 80, physical: 70, mental: 75 }
  };
  
  return skillMap[category] || { technical: 50, tactical: 50, physical: 50, mental: 50 };
};

// Main hook for analysis calculations
export const useAnalysisCalculations = (drill: TrainingDrill | null, elements: TrainingElement[]) => {
  const metrics = useMemo((): AnalysisMetrics | null => {
    if (!drill || !elements.length) return null;

    try {
      // Análise de intensidade baseada na duração e número de elementos
      const intensity = Math.min(100, (elements.length / drill.totalDuration) * 1000);
      
      // Complexidade baseada no número de passos e tipos de elementos
      const elementTypes = new Set(elements.map(e => e.type)).size;
      const complexity = Math.min(100, (drill.steps.length * elementTypes * 10));
      
      // Cobertura espacial baseada na distribuição dos elementos
      const positions = elements.map(e => ({ x: e.position.x, y: e.position.y }));
      const spatialCoverage = calculateSpatialCoverage(positions);
      
      // Interação entre jogadores baseada na proximidade dos elementos
      const playerInteraction = calculatePlayerInteraction(elements);
      
      // Foco tático baseado na categoria e objetivos
      const tacticalFocus = analyzeTacticalFocus(drill);
      
      // Demanda física baseada na duração e tipo de exercício
      const physicalDemand = analyzePhysicalDemand(drill);
      
      // Desenvolvimento de habilidades
      const skillDevelopment = analyzeSkillDevelopment(drill);

      return {
        intensity,
        complexity,
        spatialCoverage,
        playerInteraction,
        tacticalFocus,
        physicalDemand,
        skillDevelopment
      };
    } catch (error) {
      console.error('Error calculating analysis metrics:', error);
      return null;
    }
  }, [drill, elements]);

  return { metrics };
};