import React from 'react';
import { AlertCircle, RefreshCw, Settings, Database, Wifi, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ErrorMessageProps {
  error: string;
  onRetry?: () => void;
  retryCount?: number;
  isRetrying?: boolean;
  showDetails?: boolean;
}

// Error type detection and categorization
const getErrorCategory = (error: string) => {
  if (error.includes('não encontrada') || error.includes('PGRST202')) {
    return 'missing_function';
  }
  if (error.includes('Campos não encontrados') || error.includes('PGRST116')) {
    return 'missing_fields';
  }
  if (error.includes('network') || error.includes('fetch')) {
    return 'network';
  }
  if (error.includes('timeout') || error.includes('ETIMEDOUT')) {
    return 'timeout';
  }
  if (error.includes('permission') || error.includes('unauthorized')) {
    return 'permission';
  }
  return 'generic';
};

const getErrorInfo = (category: string) => {
  switch (category) {
    case 'missing_function':
      return {
        title: 'Função do Banco de Dados Não Encontrada',
        description: 'Uma função necessária não foi encontrada no banco de dados.',
        icon: Database,
        color: 'red',
        actionable: true,
        suggestions: [
          'Verifique se as migrações do banco foram aplicadas',
          'Entre em contato com o administrador do sistema',
          'Tente novamente em alguns minutos'
        ]
      };
    
    case 'missing_fields':
      return {
        title: 'Campos do Banco de Dados Ausentes',
        description: 'Alguns campos necessários não foram encontrados nas tabelas.',
        icon: Database,
        color: 'red',
        actionable: true,
        suggestions: [
          'Verifique se as migrações do banco foram aplicadas',
          'Entre em contato com o administrador do sistema',
          'Aguarde a correção do problema'
        ]
      };
    
    case 'network':
      return {
        title: 'Erro de Conexão',
        description: 'Não foi possível conectar com o servidor.',
        icon: Wifi,
        color: 'orange',
        actionable: true,
        suggestions: [
          'Verifique sua conexão com a internet',
          'Tente atualizar a página',
          'Aguarde alguns minutos e tente novamente'
        ]
      };
    
    case 'timeout':
      return {
        title: 'Tempo Limite Excedido',
        description: 'A operação demorou mais que o esperado.',
        icon: RefreshCw,
        color: 'yellow',
        actionable: true,
        suggestions: [
          'Tente novamente - pode ter sido um problema temporário',
          'Verifique se há muitos usuários no sistema',
          'Entre em contato com o suporte se o problema persistir'
        ]
      };
    
    case 'permission':
      return {
        title: 'Acesso Negado',
        description: 'Você não tem permissão para realizar esta operação.',
        icon: Settings,
        color: 'red',
        actionable: false,
        suggestions: [
          'Entre em contato com o administrador do sistema',
          'Verifique se você está logado com a conta correta',
          'Solicite as permissões necessárias'
        ]
      };
    
    default:
      return {
        title: 'Erro Inesperado',
        description: 'Ocorreu um erro inesperado no sistema.',
        icon: AlertCircle,
        color: 'red',
        actionable: true,
        suggestions: [
          'Tente atualizar a página',
          'Aguarde alguns minutos e tente novamente',
          'Entre em contato com o suporte se o problema persistir'
        ]
      };
  }
};

export const MasterErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  onRetry,
  retryCount = 0,
  isRetrying = false,
  showDetails = false
}) => {
  const category = getErrorCategory(error);
  const errorInfo = getErrorInfo(category);
  const IconComponent = errorInfo.icon;

  const colorClasses = {
    red: 'border-red-200 bg-red-50 text-red-800',
    orange: 'border-orange-200 bg-orange-50 text-orange-800',
    yellow: 'border-yellow-200 bg-yellow-50 text-yellow-800'
  };

  const iconColorClasses = {
    red: 'text-red-500',
    orange: 'text-orange-500',
    yellow: 'text-yellow-500'
  };

  return (
    <Card className={colorClasses[errorInfo.color as keyof typeof colorClasses]}>
      <CardContent className="p-6">
        <div className="flex items-start space-x-3">
          <IconComponent className={`w-6 h-6 mt-0.5 ${iconColorClasses[errorInfo.color as keyof typeof iconColorClasses]}`} />
          
          <div className="flex-1 space-y-3">
            <div>
              <h3 className="font-medium">{errorInfo.title}</h3>
              <p className="text-sm mt-1">{errorInfo.description}</p>
            </div>

            {/* Suggestions */}
            <div>
              <h4 className="text-sm font-medium mb-2">O que você pode fazer:</h4>
              <ul className="text-sm space-y-1">
                {errorInfo.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 mr-2 flex-shrink-0" />
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>

            {/* Error details */}
            {showDetails && (
              <details className="text-xs bg-white bg-opacity-50 p-3 rounded border">
                <summary className="cursor-pointer font-medium mb-2">
                  Detalhes técnicos
                </summary>
                <div className="mt-2 font-mono break-all">
                  {error}
                </div>
              </details>
            )}

            {/* Retry info */}
            {retryCount > 0 && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  Tentativas: {retryCount}
                </Badge>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col space-y-2">
            {errorInfo.actionable && onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                disabled={isRetrying}
                className="bg-white"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Tentando...' : 'Tentar Novamente'}
              </Button>
            )}
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="bg-white"
            >
              <HelpCircle className="w-4 h-4 mr-2" />
              Suporte
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Simplified error message for inline use
export const InlineErrorMessage: React.FC<{
  message: string;
  onRetry?: () => void;
  isRetrying?: boolean;
}> = ({ message, onRetry, isRetrying = false }) => {
  return (
    <div className="flex items-center space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
      <AlertCircle className="w-5 h-5 text-red-500" />
      <div className="flex-1">
        <p className="text-sm text-red-800">{message}</p>
      </div>
      {onRetry && (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onRetry}
          disabled={isRetrying}
          className="bg-white"
        >
          <RefreshCw className={`w-4 h-4 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
          Tentar Novamente
        </Button>
      )}
    </div>
  );
};

// Network status indicator
export const NetworkStatusIndicator: React.FC<{
  isOnline: boolean;
  lastSync?: Date;
}> = ({ isOnline, lastSync }) => {
  return (
    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
      isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }`}>
      <div className={`w-2 h-2 rounded-full ${
        isOnline ? 'bg-green-500' : 'bg-red-500'
      }`} />
      <span>
        {isOnline ? 'Online' : 'Offline'}
        {lastSync && isOnline && (
          <span className="ml-1 text-gray-600">
            • Última sincronização: {lastSync.toLocaleTimeString('pt-BR')}
          </span>
        )}
      </span>
    </div>
  );
};