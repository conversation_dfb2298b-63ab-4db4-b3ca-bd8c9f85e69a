import React, { createContext, useContext, ReactNode, useEffect } from "react";
import { validateUserClubAccess } from "@/api/clubAccess";
import { supabase } from "@/integrations/supabase/client";

// O tipo do contexto: clubId pode ser number ou undefined
export const ClubContext = createContext<number | undefined>(undefined);

export function useCurrentClubId(): number {
  const ctx = useContext(ClubContext);
  if (ctx === undefined) {
    throw new Error("ClubContext not found. Certifique-se de envolver sua aplicação com <ClubContext.Provider />");
  }
  return ctx;
}

interface ClubProviderProps {
  clubId: number;
  children: ReactNode;
}

export function ClubProvider({ clubId, children }: ClubProviderProps) {
  // Validação contínua de acesso ao clube
  useEffect(() => {
    let isMounted = true;
    
    async function validateAccess() {
      try {
        // Verificar se há sessão ativa antes de validar
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          return; // Não validar se não há sessão
        }

        const hasAccess = await validateUserClubAccess(clubId);
        
        if (!hasAccess && isMounted) {
          console.error(`VIOLAÇÃO DE SEGURANÇA: Acesso não autorizado ao clube ${clubId} detectado no contexto`);
          // Limpar dados e redirecionar
          localStorage.clear();
          window.location.href = '/login';
        }
      } catch (error) {
        console.error("Erro na validação contínua de acesso:", error);
      }
    }

    // Aguardar 3 segundos antes da primeira validação (para permitir login)
    const initialTimeout = setTimeout(validateAccess, 3000);
    
    // Validar periodicamente (a cada 5 minutos)
    const interval = setInterval(validateAccess, 5 * 60 * 1000);
    
    return () => {
      isMounted = false;
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [clubId]);

  return <ClubContext.Provider value={clubId}>{children}</ClubContext.Provider>;
}
