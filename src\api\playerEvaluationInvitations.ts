import { supabase } from "@/integrations/supabase/client";
import { getSupabaseClientWithClubId } from "@/integrations/supabase/clientWithClubId";
import { v4 as uuidv4 } from "uuid";
import { withPermission, withAuditLog } from "./middleware";
import { EVALUATION_PERMISSIONS } from "@/constants/permissions";
import { sendEmailWithBrevo } from "@/services/brevoEmailService";
import { deletePlayer } from "./players";
// Types
export type PlayerEvaluationInvitation = {
  id: number;
  club_id: number;
  club_name: string;
  token: string;
  email: string;
  cpf?: string;
  status: "pending" | "used" | "expired";
  created_at: string;
  expires_at: string;
  created_by: string;
  used_at?: string;
  player_id?: string;
  evaluation_date?: string;
  evaluation_location?: string;
  evaluation_requirements?: string;
  evaluation_notes?: string;

  // Document approval fields
  documents_status?: "pending" | "approved" | "rejected";
  documents_verified_at?: string;
  documents_verified_by?: string;
  documents_rejection_reason?: string;

  // Evaluation status field
  evaluation_status?: "em avaliacao" | "aprovado" | "disponivel";
  
  // Creator user info
  created_by_user?: {
    id: string;
    name: string;
  };
};

/**
 * Creates a new player evaluation invitation
 * @param clubId Club ID
 * @param email Email of the player/agent
 * @param cpf CPF of the player (optional)
 * @param userId User ID creating the invitation
 * @param expirationDays Number of days until the invitation expires (default: 5)
 * @returns Created invitation
 */
export async function createPlayerEvaluationInvitation(
  clubId: number,
  email: string,
  cpf: string | undefined,
  userId: string,
  expirationDays: number = 5
): Promise<PlayerEvaluationInvitation> {
  console.log(`[DEBUG] Iniciando createPlayerEvaluationInvitation - clubId: ${clubId}, email: ${email}, userId: ${userId}`);

  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.INVITATIONS.CREATE,
    async () => {
      console.log(`[DEBUG] Permissão verificada com sucesso para clubId: ${clubId}, userId: ${userId}`);

      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.create_invitation",
        { email, cpf },
        async () => {
          // Generate unique token
          const token = uuidv4();
          console.log(`[DEBUG] Token gerado: ${token}`);

          // Calculate expiration date
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + expirationDays);
          console.log(`[DEBUG] Data de expiração: ${expiresAt.toISOString()}`);

          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);
          console.log(`[DEBUG] Cliente Supabase criado com club_id: ${clubId}`);

          // Verificar se o usuário tem permissão para criar convites
          const { data: userPermissions, error: permissionsError } = await supabase
            .from("club_members")
            .select("permissions")
            .eq("club_id", clubId)
            .eq("user_id", userId)
            .single();

          console.log(`[DEBUG] Permissões do usuário:`, userPermissions?.permissions);

          if (permissionsError) {
            console.error(`[ERROR] Erro ao verificar permissões: ${permissionsError.message}`);
          }

          // Dados a serem inseridos
          const invitationData = {
            club_id: clubId,
            token,
            email,
            cpf,
            status: "pending",
            expires_at: expiresAt.toISOString(),
            created_by: userId
          };

          console.log(`[DEBUG] Dados do convite a serem inseridos:`, invitationData);

          try {
            // Create invitation
            const { data, error } = await supabaseWithClubId
              .from("player_evaluation_invitations")
              .insert(invitationData)
              .select()
              .single();

            if (error) {
              console.error(`[ERROR] Erro ao criar convite: ${error.message}`, error);
              throw new Error(`Error creating player evaluation invitation: ${error.message}`);
            }

            console.log(`[DEBUG] Convite criado com sucesso:`, data);
            return data as PlayerEvaluationInvitation;
          } catch (error: any) {
            console.error(`[ERROR] Exceção ao criar convite:`, error);
            throw error;
          }
        }
      );
    }
  );
}

/**
 * Gets a player evaluation invitation by token
 * @param token Invitation token
 * @returns Invitation or null if not found
 */
export async function getPlayerEvaluationInvitation(
  token: string
): Promise<PlayerEvaluationInvitation | null> {
  if (!token) {
    return null;
  }

  // Não podemos usar o club_id aqui porque estamos buscando pelo token
  // e não sabemos o club_id ainda
  const { data, error } = await supabase
    .from("player_evaluation_invitations")
    .select("*, club_info(name)")
    .eq("token", token)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // No invitation found
      return null;
    }
    throw new Error(`Error fetching player evaluation invitation: ${error.message}`);
  }

  return data as PlayerEvaluationInvitation;
}

/**
 * Validates a player evaluation invitation
 * @param token Invitation token
 * @returns true if invitation is valid, false otherwise
 */
export async function validatePlayerEvaluationInvitation(
  token: string
): Promise<boolean> {
  const invitation = await getPlayerEvaluationInvitation(token);

  if (!invitation) {
    return false;
  }

  // Check if invitation is pending
  if (invitation.status !== "pending") {
    return false;
  }

  // Check if invitation has expired
  const expiresAt = new Date(invitation.expires_at);
  const now = new Date();

  if (expiresAt < now) {
    // Criar cliente Supabase com club_id nos headers
    const supabaseWithClubId = getSupabaseClientWithClubId(invitation.club_id);

    // Mark invitation as expired
    await supabaseWithClubId
      .from("player_evaluation_invitations")
      .update({ status: "expired" })
      .eq("id", invitation.id);

    return false;
  }

  return true;
}

/**
 * Marks a player evaluation invitation as used
 * @param token Invitation token
 * @param playerId Player ID
 * @returns Updated invitation
 */
export async function usePlayerEvaluationInvitation(
  token: string,
  playerId: string
): Promise<PlayerEvaluationInvitation | null> {
  const invitation = await getPlayerEvaluationInvitation(token);

  if (!invitation) {
    return null;
  }

  // Criar cliente Supabase com club_id nos headers
  const supabaseWithClubId = getSupabaseClientWithClubId(invitation.club_id);

  // Update invitation
  const { data, error } = await supabaseWithClubId
    .from("player_evaluation_invitations")
    .update({
      status: "used",
      used_at: new Date().toISOString(),
      player_id: playerId
    })
    .eq("id", invitation.id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error updating player evaluation invitation: ${error.message}`);
  }

  return data as PlayerEvaluationInvitation;
}

/**
 * Gets all player evaluation invitations for a club
 * @param clubId Club ID
 * @param startDate Optional start date filter (YYYY-MM-DD)
 * @param endDate Optional end date filter (YYYY-MM-DD)
 * @returns List of invitations
 */
export async function getPlayerEvaluationInvitations(
  clubId: number,
  startDate?: string,
  endDate?: string
): Promise<PlayerEvaluationInvitation[]> {
  // Criar cliente Supabase com club_id nos headers
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  let query = supabaseWithClubId
    .from("player_evaluation_invitations")
    .select("*")
    .eq("club_id", clubId);

  // Apply date filters if provided
  if (startDate) {
    query = query.gte("created_at", `${startDate}T00:00:00.000Z`);
  }
  if (endDate) {
    query = query.lte("created_at", `${endDate}T23:59:59.999Z`);
  }

  const { data, error } = await query.order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Error fetching player evaluation invitations: ${error.message}`);
  }
  const invitations = (data as PlayerEvaluationInvitation[]) || [];

  // Buscar nomes dos usuários que criaram os convites
  const userIds = Array.from(new Set(invitations.map((inv) => inv.created_by).filter(Boolean)));
  if (userIds.length > 0) {
    const { data: usersData, error: usersError } = await supabase
      .from("users")
      .select("id, name")
      .in("id", userIds);

    if (!usersError && usersData) {
      const userMap = new Map<string, string>();
      usersData.forEach((user) => {
        userMap.set(user.id, user.name);
      });

      invitations.forEach((inv) => {
        if (inv.created_by && userMap.has(inv.created_by)) {
          inv.created_by_user = {
            id: inv.created_by,
            name: userMap.get(inv.created_by) || "",
          };
        }
      });
    }
  }

  return invitations;
}

/**
 * Gets all players in evaluation for a club
 * @param clubId Club ID
 * @returns List of players in evaluation
 */
export async function getPlayersInEvaluation(
  clubId: number
): Promise<any[]> {
  // Criar cliente Supabase com club_id nos headers
  const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

  const { data, error } = await supabaseWithClubId
    .from("players")
    .select(`
      *,
      player_evaluation_invitations(
        *
      )
    `)
    .eq("club_id", clubId)
    .in("status", ["em avaliacao", "aguardando documentação", "jogador agendado"]);
  let playersData = data as any[] | null;

  if (!error && playersData) {
    // Remover jogadores cujo status de avaliacao esteja marcado como disponivel ou aprovado
    playersData = playersData.filter(player => {
      if (!player.player_evaluation_invitations || player.player_evaluation_invitations.length === 0) {
        return true;
      }
      return player.player_evaluation_invitations.some(
        (inv: any) => inv.evaluation_status !== "disponivel" && inv.evaluation_status !== "aprovado"
      );
    });

    // Coletar todos os IDs de usuários únicos que criaram convites
    const createdByUserIds = new Set<string>();

    for (const player of playersData) {
      if (player.player_evaluation_invitations && player.player_evaluation_invitations.length > 0) {
        for (const invitation of player.player_evaluation_invitations) {
          if (invitation.created_by) {
            createdByUserIds.add(invitation.created_by);
          }
        }
      }
    }

    // Se houver usuários, buscar seus nomes
    if (createdByUserIds.size > 0) {
      const userIds = Array.from(createdByUserIds);

      // Buscar informações dos usuários
      const { data: usersData, error: usersError } = await supabase
        .from("users")
        .select("id, name")
        .in("id", userIds);

      if (!usersError && usersData) {
        // Criar um mapa de ID para nome de usuário
        const userMap = new Map<string, string>();
        for (const user of usersData) {
          userMap.set(user.id, user.name);
        }

        // Adicionar os nomes dos usuários aos convites
        for (const player of playersData) {
          if (player.player_evaluation_invitations && player.player_evaluation_invitations.length > 0) {
            for (const invitation of player.player_evaluation_invitations) {
              if (invitation.created_by && userMap.has(invitation.created_by)) {
                invitation.created_by_user = {
                  id: invitation.created_by,
                  name: userMap.get(invitation.created_by) || ""
                };
              }
            }
          }
        }
      }
    }
  }

  if (error) {
    throw new Error(`Error fetching players in evaluation: ${error.message}`);
  }

  return playersData || [];
}

/**
 * Schedules an evaluation for a player
 * @param clubId Club ID
 * @param playerId Player ID
 * @param date Evaluation date
 * @param location Evaluation location
 * @param requirements Evaluation requirements
 * @param userId User ID scheduling the evaluation
 * @returns Updated invitation
 */
export async function schedulePlayerEvaluation(
  clubId: number,
  playerId: string,
  date: string,
  location: string,
  requirements: string,
  userId: string
): Promise<PlayerEvaluationInvitation | null> {
  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.PLAYERS.SCHEDULE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.schedule",
        { player_id: playerId },
        async () => {
          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

          // Get invitation for player
          const { data: invitation, error: invitationError } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .select("*")
            .eq("club_id", clubId)
            .eq("player_id", playerId)
            .single();

          if (invitationError) {
            throw new Error(`Error fetching player evaluation invitation: ${invitationError.message}`);
          }

          // Update invitation with evaluation details
          const { data, error } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .update({
              evaluation_date: date,
              evaluation_location: location,
              evaluation_requirements: requirements
            })
            .eq("id", invitation.id)
            .select()
            .single();

          if (error) {
            throw new Error(`Error scheduling player evaluation: ${error.message}`);
          }

          // Get player details for email
          const { data: player, error: playerError } = await supabase
            .from("players")
            .select("name, email")
            .eq("id", playerId)
            .single();

          if (playerError) {
            throw new Error(`Error fetching player details: ${playerError.message}`);
          }

          // Update player status to indicate the evaluation has been scheduled
          await supabaseWithClubId
            .from("players")
            .update({ status: "jogador agendado" })
            .eq("id", playerId)
            .eq("club_id", clubId);

          // Send email notification
          await sendEvaluationScheduleEmail(
            player.email || invitation.email,
            player.name,
            date,
            location,
            requirements
          );

          return data as PlayerEvaluationInvitation;
        }
      );
    }
  );
}

/**
 * Updates a player's status after evaluation
 * @param clubId Club ID
 * @param playerId Player ID
 * @param status New status
 * @param notes Evaluation notes
 * @param userId User ID updating the status
 * @returns Updated player
 */
export async function updatePlayerEvaluationStatus(
  clubId: number,
  playerId: string,
  status: string,
  notes: string | undefined,
  userId: string
): Promise<any> {
  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.PLAYERS.UPDATE_STATUS,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.status_update",
        { player_id: playerId, status },
        async () => {
          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

          // Update player status
          const { data: player, error: playerError } = await supabaseWithClubId
            .from("players")
            .update({
              status,
              observation: notes
            })
            .eq("club_id", clubId)
            .eq("id", playerId)
            .select()
            .single();

          if (playerError) {
            throw new Error(`Error updating player status: ${playerError.message}`);
          }

          // Se o status foi alterado para 'inativo', executar limpeza de vinculações
          if (status === 'inativo') {
            try {
              console.log(`Jogador ${playerId} alterado para status 'inativo' via avaliação. Executando limpeza de vinculações...`);

              // Chamar a função SQL para remover vinculações
              const { error: cleanupError } = await supabaseWithClubId
                .rpc('remove_player_associations', {
                  p_club_id: clubId,
                  p_player_id: playerId
                });

              if (cleanupError) {
                console.error("Erro ao executar limpeza de vinculações:", cleanupError);
                // Não lançamos erro aqui para não interromper o fluxo principal
              } else {
                console.log(`Vinculações removidas com sucesso para o jogador ${playerId}`);
              }
            } catch (cleanupError) {
              console.error("Erro ao executar limpeza de vinculações:", cleanupError);
            }
          }

          // Update invitation notes
          if (notes) {
            await supabaseWithClubId
              .from("player_evaluation_invitations")
              .update({
                evaluation_notes: notes
              })
              .eq("club_id", clubId)
              .eq("player_id", playerId);
          }

          return player;
        }
      );
    }
  );
}

/**
 * Sends an evaluation invitation email
 * @param email Recipient email
 * @param token Invitation token
 * @param clubName Club name
 * @returns true if email was sent successfully
 */
export async function sendEvaluationInvitationEmail(
  email: string,
  token: string,
  clubName: string
): Promise<boolean> {
  // Use environment variable for site URL
  const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
  const registrationUrl = `${SITE_URL}/evaluation-registration?token=${token}`;

  const emailBody = `
    <h2>Olá!</h2>
    <p>Você foi convidado para participar do processo de pré cadastro do clube <strong>${clubName}</strong>.</p>
    <p>Para se cadastrar e iniciar o processo de pré cadastro, clique no link abaixo:</p>
    <p><a href="${registrationUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Cadastrar para pré cadastro</a></p>
    <p>Ou copie e cole o seguinte link no seu navegador:</p>
    <p>${registrationUrl}</p>
    <p>Este link é válido por 5 dias.</p>
    <p>Atenciosamente,<br>Equipe ${clubName}</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Convite para pré cadastro - ${clubName}`,
    body: emailBody,
  });
}

/**
 * Sends an evaluation schedule email
 * @param email Recipient email
 * @param playerName Player name
 * @param date Evaluation date
 * @param location Evaluation location
 * @param requirements Evaluation requirements
 * @returns true if email was sent successfully
 */
export async function sendEvaluationScheduleEmail(
  email: string,
  playerName: string,
  date: string,
  location: string,
  requirements: string
): Promise<boolean> {
  const formattedDate = new Date(date).toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const emailBody = `
    <h2>Olá ${playerName}!</h2>
    <p>Sua sessão de pré cadastro foi agendada com sucesso.</p>
    <p><strong>Data e Hora:</strong> ${formattedDate}</p>
    <p><strong>Local:</strong> ${location}</p>
    <p><strong>O que levar:</strong> ${requirements}</p>
    <p>Por favor, chegue com pelo menos 30 minutos de antecedência.</p>
    <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
  `;

  return sendEmailWithBrevo({
    to: email,
    subject: `Agendamento de Pré Cadastro - ${playerName}`,
    body: emailBody,
  });
}

/**
 * Approves or rejects player documents
 * @param clubId Club ID
 * @param invitationId Invitation ID
 * @param status Document status (approved or rejected)
 * @param rejectionReason Reason for rejection (required if status is rejected)
 * @param userId User ID approving/rejecting the documents
 * @returns Updated invitation
 */
export async function updateDocumentsStatus(
  clubId: number,
  invitationId: number,
  status: "approved" | "rejected",
  rejectionReason: string | undefined,
  userId: string
): Promise<PlayerEvaluationInvitation> {
  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.PLAYERS.VERIFY_DOCUMENTS,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.documents_update",
        { invitation_id: invitationId, status },
        async () => {
          // Validate input
          if (status === "rejected" && !rejectionReason) {
            throw new Error("Rejection reason is required when rejecting documents");
          }

          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

          // Update document status
          const updateData: any = {
            documents_status: status,
            documents_verified_at: new Date().toISOString(),
            documents_verified_by: userId
          };

          // If documents were approved, set evaluation status to 'aguardando agendamento'
          if (status === "approved") {
            updateData.evaluation_status = "aguardando agendamento";
          }
          if (status === "rejected") {
            updateData.documents_rejection_reason = rejectionReason;
          } else {
            updateData.documents_rejection_reason = null;
          }

          const { data, error } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .update(updateData)
            .eq("id", invitationId)
            .eq("club_id", clubId)
            .select()
            .single();

          if (error) {
            throw new Error(`Error updating document status: ${error.message}`);
          }

          // Get player details for email notification
          const { data: player, error: playerError } = await supabase
            .from("players")
            .select("name, email")
            .eq("id", data.player_id)
            .single();

          if (playerError) {
            console.error(`Error fetching player details: ${playerError.message}`);
          } else {
            // Send email notification
            await sendDocumentStatusEmail(
              player.email || data.email,
              player.name,
              status,
              rejectionReason
            );
          }

          return data as PlayerEvaluationInvitation;
        }
      );
    }
  );
}

/**
 * Updates the evaluation status of a player
 * @param clubId Club ID
 * @param invitationId Invitation ID
 * @param status New evaluation status
 * @param userId User ID updating the status
 * @returns Updated invitation
 */
export async function updateEvaluationStatus(
  clubId: number,
  invitationId: number,
  status: "em avaliacao" | "aguardando agendamento" | "aprovado" | "disponivel",
  userId: string
): Promise<PlayerEvaluationInvitation> {
  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.PLAYERS.UPDATE_STATUS,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.status_update",
        { invitation_id: invitationId, status },
        async () => {
          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

          // Get club name
          const { data: clubData, error: clubError } = await supabase
            .from("club_info")
            .select("name")
            .eq("id", clubId)
            .single();

          if (clubError) {
            throw new Error(`Error fetching club name: ${clubError.message}`);
          }
          const clubName = clubData.name;

          // Update evaluation status
          const { data, error } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .update({
              evaluation_status: status
            })
            .eq("id", invitationId)
            .eq("club_id", clubId)
            .select("*, players(*)")
            .single();

          if (error) {
            throw new Error(`Error updating evaluation status: ${error.message}`);
          }

          // Atualizar status do jogador no elenco se necessário
          if (data.player_id) {
            if (status === "aprovado") {
              await supabaseWithClubId
                .from("players")
                .update({ status: "disponivel" })
                .eq("id", data.player_id)
                .eq("club_id", clubId);
            } else if (status === "disponivel") {
              await supabaseWithClubId
                .from("players")
                .update({ status: "em avaliacao" })
                .eq("id", data.player_id)
                .eq("club_id", clubId);
            }
          }

          // Get player details for email notification
          const player = data.players;
          const playerEmail = player?.email || data.email;
          const playerName = player?.name || "Atleta";

          // Send email notification
          await sendEvaluationStatusEmail(
            playerEmail,
            playerName,
            status,
            clubName
          );

          return data as PlayerEvaluationInvitation;
        }
      );
    }
  );
}

/**
 * Sends an email notification about document status
 * @param email Recipient email
 * @param playerName Player name
 * @param status Document status
 * @param rejectionReason Reason for rejection (if status is rejected)
 * @returns true if email was sent successfully
 */
export async function sendDocumentStatusEmail(
  email: string,
  playerName: string,
  status: "approved" | "rejected",
  rejectionReason?: string
): Promise<boolean> {
  let subject = "";
  let body = "";

  if (status === "approved") {
    subject = `Documentos Aprovados - ${playerName}`;
    body = `
      <h2>Olá ${playerName}!</h2>
      <p>Seus documentos foram aprovados com sucesso.</p>
      <p>Em breve entraremos em contato para agendar seu pré cadastro.</p>
      <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
    `;
  } else {
    subject = `Documentos Precisam de Revisão - ${playerName}`;
    body = `
      <h2>Olá ${playerName}!</h2>
      <p>Seus documentos foram analisados, mas precisamos de algumas correções:</p>
      <p><strong>Motivo:</strong> ${rejectionReason}</p>
      <p>Para corrigir os documentos, por favor:</p>
      <ol>
        <li>Entre em contato com o clube pelo telefone ou email disponível no site</li>
        <li>Informe seu nome completo e o motivo da rejeição mencionado acima</li>
        <li>Envie os documentos corrigidos conforme solicitado</li>
      </ol>
      <p>Após o envio dos documentos corrigidos, faremos uma nova análise e entraremos em contato.</p>
      <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
    `;
  }

  return sendEmailWithBrevo({
    to: email,
    subject,
    body,
  });
}

/**
 * Sends an email notification about evaluation status change
 * @param email Recipient email
 * @param playerName Player name
 * @param status New evaluation status
 * @returns true if email was sent successfully
 */
/**
 * Deletes a player evaluation invitation
 * @param clubId Club ID
 * @param invitationId Invitation ID
 * @param userId User ID deleting the invitation
 * @returns Success status
 */
export async function deletePlayerEvaluationInvitation(
  clubId: number,
  invitationId: number,
  userId: string
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    EVALUATION_PERMISSIONS.INVITATIONS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_evaluation.delete_invitation",
        { invitation_id: invitationId },
        async () => {
          // Criar cliente Supabase com club_id nos headers
          const supabaseWithClubId = getSupabaseClientWithClubId(clubId);

          // Check if the invitation exists and belongs to this club
          const { data: invitation, error: checkError } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .select("id, status, player_id")
            .eq("id", invitationId)
            .eq("club_id", clubId)
            .single();

          if (checkError) {
            throw new Error(`Error checking invitation: ${checkError.message}`);
          }

          // If the invitation is already used and has a player attached,
          // also remove the player before deleting the invitation
          if (invitation.status === "used" && invitation.player_id) {
            await deletePlayer(clubId, invitation.player_id, userId);
          }

          // Delete the invitation itself
          const { error } = await supabaseWithClubId
            .from("player_evaluation_invitations")
            .delete()
            .eq("id", invitationId)
            .eq("club_id", clubId);

          if (error) {
            throw new Error(`Error deleting invitation: ${error.message}`);
          }

          return true;
        }
      );
    }
  );
}

export async function sendEvaluationStatusEmail(
  email: string,
  playerName: string,
  status: "em avaliacao" | "aguardando agendamento" | "aprovado" | "disponivel",
  clubName: string
): Promise<boolean> {
  let subject = "";
  let body = "";

  switch (status) {
    case "aguardando agendamento":
      subject = `Documentos Aprovados - ${playerName}`;
      body = `
        <h2>Prezado atleta,</h2>
        <p>Sua documentação foi analisada com sucesso.</p>
        <p>Em breve você receberá outro e-mail com a data da apresentação.</p>
        <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
      `;
      break;
    case "aprovado":
      subject = `Pré Cadastro Aprovado - ${playerName}`;
      body = `
        <h2>Parabéns ${playerName}!</h2>
        <p>Temos o prazer de informar que você foi aprovado em nosso pré cadastro.</p>
        <p>Em breve entraremos em contato com mais informações sobre os próximos passos.</p>
        <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
      `;
      break;
    case "disponivel":
      subject = `Bem-vindo ao Elenco - ${playerName}`;
      body = `
        <h2>Parabéns ${playerName}!</h2>
        <p>Informamos que você agora passará pelo período de avaliação..</p>
        <p>Solicitamos que procure o Departamento de Futebol para agendar as próximas etapas e dar início aos treinos.</p>
        <p>Atenciosamente,<br>Departamento de Futebol - ${clubName}</p>
      `;
      break;
    default:
      subject = `Atualização de Status - ${playerName}`;
      body = `
        <h2>Olá ${playerName}!</h2>
        <p>Seu status de pré cadastro foi atualizado.</p>
        <p>Entre em contato com a administração do clube para mais informações.</p>
        <p>Atenciosamente,<br>Equipe de Pré Cadastro</p>
      `;
  }

  return sendEmailWithBrevo({
    to: email,
    subject,
    body,
  });
}

