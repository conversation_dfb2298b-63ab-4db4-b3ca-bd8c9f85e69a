-- Create table for operation staff roles
CREATE TABLE IF NOT EXISTS public.game_operation_roles (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_game_operation_roles_club ON public.game_operation_roles(club_id);

ALTER TABLE public.game_operation_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY game_operation_roles_select ON public.game_operation_roles
  FOR SELECT
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_roles_insert ON public.game_operation_roles
  FOR INSERT
  WITH CHECK (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_roles_update ON public.game_operation_roles
  FOR UPDATE
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

CREATE POLICY game_operation_roles_delete ON public.game_operation_roles
  FOR DELETE
  USING (club_id IN (SELECT club_id FROM public.club_members WHERE user_id = auth.uid()));

-- Insert default roles for existing clubs
INSERT INTO public.game_operation_roles (club_id, name)
SELECT id, role
FROM public.club_info,
LATERAL unnest(ARRAY[
  'Gandula',
  'Marqueiro',
  'Bilheteiro',
  'Orientador do Público',
  'Mascote',
  'Segurança Patrimonial',
  'Médico Ambulancia UTI',
  'Médico Ambulancia Remoção',
  'Médico',
  'Zelador',
  'Enfermeira',
  'Enfermeiro',
  'Motorista'
]) AS role
ON CONFLICT DO NOTHING;
