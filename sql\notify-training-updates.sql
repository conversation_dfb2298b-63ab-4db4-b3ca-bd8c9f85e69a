
-- Arquivo: sql/notify-training-updates.sql
-- Descrição: Cria uma função e um gatilho no PostgreSQL para notificar
-- administradores e presidentes sobre alterações na tabela de treinamentos.

-- 1. Criação da Função de Notificação
-- Esta função será chamada pelo gatilho e é responsável por criar as notificações.
CREATE OR REPLACE FUNCTION public.notify_training_changes()
RETURNS TRIGGER AS $$
DECLARE
    user_name TEXT;
    operation_type TEXT;
    notification_title TEXT;
    notification_message TEXT;
    target_club_id INT;
    record_id TEXT;
BEGIN
    -- Define o club_id e o ID do registro com base na operação (INSERT, UPDATE, DELETE)
    IF (TG_OP = 'DELETE') THEN
        target_club_id := OLD.club_id;
        record_id := OLD.id::TEXT;
    ELSE
        target_club_id := NEW.club_id;
        record_id := NEW.id::TEXT;
    END IF;

    -- Busca o nome do usuário que realizou a ação na tabela de usuários,
    -- juntando com a tabela de colaboradores para garantir a associação com o clube.
    -- Se não encontrar, usa o email do usuário como fallback.
    SELECT u.name INTO user_name
    FROM public.users u
    JOIN public.collaborators c ON u.id = c.user_id
    WHERE c.user_id = auth.uid() AND c.club_id = target_club_id;

    IF user_name IS NULL THEN
        SELECT email INTO user_name FROM auth.users WHERE id = auth.uid();
    END IF;

    -- Define o texto da operação para a mensagem de notificação
    IF (TG_OP = 'INSERT') THEN
        operation_type := 'criou um novo treinamento';
    ELSIF (TG_OP = 'UPDATE') THEN
        operation_type := 'atualizou um treinamento';
    ELSIF (TG_OP = 'DELETE') THEN
        operation_type := 'excluiu um treinamento';
    END IF;

    -- Constrói o título e a mensagem da notificação
    notification_title := 'Atualização no Módulo de Treinamento';
    notification_message := 'O usuário ' || COALESCE(user_name, 'desconhecido') || ' ' || operation_type || '.';

    -- Insere as notificações para todos os usuários com as roles 'admin' ou 'president'
    -- do clube correspondente.
    INSERT INTO public.notifications (club_id, user_id, title, message, type, reference_id, reference_type)
    SELECT
        target_club_id,
        cm.user_id,
        notification_title,
        notification_message,
        'system',       -- Tipo da notificação
        record_id,      -- ID do treinamento como referência
        'training'      -- Tipo da referência
    FROM public.club_members cm
    WHERE cm.club_id = target_club_id
      AND cm.role IN ('admin', 'president');

    -- O valor de retorno é ignorado para gatilhos AFTER
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Criação do Gatilho (Trigger)
-- Este gatilho aciona a função notify_training_changes() após qualquer
-- inserção, atualização ou exclusão na tabela 'trainings'.
DROP TRIGGER IF EXISTS trg_notify_training_changes ON public.trainings;
CREATE TRIGGER trg_notify_training_changes
AFTER INSERT OR UPDATE OR DELETE ON public.trainings
FOR EACH ROW EXECUTE FUNCTION public.notify_training_changes();

-- 3. Comentários para Documentação
COMMENT ON FUNCTION public.notify_training_changes() IS 'Envia uma notificação para admins e presidentes quando um treinamento é criado, atualizado ou excluído.';
COMMENT ON TRIGGER trg_notify_training_changes ON public.trainings IS 'Executa a função notify_training_changes() após uma alteração em um registro de treinamento.';

