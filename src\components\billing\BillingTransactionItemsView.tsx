import { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package } from 'lucide-react';
import { getBillingTransactionItems, BillingTransactionItem } from '@/api/billing';

interface BillingTransactionItemsViewProps {
  clubId: number;
  transactionId: number;
}

export function BillingTransactionItemsView({ clubId, transactionId }: BillingTransactionItemsViewProps) {
  const [items, setItems] = useState<BillingTransactionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clubId && transactionId) {
      loadItems();
    }
  }, [clubId, transactionId]);

  const loadItems = async () => {
    if (!clubId || !transactionId) {
      return;
    }

    try {
      setLoading(true);
      const data = await getBillingTransactionItems(clubId, transactionId);
      setItems(data);
    } catch (error) {
      console.error('Erro ao carregar itens da transação:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getTotalValue = () => {
    return items.reduce((sum, item) => sum + (item.total_price || 0), 0);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Itens do Estoque
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Itens do Estoque
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum item do estoque vinculado</p>
            <p className="text-sm">Esta transação não possui itens do estoque</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="w-5 h-5" />
          Itens do Estoque ({items.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Produto</TableHead>
                <TableHead>Departamento</TableHead>
                <TableHead className="text-center">Quantidade</TableHead>
                <TableHead className="text-right">Preço Unit.</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    {item.product_name}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {item.product_department}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    {item.quantity}
                  </TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(item.unit_price || 0)}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(item.total_price || 0)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex justify-end">
          <div className="text-right">
            <p className="text-sm text-muted-foreground">Total dos Itens</p>
            <p className="text-lg font-semibold">{formatCurrency(getTotalValue())}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}