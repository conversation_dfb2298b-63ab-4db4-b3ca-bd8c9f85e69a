import { useEffect, useRef, useState } from 'react';
import funcionalidades from '../../../funcionalidades-completas-sistema.md?raw';
import { mockLLMResponse } from '@/utils/mockLLM';
import ReactMarkdown from 'react-markdown';

// Estilos CSS para animações
const chatStyles = `
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes slide-up {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }
  
  .chat-message {
    animation: slide-up 0.3s ease-out;
  }
`;

// Adiciona os estilos ao documento
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = chatStyles;
  document.head.appendChild(styleSheet);
}

interface Message {
  role: 'user' | 'assistant';
  text: string;
}

// Componente para efeito de digitação
function TypingEffect({ text, onComplete }: { text: string; onComplete?: () => void }) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, 20); // Velocidade de digitação
      return () => clearTimeout(timer);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, onComplete]);

  return (
    <ReactMarkdown
      className="text-sm leading-relaxed prose prose-sm max-w-none prose-headings:text-gray-800 prose-headings:font-bold prose-h2:text-base prose-h3:text-sm prose-p:text-gray-700 prose-strong:text-gray-800 prose-ul:text-gray-700 prose-li:text-gray-700"
      components={{
        h2: ({ children }) => <h2 className="text-base font-bold text-gray-800 mt-3 mb-2 flex items-center gap-2">{children}</h2>,
        h3: ({ children }) => <h3 className="text-sm font-semibold text-gray-800 mt-2 mb-1">{children}</h3>,
        ul: ({ children }) => <ul className="list-disc list-inside space-y-1 mt-2">{children}</ul>,
        li: ({ children }) => <li className="text-sm text-gray-700">{children}</li>,
        strong: ({ children }) => <strong className="font-semibold text-gray-800">{children}</strong>,
        p: ({ children }) => <p className="text-sm text-gray-700 mb-2">{children}</p>,
        code: ({ children }) => <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
      }}
    >
      {displayedText}
    </ReactMarkdown>
  );
}

const suggestions = [
  '💰 Planos e investimento',
  '🏥 Me fale do módulo médico',
  '⚽ Como funciona a gestão de atletas?',
  '💳 Sistema de mensalidades automático',
  '📊 Que relatórios vocês geram?',
  '🚀 Demonstração personalizada'
];

const CTA_REGEX = /(preç|plano|contratar|comercial|demo|demonstra|assinar|custo|valor|investimento)/i;
const LEAD_QUALIFICATION_REGEX = /(quantos atletas|tamanho do clube|categoria|divisão|profissional|amador)/i;

const docChunks = funcionalidades.split(/\n\n+/);

// Sistema de busca melhorado com pesos e sinônimos
const synonyms: Record<string, string[]> = {
  'atletas': ['jogadores', 'players', 'futebolistas', 'atleta'],
  'financeiro': ['dinheiro', 'pagamento', 'cobrança', 'mensalidade', 'receita'],
  'médico': ['saúde', 'lesão', 'fisioterapia', 'consulta', 'medicina'],
  'treino': ['treinamento', 'exercício', 'preparação', 'tática'],
  'partida': ['jogo', 'match', 'competição', 'campeonato'],
  'escalação': ['formação', 'time', 'lineup', 'equipe'],
  'relatório': ['report', 'análise', 'estatística', 'dados']
};

function expandQuery(query: string): string[] {
  const terms = query.toLowerCase().split(/\s+/);
  const expandedTerms = [...terms];

  terms.forEach(term => {
    Object.entries(synonyms).forEach(([key, values]) => {
      if (values.includes(term) || key.includes(term)) {
        expandedTerms.push(key, ...values);
      }
    });
  });

  return [...new Set(expandedTerms)];
}

function retrieve(query: string): string[] {
  const expandedTerms = expandQuery(query);

  return docChunks
    .map((chunk) => {
      const chunkLower = chunk.toLowerCase();
      let score = 0;

      // Pontuação por termos encontrados
      expandedTerms.forEach(term => {
        const occurrences = (chunkLower.match(new RegExp(term, 'g')) || []).length;
        score += occurrences * (term.length > 3 ? 2 : 1); // Termos maiores têm peso maior
      });

      // Bonus para títulos e seções importantes
      if (chunk.includes('##') || chunk.includes('**Descrição**')) {
        score *= 1.5;
      }

      return { chunk, score };
    })
    .filter((c) => c.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, 4) // Aumentei para 4 chunks
    .map((c) => c.chunk);
}

export function Chatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [typing, setTyping] = useState(false);
  const [streaming, setStreaming] = useState(false);
  const [streamingText, setStreamingText] = useState('');
  const [showCTA, setShowCTA] = useState(false);
  const listRef = useRef<HTMLDivElement>(null);

  const whatsappUrl =
    'https://wa.me/5519987111198?text=Olá! Quero saber mais sobre o Game Day Nexus.';

  const pushEvent = (event: string, data?: Record<string, unknown>) => {
    (
      window as Window & {
        dataLayer?: Array<Record<string, unknown>>;
      }
    ).dataLayer?.push({ event, ...data });
  };

  useEffect(() => {
    if (isOpen) {
      pushEvent('chat_open');

      // Mensagem de boas-vindas automática (sem streaming)
      if (messages.length === 0) {
        setTimeout(() => {
          setMessages([{
            role: 'assistant',
            text: `## ⚽ Olá! Sou o Assistente Especialista do Game Day Nexus!

Estou aqui para te mostrar como nosso **sistema ERP completo** pode revolucionar a gestão do seu clube:

### 🚀 **Benefícios Imediatos:**
- **Economize até R$ 50.000/ano** em custos operacionais
- **Elimine 90% da papelada** e burocracias
- **200+ funcionalidades** integradas em 15 módulos
- **ROI comprovado** de 400% no primeiro ano

### 💡 **Como posso te ajudar hoje?**
- Explicar funcionalidades específicas
- Mostrar como economizar tempo e dinheiro
- Demonstrar integrações entre módulos
- Calcular ROI para seu clube

**Digite sua pergunta ou escolha uma sugestão abaixo!** 👇`
          }]);
        }, 800);
      }
    }
  }, [isOpen, messages.length]);

  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
  }, [messages, typing, streaming, streamingText]);

  const send = async (text?: string) => {
    const userText = (text ?? input).trim();
    if (!userText) return;
    const history = [...messages, { role: 'user', text: userText }];
    setMessages(history);
    setInput('');
    pushEvent('message_user', { text: userText });

    const contextChunks = retrieve(userText);

    // Sistema de qualificação de leads
    const isCommercialIntent = CTA_REGEX.test(userText);
    const isQualificationOpportunity = LEAD_QUALIFICATION_REGEX.test(userText);

    // Adiciona contexto de qualificação se necessário
    if (isQualificationOpportunity) {
      contextChunks.push(`
        CONTEXTO DE QUALIFICAÇÃO:
        - Para clubes pequenos (até 100 atletas): Foque em economia de tempo e organização
        - Para clubes médios (100-300 atletas): Destaque automação e relatórios profissionais  
        - Para clubes grandes (300+ atletas): Enfatize integração completa e ROI
        - Sempre pergunte sobre o tamanho do clube para personalizar a resposta
      `);
    }

    setTyping(true);
    try {
      let botText: string;

      try {
        // Tenta usar API real primeiro
        const apiUrl = '/api/llm';  // Sempre usa a API da Vercel

        const res = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            history,
            contextChunks,
            metadata: {
              isCommercialIntent,
              isQualificationOpportunity,
              messageCount: history.length
            }
          }),
        });

        if (!res.ok) {
          throw new Error(`API Error: ${res.status}`);
        }

        const data = await res.json();
        botText = data.text as string;
        pushEvent('message_llm', { text: botText });
      } catch (apiError) {
        console.log('API falhou, usando mock:', apiError);
        // Se a API falhar, usa o mock
        botText = await mockLLMResponse(userText, contextChunks);
        pushEvent('message_llm_mock', { text: botText });
      }

      // Inicia o streaming da resposta
      setTyping(false);
      setStreaming(true);
      setStreamingText(botText);

      // Mostra CTA em diferentes situações
      if (isCommercialIntent || history.length >= 3) {
        setShowCTA(true);
      }
    } catch (error) {
      console.error('Erro no chatbot:', error);
      setTyping(false);
      setStreaming(true);
      setStreamingText('🤖 Ops! Tive um problema técnico. Que tal conversarmos no WhatsApp? Nosso time está online agora! 📱');
      setShowCTA(true);
    }
  };

  // Função chamada quando o streaming termina
  const handleStreamingComplete = () => {
    setMessages((prev) => [...prev, { role: 'assistant', text: streamingText }]);
    setStreaming(false);
    setStreamingText('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      send();
    }
  };

  return (
    <div>
      {isOpen && (
        <div className="fixed bottom-20 right-4 z-[1000] w-80 sm:w-96 max-h-[85vh] flex flex-col">
          {/* Container principal com glassmorphism */}
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
            {/* Header com gradiente */}
            <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 px-6 py-4">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 via-purple-600/90 to-blue-700/90" />
              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <img src="/logo-branca.png" alt="Logo" className="h-8 w-8 rounded-lg" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse" />
                  </div>
                  <div>
                    <span className="font-bold text-white text-lg">Game Day Nexus</span>
                    <p className="text-blue-100 text-xs">Assistente Virtual • Online</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-white/80 hover:text-white hover:bg-white/10 rounded-full p-2 transition-all duration-200 hover:rotate-90"
                  aria-label="Fechar chat"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Área de mensagens */}
            <div
              ref={listRef}
              className="flex-1 p-6 space-y-4 overflow-y-auto max-h-[50vh] bg-gradient-to-b from-gray-50/50 to-white/50"
              aria-live="polite"
            >
              {messages.length === 0 && (
                <div className="text-center py-8 animate-fade-in">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-500 via-purple-600 to-blue-700 rounded-3xl flex items-center justify-center shadow-lg animate-bounce">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-800 mb-2 text-lg">Assistente Inteligente ⚽</h3>
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    Descubra como economizar <strong>R$ 50.000/ano</strong><br />
                    na gestão do seu clube de futebol!
                  </p>
                  <div className="flex items-center justify-center gap-2 text-xs text-green-600 bg-green-50 rounded-full px-3 py-1 mx-auto w-fit">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    Online agora
                  </div>
                </div>
              )}

              {messages.map((msg, idx) => (
                <div key={idx} className={`flex chat-message ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`flex items-end gap-2 max-w-[85%] ${msg.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                    {msg.role === 'assistant' && (
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                    )}
                    <div
                      className={`px-4 py-3 rounded-2xl shadow-sm ${msg.role === 'user'
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-br-md'
                        : 'bg-white border border-gray-200 text-gray-800 rounded-bl-md'
                        }`}
                    >
                      {msg.role === 'user' ? (
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.text}</p>
                      ) : (
                        <ReactMarkdown
                          className="text-sm leading-relaxed prose prose-sm max-w-none prose-headings:text-gray-800 prose-headings:font-bold prose-h2:text-base prose-h3:text-sm prose-p:text-gray-700 prose-strong:text-gray-800 prose-ul:text-gray-700 prose-li:text-gray-700"
                          components={{
                            h2: ({ children }) => <h2 className="text-base font-bold text-gray-800 mt-3 mb-2 flex items-center gap-2">{children}</h2>,
                            h3: ({ children }) => <h3 className="text-sm font-semibold text-gray-800 mt-2 mb-1">{children}</h3>,
                            ul: ({ children }) => <ul className="list-disc list-inside space-y-1 mt-2">{children}</ul>,
                            li: ({ children }) => <li className="text-sm text-gray-700">{children}</li>,
                            strong: ({ children }) => <strong className="font-semibold text-gray-800">{children}</strong>,
                            p: ({ children }) => <p className="text-sm text-gray-700 mb-2">{children}</p>,
                            code: ({ children }) => <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                          }}
                        >
                          {msg.text}
                        </ReactMarkdown>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Mensagem sendo digitada (streaming) */}
              {streaming && (
                <div className="flex justify-start">
                  <div className="flex items-end gap-2 max-w-[85%]">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
                      <TypingEffect text={streamingText} onComplete={handleStreamingComplete} />
                    </div>
                  </div>
                </div>
              )}

              {typing && (
                <div className="flex justify-start">
                  <div className="flex items-end gap-2">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:150ms]" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:300ms]" />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {showCTA && (
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-4 mx-2">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-green-800 text-sm">Quer falar com um especialista?</p>
                      <a
                        href={whatsappUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={() => pushEvent('cta_whatsapp_click')}
                        className="text-green-600 hover:text-green-700 text-sm font-medium underline"
                      >
                        Conversar no WhatsApp →
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {/* Sugestões rápidas - apenas após mensagem de boas-vindas */}
              {messages.length === 1 && !streaming && !typing && (
                <div className="px-2 pb-4">
                  <p className="text-xs text-gray-500 mb-3 font-medium">Sugestões rápidas:</p>
                  <div className="flex flex-wrap gap-2">
                    {suggestions.map((s) => (
                      <button
                        key={s}
                        onClick={() => send(s)}
                        className="px-3 py-2 text-xs font-medium rounded-full bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200 hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all duration-200 hover:scale-105"
                      >
                        {s}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>



            {/* Input de mensagem */}
            <div className="border-t border-gray-200 bg-white/80 backdrop-blur-sm">
              <div className="flex items-center gap-3 px-4 py-4">
                <div className="flex-1 relative">
                  <input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={streaming ? "Aguarde a resposta..." : "Digite sua pergunta..."}
                    disabled={streaming}
                    className="w-full bg-gray-50 border border-gray-200 rounded-2xl px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                </div>
                <button
                  onClick={() => send()}
                  disabled={!input.trim() || typing || streaming}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>

              {/* Footer com WhatsApp */}
              <div className="px-4 pb-4">
                <a
                  href={whatsappUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => pushEvent('cta_whatsapp_click')}
                  className="flex items-center justify-center gap-2 w-full py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-medium rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 hover:scale-105"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                  </svg>
                  Falar no WhatsApp
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Botão flutuante */}
      <button
        onClick={() => setIsOpen((o) => !o)}
        className="fixed bottom-6 right-6 z-[1000] w-16 h-16 rounded-2xl shadow-2xl flex items-center justify-center bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 border-2 border-white/20 transition-all duration-300 hover:scale-110 hover:shadow-3xl focus:outline-none focus:ring-4 focus:ring-blue-500/30 group"
        aria-label={isOpen ? 'Fechar chat' : 'Abrir chat'}
      >
        <div className="relative">
          {!isOpen ? (
            <>
              <svg className="w-7 h-7 text-white transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              {/* Indicador de notificação */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse" />
            </>
          ) : (
            <svg className="w-6 h-6 text-white transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          )}
        </div>

        {/* Efeito de ondas */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 animate-ping opacity-20" />
      </button>
    </div>
  );
}

export default Chatbot;