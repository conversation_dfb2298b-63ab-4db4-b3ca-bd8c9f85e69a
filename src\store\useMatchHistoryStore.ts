import { create } from "zustand";
import { MatchHistory } from "../api/api";
import { getMatchHistory, createMatchHistory, updateMatchHistory, deleteMatchHistory } from "../api/api";

interface MatchHistoryState {
  matchHistory: MatchHistory[];
  loading: boolean;
  error: string | null;
  fetchMatchHistory: (clubId: number, seasonId?: number, categoryId?: number) => Promise<void>;
  addMatchHistory: (clubId: number, match: Omit<MatchHistory, "id">) => Promise<void>;
  updateMatchHistory: (clubId: number, id: string, match: Partial<MatchHistory>) => Promise<void>;
  deleteMatchHistory: (clubId: number, id: string) => Promise<void>;
  syncMatchHistory: (clubId: number, seasonId?: number, categoryId?: number) => Promise<void>;
}

export const useMatchHistoryStore = create<MatchHistoryState>((set) => ({
  matchHistory: [],
  loading: false,
  error: null,

  fetchMatchHistory: async (clubId: number, seasonId?: number, categoryId?: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const matchHistory = await getMatchHistory(clubId, seasonId ?? undefined, categoryId ?? undefined);
      set({ matchHistory, loading: false });
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erro ao buscar histórico de partidas";
      set({ error: message, loading: false });
    }
  },

  addMatchHistory: async (clubId: number, match: Omit<MatchHistory, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newMatch = await createMatchHistory(clubId, match);
      set((state) => ({ matchHistory: [...state.matchHistory, newMatch], loading: false }));
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erro ao adicionar partida ao histórico";
      set({ error: message, loading: false });
    }
  },

  updateMatchHistory: async (clubId: number, id: string, match: Partial<MatchHistory>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMatchHistory(clubId, id, match);
      if (updated) {
        set((state) => ({ matchHistory: state.matchHistory.map(m => m.id === id ? updated : m), loading: false }));
      } else {
        set({ error: "Partida não encontrada", loading: false });
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erro ao atualizar histórico de partida";
      set({ error: message, loading: false });
    }
  },

  deleteMatchHistory: async (clubId: number, id: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteMatchHistory(clubId, id);
      if (ok) {
        set((state) => ({ matchHistory: state.matchHistory.filter(m => m.id !== id), loading: false }));
      } else {
        set({ error: "Partida não encontrada", loading: false });
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : "Erro ao deletar histórico de partida";
      set({ error: message, loading: false });
    }
  },

  // NOVAS FUNÇÕES DE SINCRONIZAÇÃO
  syncMatchHistory: async (clubId: number, seasonId?: number, categoryId?: number): Promise<void> => {
    const matchHistory = await getMatchHistory(clubId, seasonId ?? undefined, categoryId ?? undefined);
    set({ matchHistory });
  },
}));