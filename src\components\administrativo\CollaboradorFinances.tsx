import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useFinancialStore } from "@/store/useFinancialStore";
import {
  addCollaboratorFinancialEntry,
  removeCollaboratorFinancialEntry,
  calculateCollaboratorMonthlyBalance,
  calculateCollaboratorTotalBalance,
  getCollaboratorYearlyFinancialData,
  getCollaboratorBankingInfo,
  updateCollaboratorBankingInfo,
  CollaboratorFinancialData,
  CollaboratorFinancialEntry,
  CollaboratorBankingInfo,
} from "@/api/api";
import { cancelSalaryAdvance } from "@/api/salaryAdvances";
import { getCollaboratorFinancialData } from "@/api/collaboratorFinances";
import { Plus, Trash2, DollarSign, TrendingUp, TrendingDown, CreditCard, Edit, Save } from "lucide-react";
import { useUser } from "@/context/UserContext";

interface CollaboradorFinancesProps {
  collaboratorId: number;
  canEdit?: boolean;
  salaryAdvances?: any[];
  setSalaryAdvances?: (updater: (prev: any[]) => any[]) => void;
  collaborator?: any;
}

const MONTHS = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

export function CollaboradorFinances({ 
  collaboratorId, 
  canEdit = false, 
  salaryAdvances = [], 
  setSalaryAdvances,
  collaborator 
}: CollaboradorFinancesProps) {
  const clubId = useCurrentClubId();
  const { fetchTransactions } = useFinancialStore();
  const [financialData, setFinancialData] = useState<CollaboratorFinancialData>({});
  const [bankingInfo, setBankingInfo] = useState<CollaboratorBankingInfo>({});
  const [loading, setLoading] = useState(true);
  const [loadingBankingInfo, setLoadingBankingInfo] = useState(true);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [isAddEntryDialogOpen, setIsAddEntryDialogOpen] = useState(false);
  const [isBankingInfoDialogOpen, setIsBankingInfoDialogOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [description, setDescription] = useState("");
  const [isPositive, setIsPositive] = useState(false);
  const [transactionType, setTransactionType] = useState<"normal" | "vale" | "bonus">("normal");
  const [editingBankingInfo, setEditingBankingInfo] = useState<CollaboratorBankingInfo>({
    bank_name: "",
    account_number: "",
    agency: "",
    pix: ""
  });
  const [localSalaryAdvances, setLocalSalaryAdvances] = useState<any[]>(salaryAdvances || []);
  const { user } = useUser();

  // Sincronizar localSalaryAdvances quando a prop salaryAdvances mudar
  useEffect(() => {
    if (salaryAdvances) {
      setLocalSalaryAdvances(salaryAdvances);
    }
  }, [salaryAdvances]);

  // Carregar dados financeiros do colaborador
  useEffect(() => {
    const fetchFinancialData = async () => {
      try {
        setLoading(true);
        const data = await getCollaboratorFinancialData(clubId, collaboratorId);
        setFinancialData(data);
      } catch (err: any) {
        console.error("Erro ao carregar dados financeiros:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados financeiros",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    const fetchBankingInfo = async () => {
      try {
        setLoadingBankingInfo(true);
        const data = await getCollaboratorBankingInfo(clubId, collaboratorId);
        setBankingInfo(data);
        setEditingBankingInfo(data);
      } catch (err: any) {
        console.error("Erro ao carregar informações bancárias:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as informações bancárias",
          variant: "destructive",
        });
      } finally {
        setLoadingBankingInfo(false);
      }
    };

    if (collaboratorId) {
      fetchFinancialData();
      fetchBankingInfo();
    }
  }, [clubId, collaboratorId]);

  // Obter dados financeiros do ano selecionado
  const yearlyData = getCollaboratorYearlyFinancialData(financialData, selectedYear);

  // Calcular saldo total
  const totalBalance = calculateCollaboratorTotalBalance(financialData);

  // Função para adicionar uma entrada financeira
  const handleAddEntry = async () => {
    if (!amount.trim() || !description.trim()) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos",
        variant: "destructive",
      });
      return;
    }

    try {
      const amountValue = parseFloat(amount);
      if (isNaN(amountValue) || amountValue <= 0) {
        toast({
          title: "Erro",
          description: "Valor inválido",
          variant: "destructive",
        });
        return;
      }

      // Determinar o valor final e a categoria com base no tipo de transação
      let finalAmount = 0;
      let category = "salários";

      if (transactionType === "normal") {
        // Se isPositive for true, o clube está recebendo do colaborador (valor positivo)
        // Se isPositive for false, o colaborador está recebendo do clube (valor negativo)
        finalAmount = isPositive ? amountValue : -amountValue;
      } else if (transactionType === "vale") {
        // Vale não deve mais ser criado aqui - usar o diálogo específico
        toast({
          title: "Erro",
          description: "Use o diálogo específico para criar vales",
          variant: "destructive",
        });
        return;
      } else if (transactionType === "bonus") {
        // Bônus é sempre uma entrada para o colaborador (valor negativo para o clube)
        finalAmount = -amountValue;
        category = "bônus";
      }

      const updatedData = await addCollaboratorFinancialEntry(
        clubId,
        collaboratorId,
        selectedMonth,
        selectedYear,
        finalAmount,
        description,
        category
      );

      setFinancialData(updatedData);
      setIsAddEntryDialogOpen(false);
      setAmount("");
      setDescription("");
      setIsPositive(false);
      setTransactionType("normal");

      // Atualizar o store de finanças para refletir a nova transação
      fetchTransactions(clubId);

      toast({
        title: "Sucesso",
        description: "Entrada financeira adicionada com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao adicionar entrada financeira:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao adicionar entrada financeira",
        variant: "destructive",
      });
    }
  };

  // Função para remover uma entrada financeira
  const handleRemoveEntry = async (month: number, year: number, index: number) => {
    if (!confirm("Tem certeza que deseja remover esta entrada financeira?")) {
      return;
    }

    try {
      const updatedData = await removeCollaboratorFinancialEntry(
        clubId,
        collaboratorId,
        month,
        year,
        index
      );

      setFinancialData(updatedData);

      // Atualizar o store de finanças para refletir a remoção da transação
      fetchTransactions(clubId);

      toast({
        title: "Sucesso",
        description: "Entrada financeira removida com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao remover entrada financeira:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao remover entrada financeira",
        variant: "destructive",
      });
    }
  };

  // Função para cancelar um vale
  const handleCancelAdvance = async (advanceId: number) => {
    if (!confirm("Tem certeza que deseja cancelar este vale? Esta ação não pode ser desfeita.")) {
      return;
    }

    try {
      // Encontrar o vale para mostrar informações no feedback
      const advance = localSalaryAdvances.find(a => a.id === advanceId);
      const advanceValue = advance ? formatCurrency(advance.amount) : '';
      
      // Chamar a API para cancelar o vale
      if (!user?.id) {
        throw new Error("Usuário não autenticado");
      }
      await cancelSalaryAdvance(clubId, user.id, advanceId);
      
      // Atualizar a lista de vales marcando o vale como cancelado
      setLocalSalaryAdvances(prev => 
        prev.map(adv => 
          adv.id === advanceId ? { ...adv, status: "cancelled" } : adv
        )
      );
      
      // Atualizar também a prop se fornecida
      if (setSalaryAdvances) {
        setSalaryAdvances(prev => 
          prev.map(adv => 
            adv.id === advanceId ? { ...adv, status: "cancelled" } : adv
          )
        );
      }

      // Feedback de sucesso
      toast({
        title: "Vale cancelado",
        description: `Vale de ${advanceValue} foi cancelado com sucesso.`,
      });
      
      // Recarregar os dados financeiros para garantir sincronização
      const data = await getCollaboratorFinancialData(clubId, Number(collaboratorId));
      setFinancialData(data);
      
    } catch (err: any) {
      console.error("Erro ao cancelar vale:", err);
      
      let errorMessage = "Ocorreu um erro ao cancelar o vale. Por favor, tente novamente.";
      
      if (err.message.includes("permission")) {
        errorMessage = "Você não tem permissão para cancelar este vale.";
      } else if (err.message.includes("not found")) {
        errorMessage = "Vale não encontrado. Pode já ter sido cancelado.";
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      toast({
        title: "Erro ao cancelar",
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  // Função para atualizar informações bancárias
  const handleUpdateBankingInfo = async () => {
    try {
      const updatedInfo = await updateCollaboratorBankingInfo(clubId, collaboratorId, editingBankingInfo);
      setBankingInfo(updatedInfo);
      setIsBankingInfoDialogOpen(false);

      toast({
        title: "Sucesso",
        description: "Informações bancárias atualizadas com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao atualizar informações bancárias:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar informações bancárias",
        variant: "destructive",
      });
    }
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Finanças</span>
          <div className="flex items-center space-x-4 text-sm font-normal">
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              <span>Saldo Total: {formatCurrency(totalBalance)}</span>
            </div>
            {canEdit && (
              <>
                <Button
                  size="sm"
                  onClick={() => setIsAddEntryDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Nova Entrada
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsBankingInfoDialogOpen(true)}
                >
                  <CreditCard className="h-4 w-4 mr-1" />
                  {bankingInfo.bank_name ? "Editar Dados Bancários" : "Adicionar Dados Bancários"}
                </Button>
              </>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Seção de Dados Bancários */}
        <Card className="mb-6">
          <CardHeader className="py-3">
            <CardTitle className="text-base flex items-center">
              <CreditCard className="h-4 w-4 mr-2" />
              Dados Bancários
            </CardTitle>
          </CardHeader>
          <CardContent className="py-3">
            {loadingBankingInfo ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Banco</p>
                  <p className="font-medium">{bankingInfo.bank_name || "Não informado"}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Agência</p>
                  <p className="font-medium">{bankingInfo.agency || "Não informado"}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Conta</p>
                  <p className="font-medium">{bankingInfo.account_number || "Não informado"}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">PIX</p>
                  <p className="font-medium">{bankingInfo.pix || "Não informado"}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Seção de Transações Financeiras */}
        <h3 className="text-lg font-semibold mb-4">Transações Financeiras</h3>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex space-x-4">
              <div>
                <Label htmlFor="year">Ano</Label>
                <select
                  id="year"
                  className="border rounded px-3 py-1.5 w-full"
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                >
                  {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(
                    (year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    )
                  )}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {MONTHS.map((month, index) => {
                const monthNumber = index + 1;
                const entries = yearlyData[monthNumber] || [];
                const balance = calculateCollaboratorMonthlyBalance(
                  financialData,
                  monthNumber,
                  selectedYear
                );

                // Calcular salário base (apenas transações de salário, excluindo vales)
                const salarioBase = entries.filter(e =>
                  e.description.includes('Salário') &&
                  !e.description.toLowerCase().includes('vale') &&
                  !e.description.toLowerCase().includes('adiantamento')
                ).reduce((sum, e) => sum + Math.abs(e.amount), 0);

                // Buscar vales da tabela salary_advances em vez das transações financeiras
                const valesDoMes = salaryAdvances
                  .filter(advance =>
                    advance.person_type === 'collaborator' &&
                    advance.person_id.toString() === collaborator.id.toString() &&
                    advance.month === monthNumber &&
                    advance.year === selectedYear &&
                    advance.status === 'active'
                  )
                  .reduce((total, advance) => total + advance.amount, 0);

                const salarioLiquido = salarioBase - valesDoMes;

                return (
                  <Card key={month} className="overflow-hidden">
                    <CardHeader className="p-4">
                      <CardTitle className="text-base flex justify-between items-center">
                        <span>{month}</span>
                        <div className="text-right text-sm">
                          <div className="font-medium">R$ {salarioLiquido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</div>
                          {salarioBase > 0 && (
                            <div className="text-xs text-muted-foreground">
                              Base: R$ {salarioBase.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                              {valesDoMes > 0 && <div>Vales: R$ {valesDoMes.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</div>}
                            </div>
                          )}
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      {entries.length === 0 ? (
                        <div className="p-4 text-center text-gray-500 text-sm">
                          Nenhuma entrada
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Descrição</TableHead>
                              <TableHead className="text-right">Valor</TableHead>
                              {canEdit && <TableHead className="w-10"></TableHead>}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {entries.map((entry, entryIndex) => (
                              <TableRow key={entryIndex}>
                                <TableCell className="py-2">{entry.description}</TableCell>
                                <TableCell
                                  className={`py-2 text-right ${
                                    entry.amount > 0
                                      ? "text-green-600"
                                      : "text-red-600"
                                  }`}
                                >
                                  {formatCurrency(entry.amount)}
                                </TableCell>
                                {canEdit && (
                                  <TableCell className="py-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        handleRemoveEntry(
                                          monthNumber,
                                          selectedYear,
                                          entryIndex
                                        )
                                      }
                                    >
                                      <Trash2 className="h-4 w-4 text-red-500" />
                                    </Button>
                                  </TableCell>
                                )}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                      
                      {/* Tabela de Vales */}
                      {localSalaryAdvances.filter(adv => 
                        adv.person_type === 'collaborator' && 
                        adv.person_id === collaboratorId && 
                        adv.month === monthNumber && 
                        adv.year === selectedYear &&
                        adv.status === 'active'
                      ).length > 0 && (
                        <div className="mt-4">
                          <h4 className="text-sm font-medium mb-2">Vales</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Data</TableHead>
                                <TableHead>Valor</TableHead>
                                <TableHead>Status</TableHead>
                                {canEdit && <TableHead className="w-10"></TableHead>}
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {localSalaryAdvances
                                .filter(adv => 
                                  adv.person_type === 'collaborator' && 
                                  adv.person_id === collaboratorId && 
                                  adv.month === monthNumber && 
                                  adv.year === selectedYear
                                )
                                .map((advance, idx) => (
                                  <TableRow key={advance.id || idx}>
                                    <TableCell>
                                      {new Date(advance.created_at).toLocaleDateString('pt-BR')}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      {formatCurrency(advance.amount)}
                                    </TableCell>
                                    <TableCell>
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        advance.status === 'active' ? 'bg-green-100 text-green-800' :
                                        advance.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {advance.status === 'active' ? 'Ativo' :
                                         advance.status === 'cancelled' ? 'Cancelado' : advance.status}
                                      </span>
                                    </TableCell>
                                    {canEdit && advance.status === 'active' && (
                                      <TableCell>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleCancelAdvance(advance.id)}
                                          title="Cancelar vale"
                                        >
                                          <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                      </TableCell>
                                    )}
                                  </TableRow>
                                ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Modal para adicionar entrada financeira */}
        <Dialog open={isAddEntryDialogOpen} onOpenChange={setIsAddEntryDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Nova Entrada Financeira</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="month">Mês</Label>
                <select
                  id="month"
                  className="border rounded px-3 py-1.5 w-full"
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                >
                  {MONTHS.map((month, index) => (
                    <option key={month} value={index + 1}>
                      {month}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount">Valor</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Input
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Descrição da entrada"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="transaction_type">Tipo de Transação</Label>
                <div className="grid grid-cols-1 gap-2">
                  <label className="flex items-center space-x-2 p-2 border rounded-md hover:bg-gray-50">
                    <input
                      type="radio"
                      name="transaction_type"
                      checked={transactionType === "normal"}
                      onChange={() => setTransactionType("normal")}
                    />
                    <span>Transação Normal</span>
                  </label>
                  {/* Vale removido - agora é criado apenas através do diálogo específico */}
                  <label className="flex items-center space-x-2 p-2 border rounded-md hover:bg-gray-50">
                    <input
                      type="radio"
                      name="transaction_type"
                      checked={transactionType === "bonus"}
                      onChange={() => {
                        setTransactionType("bonus");
                        setIsPositive(false); // Bônus é sempre entrada para o colaborador (saída para o clube)
                      }}
                    />
                    <span>Bônus</span>
                  </label>
                </div>
              </div>

              {transactionType === "normal" && (
                <div className="space-y-2">
                  <Label htmlFor="type">Direção</Label>
                  <div className="flex space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        checked={!isPositive}
                        onChange={() => setIsPositive(false)}
                      />
                      <span className="flex items-center">
                        <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                        Pagamento ao colaborador
                      </span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        checked={isPositive}
                        onChange={() => setIsPositive(true)}
                      />
                      <span className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                        Recebimento do colaborador
                      </span>
                    </label>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddEntryDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleAddEntry}>Adicionar</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Modal para editar informações bancárias */}
        <Dialog open={isBankingInfoDialogOpen} onOpenChange={setIsBankingInfoDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Dados Bancários</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="bank_name">Banco</Label>
                <Input
                  id="bank_name"
                  value={editingBankingInfo.bank_name || ""}
                  onChange={(e) =>
                    setEditingBankingInfo({
                      ...editingBankingInfo,
                      bank_name: e.target.value,
                    })
                  }
                  placeholder="Nome do banco"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="agency">Agência</Label>
                <Input
                  id="agency"
                  value={editingBankingInfo.agency || ""}
                  onChange={(e) =>
                    setEditingBankingInfo({
                      ...editingBankingInfo,
                      agency: e.target.value,
                    })
                  }
                  placeholder="Número da agência"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="account_number">Conta</Label>
                <Input
                  id="account_number"
                  value={editingBankingInfo.account_number || ""}
                  onChange={(e) =>
                    setEditingBankingInfo({
                      ...editingBankingInfo,
                      account_number: e.target.value,
                    })
                  }
                  placeholder="Número da conta"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="pix">PIX</Label>
                <Input
                  id="pix"
                  type="text"
                  value={editingBankingInfo.pix || ""}
                  onChange={(e) =>
                    setEditingBankingInfo({
                      ...editingBankingInfo,
                      pix: e.target.value,
                    })
                  }
                  placeholder="Chave PIX"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsBankingInfoDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleUpdateBankingInfo}>Salvar</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
