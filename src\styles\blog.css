/* Blog Styles */

/* Prose styling for blog content */
.prose {
  color: #374151;
  max-width: none;
}

.prose h1 {
  color: #111827;
  font-weight: 800;
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 2rem;
  line-height: 1.1111111;
}

.prose h2 {
  color: #111827;
  font-weight: 700;
  font-size: 1.875rem;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.3333333;
  scroll-margin-top: 5rem;
}

.prose h3 {
  color: #111827;
  font-weight: 600;
  font-size: 1.5rem;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.6;
  scroll-margin-top: 5rem;
}

.prose h4 {
  color: #111827;
  font-weight: 600;
  font-size: 1.25rem;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.prose p {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  line-height: 1.75;
}

.prose ul {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  padding-left: 1.625rem;
}

.prose li {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.75;
}

.prose li::marker {
  color: #6b7280;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose a {
  color: #2563eb;
  text-decoration: underline;
  font-weight: 500;
}

.prose a:hover {
  color: #1d4ed8;
}

.prose blockquote {
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6rem;
  margin-bottom: 1.6rem;
  padding-left: 1rem;
}

.prose code {
  color: #111827;
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #f3f4f6;
  padding: 0.25rem 0.375rem;
  border-radius: 0.25rem;
}

.prose pre {
  color: #e5e7eb;
  background-color: #1f2937;
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding: 0.8571429em 1.1428571em;
}

.prose pre code {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

/* Line clamp utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Blog specific animations */
.blog-card-hover {
  transition: all 0.3s ease;
}

.blog-card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Table of contents active state */
.toc-active {
  position: relative;
}

.toc-active::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #2563eb;
}

/* Reading progress bar */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #2563eb, #7c3aed);
  transform-origin: left;
  z-index: 50;
}

/* Blog search highlight */
.search-highlight {
  background-color: #fef3c7;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .prose h1 {
    font-size: 2rem;
  }
  
  .prose h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
  }
  
  .prose h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
  }
}

/* Print styles */
@media print {
  .prose {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .prose h1, .prose h2, .prose h3 {
    page-break-after: avoid;
  }
  
  .prose p, .prose li {
    page-break-inside: avoid;
  }
}