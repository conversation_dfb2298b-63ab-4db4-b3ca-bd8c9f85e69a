export interface ChatRoom {
  id: string;
  club_id: string;
  name: string;
  description?: string;
  /** Optional friendly name for displaying in the UI */
  display_name?: string;
  is_general: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
  unread_count?: number;
  last_message?: ChatMessage;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  user_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
    metadata?: {
      file_url?: string;
      file_name?: string;
      file_size?: number;
      mentions?: string[];
      [key: string]: unknown;
    };
  reply_to?: string;
  edited_at?: string;
  created_at: string;
  user?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  reply_message?: ChatMessage;
}

export interface ChatRoomParticipant {
  id: string;
  room_id: string;
  user_id: string;
  joined_at: string;
  last_read_at: string;
  is_admin: boolean;
  user?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

export interface UserPresence {
  user_id: string;
  club_id: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen: string;
  updated_at: string;
  /** Optional convenience fields returned by the server */
  email?: string;
  name?: string;
  /** Full user object when available */
  user?: {
    id: string;
    name: string;
    email?: string;
    avatar_url?: string;
  };
}

export interface ChatState {
  rooms: ChatRoom[];
  currentRoom: ChatRoom | null;
  messages: Record<string, ChatMessage[]>;
  participants: Record<string, ChatRoomParticipant[]>;
  onlineUsers: UserPresence[];
  isLoading: boolean;
  error: string | null;
  isConnected?: boolean;
}

export interface SendMessageData {
  room_id: string;
  content: string;
  message_type?: 'text' | 'image' | 'file';
    metadata?: Record<string, unknown>;
  reply_to?: string;
}