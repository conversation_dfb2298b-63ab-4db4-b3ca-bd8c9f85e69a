/**
 * Utilitário para navegação com slug do clube
 * Para usar em componentes que não podem usar hooks
 */

// Cache do slug atual para evitar parsing repetido
let cachedSlug: string | null = null;
let lastPathname: string | null = null;

function getCurrentClubSlug(): string | null {
  const currentPathname = window.location.pathname;
  
  // Usar cache se o pathname não mudou
  if (lastPathname === currentPathname && cachedSlug) {
    return cachedSlug;
  }
  
  const pathParts = currentPathname.split('/');
  const slug = pathParts[1];
  
  // Validar se é um slug válido (não é uma página pública)
  const publicPages = ['login', 'register', 'blog', 'recursos', '', 'redirect-to-club'];
  if (slug && !publicPages.includes(slug)) {
    cachedSlug = slug;
    lastPathname = currentPathname;
    return slug;
  }
  
  return null;
}

export function navigateToClubPath(path: string) {
  const clubSlug = getCurrentClubSlug();
  
  if (clubSlug) {
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    // Usar navigate do React Router se disponível, senão usar window.location
    if (window.history && window.history.pushState) {
      window.history.pushState(null, '', `/${clubSlug}${cleanPath}`);
      // Disparar evento para que o React Router detecte a mudança
      window.dispatchEvent(new PopStateEvent('popstate'));
    } else {
      window.location.href = `/${clubSlug}${cleanPath}`;
    }
  } else {
    // Fallback para redirecionamento
    window.location.href = '/redirect-to-club';
  }
}

export function getClubPath(path: string): string {
  const clubSlug = getCurrentClubSlug();
  
  if (clubSlug) {
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `/${clubSlug}${cleanPath}`;
  }
  
  return path;
}

// Limpar cache quando a URL muda
window.addEventListener('popstate', () => {
  cachedSlug = null;
  lastPathname = null;
});