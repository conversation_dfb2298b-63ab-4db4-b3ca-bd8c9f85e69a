import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Trash2, Edit2, Check, X } from "lucide-react";
import type { MatchSquadMember } from "@/api/matchLineups";

interface StaffMemberCardProps {
  member: MatchSquadMember;
  onRemove: () => void;
  onEditRole: (newRole: string) => void;
}

export function StaffMemberCard({ member, onRemove, onEditRole }: StaffMemberCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editRole, setEditRole] = useState("");

  const handleStartEdit = () => {
    setEditRole(getCurrentRole());
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (editRole.trim() && editRole.trim() !== getCurrentRole()) {
      onEditRole(editRole.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditRole("");
  };

  const getCurrentRole = () => {
    // Priorizar custom_role se existir, senão usar a role do colaborador
    return (member as any).custom_role || 
           member.collaborator?.role || 
           member.user?.name || 
           getRoleLabel(member.role);
  };

  const getName = () => {
    return member.collaborator?.nickname ||
           member.collaborator?.full_name ||
           member.user?.name ||
           member.user?.email ||
           member.player?.name ||
           'Sem nome';
  };

  const getAvatar = () => {
    return member.collaborator?.image ||
           member.user?.avatar_url ||
           member.player?.image ||
           '';
  };

  const getInitials = () => {
    const name = getName();
    return name.split(' ').map(n => n.charAt(0)).join('').substring(0, 2).toUpperCase();
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'technical_staff':
        return 'Comissão Técnica';
      case 'staff':
        return 'Staff';
      case 'executive':
        return 'Diretoria';
      default:
        return role;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'technical_staff':
        return 'bg-blue-100 text-blue-800';
      case 'executive':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  return (
    <div className="group flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <Avatar className="w-10 h-10">
          <AvatarImage src={getAvatar()} />
          <AvatarFallback className="bg-blue-600 text-white font-bold text-xs">
            {getInitials()}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="font-medium truncate">{getName()}</div>
          
          {isEditing ? (
            <div className="flex items-center gap-2 mt-1">
              <Input
                value={editRole}
                onChange={(e) => setEditRole(e.target.value)}
                className="h-7 text-xs"
                placeholder="Função na partida..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveEdit();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
                autoFocus
              />
              <Button size="sm" variant="ghost" className="h-7 w-7 p-0" onClick={handleSaveEdit}>
                <Check className="h-3 w-3 text-green-600" />
              </Button>
              <Button size="sm" variant="ghost" className="h-7 w-7 p-0" onClick={handleCancelEdit}>
                <X className="h-3 w-3 text-red-600" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-muted-foreground truncate">
                {getCurrentRole()}
              </span>
              <Button
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleStartEdit}
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            </div>
          )}
          
          {/* Badge da categoria */}
          <Badge className={`text-xs mt-1 ${getRoleColor(member.role)}`}>
            {getRoleLabel(member.role)}
          </Badge>
        </div>
      </div>

      <div className="flex items-center gap-1 ml-2">
        {!isEditing && (
          <>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={handleStartEdit}
              title="Editar função"
            >
              <Edit2 className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={onRemove}
              title="Remover do squad"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </>
        )}
      </div>
    </div>
  );
}