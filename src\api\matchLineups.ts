import { supabase } from "@/integrations/supabase/client";
import { addPlayerToCallup } from "./callups";
import type { Player } from "./players";
import { getCollaboratorById } from "./collaborators";

// Tipos para o sistema de escalação por partida
export interface MatchLineup {
  id: number;
  club_id: number;
  match_id: string;
  formation: string;
  lineup: Record<string, string | null>; // posição -> player_id
  created_at: string;
  updated_at: string;
}

export interface MatchSquadMember {
  id: number;
  club_id: number;
  match_id: string;
  player_id?: string;
  user_id?: string;
  collaborator_id?: number;
  role: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
  custom_role?: string; // Nova propriedade para função personalizada
  position?: string;
  jersey_number?: number;
  created_at: string;
  // Dados relacionados (populados via join)
  player?: {
    id: string;
    name: string;
    position: string;
    number: number;
    image?: string;
    nickname?: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
    avatar_url?: string;
  };
  collaborator?: {
    nickname?: string;
    id: number;
    full_name: string;
    role: string;
    user_id?: string;
    image?: string;
  };
}

export interface MatchSubstitution {
  id: number;
  club_id: number;
  match_id: string;
  player_out_id: string;
  player_in_id: string;
  minute: number;
  reason?: string;
  created_at: string;
  // Dados relacionados
  player_out?: {
    id: string;
    name: string;
    number: number;
  };
  player_in?: {
    id: string;
    name: string;
    number: number;
  };
}

export interface MatchPlayerMinutes {
  id: number;
  club_id: number;
  match_id: string;
  player_id: string;
  minutes_played: number;
  started: boolean;
  substituted_in_minute?: number;
  substituted_out_minute?: number;
  created_at: string;
  updated_at: string;
  // Dados relacionados
  player?: {
    id: string;
    name: string;
    number: number;
    position: string;
  };
}

/**
 * Busca a escalação de uma partida específica
 */
export async function getMatchLineup(clubId: number, matchId: string): Promise<MatchLineup | null> {
  try {
    const { data, error } = await supabase
      .from("match_lineups")
      .select("*")
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .maybeSingle();

    if (error) {
      console.error("Erro ao buscar escalação da partida:", error);
      throw new Error(`Erro ao buscar escalação da partida: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    // Se a tabela não existir, retornar null
    if (error.message?.includes('relation "match_lineups" does not exist') ||
        error.message?.includes('Not Acceptable') ||
        error.code === 'PGRST106') {
      console.warn("Tabela match_lineups não existe. Execute o script SQL para criar as tabelas.");
      return null;
    }
    throw error;
  }
}

/**
 * Salva ou atualiza a escalação de uma partida
 */
export async function saveMatchLineup(
  clubId: number,
  matchId: string,
  lineup: Record<string, string | null>,
  formation: string
): Promise<MatchLineup> {
  // Verificar se já existe uma escalação para esta partida
  const existing = await getMatchLineup(clubId, matchId);

  let result: MatchLineup;

  if (existing) {
    // Atualizar escalação existente
    const { data, error } = await supabase
      .from("match_lineups")
      .update({
        lineup,
        formation,
        updated_at: new Date().toISOString()
      })
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar escalação da partida:", error);
      throw new Error(`Erro ao atualizar escalação da partida: ${error.message}`);
    }

    result = data;
  } else {
    // Criar nova escalação
    const { data, error } = await supabase
      .from("match_lineups")
      .insert({
        club_id: clubId,
        match_id: matchId,
        lineup,
        formation
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar escalação da partida:", error);
      throw new Error(`Erro ao criar escalação da partida: ${error.message}`);
    }

    result = data;
  }

  // Automaticamente sincronizar com o squad da partida
  await syncLineupWithSquad(clubId, matchId, lineup);

  // Automaticamente sincronizar com a convocação se existir
  await syncWithCallup(clubId, matchId);

  return result;
}

/**
 * Busca todos os membros do squad de uma partida
 */
export async function getMatchSquad(clubId: number, matchId: string): Promise<MatchSquadMember[]> {
  try {
    const { data, error } = await supabase
      .from("match_squad")
      .select(`
        *,
        player:players(id, name, position, number, image, nickname)
      `)
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .order("role", { ascending: true })
      .order("jersey_number", { ascending: true });

    if (error) {
      console.error("Erro ao buscar squad da partida:", error);
      throw new Error(`Erro ao buscar squad da partida: ${error.message}`);
    }

    // Se temos dados, buscar informações dos usuários e colaboradores separadamente
    if (data && data.length > 0) {
      const userIds = data.filter(item => item.user_id).map(item => item.user_id);
      const collaboratorIds = data.filter(item => item.collaborator_id).map(item => item.collaborator_id);

      let usersData: any[] = [];
      if (userIds.length > 0) {
        const { data: users, error: usersError } = await supabase
          .from("users")
          .select("id, email, name, avatar_url")
          .in("id", userIds);

        if (!usersError && users) {
          usersData = users;
        }
      }

      let collaboratorsData: any[] = [];
      if (collaboratorIds.length > 0) {
        const { data: collaborators, error: collaboratorsError } = await supabase
          .from("collaborators")
          .select("id, full_name, role, nickname, image")
          .in("id", collaboratorIds);

        if (!collaboratorsError && collaborators) {
          collaboratorsData = collaborators;
        }
      }

      // Combinar dados
      const enrichedData = data.map(item => ({
        ...item,
        user: item.user_id ? usersData.find(u => u.id === item.user_id) : null,
        collaborator: item.collaborator_id ? collaboratorsData.find(c => c.id === item.collaborator_id) : null
      }));

      return enrichedData;
    }

    return data || [];
  } catch (error: any) {
    // Se a tabela não existir, retornar array vazio
    if (error.message?.includes('relation "match_squad" does not exist') ||
        error.message?.includes('Not Acceptable') ||
        error.code === 'PGRST106') {
      console.warn("Tabela match_squad não existe. Execute o script SQL para criar as tabelas.");
      return [];
    }
    throw error;
  }
}

/**
 * Adiciona um membro ao squad da partida
 */
export async function addMatchSquadMember(
  clubId: number,
  matchId: string,
  member: {
    player_id?: string;
    user_id?: string;
    collaborator_id?: number;
    role: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
    custom_role?: string;
    position?: string;
    jersey_number?: number;
  }
): Promise<MatchSquadMember> {
  const { data, error } = await supabase
    .from("match_squad")
    .insert({
      club_id: clubId,
      match_id: matchId,
      ...member
    })
    .select(`
      *,
      player:players(id, name, position, number, image, nickname)
    `)
    .single();

  if (error) {
    console.error("Erro ao adicionar membro ao squad:", error);
    throw new Error(`Erro ao adicionar membro ao squad: ${error.message}`);
  }

  // Buscar informações do usuário se necessário
  let enrichedData = data;
  if (data.user_id) {
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, email, name")
      .eq("id", data.user_id)
      .single();

    if (!userError && userData) {
      enrichedData = {
        ...data,
        user: userData
      };
    }
  }

  // Buscar informações do colaborador se necessário
  if (data.collaborator_id) {
    const { data: collaboratorData, error: collaboratorError } = await supabase
      .from("collaborators")
      .select("id, full_name, role, nickname")
      .eq("id", data.collaborator_id)
      .single();

    if (!collaboratorError && collaboratorData) {
      enrichedData = {
        ...enrichedData,
        collaborator: collaboratorData
      };
    }
  }

  // Sincronizar com convocação após adicionar ao squad
  try {
    await syncWithCallup(clubId, matchId);
  } catch (syncError) {
    console.error("Erro ao sincronizar com convocação:", syncError);
    // Não falhar a operação principal
  }

  return enrichedData;
}

/**
 * Remove um membro do squad da partida
 */
export async function removeMatchSquadMember(
  clubId: number,
  matchId: string,
  memberId: number
): Promise<void> {
  const { error } = await supabase
    .from("match_squad")
    .delete()
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .eq("id", memberId);

  if (error) {
    console.error("Erro ao remover membro do squad:", error);
    throw new Error(`Erro ao remover membro do squad: ${error.message}`);
  }
}

/**
 * Atualiza um membro do squad da partida
 */
export async function updateMatchSquadMember(
  clubId: number,
  matchId: string,
  memberId: number,
  updates: {
    role?: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
    custom_role?: string;
    position?: string;
    jersey_number?: number;
  }
): Promise<MatchSquadMember> {
  const { data, error } = await supabase
    .from("match_squad")
    .update(updates)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .eq("id", memberId)
    .select(`
      *,
      player:players(id, name, position, number, image, nickname)
    `)
    .single();

  if (error) {
    console.error("Erro ao atualizar membro do squad:", error);
    throw new Error(`Erro ao atualizar membro do squad: ${error.message}`);
  }

  // Buscar informações do usuário se necessário
  let enrichedData = data;
  if (data.user_id) {
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, email, name")
      .eq("id", data.user_id)
      .single();

    if (!userError && userData) {
      enrichedData = {
        ...data,
        user: userData
      };
    }
  }

  return enrichedData;
}

/**
 * Busca todas as substituições de uma partida
 */
export async function getMatchSubstitutions(clubId: number, matchId: string): Promise<MatchSubstitution[]> {
  const { data, error } = await supabase
    .from("match_substitutions")
    .select(`
      *,
      player_out:players!match_substitutions_player_out_id_fkey(id, name, number),
      player_in:players!match_substitutions_player_in_id_fkey(id, name, number)
    `)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .order("minute", { ascending: true });

  if (error) {
    console.error("Erro ao buscar substituições da partida:", error);
    throw new Error(`Erro ao buscar substituições da partida: ${error.message}`);
  }

  return data || [];
}

/**
 * Registra uma substituição
 */
export async function createMatchSubstitution(
  clubId: number,
  matchId: string,
  substitution: {
    player_out_id: string;
    player_in_id: string;
    minute: number;
    reason?: string;
  }
): Promise<MatchSubstitution> {
  const { data, error } = await supabase
    .from("match_substitutions")
    .insert({
      club_id: clubId,
      match_id: matchId,
      ...substitution
    })
    .select(`
      *,
      player_out:players!match_substitutions_player_out_id_fkey(id, name, number),
      player_in:players!match_substitutions_player_in_id_fkey(id, name, number)
    `)
    .single();

  if (error) {
    console.error("Erro ao criar substituição:", error);
    throw new Error(`Erro ao criar substituição: ${error.message}`);
  }

  return data;
}

/**
 * Busca os minutos jogados por todos os jogadores de uma partida
 */
export async function getMatchPlayerMinutes(clubId: number, matchId: string): Promise<MatchPlayerMinutes[]> {
  const { data, error } = await supabase
    .from("match_player_minutes")
    .select(`
      *,
      player:players(id, name, number, position)
    `)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .order("minutes_played", { ascending: false });

  if (error) {
    console.error("Erro ao buscar minutos dos jogadores:", error);
    throw new Error(`Erro ao buscar minutos dos jogadores: ${error.message}`);
  }

  return data || [];
}

/**
 * Salva manualmente os minutos jogados de um jogador em uma partida
 */
export async function saveMatchPlayerMinutes(
  clubId: number,
  matchId: string,
  playerId: string,
  minutesPlayed: number,
  started: boolean
): Promise<MatchPlayerMinutes> {
  const { data, error } = await supabase
    .from("match_player_minutes")
    .upsert(
      {
        club_id: clubId,
        match_id: matchId,
        player_id: playerId,
        minutes_played: minutesPlayed,
        started,
      },
      {
        onConflict: "club_id, match_id, player_id",
      }
    )
    .select(
      `*, player:players(id, name, number, position)`
    )
    .single();

  if (error) {
    console.error("Erro ao salvar minutos do jogador:", error);
    throw new Error(`Erro ao salvar minutos do jogador: ${error.message}`);
  }

  return data as MatchPlayerMinutes;
}

/**
 * Finaliza uma partida e calcula os minutos finais de todos os jogadores
 */
export async function finalizeMatchMinutes(
  clubId: number,
  matchId: string,
  matchDuration: number = 90
): Promise<void> {
  // Buscar todos os jogadores que participaram da partida
  const squad = await getMatchSquad(clubId, matchId);
  const starters = squad.filter(m => m.role === 'starter' && m.player_id);
  const substitutes = squad.filter(m => m.role === 'substitute' && m.player_id);

  // Calcular minutos para jogadores titulares
  for (const starter of starters) {
    if (starter.player_id) {
      const { error } = await supabase.rpc('calculate_player_minutes', {
        p_club_id: clubId,
        p_match_id: matchId,
        p_player_id: starter.player_id,
        p_match_duration: matchDuration
      });

      if (error) {
        console.error("Erro ao calcular minutos do jogador:", error);
      }
    }
  }

  // Atualizar minutos dos substitutos que entraram
  const substitutions = await getMatchSubstitutions(clubId, matchId);
  for (const sub of substitutions) {
    const { error } = await supabase.rpc('calculate_player_minutes', {
      p_club_id: clubId,
      p_match_id: matchId,
      p_player_id: sub.player_in_id,
      p_match_duration: matchDuration
    });

    if (error) {
      console.error("Erro ao calcular minutos do substituto:", error);
    }
  }
}

/**
 * Busca jogadores disponíveis para escalação (da categoria da partida)
 */
export type PlayerWithCategories = Player & {
  categories: { id: number; name: string }[];
};

export async function getAvailablePlayersForMatch(
  clubId: number,
  matchId: string
): Promise<{ players: PlayerWithCategories[]; matchCategoryId: number | null; matchCompetitionId: string | null }> {
  // Buscar categoria e competição da partida
  const { data: match, error: matchError } = await supabase
    .from("matches")
    .select("category_id, competition_id")
    .eq("id", matchId)
    .eq("club_id", clubId)
    .single();

  const matchCategoryId = match?.category_id ?? null;
  const matchCompetitionId = match?.competition_id ?? null;
  if (matchError) {
    console.error("Erro ao buscar categoria da partida:", matchError);
  }

  // Buscar todos os jogadores do clube com suas categorias
  const { data: playersData, error: playersError } = await supabase
    .from("players")
    .select(
      `*,
       player_categories!left(
         categories!player_categories_category_id_fkey(id, name)
       )`
    )
    .eq("club_id", clubId)
    .neq("status", "inativo")
    .order("name");

  if (playersError) {
    console.error("Erro ao buscar dados dos jogadores:", playersError);
    throw new Error(`Erro ao buscar dados dos jogadores: ${playersError.message}`);
  }

  const players: PlayerWithCategories[] = (playersData || []).map((p: any) => ({
    ...(p as Player),
    categories: (p.player_categories || []).map((pc: any) => pc.categories),
  }));

  return { players, matchCategoryId, matchCompetitionId };
}

/**
 * Sincroniza a escalação com o squad da partida
 * Adiciona automaticamente jogadores titulares ao squad
 */
async function syncLineupWithSquad(
  clubId: number,
  matchId: string,
  lineup: Record<string, string | null>
): Promise<void> {
  try {
    // Buscar squad atual da partida
    const currentSquad = await getMatchSquad(clubId, matchId);

    // Extrair IDs dos jogadores titulares da escalação
    const starterPlayerIds = Object.values(lineup).filter(id => id !== null) as string[];

    // Encontrar jogadores que estão na escalação mas não no squad como titulares
    const currentStarters = currentSquad.filter(member => member.role === 'starter');
    const currentStarterIds = currentStarters.map(member => member.player_id).filter(Boolean) as string[];

    // Remover jogadores que não estão mais na escalação
    for (const member of currentStarters) {
      if (member.player_id && !starterPlayerIds.includes(member.player_id)) {
        await removeMatchSquadMember(clubId, matchId, member.id);
      }
    }

    // Adicionar novos jogadores titulares
    for (const playerId of starterPlayerIds) {
      if (!currentStarterIds.includes(playerId)) {
        // Verificar se o jogador já está no squad com outro papel
        const existingMember = currentSquad.find(member => member.player_id === playerId);

        if (existingMember) {
          // Atualizar papel para titular
          await updateMatchSquadMember(clubId, matchId, existingMember.id, {
            role: 'starter'
          });
        } else {
          // Adicionar como novo titular
          await addMatchSquadMember(clubId, matchId, {
            player_id: playerId,
            role: 'starter'
          });
        }
      }
    }

    // Atualizar posições dos titulares
    for (const [position, playerId] of Object.entries(lineup)) {
      if (playerId) {
        const member = currentSquad.find(m => m.player_id === playerId && m.role === 'starter');
        if (member && member.position !== position) {
          await updateMatchSquadMember(clubId, matchId, member.id, {
            position: position
          });
        }
      }
    }

  } catch (error) {
    console.error("Erro ao sincronizar escalação com squad:", error);
    // Não falhar a operação principal se a sincronização falhar
  }
}

/**
 * Mapeia a função de um membro do squad para a função correspondente na convocação
 */
function getCallupRoleFromSquadMember(member: MatchSquadMember): string {
  // SEMPRE usar a custom_role se existir - essa é a função específica definida na escalação
  if (member.custom_role && member.custom_role.trim()) {
    return member.custom_role.trim();
  }
  
  // Se não tem custom_role, usar a função do colaborador como fallback
  if (member.collaborator?.role) {
    return member.collaborator.role;
  }
  
  // Último fallback baseado no tipo de squad
  switch (member.role) {
    case 'technical_staff':
      return "Técnico";
    case 'executive':
      return "Diretor";
    case 'staff':
    default:
      return "Staff";
  }
}

/**
 * Sincroniza automaticamente com a convocação da partida
 * Adiciona jogadores e colaboradores do squad à convocação se ela existir
 */
async function syncWithCallup(clubId: number, matchId: string): Promise<void> {
  try {
    // Buscar a partida para obter informações da convocação, incluindo o callup_id
    const { data: match, error: matchError } = await supabase
      .from("matches")
      .select("id, date, category_id, callup_id, opponent") // Adicionado callup_id
      .eq("id", matchId)
      .single();

    if (matchError || !match) {
      console.log("Partida não encontrada para sincronização com convocação");
      return;
    }

    let targetCallup: { id: number; category_id: number | null; match_date: string } | null = null;

    // Tentar buscar a convocação pelo callup_id da partida, se existir
    if (match.callup_id) {
      const { data: directCallup, error: directCallupError } = await supabase
        .from("callups")
        .select("id, category_id, match_date")
        .eq("club_id", clubId)
        .eq("id", match.callup_id)
        .single();

      if (directCallupError) {
        console.error(`Erro ao buscar convocação pelo ID ${match.callup_id}:`, directCallupError);
        // Se houver erro, tentar a busca por data/categoria como fallback
      } else if (directCallup) {
        targetCallup = directCallup;
      }
    }

    // Se não encontrou pelo callup_id, tentar buscar pelo match_id
    if (!targetCallup) {
      const { data: callupByMatch, error: callupByMatchError } = await supabase
        .from("callups")
        .select("id, category_id, match_date")
        .eq("club_id", clubId)
        .eq("match_id", matchId)
        .single();

      if (!callupByMatchError && callupByMatch) {
        targetCallup = callupByMatch;
      }
    }

    // Se não encontrou pelo callup_id ou match_id, buscar por data e categoria
    // e tentar filtrar pelo adversário
    if (!targetCallup) {
      const matchDate = new Date(match.date);
      const startOfDay = new Date(matchDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(matchDate);
      endOfDay.setHours(23, 59, 59, 999);

      let { data: callups, error: callupError } = await supabase
        .from("callups")
        .select("id, category_id, match_date, match_id, matches(opponent)")
        .eq("club_id", clubId)
        .gte("match_date", startOfDay.toISOString())
        .lte("match_date", endOfDay.toISOString());

      if (callupError) {
        console.error("Erro ao buscar convocações:", callupError);
        return;
      }

      if (!callups || callups.length === 0) {
        // Se não encontrou na data exata, buscar convocações próximas (±3 dias)
        const threeDaysBefore = new Date(matchDate);
        threeDaysBefore.setDate(threeDaysBefore.getDate() - 3);
        const threeDaysAfter = new Date(matchDate);
        threeDaysAfter.setDate(threeDaysAfter.getDate() + 3);

        const { data: nearbyCallups, error: nearbyError } = await supabase
          .from("callups")
          .select("id, category_id, match_date, match_id, matches(opponent)")
          .eq("club_id", clubId)
          .gte("match_date", threeDaysBefore.toISOString())
          .lte("match_date", threeDaysAfter.toISOString());

        if (nearbyError || !nearbyCallups || nearbyCallups.length === 0) {
          console.log("Nenhuma convocação encontrada para sincronização");
          return;
        }

        callups = nearbyCallups;
      }

      // Filtrar por adversário quando possível
      if (match.opponent) {
        const opponentCallups = callups.filter(c => c.matches?.opponent === match.opponent);
        if (opponentCallups.length > 0) {
          callups = opponentCallups;
        }
      }

      // Encontrar convocação da mesma categoria ou a primeira disponível
      targetCallup = callups.find(c => c.category_id === match.category_id) || callups[0];
    }

    if (!targetCallup) {
      console.log("Nenhuma convocação alvo encontrada após todas as tentativas.");
      return;
    }

    // Buscar squad atual da partida
    const squad = await getMatchSquad(clubId, matchId);

    // Buscar jogadores já convocados para evitar duplicações
    const { data: existingCallupPlayers, error: existingError } = await supabase
      .from("callup_players")
      .select("player_id, user_id, name")
      .eq("club_id", clubId)
      .eq("callup_id", targetCallup.id);

    if (existingError) {
      console.error("Erro ao buscar jogadores já convocados:", existingError);
      return;
    }

    const existingPlayerIds = existingCallupPlayers?.map((p: any) => p.player_id).filter(Boolean) || [];
    const existingUserIds = existingCallupPlayers?.map((p: any) => p.user_id).filter(Boolean) || [];
    const existingNames = existingCallupPlayers?.map((p: any) => p.name).filter(Boolean) || [];

    // Adicionar jogadores do squad à convocação
    for (const member of squad) {
      try {
        if (member.player_id && !existingPlayerIds.includes(member.player_id)) {
          // Todos os jogadores (titulares e reservas) são "Atleta" na convocação
          const callupRole = "Atleta";

          await addPlayerToCallup(
            clubId,
            targetCallup.id,
            member.player_id, // playerId
            callupRole, // role
            undefined, // currentUserId (sem verificação de permissão)
            undefined, // userId
            undefined, // userName
            member.player?.name // playerName
          );
        } else if (member.user_id && !existingUserIds.includes(member.user_id)) {
          // Adicionar usuários (que não são jogadores)
          let callupRole = getCallupRoleFromSquadMember(member);

          await addPlayerToCallup(
            clubId,
            targetCallup.id,
            undefined, // playerId
            callupRole, // role
            undefined, // currentUserId
            member.user_id, // userId
            member.user?.name || member.user?.email, // userName
            undefined // playerName
          );
        } else if (member.collaborator_id) {
          // Adicionar colaboradores
          const collaborator = await getCollaboratorById(clubId, member.collaborator_id);
          if (!collaborator) {
            console.warn(`Colaborador com ID ${member.collaborator_id} não encontrado.`);
            continue;
          }

          let callupRole = getCallupRoleFromSquadMember(member);

          let userIdToAdd: string | undefined = undefined;
          let userNameToAdd: string = collaborator.full_name;

          if (collaborator.user_id) {
            userIdToAdd = collaborator.user_id;
          }

          const alreadyAdded = userIdToAdd
            ? existingUserIds.includes(userIdToAdd)
            : existingNames.includes(userNameToAdd);

          if (!alreadyAdded) {
            await addPlayerToCallup(
              clubId,
              targetCallup.id,
              undefined, // playerId
              callupRole, // role
              undefined, // currentUserId
              userIdToAdd, // userId (pode ser undefined se o colaborador não tiver user_id)
              userNameToAdd, // userName
              undefined // playerName
            );

            if (userIdToAdd) {
              existingUserIds.push(userIdToAdd);
            } else {
              existingNames.push(userNameToAdd);
            }
          }
        }
      } catch (error) {
        console.error(`Erro ao adicionar membro ${member.id} à convocação:`, error);
        // Continuar com os próximos membros mesmo se um falhar
      }
    }

    console.log(`Sincronização com convocação ${targetCallup.id} concluída`);
  } catch (error) {
    console.error("Erro ao sincronizar com convocação:", error);
    // Não falhar a operação principal se a sincronização falhar
  }
}