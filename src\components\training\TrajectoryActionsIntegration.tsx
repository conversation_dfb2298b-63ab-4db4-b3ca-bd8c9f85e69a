import React, { useState, use<PERSON><PERSON>back, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Target,
  Zap,
  ArrowRight,
  RotateCcw,
  Play,
  Pause,
  Settings,
  Trash2,
  Copy,
  Edit,
  Plus,
  Timer,
  Activity,
  Users,
  Circle,
  Square,
  Triangle,
  Star,
  Eye,
  EyeOff,
  TrendingUp,
  Save,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Gauge,
  Navigation
} from 'lucide-react';
import { TrajectoryAction, ActionTrigger, ElementInteraction } from './TrajectoryActions';
import { Trajectory, TrajectoryPoint } from './TrajectorySystem';
import { useAnimationEngine } from '@/hooks/useAnimationEngine';
import { TrainingElement } from './InteractiveTrainingBuilder';

interface TrajectoryActionsIntegrationProps {
  trajectories: Trajectory[];
  elements: TrainingElement[];
  onTrajectoriesChange: (trajectories: Trajectory[]) => void;
  onElementsChange: (elements: TrainingElement[]) => void;
  selectedTrajectoryId?: string;
  onSelectedTrajectoryChange?: (id: string | null) => void;
  isPlaying: boolean;
  onPlayStateChange: (playing: boolean) => void;
}

export function TrajectoryActionsIntegration({
  trajectories,
  elements,
  onTrajectoriesChange,
  onElementsChange,
  selectedTrajectoryId,
  onSelectedTrajectoryChange,
  isPlaying,
  onPlayStateChange
}: TrajectoryActionsIntegrationProps) {
  // State management
  const [actions, setActions] = useState<TrajectoryAction[]>([]);
  const [triggers, setTriggers] = useState<ActionTrigger[]>([]);
  const [interactions, setInteractions] = useState<ElementInteraction[]>([]);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [activeActions, setActiveActions] = useState<Set<string>>(new Set());
  const [actionHistory, setActionHistory] = useState<Array<{
    actionId: string;
    timestamp: number;
    success: boolean;
  }>>([]);

  // Animation engine integration
  const animationEngine = useAnimationEngine({
    settings: {
      fps: 30,
      duration: 30000, // 30 seconds
      quality: 'high',
      smoothing: true,
      interpolation: 'ease'
    },
    onFrameChange: (frame) => {
      const currentTime = (frame / 30) * 1000; // Convert frame to milliseconds
      checkAndExecuteActions(currentTime);
    },
    onPlayStateChange: (playing) => {
      onPlayStateChange(playing);
    }
  });

  const selectedTrajectory = useMemo(() =>
    selectedTrajectoryId ? trajectories.find(t => t.id === selectedTrajectoryId) : null,
    [selectedTrajectoryId, trajectories]
  );

  // Action type configurations with enhanced properties
  const actionTypes = {
    pass: {
      icon: Target,
      color: '#3b82f6',
      defaultIntensity: 70,
      defaultDuration: 1000,
      properties: ['direction', 'power', 'accuracy', 'technique', 'height'],
      visualEffects: ['trail', 'impact', 'arc'],
      soundEffect: '/sounds/pass.mp3'
    },
    shoot: {
      icon: Zap,
      color: '#ef4444',
      defaultIntensity: 90,
      defaultDuration: 800,
      properties: ['direction', 'power', 'accuracy', 'technique', 'height'],
      visualEffects: ['trail', 'impact', 'explosion'],
      soundEffect: '/sounds/shoot.mp3'
    },
    dribble: {
      icon: RotateCcw,
      color: '#10b981',
      defaultIntensity: 60,
      defaultDuration: 2000,
      properties: ['direction', 'technique', 'bodyPart'],
      visualEffects: ['zigzag', 'footwork'],
      soundEffect: '/sounds/dribble.mp3'
    },
    tackle: {
      icon: Activity,
      color: '#f59e0b',
      defaultIntensity: 80,
      defaultDuration: 500,
      properties: ['direction', 'power', 'technique'],
      visualEffects: ['collision', 'impact'],
      soundEffect: '/sounds/tackle.mp3'
    },
    run: {
      icon: TrendingUp,
      color: '#8b5cf6',
      defaultIntensity: 50,
      defaultDuration: 3000,
      properties: ['direction'],
      visualEffects: ['speed_lines'],
      soundEffect: null
    },
    walk: {
      icon: Users,
      color: '#06b6d4',
      defaultIntensity: 30,
      defaultDuration: 5000,
      properties: ['direction'],
      visualEffects: [],
      soundEffect: null
    },
    stop: {
      icon: Square,
      color: '#6b7280',
      defaultIntensity: 0,
      defaultDuration: 1000,
      properties: [],
      visualEffects: ['brake'],
      soundEffect: null
    },
    receive: {
      icon: Circle,
      color: '#84cc16',
      defaultIntensity: 40,
      defaultDuration: 500,
      properties: ['bodyPart', 'technique'],
      visualEffects: ['catch', 'control'],
      soundEffect: '/sounds/receive.mp3'
    },
    cross: {
      icon: ArrowRight,
      color: '#f97316',
      defaultIntensity: 75,
      defaultDuration: 1200,
      properties: ['direction', 'power', 'accuracy', 'height'],
      visualEffects: ['arc', 'trail'],
      soundEffect: '/sounds/cross.mp3'
    },
    header: {
      icon: Triangle,
      color: '#ec4899',
      defaultIntensity: 65,
      defaultDuration: 600,
      properties: ['direction', 'power', 'accuracy'],
      visualEffects: ['impact', 'head_movement'],
      soundEffect: '/sounds/header.mp3'
    }
  };

  // Create new action with enhanced properties
  const createAction = useCallback((type: TrajectoryAction['type']) => {
    if (!selectedTrajectory) return;

    const config = actionTypes[type];
    const currentTime = animationEngine.getCurrentTime();

    const newAction: TrajectoryAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: currentTime,
      type,
      intensity: config.defaultIntensity,
      duration: config.defaultDuration,
      properties: {
        direction: 0,
        power: config.defaultIntensity,
        accuracy: 80,
        technique: 'inside',
        bodyPart: 'right_foot',
        height: 'medium',
        spin: 'none'
      },
      visualFeedback: {
        color: config.color,
        icon: config.icon.name,
        size: 'medium',
        animation: 'pulse',
        showTrail: true,
        showImpact: true
      },
      soundEffect: config.soundEffect,
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} action at ${(currentTime / 1000).toFixed(1)}s`
    };

    setActions(prev => [...prev, newAction]);
    setSelectedAction(newAction.id);

    // Create default time-based trigger
    const defaultTrigger: ActionTrigger = {
      id: `trigger_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      actionId: newAction.id,
      condition: 'time',
      value: currentTime,
      tolerance: 100,
      active: true
    };

    setTriggers(prev => [...prev, defaultTrigger]);
  }, [selectedTrajectory, animationEngine]);

  // Enhanced action execution with visual feedback
  const executeAction = useCallback((action: TrajectoryAction) => {
    setActiveActions(prev => new Set([...prev, action.id]));

    // Add to history
    setActionHistory(prev => [...prev, {
      actionId: action.id,
      timestamp: Date.now(),
      success: true
    }]);

    // Create enhanced visual feedback
    createVisualFeedback(action);

    // Play sound effect
    if (action.soundEffect) {
      playSound(action.soundEffect);
    }

    // Apply action effects to elements
    applyActionEffects(action);

    // Remove from active actions after duration
    setTimeout(() => {
      setActiveActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(action.id);
        return newSet;
      });
    }, action.duration);
  }, []);

  // Enhanced visual feedback creation
  const createVisualFeedback = useCallback((action: TrajectoryAction) => {
    if (!selectedTrajectory) return;

    const currentPoint = getCurrentTrajectoryPoint(selectedTrajectory, animationEngine.getCurrentTime());
    if (!currentPoint) return;

    const feedbackElement = document.createElement('div');
    feedbackElement.className = `action-feedback action-${action.type}`;

    // Enhanced styling based on action type
    const config = actionTypes[action.type];
    feedbackElement.style.cssText = `
      position: absolute;
      background: ${action.visualFeedback.color};
      color: white;
      padding: 6px 12px;
      border-radius: 8px;
      font-size: 11px;
      font-weight: bold;
      pointer-events: none;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      border: 2px solid rgba(255,255,255,0.3);
      animation: ${action.visualFeedback.animation} 0.6s ease-in-out;
      transform: translateX(-50%) translateY(-100%);
    `;

    // Add icon and text
    feedbackElement.innerHTML = `
      <div style="display: flex; align-items: center; gap: 4px;">
        <span style="font-size: 14px;">⚡</span>
        <span>${action.type.toUpperCase()}</span>
        <span style="opacity: 0.8; font-size: 10px;">${action.intensity}%</span>
      </div>
    `;

    // Position feedback
    feedbackElement.style.left = `${currentPoint.x}px`;
    feedbackElement.style.top = `${currentPoint.y}px`;

    document.body.appendChild(feedbackElement);

    // Create additional visual effects
    if (action.visualFeedback.showTrail) {
      createTrailEffect(currentPoint, action);
    }

    if (action.visualFeedback.showImpact) {
      createImpactEffect(currentPoint, action);
    }

    // Remove feedback after animation
    setTimeout(() => {
      if (feedbackElement.parentNode) {
        feedbackElement.parentNode.removeChild(feedbackElement);
      }
    }, 1500);
  }, [selectedTrajectory, animationEngine]);

  // Create trail effect
  const createTrailEffect = useCallback((point: TrajectoryPoint, action: TrajectoryAction) => {
    const trail = document.createElement('div');
    trail.style.cssText = `
      position: absolute;
      width: 4px;
      height: 20px;
      background: linear-gradient(to bottom, ${action.visualFeedback.color}, transparent);
      left: ${point.x - 2}px;
      top: ${point.y - 10}px;
      pointer-events: none;
      z-index: 999;
      animation: fadeOut 1s ease-out forwards;
    `;

    document.body.appendChild(trail);

    setTimeout(() => {
      if (trail.parentNode) {
        trail.parentNode.removeChild(trail);
      }
    }, 1000);
  }, []);

  // Create impact effect
  const createImpactEffect = useCallback((point: TrajectoryPoint, action: TrajectoryAction) => {
    const impact = document.createElement('div');
    impact.style.cssText = `
      position: absolute;
      width: 20px;
      height: 20px;
      border: 2px solid ${action.visualFeedback.color};
      border-radius: 50%;
      left: ${point.x - 10}px;
      top: ${point.y - 10}px;
      pointer-events: none;
      z-index: 998;
      animation: expandFade 0.8s ease-out forwards;
    `;

    document.body.appendChild(impact);

    setTimeout(() => {
      if (impact.parentNode) {
        impact.parentNode.removeChild(impact);
      }
    }, 800);
  }, []);

  // Play sound effect
  const playSound = useCallback((soundUrl: string) => {
    try {
      const audio = new Audio(soundUrl);
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore audio play errors (user interaction required)
      });
    } catch (error) {
      // Ignore audio errors
    }
  }, []);

  // Apply action effects to elements
  const applyActionEffects = useCallback((action: TrajectoryAction) => {
    if (!selectedTrajectory) return;

    // Find the element associated with the trajectory
    const element = elements.find(e => e.id === selectedTrajectory.elementId);
    if (!element) return;

    // Apply temporary visual changes based on action type
    const updatedElements = elements.map(el => {
      if (el.id === element.id) {
        return {
          ...el,
          properties: {
            ...el.properties,
            // Add temporary highlight or effect
            highlight: true,
            highlightColor: action.visualFeedback.color,
            highlightDuration: action.duration
          }
        };
      }
      return el;
    });

    onElementsChange(updatedElements);

    // Remove highlight after duration
    setTimeout(() => {
      const resetElements = elements.map(el => {
        if (el.id === element.id) {
          return {
            ...el,
            properties: {
              ...el.properties,
              highlight: false,
              highlightColor: undefined,
              highlightDuration: undefined
            }
          };
        }
        return el;
      });
      onElementsChange(resetElements);
    }, action.duration);
  }, [selectedTrajectory, elements, onElementsChange]);

  // Check and execute actions based on current time and triggers
  const checkAndExecuteActions = useCallback((currentTime: number) => {
    if (!isPlaying) return;

    const actionsToExecute: TrajectoryAction[] = [];

    triggers.forEach(trigger => {
      if (!trigger.active) return;

      const action = actions.find(a => a.id === trigger.actionId);
      if (!action || activeActions.has(action.id)) return;

      let shouldTrigger = false;

      switch (trigger.condition) {
        case 'time':
          shouldTrigger = Math.abs(currentTime - trigger.value) <= trigger.tolerance;
          break;
        case 'position':
          if (selectedTrajectory) {
            const currentPoint = getCurrentTrajectoryPoint(selectedTrajectory, currentTime);
            if (currentPoint) {
              const targetX = trigger.value % 1000;
              const targetY = Math.floor(trigger.value / 1000);
              const distance = Math.sqrt(
                Math.pow(currentPoint.x - targetX, 2) +
                Math.pow(currentPoint.y - targetY, 2)
              );
              shouldTrigger = distance <= trigger.tolerance;
            }
          }
          break;
        case 'speed':
          if (selectedTrajectory) {
            const currentPoint = getCurrentTrajectoryPoint(selectedTrajectory, currentTime);
            if (currentPoint && currentPoint.speed) {
              shouldTrigger = Math.abs(currentPoint.speed - trigger.value) <= trigger.tolerance;
            }
          }
          break;
        case 'proximity':
          shouldTrigger = checkElementProximity(trigger.value, trigger.tolerance);
          break;
        case 'direction':
          if (selectedTrajectory) {
            const direction = getCurrentTrajectoryDirection(selectedTrajectory, currentTime);
            if (direction !== null) {
              const angleDiff = Math.abs(direction - trigger.value);
              shouldTrigger = Math.min(angleDiff, 360 - angleDiff) <= trigger.tolerance;
            }
          }
          break;
      }

      if (shouldTrigger) {
        actionsToExecute.push(action);
      }
    });

    // Execute all triggered actions
    actionsToExecute.forEach(action => {
      executeAction(action);
    });
  }, [isPlaying, triggers, actions, activeActions, selectedTrajectory, executeAction]);

  // Helper functions
  const getCurrentTrajectoryPoint = (trajectory: Trajectory, time: number): TrajectoryPoint | null => {
    if (trajectory.points.length === 0) return null;

    let closestPoint = trajectory.points[0];
    for (const point of trajectory.points) {
      if (point.timestamp <= time) {
        closestPoint = point;
      } else {
        break;
      }
    }

    return closestPoint;
  };

  const getCurrentTrajectoryDirection = (trajectory: Trajectory, time: number): number | null => {
    const currentPoint = getCurrentTrajectoryPoint(trajectory, time);
    if (!currentPoint) return null;

    const currentIndex = trajectory.points.findIndex(p => p === currentPoint);
    if (currentIndex === -1 || currentIndex === trajectory.points.length - 1) return null;

    const nextPoint = trajectory.points[currentIndex + 1];
    const dx = nextPoint.x - currentPoint.x;
    const dy = nextPoint.y - currentPoint.y;

    return Math.atan2(dy, dx) * (180 / Math.PI);
  };

  const checkElementProximity = (distance: number, tolerance: number): boolean => {
    // Implementation would check proximity to other elements
    return false;
  };

  // Add CSS animations
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
      
      @keyframes expandFade {
        from { 
          transform: scale(0.5); 
          opacity: 1; 
        }
        to { 
          transform: scale(2); 
          opacity: 0; 
        }
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }
      
      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
      }
      
      @keyframes flash {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.3; }
      }
      
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const selectedActionData = selectedAction ? actions.find(a => a.id === selectedAction) : null;
  const actionTriggers = selectedAction ? triggers.filter(t => t.actionId === selectedAction) : [];

  return (
    <div className="space-y-4">
      {/* Action Creation Panel */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Sistema de Ações Integrado
          </CardTitle>
          <CardDescription className="text-xs">
            Configure ações, triggers e feedback visual com integração completa ao sistema de animação
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Quick Action Creation */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Criar Ação Rápida</Label>
            {!selectedTrajectory ? (
              <p className="text-xs text-muted-foreground">
                Selecione uma trajetória para criar ações
              </p>
            ) : (
              <div className="grid grid-cols-3 gap-1">
                {Object.entries(actionTypes).slice(0, 6).map(([type, config]) => {
                  const IconComponent = config.icon;
                  return (
                    <Button
                      key={type}
                      variant="outline"
                      size="sm"
                      onClick={() => createAction(type as TrajectoryAction['type'])}
                      className="text-xs flex items-center gap-1"
                      style={{ borderColor: config.color + '40' }}
                    >
                      <IconComponent className="h-3 w-3" style={{ color: config.color }} />
                      {type}
                    </Button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Active Actions Display */}
          {activeActions.size > 0 && (
            <div className="space-y-2">
              <Label className="text-xs font-medium text-green-600">
                Ações Ativas ({activeActions.size})
              </Label>
              <div className="flex flex-wrap gap-1">
                {Array.from(activeActions).map(actionId => {
                  const action = actions.find(a => a.id === actionId);
                  if (!action) return null;
                  const config = actionTypes[action.type];
                  const IconComponent = config.icon;
                  return (
                    <Badge key={actionId} variant="default" className="text-xs animate-pulse">
                      <IconComponent className="h-3 w-3 mr-1" />
                      {action.type}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          {/* Actions List with Enhanced Display */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">
              Ações Configuradas ({actions.length})
            </Label>

            <div className="space-y-1 max-h-40 overflow-y-auto">
              {actions.map(action => {
                const config = actionTypes[action.type];
                const IconComponent = config.icon;
                const isActive = activeActions.has(action.id);
                const triggerCount = triggers.filter(t => t.actionId === action.id).length;

                return (
                  <div
                    key={action.id}
                    className={`p-2 rounded border cursor-pointer transition-all ${selectedAction === action.id
                      ? 'border-primary bg-primary/5'
                      : isActive
                        ? 'border-green-500 bg-green-50'
                        : 'border-muted hover:border-primary/50'
                      }`}
                    onClick={() => setSelectedAction(action.id)}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <IconComponent
                          className={`h-3 w-3 ${isActive ? 'animate-pulse' : ''}`}
                          style={{ color: config.color }}
                        />
                        <span className="text-xs font-medium">{action.type}</span>
                        <Badge variant="secondary" className="text-xs">
                          {(action.timestamp / 1000).toFixed(1)}s
                        </Badge>
                        {triggerCount > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {triggerCount} triggers
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-1">
                        {isActive && (
                          <CheckCircle className="h-3 w-3 text-green-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4"
                          onClick={(e) => {
                            e.stopPropagation();
                            executeAction(action);
                          }}
                        >
                          <Play className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      {action.description || `${action.type} action`}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Configuration Panel */}
      {selectedActionData && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configurar Ação: {selectedActionData.type}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Basic Properties */}
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Timestamp (s)</Label>
                <Input
                  type="number"
                  value={(selectedActionData.timestamp / 1000).toFixed(1)}
                  onChange={(e) => {
                    const newActions = actions.map(a =>
                      a.id === selectedActionData.id
                        ? { ...a, timestamp: parseFloat(e.target.value) * 1000 }
                        : a
                    );
                    setActions(newActions);
                  }}
                  className="text-xs"
                  step="0.1"
                />
              </div>

              <div>
                <Label className="text-xs">Duração (ms)</Label>
                <Input
                  type="number"
                  value={selectedActionData.duration}
                  onChange={(e) => {
                    const newActions = actions.map(a =>
                      a.id === selectedActionData.id
                        ? { ...a, duration: parseInt(e.target.value) }
                        : a
                    );
                    setActions(newActions);
                  }}
                  className="text-xs"
                />
              </div>
            </div>

            {/* Intensity Slider */}
            <div>
              <Label className="text-xs">Intensidade: {selectedActionData.intensity}%</Label>
              <Slider
                value={[selectedActionData.intensity]}
                onValueChange={([intensity]) => {
                  const newActions = actions.map(a =>
                    a.id === selectedActionData.id
                      ? { ...a, intensity }
                      : a
                  );
                  setActions(newActions);
                }}
                max={100}
                min={0}
                step={5}
                className="mt-1"
              />
            </div>

            {/* Triggers Management */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">
                  Triggers ({actionTriggers.length})
                </Label>
                <Select onValueChange={(condition) => {
                  const newTrigger: ActionTrigger = {
                    id: `trigger_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    actionId: selectedActionData.id,
                    condition: condition as ActionTrigger['condition'],
                    value: condition === 'time' ? selectedActionData.timestamp : 0,
                    tolerance: condition === 'time' ? 100 : 10,
                    active: true
                  };
                  setTriggers(prev => [...prev, newTrigger]);
                }}>
                  <SelectTrigger className="w-32 text-xs">
                    <SelectValue placeholder="+ Trigger" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="time">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        Tempo
                      </div>
                    </SelectItem>
                    <SelectItem value="position">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-3 w-3" />
                        Posição
                      </div>
                    </SelectItem>
                    <SelectItem value="speed">
                      <div className="flex items-center gap-2">
                        <Gauge className="h-3 w-3" />
                        Velocidade
                      </div>
                    </SelectItem>
                    <SelectItem value="direction">
                      <div className="flex items-center gap-2">
                        <Navigation className="h-3 w-3" />
                        Direção
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1 max-h-24 overflow-y-auto">
                {actionTriggers.map(trigger => (
                  <div
                    key={trigger.id}
                    className="flex items-center justify-between p-2 rounded border border-muted"
                  >
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={trigger.active}
                        onCheckedChange={(active) => {
                          const newTriggers = triggers.map(t =>
                            t.id === trigger.id ? { ...t, active } : t
                          );
                          setTriggers(newTriggers);
                        }}
                      />
                      <span className="text-xs font-medium">{trigger.condition}</span>
                      <Badge variant="outline" className="text-xs">
                        {trigger.condition === 'time'
                          ? `${(trigger.value / 1000).toFixed(1)}s`
                          : trigger.value
                        }
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        ±{trigger.tolerance}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4"
                      onClick={() => {
                        setTriggers(prev => prev.filter(t => t.id !== trigger.id));
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action History */}
      {actionHistory.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Timer className="h-4 w-4" />
              Histórico de Execução
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {actionHistory.slice(-10).reverse().map((entry, index) => {
                const action = actions.find(a => a.id === entry.actionId);
                if (!action) return null;

                return (
                  <div key={index} className="flex items-center justify-between text-xs p-2 rounded bg-muted/50">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>{action.type}</span>
                    </div>
                    <span className="text-muted-foreground">
                      {new Date(entry.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}